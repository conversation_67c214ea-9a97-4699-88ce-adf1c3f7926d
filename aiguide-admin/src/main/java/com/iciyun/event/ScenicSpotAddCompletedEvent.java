package com.iciyun.event;

import lombok.*;

import java.io.Serializable;

@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ScenicSpotAddCompletedEvent implements Serializable {
    private Integer id;
    private String scenicSpotId;
    /**
     * 景区名称
     */
    private String name;

    /**
     * 讲解点ID
     */
    private Long labelId;

    private CurrentStatus currentStatus;


    public enum CurrentStatus {
        /**
         * 景区新增已完成
         */
        SCENIC_SPOT_ADD_COMPLETED,


        /**
         * 景区补充地图信息已完成
         */
        SCENIC_SPOT_SUPPLEMENT_MAP_INFO_COMPLETED,

        /**
         * 讲解点新增已完成
         */
        LABEL_ADD_COMPLETED
    }

}
