package com.iciyun.event;

import com.iciyun.common.core.domain.model.UserStyleDto;
import com.iciyun.system.domain.ScenicSpot;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class StreamChatSyncCacheEvent {

    private String phoneNumber;

    private String syncId;

    private String question;

    private String content;

    /**
     *
     */
    private String status;

    private String chatId;


    //0 按住说话  1 景区简介   2 景区label  3 AI眼睛照片
    private Integer type = 0;

    /**
     * 音色 ID
     */
    private String voiceId;

    private String scenicId; // 景区id

    private String scenicName; // 景区名

    private String scenicLabel; // 景区label

    //是否缓存 true 缓存  false 不缓存
    private boolean cacheFlag;

    private UserStyleDto userStyleDto;

    private Long userId;

    ScenicSpot scenicSpot;

}
