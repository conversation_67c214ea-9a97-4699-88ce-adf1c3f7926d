package com.iciyun.event;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.iciyun.common.constant.CacheConstants;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;


@Service
@Slf4j
public class StreamChatSyncQueueService {

//    //key : phoneNumber  val: syncIdList
//    private final Map<String, List<String>> userSyncIds = new ConcurrentHashMap<>();
//
//    //key : syncId
//    private final Map<String, LinkedList<StreamChatSyncQueueData>> map = new ConcurrentHashMap<>();

    //key : syncId
    private final Cache<String, ConcurrentLinkedQueue<StreamChatSyncQueueData>> cache = CacheBuilder.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES) // 5分钟过期
            .concurrencyLevel(Runtime.getRuntime().availableProcessors()) // 并发级别=CPU核心数
            .maximumSize(1000)
            .build();


    public void push(StreamChatSyncQueueData queueData) {
        String syncId = queueData.getSyncId();

        ConcurrentLinkedQueue<StreamChatSyncQueueData> concurrentLinkedQueue = cache.getIfPresent(syncId);
        if (concurrentLinkedQueue == null) {
            concurrentLinkedQueue = new ConcurrentLinkedQueue<>();

        }
        concurrentLinkedQueue.add(queueData);
        cache.put(syncId, concurrentLinkedQueue);

//        LinkedList<StreamChatSyncQueueData> queue = map.get(syncId);
//        if (queue == null) {
//            queue = new LinkedList<>();
//        }
//        queue.add(queueData);
//        map.put(syncId, queue);
//
//        String phoneNumber = queueData.getPhoneNumber();
//        List<String> syncIds = userSyncIds.get(phoneNumber);
//        if (syncIds == null) {
//            syncIds = new ArrayList<>();
//        }
//        syncIds.add(syncId);
//        userSyncIds.put(phoneNumber, syncIds);

    }

    public StreamChatSyncQueueData get(String syncId) {

        ConcurrentLinkedQueue<StreamChatSyncQueueData> concurrentLinkedQueue = cache.getIfPresent(syncId);
        if (concurrentLinkedQueue == null || concurrentLinkedQueue.isEmpty()) {
            return StreamChatSyncQueueData.builder()
                    .syncId(syncId).status(CacheConstants.EVENT_WAIT)
                    .build();
        }

        return concurrentLinkedQueue.poll();

//        LinkedList<StreamChatSyncQueueData> queue = map.get(syncId);
//        if (queue == null) {
//            return StreamChatSyncQueueData.builder()
//                    .syncId(syncId).status(CacheConstants.EVENT_WAIT)
//                    .build();
//        }
//
//        if (queue.isEmpty()) {
//            return StreamChatSyncQueueData.builder()
//                    .syncId(syncId).status(CacheConstants.EVENT_WAIT)
//                    .build();
//        }
//
//        return queue.removeFirst();

    }

//    public void clear(String phoneNumber) {
//
//        List<String> syncIds = userSyncIds.remove(phoneNumber);
//        if (syncIds != null) {
//            syncIds.forEach(syncId -> {
//                LinkedList<StreamChatSyncQueueData> queue = map.remove(syncId);
//                if (queue != null) {
//                    queue.clear();
//                }
//            });
//        }
//
//    }

    @Data
    @Builder
    public static class StreamChatSyncQueueData implements Serializable {

        /**
         * 当前用户手机号
         */
        private String phoneNumber;

        /**
         * 流式异步 Id
         */
        private String syncId;

        /**
         * 文本内容
         */
        private String content;

        /**
         * mp3 地址
         */
        private String filePath;

        /**
         * event:wait: 等待
         * event:conversation.message.delta: 增量消息
         * event:done: 完成
         */
        private String status;


        private String chatId;

    }

    @Data
    @NoArgsConstructor
    public static class StreamChatSyncQueueRoundReq implements Serializable {


        /**
         * 流式异步 Id
         */
        private String syncId;

    }
}
