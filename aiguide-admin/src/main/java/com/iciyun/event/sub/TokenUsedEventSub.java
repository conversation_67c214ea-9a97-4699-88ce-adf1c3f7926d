package com.iciyun.event.sub;

import com.iciyun.event.TokenUsedEvent;
import com.iciyun.system.domain.UseToken;
import com.iciyun.system.service.IUseTokenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;


@Slf4j
@Component
@RequiredArgsConstructor
public class TokenUsedEventSub {

    private final IUseTokenService useTokenService;

    @Async
    @EventListener
    public void onEvent(TokenUsedEvent event) {
        log.info("游豆消耗订阅：{}", event.toString());
        try {
            UseToken useToken = event.getUseToken();
            useTokenService.save(useToken);
        } catch (Exception e) {
            log.error("游豆消耗订阅异常：{}", e);
            throw new RuntimeException(e);
        }
    }
}
