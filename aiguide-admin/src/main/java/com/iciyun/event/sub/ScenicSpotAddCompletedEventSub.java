package com.iciyun.event.sub;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.iciyun.amap.AMapService;
import com.iciyun.common.utils.HaversineFormula;
import com.iciyun.common.utils.StringUtils;
import com.iciyun.event.ScenicSpotAddCompletedEvent;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.SysTouristLabel;
import com.iciyun.system.domain.dto.GenTextAgainParam;
import com.iciyun.system.domain.qo.SyncTouristLabelQry;
import com.iciyun.system.service.IDistrictService;
import com.iciyun.system.service.IScenicSpotCustomizeService;
import com.iciyun.system.service.ISysTouristLabelService;
import com.iciyun.task.GenTextAgainService;
import com.iciyun.tmap.TMapClient;
import com.iciyun.web.controller.system.ScenicSpotSyncTmapService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Component
@RequiredArgsConstructor
public class ScenicSpotAddCompletedEventSub {

    private final IDistrictService districtService;
    private final AMapService aMapService;
    private final IScenicSpotCustomizeService scenicSpotCustomizeService;
    private final ISysTouristLabelService sysTouristLabelService;
    private final ScenicSpotSyncTmapService scenicSpotSyncTmapService;

    @Autowired
    GenTextAgainService genTextAgainService;

    @Value("${ai.add_second.add_url}")
    private String addSceondUrl;
    private final TMapClient tMapClient;

    @Async
    @EventListener
    public void onEvent(ScenicSpotAddCompletedEvent event) {

        ScenicSpotAddCompletedEvent.CurrentStatus currentStatus = event.getCurrentStatus();
        if (currentStatus == ScenicSpotAddCompletedEvent.CurrentStatus.SCENIC_SPOT_ADD_COMPLETED) {
            scenicSpotSyncTmapService.syncScenicSpot(event);
        }

    }

    /**
     * 同步讲解点
     * @param event
     */
    @Async
    @EventListener
    public void onEvent4labels(ScenicSpotAddCompletedEvent event) {
        ScenicSpotAddCompletedEvent.CurrentStatus currentStatus = event.getCurrentStatus();
        if (currentStatus == ScenicSpotAddCompletedEvent.CurrentStatus.SCENIC_SPOT_SUPPLEMENT_MAP_INFO_COMPLETED) {
            SyncTouristLabelQry qry = new SyncTouristLabelQry();
            qry.setId(event.getId());
            scenicSpotSyncTmapService.syncLabels(qry);
        }

    }

    /**
     * 同步讲解点
     * @param event
     */
    @Async
    @EventListener
    public void onEvent4labelJoin(ScenicSpotAddCompletedEvent event) {
        ScenicSpotAddCompletedEvent.CurrentStatus currentStatus = event.getCurrentStatus();
        if (currentStatus == ScenicSpotAddCompletedEvent.CurrentStatus.LABEL_ADD_COMPLETED) {

            SysTouristLabel condition = new SysTouristLabel();
            condition.setTouristId(event.getId().longValue());
            List<SysTouristLabel> sysTouristLabels = sysTouristLabelService.selectSyncTouris(condition);
            if (sysTouristLabels == null || sysTouristLabels.size() < 2) {
                log.info("[{}]景区内讲解点少于2个，无需进行聚合判断", event.getName());
                return;
            }

            // 按ID排序（可选，确保顺序稳定）
            sysTouristLabels.sort(Comparator.comparing(SysTouristLabel::getId));

            // 遍历所有标签对，避免重复比较
            for (int i = 0; i < sysTouristLabels.size(); i++) {
                SysTouristLabel labelA = sysTouristLabels.get(i);

                // 只比较i之后的标签（确保每对ID只比较一次）
                for (int j = i + 1; j < sysTouristLabels.size(); j++) {
                    SysTouristLabel labelB = sysTouristLabels.get(j);

                    // 获取经纬度坐标（使用 Apache Commons Lang 优化空值检查）
                    if (StringUtils.isBlank(labelA.getLatitude()) || StringUtils.isBlank(labelA.getLongitude()) ||
                            StringUtils.isBlank(labelB.getLatitude()) || StringUtils.isBlank(labelB.getLongitude())) {
                        continue;
                    }

                    // 转换经纬度为double类型
                    double latA = Double.parseDouble(labelA.getLatitude());
                    double lonA = Double.parseDouble(labelA.getLongitude());
                    double latB = Double.parseDouble(labelB.getLatitude());
                    double lonB = Double.parseDouble(labelB.getLongitude());

                    // 计算距离
                    double distance = HaversineFormula.calculateDistance(latA, lonA, latB, lonB);
                    if (distance < 10) {
                        labelB.setJoinCluster(false);
                        sysTouristLabelService.updateSysTouristLabel(labelB);
                        log.info("景区内:【{}】讲解点【{}】和讲解点【{}】距离小于10米，将讲解点【{}】设置为不聚合", labelA.getTouristName(), labelA.getLabelName(), labelB.getLabelName(), labelB.getLabelName());
                    }

                }
            }

        }

    }

    /**
     * 同步讲解点
     * @param event
     */
    @Async
    @EventListener
    public void onEvent4labelCache(ScenicSpotAddCompletedEvent event) {
        ScenicSpotAddCompletedEvent.CurrentStatus currentStatus = event.getCurrentStatus();
        if (currentStatus == ScenicSpotAddCompletedEvent.CurrentStatus.LABEL_ADD_COMPLETED) {
            SysTouristLabel condition = new SysTouristLabel();
            condition.setTouristId(event.getId().longValue());
            condition.setId(event.getLabelId());
            List<SysTouristLabel> sysTouristLabels = sysTouristLabelService.selectSyncTouris(condition);

            ScenicSpot scenicSpot = scenicSpotCustomizeService.getById(event.getId());

            //缓存景区简介
            {
                GenTextAgainParam genTextAgainParam = new GenTextAgainParam();
                genTextAgainParam.setLabelId(0L);
                genTextAgainParam.setType(0);
                genTextAgainParam.setScenicId(scenicSpot.getId().longValue());
                genTextAgainService.dealGenTextAgain(scenicSpot, genTextAgainParam);
            }

           //缓存景区label
            for (SysTouristLabel item:sysTouristLabels) {
                GenTextAgainParam genTextAgainParam = new GenTextAgainParam();
                genTextAgainParam.setLabelId(item.getId());
                genTextAgainParam.setType(0);
                genTextAgainParam.setScenicId(scenicSpot.getId().longValue());
                genTextAgainService.dealGenTextAgain(scenicSpot, item, genTextAgainParam);
            }

        }
    }



    @Async
    @EventListener
    public void onEvent4ToEmbeddingDataBase(ScenicSpotAddCompletedEvent event) {
        try {
            ScenicSpotAddCompletedEvent.CurrentStatus currentStatus = event.getCurrentStatus();
            if (currentStatus == ScenicSpotAddCompletedEvent.CurrentStatus.SCENIC_SPOT_ADD_COMPLETED) {
                ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getScenicSpotId, event.getScenicSpotId()).one();
                if( scenicSpot == null ){
                    log.info("新增景区后，同步到向量数据库，未查询到景区，{}", event.getScenicSpotId());
                }
                Map<String, Object> map = new HashMap<>();
                map.put("name", scenicSpot.getName());
                map.put("code", scenicSpot.getId());
                String response = HttpUtil.post(addSceondUrl, map);
                JSONObject obj = JSONUtil.parseObj(response);
                int code = obj.getInt("code");
                if (code != 200) {
                    log.info("新增景区后，同步到向量数据库，失败: " + code);
                }
            }
        }catch (Exception e){
            log.error("新增景区后，同步到向量数据库，异常：", e);
        }


    }


    public static void main(String[] args) {
        ScenicSpotAddCompletedEvent.CurrentStatus currentStatus = ScenicSpotAddCompletedEvent.CurrentStatus.SCENIC_SPOT_ADD_COMPLETED;
        if (currentStatus == ScenicSpotAddCompletedEvent.CurrentStatus.SCENIC_SPOT_ADD_COMPLETED) {
            System.out.println(currentStatus);
        }
    }

}
