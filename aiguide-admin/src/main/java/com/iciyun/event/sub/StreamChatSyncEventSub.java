package com.iciyun.event.sub;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import com.iciyun.common.constant.CacheConstants;
import com.iciyun.common.core.domain.entity.SysDictData;
import com.iciyun.common.core.domain.entity.TextSpeechCmd;
import com.iciyun.common.core.domain.model.UserStyleDto;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.common.utils.HelpMe;
import com.iciyun.event.StreamChatSyncEvent;
import com.iciyun.event.StreamChatSyncQueueService;
import com.iciyun.system.alitts.AliTokenManager;
import com.iciyun.system.alitts.SpeechSynthesizerRestful;
import com.iciyun.system.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Slf4j
@Component
public class StreamChatSyncEventSub {

    @Autowired
    private StreamChatSyncQueueService streamChatSyncQueueService;

    @Autowired
    GenAudioService genAudioService;

    @EventListener
    public void onEvent(StreamChatSyncEvent event) {

        String syncId = event.getSyncId();
        String content = event.getContent();
        String voiceId = event.getVoiceId();
        String status = event.getStatus();
        String phoneNumber = event.getPhoneNumber();
        String chatId = event.getChatId();
        Long userId = event.getUserId();
        String filePath = "";
        try {
            if (List.of(CacheConstants.EVENT_DELTA, CacheConstants.EVENT_PRE).contains(status)) {

                log.info("接收到流式文本：{}", content);

                filePath = genAudioService.genAudioByCoze(event.getUserStyleDto().getLanguageFlag(),voiceId, HelpMe.replaceContent(content));
            }
        } catch (Exception e) {
            log.error("文本换音频异常", e);
        }

        List<String> needPushs = List.of(CacheConstants.EVENT_DELTA, CacheConstants.CHAT_COMPLETED, CacheConstants.EVENT_PRE, CacheConstants.EVENT_DONE);
        if (needPushs.contains(status)) {
            StreamChatSyncQueueService.StreamChatSyncQueueData queueData = StreamChatSyncQueueService.StreamChatSyncQueueData.builder()
                    .syncId(syncId).content(content).filePath(filePath).status(status).phoneNumber(phoneNumber).chatId(chatId)
                    .build();
            log.info("流式对话准备入队 syncId:{} 文本内容:{} 音频地址:{} 状态:{}", syncId, content, filePath, status);
            streamChatSyncQueueService.push(queueData);
        }
    }

}
