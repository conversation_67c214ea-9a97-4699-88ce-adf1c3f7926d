package com.iciyun.event.sub;

import com.iciyun.common.constant.CacheConstants;
import com.iciyun.common.core.domain.entity.TextSpeechCmd;
import com.iciyun.common.core.domain.model.UserStyleDto;
import com.iciyun.common.enums.HobbyTypeEnum;
import com.iciyun.common.utils.HelpMe;
import com.iciyun.event.StreamChatSyncCacheEvent;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.SysTouristLabel;
import com.iciyun.system.mapper.SysTouristLabelMapper;
import com.iciyun.system.service.AsyncService;
import com.iciyun.system.service.GenAudioService;
import com.iciyun.task.ScenicCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@Component
public class StreamMsgSyncEventSub {

    @Autowired
    private AsyncService asyncService;
    @Autowired
    GenAudioService genAudioService;
    @Autowired
    ScenicCacheService scenicCacheService;
    @Autowired
    SysTouristLabelMapper sysTouristLabelMapper;

    @Async("threadPoolTaskExecutor")
    @EventListener
    public void onMessageCompleted(StreamChatSyncCacheEvent event) {

        String syncId = event.getSyncId();
        String content = event.getContent();

        ScenicSpot scenicSpot = event.getScenicSpot();

        String status = event.getStatus();
        UserStyleDto userStyleDto = event.getUserStyleDto();

        String filePath = "";
        List<String> filePaths = new ArrayList<>();

        if (status.equals(CacheConstants.MESSAGE_COMPLETED) && event.isCacheFlag()) {
            log.info("接收到全量文本：{}", content);
            content = HelpMe.replaceContent(content);

            filePath = genAudioService.genAudio(userStyleDto.getLanguageFlag(), event.getVoiceId(),content);

            filePaths.add(filePath);

            //景区缓存
            for(String path : filePaths){
                TextSpeechCmd.ScenicCache scenicCache = new TextSpeechCmd.ScenicCache();
                scenicCache.setScenicId(event.getScenicId());
                scenicCache.setScenicLabel(event.getScenicLabel());
                scenicCache.setSyncId(syncId);
                scenicCache.setText(content);
                scenicCache.setUserId(event.getUserId());
                scenicCache.setVoiceId(event.getVoiceId());
                scenicCache.setQuestion(event.getQuestion());
                scenicCache.setUserStyleDto(userStyleDto);

                asyncService.doCache(scenicCache, path);
            }

            if (event.getType()==1){
                scenicCacheService.saveLabelText(scenicSpot,userStyleDto, content);
            }else if (event.getType()==2){
                SysTouristLabel temp = sysTouristLabelMapper.selectByTouristIdAndLabel(event.getScenicLabel(), Long.parseLong(event.getScenicId()));
                if (temp != null) {
                    scenicCacheService.saveLabelText(temp, HobbyTypeEnum.of(userStyleDto.getGuideStyle()), userStyleDto.getLanguageFlag(), content);
                }
            }

        }
    }

}
