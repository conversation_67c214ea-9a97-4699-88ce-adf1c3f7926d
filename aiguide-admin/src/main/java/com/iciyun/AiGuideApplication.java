package com.iciyun;

import com.iciyun.common.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.core.env.Environment;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@Slf4j
@EnableAsync
@EnableRetry
//多数据源 关闭自动配置
@MapperScan({"com.iciyun.**.mapper"})//映射mapper地址
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class AiGuideApplication implements CommandLineRunner {
    public static void main(String[] args) {
        new SpringApplicationBuilder(AiGuideApplication.class)
                .allowCircularReferences(true)  // 显式允许循环依赖
                .run(args);
    }

    @Autowired
    private Environment evn;

    @Override
    public void run(String... args) throws Exception {

        String[] activeProfiles = evn.getActiveProfiles();
        if(activeProfiles != null && (activeProfiles[0].equals(Constants.PRO))) {
            Constants.isPro = true;
        }
        log.info("当前启动环境是否为生产环境：{}", Constants.isPro);

    }
}
