package com.iciyun.amap;

import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.context.annotation.Bean;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * <AUTHOR> 2023/8/15 10:05
 */
@Configurable
public class AMapConfig {

    @Bean
    public AMapApi aMapService() {
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl("https://restapi.amap.com/")
                .addConverterFactory(GsonConverterFactory.create())
                .build();
        return retrofit.create(AMapApi.class);
    }

}
