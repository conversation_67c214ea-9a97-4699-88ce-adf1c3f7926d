package com.iciyun.amap.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PlaceTextResp extends BaseResp{

    private List<PoiData> pois;

    @Data
    public static class PoiData {
        private String location;
        private String adcode;
        private String cityname;

        public String getX(){
            return location.split(",")[0];
        }

        public String getY(){
            return location.split(",")[1];
        }
    }

}



