package com.iciyun.amap.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DistrictResp extends BaseResp{

    private List<DistrictData> districts;

    @Data
    public static class DistrictData {
        private String adcode;
        private String name;
        private String center;
        private String level;

        private List<DistrictData> districts;

    }

}



