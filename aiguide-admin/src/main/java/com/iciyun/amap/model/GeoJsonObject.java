package com.iciyun.amap.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GeoJsonObject implements Serializable {

    private List<Feature> features;
    private String type;

}

@Data
class Feature {
    private Geometry geometry;
    private String type;
    private Properties properties;

}

@Data
class Geometry {
    private List<List<List<Double>>> coordinates;
    private String type;

}

@Data
class Properties {
    private String name;
}