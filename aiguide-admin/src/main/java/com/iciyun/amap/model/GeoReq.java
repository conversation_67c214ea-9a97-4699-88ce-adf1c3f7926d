package com.iciyun.amap.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.Map;

@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class GeoReq extends BaseReq {

    private String address;

    @Override
    public Map<String, Object> toMap() {
        return Map.of("address", this.address, "key", getKey());
    }

    public static void main(String[] args) {
        GeoReq req = GeoReq.builder().address("故宫").key("213123").build();
        Map<String, Object> map = req.toMap();
        System.out.println(map);
    }


}
