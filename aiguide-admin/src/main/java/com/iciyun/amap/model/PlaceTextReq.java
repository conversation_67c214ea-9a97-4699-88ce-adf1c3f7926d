package com.iciyun.amap.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PlaceTextReq extends BaseReq {

    /**
     * 需要被检索的地点文本信息。
     * 只支持一个关键字 ，文本总长度不可超过80字符
     */
    private String keywords;

    /**
     * 指定地点类型
     */
    private String types;

    @Override
    public Map<String, Object> toMap() {
        return Map.of(
                "key", getKey(),
                "keywords", getKeywords(),
                "types", getTypes()
        );
    }



}
