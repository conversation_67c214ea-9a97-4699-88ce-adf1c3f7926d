package com.iciyun.amap.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class GeoResp extends BaseResp{

    private List<GeoData> geocodes;

    @Data
    public static class GeoData {
        private String location;
        private String province;
        private String city;
        private String district;
        private String adcode;

        public String getX(){
            return location.split(",")[0];
        }

        public String getY(){
            return location.split(",")[1];
        }
    }

}



