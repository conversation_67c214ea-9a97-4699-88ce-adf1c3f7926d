package com.iciyun.amap;

/**
 * <AUTHOR> on 2025-04-15 11:17.
 */

import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;

import java.util.List;

public class AMapUtils {
    private static final String KEY = "982e13c796e6e80194ad94307ebc701d"; // 替换为你的API密钥

    public static List<String> addressToGPS(String address) {
        List<String> list = Lists.newArrayList();
        try {
            String url = String.format(
                    "https://restapi.amap.com/v3/geocode/geo?address=%s&key=%s&output=json",
                    address,
                    KEY
            );

            String result = HttpUtil.get(url);

            System.out.println(result);

            // 使用Jackson库解析JSON
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(result);

            // 提取geocodes数组
            JsonNode geocodesNode = rootNode.get("geocodes");

            // 遍历geocodes数组
            for (JsonNode geocodeNode : geocodesNode) {
                // 提取location字段,  坐标点：经度，纬度
                String location = geocodeNode.get("location").asText();
                list.add(location);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    public static void main(String[] args) {
        String address = "北京市朝阳区阜通东大街6号";
        List<String> list = addressToGPS(address);

        System.out.println(list);

    }


}
