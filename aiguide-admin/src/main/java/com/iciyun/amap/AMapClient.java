package com.iciyun.amap;

import com.iciyun.amap.model.*;
import com.iciyun.common.core.domain.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

import java.io.IOException;
import java.util.Map;

@Slf4j
@Service
public class AMapClient {

    @Value("${amap.apiKey}")
    private String key;

    private static AMapApi aMapApi;

    private static final Retrofit retrofit = new Retrofit.Builder()
            .baseUrl("https://restapi.amap.com/")
            .addConverterFactory(GsonConverterFactory.create())
            .build();

    static {
        if (aMapApi == null) {
            aMapApi = retrofit.create(AMapApi.class);
        }
    }

    public R<GeoResp> geo(GeoReq req) throws Exception {
        req.setKey(key);
        log.info("Geo请求:{}",req.toString());
        Call<GeoResp> call = aMapApi.geo(req.toMap());

        Response<GeoResp> response = call.execute();
        if (response.isSuccessful()) {
            GeoResp resp = response.body();
            return R.ok(resp);
        }
        return R.fail();
    }

    public R<DistrictResp> district(DistrictReq req) throws IOException {
        req.setKey(key);
        req.setSubdistrict("3");
        req.setExtensions("base");
        Map<String, Object> map = req.toMap();
        Call<DistrictResp> call = aMapApi.district(map);

        Response<DistrictResp> response = call.execute();
        if (response.isSuccessful()) {
            DistrictResp resp = response.body();
            return R.ok(resp);
        }
        return R.fail();
    }

    public R<PlaceTextResp> plactText(PlaceTextReq req) throws IOException {
        req.setKey(key);
        Map<String, Object> map = req.toMap();
        Call<PlaceTextResp> call = aMapApi.plactText(map);

        Response<PlaceTextResp> response = call.execute();
        if (response.isSuccessful()) {
            PlaceTextResp resp = response.body();
            return R.ok(resp);
        }
        return R.fail();
    }


}
