package com.iciyun.amap;

import com.alibaba.fastjson2.JSON;
import com.iciyun.amap.model.GeoReq;
import com.iciyun.amap.model.GeoResp;
import com.iciyun.common.core.domain.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AMapService {

    private final AMapClient aMapClient;

    public String[] getLatitudeAndLongitude(String address){
        GeoReq req = GeoReq.builder().address(address).build();
        try {
            R<GeoResp> respR = aMapClient.geo(req);
            log.info("地理编码结果：{}", JSON.toJSONString(respR));
            if (R.isSuccess(respR)) {
                GeoResp geoResp = respR.getData();
                GeoResp.GeoData geoData = geoResp.getGeocodes().stream().findFirst().get();
                String location = geoData.getLocation();
                return location.split(",");
            }
        } catch (Exception e) {
            log.error("获取位置经纬度失败", e);
            throw new RuntimeException("获取位置经纬度失败");
        }

        return null;
    }

}
