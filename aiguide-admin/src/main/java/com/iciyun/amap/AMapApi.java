package com.iciyun.amap;

import com.iciyun.amap.model.DistrictResp;
import com.iciyun.amap.model.GeoResp;
import com.iciyun.amap.model.PlaceTextResp;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.QueryMap;

import java.util.Map;

public interface AMapApi {

    /**
     * 地理编码
     * @param req
     * @return
     */
    @GET("v3/geocode/geo")
    Call<GeoResp> geo(@QueryMap Map<String, Object> req);


    /**
     * 行政区划
     * @param req
     * @return
     */
    @GET("/v3/config/district")
    Call<DistrictResp> district(@QueryMap Map<String, Object> req);


    /**
     * 关键字搜索 2.0
     * @param req
     * @return
     */
    @GET("/v5/place/text")
    Call<PlaceTextResp> plactText(@QueryMap Map<String, Object> req);

}
