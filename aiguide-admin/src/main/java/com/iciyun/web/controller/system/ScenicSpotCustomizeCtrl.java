package com.iciyun.web.controller.system;

import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iciyun.amap.AMapClient;
import com.iciyun.amap.AMapService;
import com.iciyun.amap.model.GeoJsonObject;
import com.iciyun.common.annotation.Anonymous;
import com.iciyun.common.annotation.Log;
import com.iciyun.common.core.controller.BaseController;
import com.iciyun.common.core.domain.R;
import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.common.core.page.TableDataInfo;
import com.iciyun.common.enums.BusinessType;
import com.iciyun.common.enums.LabelTypeEnum;
import com.iciyun.common.enums.PartnerBusinessType;
import com.iciyun.common.utils.HaversineFormula;
import com.iciyun.common.utils.SecurityUtils;
import com.iciyun.common.utils.StringUtils;
import com.iciyun.common.utils.uuid.IdHutool;
import com.iciyun.event.ScenicSpotAddCompletedEvent;
import com.iciyun.oss.OssService;
import com.iciyun.system.domain.*;
import com.iciyun.system.domain.bo.*;
import com.iciyun.system.domain.qo.LabelsQuery;
import com.iciyun.system.domain.qo.ScenicSpotListQuery;
import com.iciyun.system.domain.qo.SyncTouristLabelQry;
import com.iciyun.system.domain.vo.LabelsListVO;
import com.iciyun.system.domain.vo.ScenicSpotExportVO;
import com.iciyun.system.mapper.ScenicSpotCustomizeMapper;
import com.iciyun.system.service.*;
import com.iciyun.tmap.TMapClient;
import com.iciyun.tmap.model.*;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipInputStream;

/**
 * 景区管理
 *
 * <AUTHOR>
 * @since 2025-04-02 14:44:00
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/scenicSpot")
public class ScenicSpotCustomizeCtrl extends BaseController {

    private final AMapClient aMapClient;
    private final AMapService aMapService;
    private final TMapClient tMapClient;
    private final ScenicSpotCustomizeMapper scenicSpotCustomizeMapper;
    private final IScenicSpotCustomizeService scenicSpotCustomizeService;
    private final IScenicGuideService scenicGuideService;
    private final OssService ossService;
    private final IDistrictService districtService;
    private final ISysTouristLabelService sysTouristLabelService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final IdHutool idHutool;
    private final IScenicRatioService scenicRatioService;
    private final ScenicSpotSyncTmapService scenicSpotSyncTmapService;
    private final IScenicAdminService scenicAdminService;


    /**
     * 景区列表导出
     *
     * @param response
     */
    @Anonymous
    @GetMapping("/export")
    public void export(HttpServletResponse response, ScenicSpotListQuery qry) {
        String name = "景区列表";
        // 设置响应头信息
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(name, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        } catch (UnsupportedEncodingException e) {
            log.error("设置响应头失败", e);
            return;
        }
        List<ScenicSpot> list = scenicSpotCustomizeService
                .lambdaQuery()
                .like(Objects.nonNull(qry.getName()), ScenicSpot::getName, qry.getName())
                .eq(Objects.nonNull(qry.getLevel()), ScenicSpot::getLevel, qry.getLevel())
                .eq(Objects.nonNull(qry.getStatus()), ScenicSpot::getStatus, qry.getStatus())
                .like(Objects.nonNull(qry.getKahuna()), ScenicSpot::getKahuna, qry.getKahuna())
                .like(Objects.nonNull(qry.getDetailAddress()), ScenicSpot::getDetailAddress, qry.getDetailAddress())
                .list();

        List<ScenicSpotExportVO> vos = list.stream().map(scenicSpot -> {
            return new ScenicSpotExportVO(scenicSpot);
        }).toList();

        try {
            EasyExcel.write(response.getOutputStream())
                    .head(ScenicSpotExportVO.class)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet(name)
                    .doWrite(vos);
        } catch (IOException e) {
            log.error("导出失败: ", e);
        }

    }

    /**
     * 批发市场列表导出
     *
     * @param response
     * @throws IOException
     */
    @Anonymous
    @Deprecated
    @GetMapping("/export2")
    public void export2(HttpServletResponse response) {

        // 设置响应头信息
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("批发市场列表", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        } catch (UnsupportedEncodingException e) {
            log.error("设置响应头失败", e);
            return;
        }

        int totalPages = 0;
        int pageIndex = 1;
        List<String> list = new ArrayList<>();
        List<SearchExport> mapList = new ArrayList<>();
        do {
            try {
                String boundary = String.format("region(%s,%d)", "重庆市", 0);
                SearchReq req = SearchReq.builder()
                        .keyword("批发市场")
//                        .filter(scenicSpot.getFilter())
                        .page_size(Const.PAGE_SIZE_DEFAULT).page_index(String.valueOf(pageIndex))
                        .boundary(boundary)
                        .build();

                SearchResp searchResp = tMapClient.search(req);
                String count = searchResp.getCount();

                if (totalPages == 0) {
                    int i = Integer.parseInt(count);
                    totalPages = (i + Integer.parseInt(Const.PAGE_SIZE_DEFAULT) - 1) / Integer.parseInt(Const.PAGE_SIZE_DEFAULT);
                }
                pageIndex++;
                List<SearchResp.RespData> data = searchResp.getData();
                for (SearchResp.RespData respData : data) {

                    if (list.contains(respData.getTitle())) {
                        continue;
                    }

                    SearchExport export = new SearchExport();
                    export.setTitle(respData.getTitle());
                    export.setTel(respData.getTel());
                    export.setAddress(respData.getAddress());
                    export.setCategory(respData.getCategory());

                    AdInfo adInfo = respData.getAd_info();
                    Adcode adcode = new Adcode(adInfo.getAdcode());
                    export.setProvince(adInfo.getProvince());
                    export.setCity(adInfo.getCity());
                    export.setDistrict(adInfo.getDistrict());

                    export.setProvinceCode(adcode.getProvinceCode());
                    export.setCityCode(adcode.getCityCode());
                    export.setDistrictCode(adcode.getDistrictCode());
                    list.add(respData.getTitle());
                    mapList.add(export);
                }
            } catch (Exception e) {
                log.error("区域搜索失败: ", e);
            }
        } while (pageIndex <= totalPages);


        try {
            EasyExcel.write(response.getOutputStream())
                    .head(SearchExport.class)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet("批发市场列表")
                    .doWrite(mapList);
        } catch (IOException e) {
            log.error("导出失败: ", e);
        }

    }

    /**
     * 更新景区状态
     *
     * @param cmd
     * @return
     */
    @PostMapping("/onStatus")
    public R<Void> onStatus(@RequestBody OnStatus4ScenicSpotCmd cmd) {

        try {
            scenicSpotCustomizeService.lambdaUpdate()
                    .eq(ScenicSpot::getId, cmd.getId())
                    .set(ScenicSpot::getStatus, cmd.getStatus())
                    .update();
        } catch (Exception e) {
            log.error("更新景区状态失败", e);
            return R.fail("更新景区状态失败");
        }
        return R.ok();
    }

    /**
     * 获取ID
     *
     * @return
     */
    @GetMapping("/getCode")
    public R<String> getCode(String code) {

        try {
            return R.ok(idHutool.genCode(code));
        } catch (Exception e) {
            log.error("获取ID失败", e);
            return R.fail("获取ID失败");
        }
    }

    /**
     * 查询景区
     */

    @Anonymous
    @GetMapping("/queryByName")
    public R queryByName(ScenicSpot qry) {
        List<ScenicSpot> scenicSpotList = scenicSpotCustomizeService.
                lambdaQuery()
                .like(ScenicSpot::getName, qry.getName())
                .list();
        return R.ok(scenicSpotList);
    }


    /**
     * 景区详情
     *
     * @param qry 搜索关键词
     * @return 景区列表视图
     */
    @Anonymous
    @GetMapping("/get")
    public R<ScenicSpot> listScenicSpots(ScenicSpot qry) {
        // 根据关键词查询景区列表
        ScenicSpot scenicSpot = scenicSpotCustomizeService.
                lambdaQuery()
                .eq(ScenicSpot::getName, qry.getName())
                .one();
        return R.ok(scenicSpot);
    }


    /**
     * 景区列表
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermi('cooperate:guide:list')")
    @Log(title = "景区管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/selectAll")
    public TableDataInfo queryScenicSpotBrowseHistory(ScenicSpotListQuery qry) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        startPage();
        if(!user.isAdmin()){
            qry.setUserId(user.getUserId());
        }
        List<ScenicSpot> list = scenicSpotCustomizeService.selectByUser(qry);
        return getDataTable(list);
    }

    /**
     * 新增景区
     *
     * @param cmd
     * @return
     */
    @PostMapping("/add")
    public R<Void> add(@RequestBody ScenicSpotAddCmd cmd) {

        try {

            ScenicSpot scenicSpot = new ScenicSpot(cmd);
            scenicSpotCustomizeService.save(scenicSpot);
            applicationEventPublisher.publishEvent(
                    ScenicSpotAddCompletedEvent.builder()
                            .id(scenicSpot.getId()).scenicSpotId(scenicSpot.getScenicSpotId())
                            .name(scenicSpot.getName()).currentStatus(ScenicSpotAddCompletedEvent.CurrentStatus.SCENIC_SPOT_SUPPLEMENT_MAP_INFO_COMPLETED)
                            .build()
            );

            return R.ok();
        } catch (Exception e) {
            log.error("新增景区失败", e);
            return R.fail("新增景区失败");
        }
    }

    /**
     * 编辑景区PC运营后台
     *
     * @param cmd
     * @return
     */
    @PostMapping("/edit")
    public R<Void> edit(@RequestBody ScenicSpotEditCmd cmd) {

        try {
            scenicSpotCustomizeService.edit(cmd);
            return R.ok();
        } catch (Exception e) {
            log.error("编辑景区失败", e);
            return R.fail("编辑景区失败");
        }
    }

    /**
     * 编辑景区补充腾讯地图信息
     *
     * @param cmd
     * @return
     */
    @Anonymous
    @PostMapping("/checkEdit")
    public R<Void> checkEdit(@RequestBody ScenicSpot cmd) {

        try {

            String tmapScenicName = cmd.getTmapScenicName();
            // 经纬度
            SuggestionReq req = SuggestionReq.builder().keyword(tmapScenicName).build();
            R<SuggestionResp> suggestion = tMapClient.suggestion(req);
            if (R.isSuccess(suggestion)) {
                SuggestionResp data = suggestion.getData();
                List<SuggestionResp.RespData> dataList = data.getData();
                if (dataList != null && !dataList.isEmpty()) {
                    SuggestionResp.RespData respData = dataList.stream().findFirst().get();

                    Location location = respData.getLocation();
                    Adcode adcode = new Adcode(respData.getAdcode());

                    cmd.setLatitude(location.getLng());
                    cmd.setLongitude(location.getLat());
                    cmd.setTmapStatus(1);
                    cmd.setDetailAddress(respData.getAddress());

                    cmd.setProvinceName(respData.getProvince());
                    cmd.setCityName(respData.getCity());
                    cmd.setDistrictName(respData.getDistrict());

                    cmd.supplementDistrictCode(adcode.getProvinceCode(), adcode.getCityCode(), adcode.getDistrictCode());

                    cmd.setPoiId(respData.getId());
                    cmd.setTmapScenicName(respData.getTitle());
                }
            }

            cmd.checkEdit();

            scenicSpotCustomizeService.updateById(cmd);

            scenicGuideService.upScenic(cmd.getId(), cmd.getName(), cmd.getAddress());

            return R.ok();
        } catch (Exception e) {
            log.error("编辑景区失败", e);
            return R.fail("编辑景区失败");
        }
    }

    /**
     * 查看景区
     *
     * @param qry
     * @return
     */
    @GetMapping("/look")
    @Anonymous
    public R<ScenicSpot> look(LabelsQuery qry) {

        try {
            ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, qry.getId()).one();
            List<ScenicRatio> list = scenicRatioService.lambdaQuery()
                    .eq(ScenicRatio::getScenicId, scenicSpot.getId())
                    .list();
            scenicSpot.setCooperate(list.size() > 0 ? true : false);

            qry.setTouristId(Long.valueOf(qry.getId()));
            List<SysTouristLabel> sysTouristLabels = sysTouristLabelService.listQuery(qry);

            List<SysTouristLabel> distinctSysTouristLabels = sysTouristLabels.stream()
                    .filter(sysTouristLabel -> {
                        if (LabelTypeEnum.POI.getCode().equals(sysTouristLabel.getLabelType())) {
                            return Objects.nonNull(sysTouristLabel.getLongitude());
                        }
                        return true;
                    }) // 过滤掉经度为空的数据
                    .collect(Collectors.toMap(
                            SysTouristLabel::getLabelName,                      // 使用经度作为键
                            Function.identity(),                                // 使用原始对象作为值
                            (oldValue, newValue) -> oldValue,                   // 如果键重复，保留旧值
                            LinkedHashMap::new                                 // 保持插入顺序
                    )).values().stream().toList();

            scenicSpot.setSysTouristLabels(distinctSysTouristLabels);

            //查询景区数据采集信息
            scenicSpot.setScenicInfoFlag(false);
            try {
                SysUser user = SecurityUtils.getLoginUser().getUser();
                if (user != null) {
                    List<ScenicAdmin> adminList = scenicAdminService.lambdaQuery()
                            .eq(ScenicAdmin::getBusinessCode, scenicSpot.getScenicSpotId())
                            .eq(ScenicAdmin::getUserPhone, user.getUserName())
                            .eq(ScenicAdmin::getBusinessType,  PartnerBusinessType.SCENIC_INFO.getCode())
                            .list();
                    if (CollectionUtils.isNotEmpty(adminList)) {
                        scenicSpot.setScenicInfoFlag(true);
                    }
                }
            } catch (Exception e) {
                log.error("用户未登录！");
            }
            return R.ok(scenicSpot);
        } catch (Exception e) {
            log.error("查看景区失败", e);
            return R.fail("查看景区失败");
        }
    }

    /**
     * 导入景区数据
     *
     * @param file
     * @return
     */
    @Anonymous
    @PostMapping("/importScenicSpot")
    public R<Void> importScenicSpot(@RequestParam("file") MultipartFile file) {

        try {
            List<ScenicSpotImportCmd> list = EasyExcel.read(file.getInputStream()).head(ScenicSpotImportCmd.class).sheet().doReadSync();
            scenicSpotSyncTmapService.handleImport(list);
            return R.ok();
        } catch (IOException e) {
            log.error("导入景区数据异常:", e);
            return R.fail("导入景区数据异常");
        }

    }

    /**
     * 同步景区数据导览图
     *
     * @return
     */
    @Anonymous
    @PostMapping("/syncScenicSpotGuideMap")
    public R<Object> syncScenicSpotGuideMap(@RequestParam("file") MultipartFile file) {

        List<String> imgExtensions = List.of(".jpg", ".jpeg", ".png", ".gif", ".webp");

        try (ZipInputStream zip = new ZipInputStream(file.getInputStream())) {
            List<Map<String, String>> guideMaps = new ArrayList<>();

            try (ZipArchiveInputStream zis = new ZipArchiveInputStream(file.getInputStream(), "UTF-8")) {
                ZipArchiveEntry entry;
                while ((entry = zis.getNextZipEntry()) != null) {

                    if (entry.isDirectory()) {
                        continue;
                    }

                    String entryName = entry.getName();
                    log.info("文件名称: {}", entryName);
                    String extension = entryName.substring(entryName.lastIndexOf("."));
                    if (!imgExtensions.contains(extension)) {
                        continue;
                    }

                    String name = entryName.substring(entryName.lastIndexOf("/") + 1, entryName.lastIndexOf("."));
                    if (name.startsWith(".")) {
                        continue;
                    }
                    log.info("文件名称d: {}", name);

                    ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getName, name).one();
                    if (scenicSpot == null) {
                        continue;
                    }

                    if (StringUtils.isNotEmpty(scenicSpot.getGuideMap())) {
                        continue;
                    }

                    ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = zis.read(buffer)) != -1) {
                        bos.write(buffer, 0, bytesRead);
                    }
                    ByteArrayInputStream bais = new ByteArrayInputStream(bos.toByteArray());
                    String ossUrl = ossService.uploadByInputstream("guideMap/" + name + extension, bais);

                    scenicSpotCustomizeService.lambdaUpdate()
                            .eq(ScenicSpot::getName, name)
                            .set(ScenicSpot::getGuideMap, ossUrl)
                            .update();

                    Map<String, String> map = Map.of(name, ossUrl);
                    guideMaps.add(map);

                }
            }

            return R.ok(guideMaps);
        } catch (Exception e) {
            log.error("同步景区数据导览图异常:", e);
            return R.fail();
        }
    }

    /**
     * 同步景区多边形
     *
     * @param file
     * @return
     */
    @Deprecated
    @Anonymous
    @PostMapping("/syncPolygon")
    public R<Object> syncPolygon(@RequestParam("file") MultipartFile file) {

        ObjectMapper objectMapper = new ObjectMapper();

        try (InputStream inputStream = file.getInputStream()) {
            // 解析JSON文件为Map对象
            GeoJsonObject geoJsonObject = objectMapper.readValue(inputStream, GeoJsonObject.class);
            log.info("geoJsonObject: {}", geoJsonObject);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return R.ok();
    }

    /**
     * 同步腾讯地图地点云
     *
     * @param tableIds
     * @return
     */
    @Anonymous
    @Async("threadPoolTaskExecutor")
    @GetMapping("/syncPlaceCloudData")
    public void syncPlaceCloudData(String tableIds) {

        scenicSpotSyncTmapService.syncPlaceCloudData(tableIds);

    }

    /**
     * 同步腾讯地图同步景点
     *
     * @param qry
     * @return
     */
    @Anonymous
    @Async("threadPoolTaskExecutor")
    @PostMapping("/syncTouristLabel")
    public void syncTouristLabel(@RequestBody SyncTouristLabelQry qry) {

        scenicSpotSyncTmapService.syncLabels(qry);

    }

    /**
     * 处理景区讲解点名称
     *
     * @return
     */
    @Anonymous
    @Async("threadPoolTaskExecutor")
    @GetMapping("/dealLabelName")
    public void dealLabelName(@RequestBody SysTouristLabel condition) {

        List<SysTouristLabel> sysTouristLabels = sysTouristLabelService.selectSyncTouris(condition);
        int total = sysTouristLabels.size();
        int dealCount = 0;
        for (SysTouristLabel sysTouristLabel : sysTouristLabels) {
            String labelName = SysTouristLabel.dealLabelName(sysTouristLabel.getTouristName(), sysTouristLabel.getLabelName());
            sysTouristLabel.setLabelName(labelName);
            sysTouristLabelService.updateSysTouristLabel(sysTouristLabel);
            dealCount++;
        }

        log.info("处理景区讲解点名称完成，共{}讲解点，处理了{}个讲解点", total, dealCount);

    }

    /**
     * 景区讲解点列表
     * @return
     */
    @GetMapping("/labels")
    public R<List<LabelsListVO>> labels(LabelsQuery qry) {

        List<SysTouristLabel> sysTouristLabels = sysTouristLabelService.listQuery(qry);

        ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, qry.getTouristId()).one();

        List<LabelsListVO> labels = sysTouristLabels.stream().map(sysTouristLabel -> {
            return new LabelsListVO(sysTouristLabel, scenicSpot);
        }).toList();

        return R.ok(labels);
    }

    /**
     * 创建景区讲解点
     * @param cmd
     * @return
     */
    @PostMapping("/createLabel")
    public R<Void> createLabel(@RequestBody LabelCreateCmd cmd) {
        log.info("创建景区讲解点: {}", JSON.toJSONString(cmd));
        try {
            ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, cmd.getTouristId()).one();
            cmd.setTouristName(scenicSpot.getName());
            SysTouristLabel sysTouristLabel = new SysTouristLabel(cmd);
            sysTouristLabelService.insertSysTouristLabel(sysTouristLabel);

            applicationEventPublisher.publishEvent(
                    ScenicSpotAddCompletedEvent.builder()
                            .id(scenicSpot.getId()).scenicSpotId(scenicSpot.getScenicSpotId())
                            .name(scenicSpot.getName()).labelId(sysTouristLabel.getId())
                            .currentStatus(ScenicSpotAddCompletedEvent.CurrentStatus.LABEL_ADD_COMPLETED)
                            .build());


        } catch (Exception e) {
            log.error("创建景区讲解点失败: ", e);
            return R.fail("创建景区讲解点失败");
        }
        return R.ok();
    }

    /**
     * 编辑景区讲解点
     * @param cmd
     * @return
     */
    @PostMapping("/editLabel")
    public R<Void> editLabel(@RequestBody LabelEditCmd cmd){
        log.info("更新景区讲解点: {}", JSON.toJSONString(cmd));
        try {
            SysTouristLabel sysTouristLabel = new SysTouristLabel();
            sysTouristLabel.editLabel(cmd);
            sysTouristLabelService.updateSysTouristLabel(sysTouristLabel);
        } catch (Exception e) {
            log.error("更新景区讲解点失败: ", e);
            return R.fail("更新景区讲解点失败");
        }

        return R.ok();
    }

    /**
     * 删除景区讲解点
     * @param cmd
     * @return
     */
    @PostMapping("/delLabel")
    public R<Void> delLabel(@RequestBody LabelDelCmd cmd) {
        try {
            sysTouristLabelService.deleteSysTouristLabelById(cmd.getId());
        } catch (Exception e) {
            log.error("删除景区讲解点失败: ", e);
            return R.fail("删除景区讲解点失败");
        }
        return R.ok();
    }

    /**
     * 审核景区讲解点
     * @param cmd
     * @return
     */
    @PostMapping("/checkLabel")
    public R<Void> checkLabel(@RequestBody LabelEditCmd cmd){

        try {
            SysTouristLabel sysTouristLabel = new SysTouristLabel();
            sysTouristLabel.editLabel(cmd);
            sysTouristLabelService.updateSysTouristLabel(sysTouristLabel);
        } catch (Exception e) {
            log.error("更新景区讲解点失败: ", e);
            return R.fail("更新景区讲解点失败");
        }

        return R.ok();
    }

    /**
     * 添加景区游玩路线
     * @param cmd
     * @return
     */
    @PostMapping("/addTouristRoute")
    public R<Object> addTouristRoute(@RequestBody TouristRouteAddCmd cmd){
        log.info("添加景区游玩路线: {}", JSONUtil.toJsonStr(cmd));

        try {

            ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, cmd.getTouristId()).one();
            TouristRoute touristRoute = scenicSpot.addTouristRoute(cmd);
            scenicSpotCustomizeService.lambdaUpdate()
                    .set(ScenicSpot::getTouristRoutesRaw, scenicSpot.getTouristRoutesRaw())
                    .eq(ScenicSpot::getId, cmd.getTouristId())
                    .update();

            return R.ok(touristRoute);

        } catch (Exception e) {
            log.error("添加景区游玩路线失败: ", e);
            return R.fail("添加景区游玩路线失败");
        }

    }

    /**
     * 编辑景区游玩路线
     * @param cmd
     * @return
     */
    @PostMapping("/editTouristRoute")
    public R<Object> editTouristRoute(@RequestBody TouristRouteEditCmd cmd){
        log.info("编辑景区游玩路线: {}", JSONUtil.toJsonStr(cmd));

        try {

            ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, cmd.getTouristId()).one();
            scenicSpot.editTouristRoute(cmd);
            scenicSpotCustomizeService.lambdaUpdate()
                    .set(ScenicSpot::getTouristRoutesRaw, scenicSpot.getTouristRoutesRaw())
                    .eq(ScenicSpot::getId, cmd.getTouristId())
                    .update();

            return R.ok();

        } catch (Exception e) {
            log.error("添加景区游玩路线失败: ", e);
            return R.fail("添加景区游玩路线失败");
        }

    }

    /**
     * 删除景区游玩路线
     * @param cmd
     * @return
     */
    @PostMapping("/delTouristRoute")
    public R<Object> delTouristRoute(@RequestBody TouristRouteDelCmd cmd){
        log.info("删除景区游玩路线: {}", JSONUtil.toJsonStr(cmd));

        try {

            ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, cmd.getTouristId()).one();
            scenicSpot.delTouristRoute(cmd);
            scenicSpotCustomizeService.lambdaUpdate()
                    .set(ScenicSpot::getTouristRoutesRaw, scenicSpot.getTouristRoutesRaw())
                    .eq(ScenicSpot::getId, cmd.getTouristId())
                    .update();

            return R.ok();

        } catch (Exception e) {
            log.error("添加景区游玩路线失败: ", e);
            return R.fail("添加景区游玩路线失败");
        }

    }

    /**
     * 景区游玩路线讲解点-保存
     * @param cmd
     * @return
     */
    @PostMapping("/saveTouristRouteLabel")
    public R<Object> saveTouristRouteLabel(@RequestBody TouristRouteLabelSaveCmd cmd){
        log.info("景区游玩路线讲解点-保存: {}", JSONUtil.toJsonStr(cmd));

        try {

            ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, cmd.getTouristId()).one();
            TouristRoute.TouristRouteLabel touristRouteLabel = scenicSpot.saveTouristRouteLabel(cmd);
            scenicSpotCustomizeService.lambdaUpdate()
                    .set(ScenicSpot::getTouristRoutesRaw, scenicSpot.getTouristRoutesRaw())
                    .eq(ScenicSpot::getId, cmd.getTouristId())
                    .update();

            return R.ok(touristRouteLabel);

        } catch (Exception e) {
            log.error("保存景区游玩路线讲解点失败: ", e);
            return R.fail("保存景区游玩路线讲解点失败");
        }

    }

    /**
     * 景区游玩路线讲解点-编辑
     * @param cmd
     * @return
     */
    @PostMapping("/editTouristRouteLabel")
    public R<Object> editTouristRouteLabel(@RequestBody TouristRouteLabelEditCmd cmd){
        log.info("景区游玩路线讲解点-编辑: {}", JSONUtil.toJsonStr(cmd));

        try {

            ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, cmd.getTouristId()).one();
            scenicSpot.editTouristRouteLabel(cmd);
            scenicSpotCustomizeService.lambdaUpdate()
                    .set(ScenicSpot::getTouristRoutesRaw, scenicSpot.getTouristRoutesRaw())
                    .eq(ScenicSpot::getId, cmd.getTouristId())
                    .update();

            return R.ok();

        } catch (Exception e) {
            log.error("编辑景区游玩路线讲解点失败: ", e);
            return R.fail("编辑景区游玩路线讲解点失败");
        }
    }

    /**
     * 景区游玩路线讲解点-删除
     * @param cmd
     * @return
     */
    @PostMapping("/delTouristRouteLabel")
    public R<Object> delTouristRouteLabel(@RequestBody TouristRouteLabelDelCmd cmd){
        log.info("景区游玩路线讲解点-删除: {}", JSONUtil.toJsonStr(cmd));
        try {

            ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, cmd.getTouristId()).one();
            scenicSpot.delTouristRouteLabel(cmd);
            scenicSpotCustomizeService.lambdaUpdate()
                    .set(ScenicSpot::getTouristRoutesRaw, scenicSpot.getTouristRoutesRaw())
                    .eq(ScenicSpot::getId, cmd.getTouristId())
                    .update();

            return R.ok();

        } catch (Exception e) {
            log.error("删除景区游玩路线讲解点失败: ", e);
            return R.fail("删除景区游玩路线讲解点失败");
        }
    }

    /**
     * 景区游玩路线讲解点-排序
     * @param cmd
     * @return
     */
    @PostMapping("/sorTouristRouteLabel")
    public R<Object> sorTouristRouteLabel(@RequestBody TouristRouteLabelSortCmd cmd){
        log.info("景区游玩路线讲解点-排序: {}", JSONUtil.toJsonStr(cmd));
        try {

            ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, cmd.getTouristId()).one();
            scenicSpot.sortTouristRouteLabel(cmd);
            scenicSpotCustomizeService.lambdaUpdate()
                    .set(ScenicSpot::getTouristRoutesRaw, scenicSpot.getTouristRoutesRaw())
                    .eq(ScenicSpot::getId, cmd.getTouristId())
                    .update();

            return R.ok();

        } catch (Exception e) {
            log.error("景区游玩路线讲解点-排序失败: ", e);
            return R.fail("景区游玩路线讲解点-排序失败");
        }
    }

    /**
     * 景区讲解包讲解点-保存
     * @param cmd
     * @return
     */
    @PostMapping("/saveTouristPackageLabel")
    public R<Object> saveTouristPackageLabel(@RequestBody TouristPackageLabelSaveCmd cmd){
        log.info("景区讲解包讲解点-保存: {}", JSONUtil.toJsonStr(cmd));
        try {

            ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, cmd.getTouristId()).one();
            TouristPackage.TouristPackageLabel touristPackageLabel = scenicSpot.saveTouristPackageLabel(cmd);
            scenicSpotCustomizeService.lambdaUpdate()
                    .set(ScenicSpot::getTouristPackagesRaw, scenicSpot.getTouristPackagesRaw())
                    .eq(ScenicSpot::getId, cmd.getTouristId())
                    .update();

            return R.ok(touristPackageLabel);

        } catch (Exception e) {
            log.error("景区讲解包讲解点-保存失败: ", e);
            return R.fail("景区讲解包讲解点-保存失败");
        }

    }

    /**
     * 景区讲解包讲解点-编辑
     * @param cmd
     * @return
     */
    @PostMapping("/editTouristPackageLabel")
    public R<Object> editTouristPackageLabel(@RequestBody TouristPackageLabelEditCmd cmd){
        log.info("景区讲解包讲解点-编辑: {}", JSONUtil.toJsonStr(cmd));
        try {

            ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, cmd.getTouristId()).one();
            scenicSpot.editTouristPackageLabel(cmd);
            scenicSpotCustomizeService.lambdaUpdate()
                    .set(ScenicSpot::getTouristPackagesRaw, scenicSpot.getTouristPackagesRaw())
                    .eq(ScenicSpot::getId, cmd.getTouristId())
                    .update();

            return R.ok();

        } catch (Exception e) {
            log.error("景区讲解包讲解点-保存失败: ", e);
            return R.fail("景区讲解包讲解点-保存失败");
        }

    }

    /**
     * 景区讲解包讲解点-试听状态变更
     * @param cmd
     * @return
     */
    @PostMapping("/changeTryListenStatus")
    public R<Object> changeTryListenStatus(@RequestBody TouristPackageLabelTryListenChangeCmd cmd){
        log.info("景区讲解包讲解点-试听状态变更: {}", JSONUtil.toJsonStr(cmd));
        try {

            ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, cmd.getTouristId()).one();
            scenicSpot.changeTryListenStatus(cmd);
            scenicSpotCustomizeService.lambdaUpdate()
                    .set(ScenicSpot::getTouristPackagesRaw, scenicSpot.getTouristPackagesRaw())
                    .eq(ScenicSpot::getId, cmd.getTouristId())
                    .update();

            return R.ok();

        } catch (Exception e) {
            log.error("景区讲解包讲解点-试听状态变更-保存失败: ", e);
            return R.fail("景区讲解包讲解点-试听状态变更-保存失败");
        }

    }


    /**
     * 景区讲解包讲解点-删除
     * @param cmd
     * @return
     */
    @PostMapping("/delTouristPackageLabel")
    public R<Object> delTouristPackageLabel(@RequestBody TouristPackageLabelDelCmd cmd){
        log.info("景区讲解包讲解点-删除: {}", JSONUtil.toJsonStr(cmd));
        try {

            ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, cmd.getTouristId()).one();
            scenicSpot.delTouristPackageLabel(cmd);
            scenicSpotCustomizeService.lambdaUpdate()
                    .set(ScenicSpot::getTouristPackagesRaw, scenicSpot.getTouristPackagesRaw())
                    .eq(ScenicSpot::getId, cmd.getTouristId())
                    .update();

            return R.ok();

        } catch (Exception e) {
            log.error("景区讲解包讲解点-试听状态变更-保存失败: ", e);
            return R.fail("景区讲解包讲解点-试听状态变更-保存失败");
        }

    }

    /**
     * 景区讲解包讲解点-排序
     * @param cmd
     * @return
     */
    @PostMapping("/sortTouristPackageLabel")
    public R<Object> sortTouristPackageLabel(@RequestBody TouristPackageLabelSortCmd cmd){
        log.info("景区讲解包讲解点-排序: {}", JSONUtil.toJsonStr(cmd));
        try {

            ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, cmd.getTouristId()).one();
            scenicSpot.sortTouristPackageLabel(cmd);
            scenicSpotCustomizeService.lambdaUpdate()
                    .set(ScenicSpot::getTouristPackagesRaw, scenicSpot.getTouristPackagesRaw())
                    .eq(ScenicSpot::getId, cmd.getTouristId())
                    .update();

            return R.ok();

        } catch (Exception e) {
            log.error("景区讲解包讲解点-排序-保存失败: ", e);
            return R.fail("景区讲解包讲解点-排序-保存失败");
        }

    }

    /**
     * 讲解包讲解点列表
     * @return
     */
    @GetMapping("/touristPackageLabels")
    public R<Object> touristPackageLabels(LabelsQuery qry){
        ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, qry.getTouristId()).one();
        return R.ok(scenicSpot.getTouristPackages());
    }

    /**
     * 景区游玩路线
     * @return
     */
    @GetMapping("/touristRouteLabels")
    public R<Object> touristRouteLabels(LabelsQuery qry){
        ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, qry.getTouristId()).one();
        return R.ok(scenicSpot.getTouristRoutes());
    }


    /**
     * 处理讲解点地图聚合问题
     * @param qry
     */
    @Anonymous
    @Async("threadPoolTaskExecutor")
    @PostMapping("/handleLabelJoinCluster")
    public void handleLabelJoinCluster(@RequestBody SysTouristLabel qry){

        Map<String, Object> params = qry.getParams();
        Object pageNumO = params.get("pageNum");
        Object pageSizeO = params.get("pageSize");

        int pageNum = Objects.nonNull(pageNumO) ? Integer.parseInt(pageNumO.toString()) : 1;
        int pageSize = Objects.nonNull(pageSizeO) ? Integer.parseInt(pageSizeO.toString()) : 10;

        String last = "LIMIT " + pageSize + " OFFSET " + pageNum;
        List<ScenicSpot> list = scenicSpotCustomizeService.lambdaQuery()
                .eq(Objects.nonNull(qry.getTouristId()), ScenicSpot::getId, qry.getTouristId())
                .last(last)
                .list();

        for (ScenicSpot scenicSpot : list) {

            SysTouristLabel condition = new SysTouristLabel();
            condition.setTouristId(Long.valueOf(scenicSpot.getId()));

            List<SysTouristLabel> sysTouristLabels = sysTouristLabelService.selectSyncTouris(condition);

            if (sysTouristLabels == null || sysTouristLabels.size() < 2) {
                log.info("[{}]景区内讲解点少于2个，无需进行聚合判断", scenicSpot.getName());
                continue;
            }

            // 按ID排序（可选，确保顺序稳定）
            sysTouristLabels.sort(Comparator.comparing(SysTouristLabel::getId));

            // 遍历所有标签对，避免重复比较
            for (int i = 0; i < sysTouristLabels.size(); i++) {
                SysTouristLabel labelA = sysTouristLabels.get(i);

                // 只比较i之后的标签（确保每对ID只比较一次）
                for (int j = i + 1; j < sysTouristLabels.size(); j++) {
                    SysTouristLabel labelB = sysTouristLabels.get(j);

                    // 获取经纬度坐标（使用 Apache Commons Lang 优化空值检查）
                    if (StringUtils.isBlank(labelA.getLatitude()) || StringUtils.isBlank(labelA.getLongitude()) ||
                            StringUtils.isBlank(labelB.getLatitude()) || StringUtils.isBlank(labelB.getLongitude())) {
                        continue;
                    }

                    // 转换经纬度为double类型
                    double latA = Double.parseDouble(labelA.getLatitude());
                    double lonA = Double.parseDouble(labelA.getLongitude());
                    double latB = Double.parseDouble(labelB.getLatitude());
                    double lonB = Double.parseDouble(labelB.getLongitude());

                    // 计算距离
                    double distance = HaversineFormula.calculateDistance(latA, lonA, latB, lonB);
                    if (distance < 10) {
                        labelB.setJoinCluster(false);
                        sysTouristLabelService.updateSysTouristLabel(labelB);
                        log.info("景区内:【{}】讲解点【{}】和讲解点【{}】距离小于10米，将讲解点【{}】设置为不聚合", labelA.getTouristName(), labelA.getLabelName(), labelB.getLabelName(), labelB.getLabelName());
                    }

                }
            }

            log.info("景区{}讲解点聚合处理完成", scenicSpot.getName());

        }

        log.info("第[{}]页景区讲解点聚合处理完成", pageNum);

    }

    public static void main(String[] args) {

//        ScenicSpot scenicSpot = new ScenicSpot();
//        scenicSpot.setPolygon("120.1357,30.2468;120.1357,30.2468");
//        List<String> polygonList = scenicSpot.getPolygonList();
//        System.out.println(polygonList);


        String str = "";

        System.out.println(org.apache.commons.lang3.StringUtils.left(str, 10));
    }

}



