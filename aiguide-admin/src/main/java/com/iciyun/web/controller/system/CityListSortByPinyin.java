package com.iciyun.web.controller.system;

import com.iciyun.system.domain.District;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

import java.util.*;

public class CityListSortByPinyin {

    private static final Map<String, String> CUSTOM_PINYIN_MAP = new HashMap<>();
    static {
        // 手动添加常见多音字词语及其正确拼音
        CUSTOM_PINYIN_MAP.put("重庆市", "CHONGQING");
        CUSTOM_PINYIN_MAP.put("重庆郊县", "CHONGQINGJIAOXIAN");
        CUSTOM_PINYIN_MAP.put("长治市", "CHANGZHISHI");
        CUSTOM_PINYIN_MAP.put("长春市", "CHANGCHUNSHI");
        CUSTOM_PINYIN_MAP.put("长沙市", "CHANGSHASHI");
    }

    // 获取字符串的拼音首字母
    public static String getFirstLetter(String chinese) {

        if (CUSTOM_PINYIN_MAP.containsKey(chinese)) {
            return CUSTOM_PINYIN_MAP.get(chinese);
        }

        StringBuilder pinyin = new StringBuilder();
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.UPPERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);

        for (char c : chinese.toCharArray()) {
            try {
                if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        pinyin.append(pinyinArray[0].charAt(0));
                    }
                } else {
                    pinyin.append(c);
                }
            } catch (BadHanyuPinyinOutputFormatCombination e) {
                e.printStackTrace();
            }
        }
        return pinyin.toString();
    }

    public static List<String> sortCitiesByPinyin(List<String> cities) {
        cities.sort(Comparator.comparing(CityListSortByPinyin::getFirstLetter));
        return cities;
    }

    public static Map<Character, List<District>> groupCitiesByInitial(List<District> cities) {
        Map<Character, List<District>> groupedCities = new TreeMap<>();
        for (District city : cities) {
            String firstLetter = getFirstLetter(city.getName());
            if (!firstLetter.isEmpty()) {
                char initial = firstLetter.charAt(0);
                groupedCities.computeIfAbsent(initial, k -> new ArrayList<>()).add(city);
            }
        }
        return groupedCities;
    }
}