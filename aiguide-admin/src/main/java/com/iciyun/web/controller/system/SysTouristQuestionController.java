package com.iciyun.web.controller.system;

import com.iciyun.common.annotation.Anonymous;
import com.iciyun.common.core.controller.BaseController;
import com.iciyun.common.core.domain.AjaxResult;
import com.iciyun.common.core.page.TableDataInfo;
import com.iciyun.common.enums.BusinessType;
import com.iciyun.common.utils.SecurityUtils;
import com.iciyun.common.utils.poi.ExcelUtil;
import com.iciyun.system.domain.SysTouristQuestion;
import com.iciyun.system.service.ISysTouristQuestionService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 景区常见问题对话记录Controller
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
@RestController
@RequestMapping("/system/question")
public class SysTouristQuestionController extends BaseController
{
    @Autowired
    private ISysTouristQuestionService sysTouristQuestionService;

    /**
     * 查询景区常见问题对话记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SysTouristQuestion sysTouristQuestion)
    {
        startPage();
        sysTouristQuestion.setUserId(SecurityUtils.getUserId()+"");
        List<SysTouristQuestion> list = sysTouristQuestionService.selectSysTouristQuestionList(sysTouristQuestion);
        return getDataTable(list);
    }

    /**
     * 导出景区常见问题对话记录列表
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysTouristQuestion sysTouristQuestion)
    {
        List<SysTouristQuestion> list = sysTouristQuestionService.selectSysTouristQuestionList(sysTouristQuestion);
        ExcelUtil<SysTouristQuestion> util = new ExcelUtil<SysTouristQuestion>(SysTouristQuestion.class);
        util.exportExcel(response, list, "景区常见问题对话记录数据");
    }

    /**
     * 获取景区常见问题，判断是否缓存过
     */
    @GetMapping(value = "/questionCache")
    public AjaxResult questionCache(Long touristId,Long questionId)
    {
        return success((Object) sysTouristQuestionService.questionCache(touristId,questionId));
    }


    /**
     * 清空常见问题缓存
     */
    @Anonymous
    @GetMapping(value = "/clearQuestionCache")
    public AjaxResult clearQuestionCache()
    {
        sysTouristQuestionService.clearQuestionCache();
        return success();
    }


    /**
     * 新增景区常见问题对话记录
     */
    @PostMapping
    public AjaxResult add(@RequestBody SysTouristQuestion sysTouristQuestion)
    {
        return toAjax(sysTouristQuestionService.insertSysTouristQuestion(sysTouristQuestion));
    }

    /**
     * 修改景区常见问题对话记录
     */
    @PutMapping
    public AjaxResult edit(@RequestBody SysTouristQuestion sysTouristQuestion)
    {
        return toAjax(sysTouristQuestionService.updateSysTouristQuestion(sysTouristQuestion));
    }

    /**
     * 删除景区常见问题对话记录
     */
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysTouristQuestionService.deleteSysTouristQuestionByIds(ids));
    }
}
