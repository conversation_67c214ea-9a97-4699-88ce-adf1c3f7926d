package com.iciyun.web.controller.system;

import com.iciyun.common.core.controller.BaseController;
import com.iciyun.common.core.domain.R;
import com.iciyun.common.core.domain.entity.SysWxUser;
import com.iciyun.common.core.page.TableDataInfo;
import com.iciyun.common.enums.UserTypeEnum;
import com.iciyun.common.utils.poi.ExcelUtil;
import com.iciyun.system.service.ISysWxUserService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 游客管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/wxUser")
public class WxSysUserController extends BaseController {

    @Autowired
    private ISysWxUserService wxUserService;

    /**
     * 获取用户列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SysWxUser user) {
        if (user == null) {
            user = new SysWxUser();
        }
        user.setUserType(UserTypeEnum.SYSTEM.getCode());
        startPage();
        List<SysWxUser> list = wxUserService.selectWxUserList(user);
        return getDataTable(list);
    }

    /**
     * 导出用户列表
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysWxUser user) {
        List<SysWxUser> list = wxUserService.selectWxUserList(user);
        ExcelUtil<SysWxUser> util = new ExcelUtil<>(SysWxUser.class);
        util.exportExcel(response, list, "用户数据");
    }

    /**
     * 用户信息
     */

    @PostMapping("/getUserInfo")
    public R<SysWxUser> getUserInfo(SysWxUser user) {
        SysWxUser userInfo = wxUserService.selectWxUserById(user.getUserId());
        //TODO 添加 游豆信息

        return R.ok(userInfo);
    }

}
