package com.iciyun.web.controller.system;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.iciyun.common.constant.Constants;
import com.iciyun.common.utils.StringUtils;
import com.iciyun.event.ScenicSpotAddCompletedEvent;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.SysTouristLabel;
import com.iciyun.system.domain.bo.ScenicSpotImportCmd;
import com.iciyun.system.domain.bo.SupplementMapInfoCmd;
import com.iciyun.system.domain.qo.SyncTouristLabelQry;
import com.iciyun.system.mapper.ScenicSpotCustomizeMapper;
import com.iciyun.system.service.IScenicSpotCustomizeService;
import com.iciyun.system.service.ISysTouristLabelService;
import com.iciyun.tmap.TMapClient;
import com.iciyun.tmap.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class ScenicSpotSyncTmapService {
    private final TMapClient tMapClient;
    private final ScenicSpotCustomizeMapper scenicSpotCustomizeMapper;
    private final IScenicSpotCustomizeService scenicSpotCustomizeService;
    private final ISysTouristLabelService sysTouristLabelService;
    private final ApplicationEventPublisher applicationEventPublisher;


    /**
     * 处理导入数据
     * @param list
     */
    public void handleImport(List<ScenicSpotImportCmd> list){

        list.forEach(scenicSpotImportCmd -> {

            ScenicSpot scenicSpot = new ScenicSpot(scenicSpotImportCmd);
            String boundary = String.format("region(%s,%d)", scenicSpot.getCityName(), 0);
            String filter = StringUtils.isNotBlank(scenicSpot.getFilter()) ? scenicSpot.getFilter() : Constants.FILTER_DEFAULT;
            SearchReq req = SearchReq.builder()
                    .keyword(scenicSpot.getKeyword()).filter(filter)
                    .boundary(boundary)
                    .build();

            SearchResp searchResp = null;
            try {
                searchResp = tMapClient.search(req);

                scenicSpot.supplementMapInfo(searchResp.first().genSupplementMapInfoCmd());

                ScenicSpot scenicSpot_ = scenicSpotCustomizeService.lambdaQuery()
                        .eq(ScenicSpot::getName, scenicSpot.getName())
                        .eq(ScenicSpot::getCityName, scenicSpot.getCityName())
                        .one();

                if (Objects.isNull(scenicSpot_)) {
                    scenicSpotCustomizeService.save(scenicSpot);
                } else {
                    scenicSpot.setId(scenicSpot_.getId());
                    scenicSpot.setScenicSpotId(scenicSpot_.getScenicSpotId());
                }

                ScenicSpotAddCompletedEvent event = ScenicSpotAddCompletedEvent.builder()
                        .id(scenicSpot.getId()).scenicSpotId(scenicSpot.getScenicSpotId())
                        .name(scenicSpot.getName()).currentStatus(ScenicSpotAddCompletedEvent.CurrentStatus.SCENIC_SPOT_SUPPLEMENT_MAP_INFO_COMPLETED)
                        .build();
                applicationEventPublisher.publishEvent(event);

            } catch (Exception e) {
                log.error("导入景区区域搜索失败，景区名称: {}", scenicSpot.getName());
            }

        });


    }

    /**
     * 同步景区
     */
    public void syncScenicSpot(ScenicSpotAddCompletedEvent event) {

        log.info("同步景区数据：{}", JSONUtil.toJsonStr(event));

        ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, event.getId()).one();

        String boundary = String.format("region(%s,%d)", scenicSpot.getCityName(), 0);
        String filter = StringUtils.isNotBlank(scenicSpot.getFilter()) ? scenicSpot.getFilter() : StringUtils.EMPTY;
        SearchReq req = SearchReq.builder()
                .keyword(scenicSpot.getKeyword()).filter(filter)
                .boundary(boundary)
                .build();
        try {
            SearchResp searchResp = tMapClient.search(req);
            scenicSpot.supplementMapInfo(searchResp.first().genSupplementMapInfoCmd());
            scenicSpotCustomizeService.updateById(scenicSpot);

            applicationEventPublisher.publishEvent(
                    ScenicSpotAddCompletedEvent.builder()
                            .id(scenicSpot.getId()).scenicSpotId(scenicSpot.getScenicSpotId())
                            .name(scenicSpot.getName()).currentStatus(ScenicSpotAddCompletedEvent.CurrentStatus.SCENIC_SPOT_SUPPLEMENT_MAP_INFO_COMPLETED)
                            .build()
            );

            log.info("同步景区数据已完成：{}", JSONUtil.toJsonStr(event));
        } catch (Exception e) {
            log.error("同步景区区域搜索失败，景区名称: {}", scenicSpot.getName());
            log.error("同步景区区域搜索失败: ", e);
            throw new RuntimeException("同步景区区域搜索失败");
        }
    }


    /**
     * 同步讲解点
     * @param qry
     * @return
     */
    public void syncLabels(SyncTouristLabelQry qry){

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<String> fail = new ArrayList<>();
        List<ScenicSpot> list = null;

        if (Objects.nonNull(qry.getLimitQuery()) && qry.getLimitQuery() > 0) {
            list = scenicSpotCustomizeMapper.nonLabels(qry);
        } else {
            list = scenicSpotCustomizeService.lambdaQuery()
                    .eq(Objects.nonNull(qry.getId()), ScenicSpot::getId, qry.getId())
                    .eq(Objects.nonNull(qry.getName()), ScenicSpot::getName, qry.getName())
                    .eq(Objects.nonNull(qry.getProvinceCode()), ScenicSpot::getProvinceCode, qry.getProvinceCode())
                    .eq(Objects.nonNull(qry.getCityCode()), ScenicSpot::getCityCode, qry.getCityCode())
                    .eq(Objects.nonNull(qry.getLevel()), ScenicSpot::getLevel, qry.getLevel())
                    .eq(Objects.nonNull(qry.getStatus()), ScenicSpot::getStatus, qry.getStatus())
                    .list();
        }

        try {
            for (ScenicSpot scenicSpot : list) {
                List<SysTouristLabel> sysTouristLabels = new ArrayList<>();

                String polygon = scenicSpot.getPolygon();
                if (StrUtil.isNotBlank(polygon)) {

                    int totalPages = 0;
                    int pageIndex = 1;

                    do {
                        try {
                            SearchByPolygonReq req = SearchByPolygonReq.builder()
                                    .keyword(scenicSpot.getKeyword()).polygon(polygon)
                                    .filter(scenicSpot.getFilter()).page_size(Const.PAGE_SIZE_DEFAULT).page_index(String.valueOf(pageIndex))
                                    .build();

                            SearchByPolygonResp searchByPolygonResp = tMapClient.searchByPolygon(req);
                            if(!searchByPolygonResp.getStatus().equals("0")) {
                                throw new RuntimeException(String.format("多边形范围搜索失败, 状态码：%s, 状态说明：%s)", searchByPolygonResp.getStatus(), searchByPolygonResp.getMessage()));
                            }

                            // 总页数 = （总数 + 每页数量 -1） / 每页数量（向上取整
                            String count = searchByPolygonResp.getCount();

                            if (totalPages == 0) {
                                totalPages = (Integer.parseInt(count) + Integer.parseInt(Const.PAGE_SIZE_DEFAULT) - 1) / Integer.parseInt(Const.PAGE_SIZE_DEFAULT);
                            }
                            pageIndex++;

                            List<SearchByPolygonResp.RespData> data = searchByPolygonResp.getData();
                            for (SearchByPolygonResp.RespData respData : data) {

                                SupplementMapInfoCmd supplementMapInfoCmd = respData.genSupplementMapInfoCmd(scenicSpot.getName());
                                SysTouristLabel sysTouristLabel = new SysTouristLabel(scenicSpot, supplementMapInfoCmd);
                                sysTouristLabels.add(sysTouristLabel);

                            }

                        } catch (Exception e) {
                            polygon = StringUtils.EMPTY;
                            fail.add(scenicSpot.getName());
                            log.error("多边形范围搜索失败，景区名称: {}", scenicSpot.getName());
                            log.error("多边形范围搜索失败: ", e);
                        }
                    } while (pageIndex <= totalPages);

                }

                if (StrUtil.isBlank(polygon)) {

                    int totalPages = 0;
                    int pageIndex = 1;

                    do {
                        try {
                            String boundary = String.format("region(%s,%d,%s,%s)", scenicSpot.getCityCode(), 0, scenicSpot.getLongitude().trim(), scenicSpot.getLatitude().trim());
                            SearchReq req = SearchReq.builder()
                                    .keyword(scenicSpot.getKeyword()).filter(scenicSpot.getFilter())
                                    .page_size(Const.PAGE_SIZE_DEFAULT).page_index(String.valueOf(pageIndex))
                                    .boundary(boundary)
                                    .build();

                            SearchResp searchResp = tMapClient.search(req);
                            String count = searchResp.getCount();

                            if (totalPages == 0) {
                                int i = Integer.parseInt(count);
                                i = i * 90 / 100;
                                totalPages = (i + Integer.parseInt(Const.PAGE_SIZE_DEFAULT) - 1) / Integer.parseInt(Const.PAGE_SIZE_DEFAULT);
                            }
                            pageIndex++;

                            List<SearchResp.RespData> data = searchResp.getData();
                            for (SearchResp.RespData respData : data) {

                                SupplementMapInfoCmd supplementMapInfoCmd = respData.genSupplementMapInfoCmd(scenicSpot.getName());
                                SysTouristLabel sysTouristLabel = new SysTouristLabel(scenicSpot, supplementMapInfoCmd);
                                sysTouristLabels.add(sysTouristLabel);

                            }

                        } catch (Exception e) {
                            fail.add(scenicSpot.getName());
                            log.error("区域搜索失败，景区名称: {}", scenicSpot.getName());
                            log.error("区域搜索失败: ", e);
                        }
                    } while (pageIndex <= totalPages);

                }

                for (SysTouristLabel sysTouristLabel : sysTouristLabels) {

                    SysTouristLabel condition = new SysTouristLabel();
                    condition.setTouristId(scenicSpot.getId().longValue());
                    condition.setLabelName(sysTouristLabel.getLabelName().trim());
                    List<SysTouristLabel> dataList = sysTouristLabelService.selectSyncTouris(condition);
                    if (CollectionUtils.isEmpty(dataList)){
                        sysTouristLabelService.insertSysTouristLabel(sysTouristLabel);
                    }
                    if (CollectionUtils.isNotEmpty(dataList)) {

                        Long[] ids = dataList.stream().map(SysTouristLabel::getId).toArray(Long[]::new);
                        sysTouristLabelService.deleteSysTouristLabelByIds(ids);

                        sysTouristLabelService.insertSysTouristLabel(sysTouristLabel);
                    }

                }


                applicationEventPublisher.publishEvent(
                        ScenicSpotAddCompletedEvent.builder()
                                .id(scenicSpot.getId()).scenicSpotId(scenicSpot.getScenicSpotId())
                                .name(scenicSpot.getName()).currentStatus(ScenicSpotAddCompletedEvent.CurrentStatus.LABEL_ADD_COMPLETED)
                                .build());

                log.info("景区{}讲解点聚合处理完成", scenicSpot.getName());

            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        stopWatch.stop();
        log.info("----------------- 景区标签同步已完成 -----------------");
        log.info("景区讲解点同步失败景区:{}", JSONUtil.toJsonStr(fail));
        log.info("----------------- 景区标签同步已完成-用时:" + stopWatch.getTotalTimeMillis() + " -----------------");


    }

    /**
     * 同步腾讯地点云数据
     * @param tableIds
     */
    public void syncPlaceCloudData(String tableIds){

        try {
            String pageNext = null;

            do {

                PlaceCloudDataListReq req = PlaceCloudDataListReq.builder()
                        .table_id(tableIds).page_next(pageNext)
                        .build();

                PlaceCloudDataListResp placeCloudDataListResp = tMapClient.placeClourdDataList(req);
                PlaceCloudDataListResp.Result result = placeCloudDataListResp.getResult();
                pageNext = result.getPage_next();
                List<PlaceCloudDataListResp.RespData> data = result.getData();
                for (PlaceCloudDataListResp.RespData respData : data) {

                    SupplementMapInfoCmd supplementMapInfoCmd = respData.genSupplementMapInfoCmd();

                    String udId = respData.getUd_id();
                    boolean creatable = NumberUtils.isCreatable(udId);
                    if (creatable) {
                        ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, Integer.parseInt(udId)).one();
                        if (scenicSpot != null) {
                            scenicSpot.supplementMapInfo(supplementMapInfoCmd);
                            scenicSpot.setMapDefaultValue();
                            scenicSpotCustomizeService.updateById(scenicSpot);
                        }
                    }


                    if (!creatable) {

                        PlaceCloudDataListResp.Customize customize = respData.getX();
                        String scenicAreaId = customize.getScenic_area_id();

                        ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, Integer.parseInt(scenicAreaId)).one();

                        SysTouristLabel condition = new SysTouristLabel();
                        condition.setTouristId(Long.valueOf(scenicAreaId));
                        condition.setLabelName(supplementMapInfoCmd.getName());
                        List<SysTouristLabel> sysTouristLabels = sysTouristLabelService.selectSyncTouris(condition);

                        if (CollectionUtils.isEmpty(sysTouristLabels)) {
                            SysTouristLabel sysTouristLabel = new SysTouristLabel(scenicSpot, supplementMapInfoCmd);
                            sysTouristLabelService.insertSysTouristLabel(sysTouristLabel);
                        }

                        if (CollectionUtils.isNotEmpty(sysTouristLabels)) {
                            for (SysTouristLabel sysTouristLabel : sysTouristLabels) {
                                sysTouristLabel.supplementMapInfo(supplementMapInfoCmd);
                                sysTouristLabelService.updateSysTouristLabel(sysTouristLabel);
                            }
                        }

                    }

                }

            } while (StrUtil.isNotBlank(pageNext));

        } catch (Exception e) {
            log.error("同步景区数据失败: ", e);
        }

    }



}
