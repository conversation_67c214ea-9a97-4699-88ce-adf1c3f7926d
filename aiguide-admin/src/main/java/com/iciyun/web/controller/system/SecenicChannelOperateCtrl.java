package com.iciyun.web.controller.system;

import com.iciyun.common.core.domain.R;
import com.iciyun.common.enums.PartnerBusinessType;
import com.iciyun.system.domain.ScenicLocation;
import com.iciyun.system.domain.ScenicOperate;
import com.iciyun.system.domain.SecenicChannelOperate;
import com.iciyun.system.service.ISecenicChannelOperateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 渠道下的运营对应的景区 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-12 11:05:25
 */
@RestController
@RequestMapping("/secenicChannelOperate")
public class SecenicChannelOperateCtrl {

    @Autowired
    private ISecenicChannelOperateService secenicChannelOperateService;

    /**
     * 查询列表
     */
    @PostMapping("/list")
    public R<List<SecenicChannelOperate>> list(@RequestBody SecenicChannelOperate secenicChannelOperate) {
        List<SecenicChannelOperate> rets = secenicChannelOperateService.lambdaQuery()
                .eq(SecenicChannelOperate::getChannelCode, secenicChannelOperate.getChannelCode())
                .list();
        return R.ok(rets);
    }

    /**
     * 编辑
     */
    @PostMapping("/edit")
    public R edit(@RequestBody SecenicChannelOperate secenicChannelOperate) {
        secenicChannelOperateService.edit(secenicChannelOperate);
        return R.ok();
    }

    /**
     * 编辑
     */
    @PostMapping("/del")
    public R del(@RequestBody SecenicChannelOperate secenicChannelOperate) {
        secenicChannelOperateService.del(secenicChannelOperate.getId());
        return R.ok();
    }

}
