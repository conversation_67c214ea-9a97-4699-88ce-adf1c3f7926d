package com.iciyun.web.controller.tool;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import com.iciyun.common.annotation.Anonymous;
import com.iciyun.common.core.controller.BaseController;
import com.iciyun.common.core.domain.R;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.common.utils.StringUtils;
import com.iciyun.system.mapper.ScenicSpotCustomizeMapper;
import com.iciyun.system.mapper.SysTouristLabelMapper;
import com.iciyun.system.service.*;
import com.iciyun.system.service.impl.ChatService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.regex.Pattern;

/**
 * swagger 用户测试方法
 *
 * <AUTHOR>
 */
@Tag(name = "用户信息管理")
@RestController
@RequestMapping("/test/user")
public class TestController extends BaseController {
    private final static Map<Integer, UserEntity> users = new LinkedHashMap<Integer, UserEntity>();

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private DirectSqlQueryService directSqlQueryService;

    @Autowired
    IGuideService guideService;

    @Autowired
    ISysDictDataService sysDictDataService;

    @Autowired
    ScenicSpotCustomizeMapper scenicSpotCustomizeMapper;

    @Autowired
    IScenicSpotCustomizeService scenicSpotCustomizeService;

    @Autowired
    SysTouristLabelMapper sysTouristLabelMapper;

    @Autowired
    GenAudioService genAudioService;

    @Autowired
    private IGuideTkService guideTkService;

    {
        users.put(1, new UserEntity(1, "admin", "admin123", "15888888888"));
        users.put(2, new UserEntity(2, "ry", "admin123", "15666666666"));
    }

    @Anonymous
    @GetMapping("/test1")
    public R test1() {

        Collection<String> keys = redisCache.keys("*");

        // 正则表达式
        String regex = "^[0-9]+:[0-9]+$";
        Pattern pattern = Pattern.compile(regex);

        // 过滤符合条件的 key
        for (String key : keys) {
            if (pattern.matcher(key).matches()) {
                System.out.println("符合条件的 key: " + key);
                redisCache.deleteObject(key);
            }
        }

        return R.ok(keys.size());
    }


    @Anonymous
    @GetMapping("/cozeToken")
    public R cozeToken() {
        String cozeToken = guideTkService.getToken();

        return R.ok(cozeToken);
    }


    @Anonymous
    @GetMapping("/test2")
    public R test2() {

//        guideService.justTestRetry("jack");

        Object object = null;

        String content = """
                哺乳類は脊椎動物の中で最も高等な分類群であり、体毛、恒温性、胎生、乳汁による子育てを主な特徴とする。現生哺乳類の主要な分類群と基本パターンは約2000万年前に形成され、山旺動物群は約1800万年前に形成された。現生哺乳類の起源と進化を研究する上で極めて重要である。
                """;
        ThreadUtil.execute(()->{
            String path = genAudioService.genAudioByCozeWithLength("Japanese","7426725529589645339", content);
            System.out.println("--->"+path);
        });


        return R.ok(object);
    }


    @Anonymous
    @GetMapping("/test3")
    public R test3() {

        String token = "pat_nXukAcBpjMg1n8VXgv7nqlBZwpc4v1SbduDUPy0FUKEY0TQmojM9SEPxEE2Wys0n";
        String botId = "7496303824152477723";
        String userId = "123321";
        String message = "请从不同的角度给我介绍一下北京与上海的区别。";

        ChatService chatService = new ChatService(token, botId);

        String id = chatService.genConversationId();

        Map<String, Object> custom_variables = new HashMap<>();
        custom_variables.put("name","故宫");


        try {
            chatService.streamChat(userId, message,"",custom_variables);
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }

        return R.ok(id);
    }


    @Anonymous
    @GetMapping("/resetTouristMap")
    public R resetTouristMap() {

        guideService.resetTouristMap();

        return R.ok();
    }


    @Operation(summary = "获取用户列表")
    @GetMapping("/list")
    public R<List<UserEntity>> userList() {
        List<UserEntity> userList = new ArrayList<UserEntity>(users.values());
        return R.ok(userList);
    }

    @Operation(summary = "获取用户详细")
    @GetMapping("/{userId}")
    public R<UserEntity> getUser(@PathVariable(name = "userId")
                                 Integer userId) {
        if (!users.isEmpty() && users.containsKey(userId)) {
            return R.ok(users.get(userId));
        } else {
            return R.fail("用户不存在");
        }
    }

    @Operation(summary = "新增用户")
    @PostMapping("/save")
    public R<String> save(UserEntity user) {
        if (StringUtils.isNull(user) || StringUtils.isNull(user.getUserId())) {
            return R.fail("用户ID不能为空");
        }
        users.put(user.getUserId(), user);
        return R.ok();
    }

    @Operation(summary = "更新用户")
    @PutMapping("/update")
    public R<String> update(@RequestBody
                            UserEntity user) {
        if (StringUtils.isNull(user) || StringUtils.isNull(user.getUserId())) {
            return R.fail("用户ID不能为空");
        }
        if (users.isEmpty() || !users.containsKey(user.getUserId())) {
            return R.fail("用户不存在");
        }
        users.remove(user.getUserId());
        users.put(user.getUserId(), user);
        return R.ok();
    }

    @Operation(summary = "删除用户信息")
    @DeleteMapping("/{userId}")
    public R<String> delete(@PathVariable(name = "userId")
                            Integer userId) {
        if (!users.isEmpty() && users.containsKey(userId)) {
            users.remove(userId);
            return R.ok();
        } else {
            return R.fail("用户不存在");
        }
    }
}

@Schema(description = "用户实体")
class UserEntity {
    @Schema(title = "用户ID")
    private Integer userId;

    @Schema(title = "用户名称")
    private String username;

    @Schema(title = "用户密码")
    private String password;

    @Schema(title = "用户手机")
    private String mobile;

    public UserEntity() {

    }

    public UserEntity(Integer userId, String username, String password, String mobile) {
        this.userId = userId;
        this.username = username;
        this.password = password;
        this.mobile = mobile;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
}
