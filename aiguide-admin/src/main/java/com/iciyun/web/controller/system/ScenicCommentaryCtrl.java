package com.iciyun.web.controller.system;

import com.iciyun.common.core.controller.BaseController;
import com.iciyun.common.core.domain.R;
import com.iciyun.common.core.domain.entity.wx.WxGetCodeCmd;
import com.iciyun.common.core.page.TableDataInfo;
import com.iciyun.common.enums.PartnerBusinessType;
import com.iciyun.common.utils.uuid.IdHutool;
import com.iciyun.system.domain.ScenicCommentary;
import com.iciyun.system.domain.ScenicLocation;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.service.IScenicCommentaryService;
import com.iciyun.system.service.IScenicLocationService;
import com.iciyun.system.service.IScenicSpotCustomizeService;
import com.iciyun.system.service.IWxUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 景区label解说词
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/scenicCommentary")
public class ScenicCommentaryCtrl extends BaseController {

    @Autowired
    private IScenicCommentaryService scenicCommentaryService;
    @Autowired
    private IScenicSpotCustomizeService scenicSpotCustomizeService;


    /**
     * 列表
     *
     * @return
     */
    @GetMapping("/queryList")
    public TableDataInfo queryList(ScenicCommentary qo) {
        startPage();
        List<ScenicCommentary> list = scenicCommentaryService.queryList(qo);
        return getDataTable(list);
    }

    /**
     * 新增
     *
     * @param scenicCommentary
     * @return
     */
    @PostMapping("/addCommentary")
    public R edit(@RequestBody ScenicCommentary scenicCommentary) {
        try {
            List<ScenicCommentary> list = scenicCommentaryService.lambdaQuery()
                    .eq(ScenicCommentary::getScenicId, scenicCommentary.getScenicId())
                    .eq(ScenicCommentary::getLabelName, scenicCommentary.getLabelName())
                    .list();
            if( list == null || list.size() == 0 ){
                ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, scenicCommentary.getScenicId()).one();
                scenicCommentary.setScenicName(scenicSpot.getName());
                scenicCommentary.setLabelCommentary(scenicCommentary.getLabelName());
                scenicCommentaryService.save(scenicCommentary);
            }
            return R.ok();
        } catch (Exception e) {
            log.error("新增失败", e);
            return R.fail("新增失败。");
        }
    }

    /**
     * 修改
     *
     * @param scenicCommentary
     * @return
     */
    @PostMapping("/upCommentary")
    public R defaultAdd(@RequestBody ScenicCommentary scenicCommentary) {
        try {
            scenicCommentaryService.updateById(scenicCommentary);
            return R.ok();
        } catch (Exception e) {
            log.error("修改失败", e);
            return R.fail("修改失败。");
        }
    }

}
