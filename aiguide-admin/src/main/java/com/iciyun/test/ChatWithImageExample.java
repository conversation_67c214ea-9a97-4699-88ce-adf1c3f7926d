package com.iciyun.test;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.druid.support.json.JSONUtils;
import com.coze.openapi.client.chat.CreateChatReq;
import com.coze.openapi.client.chat.CreateChatResp;
import com.coze.openapi.client.chat.model.ChatPoll;
import com.coze.openapi.client.connversations.message.model.Message;
import com.coze.openapi.client.connversations.message.model.MessageObjectString;
import com.coze.openapi.service.auth.TokenAuth;
import com.coze.openapi.service.service.CozeAPI;

import java.util.Arrays;
import java.util.Collections;

/**
 * <AUTHOR> on 2025-03-26 15:43.
 */
public class ChatWithImageExample {
    public static void main(String[] args) {

        // Get an access_token through personal access token or oauth.
        String token = "pat_z0h2V9N7RlEDEfjo9aD3fh2CzlJxK6qZFDqD1T2MBBt3YUgbLSazt2c8ZXnk7NAF";
        String botID = "7485964668297871410";
        String userID = IdUtil.fastUUID();

        TokenAuth authCli = new TokenAuth(token);

        // Init the Coze client through the access_token.
        CozeAPI coze =
                new CozeAPI.Builder()
                        .baseURL("https://api.coze.cn")
                        .auth(authCli)
                        .readTimeout(10000)
                        .build();
        ;

        String imgUrl = "https://cdn.iciyun.com/pic/456.png";

        /*
         * Step one, create chat
         * Call the coze.chat().stream() method to create a chat. The create method is a streaming
         * chat and will return a Flowable ChatEvent. Developers should iterate the iterator to get
         * chat event and handle them.
         * */
        CreateChatReq req =
                CreateChatReq.builder()
                        .botID(botID)
                        .userID(userID)
                        .messages(
                                Collections.singletonList(
                                        Message.buildUserQuestionObjects(
                                                Arrays.asList(
//                                                        MessageObjectString.buildText("Describe this picture"),
                                                        MessageObjectString.buildImageByURL(imgUrl)))))
                        .build();


        ChatPoll chat = null;
        try {
            chat = coze.chat().createAndPoll(req);
            if (CollUtil.isNotEmpty(chat.getMessages())){
                String str = chat.getMessages().get(0).getContent();
                str = StrUtil.replace(str,"json","");
                str = StrUtil.replace(str,"```","");

                String label = JSONUtil.parseObj(str).getStr("label");

                System.out.println(label);
            }
        } catch (Exception e) {
        }

    }
}
