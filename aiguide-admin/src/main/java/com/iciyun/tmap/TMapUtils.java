package com.iciyun.tmap;

/**
 * <AUTHOR> on 2025-04-15 11:17.
 */

import cn.hutool.http.HttpUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.iciyun.tmap.model.SuggestionResp;

import java.util.List;

public class TMapUtils {
    private static final String KEY = "E4UBZ-B3QKL-JP6PS-MY6T4-K5NI5-X4BHR"; // 替换为你的API密钥

    public static List<String> addressToGPS(String keyword) {
        List<String> list = Lists.newArrayList();
        try {
            String url = String.format(
                    "https://apis.map.qq.com/ws/place/v1/suggestion?keyword=%s&key=%s&output=json",
                    keyword,
                    KEY
            );

            String result = HttpUtil.get(url);

            System.out.println(result);

//            // 使用Jackson库解析JSON
//            ObjectMapper mapper = new ObjectMapper();
//            JsonNode rootNode = mapper.readTree(result);
//
//            // 提取geocodes数组
//            JsonNode geocodesNode = rootNode.get("geocodes");
//
//            // 遍历geocodes数组
//            for (JsonNode geocodeNode : geocodesNode) {
//                // 提取location字段,  坐标点：经度，纬度
//                String location = geocodeNode.get("location").asText();
//                list.add(location);
//            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    public static void main(String[] args) {
//        String address = "故宫博物院";
//        List<String> list = addressToGPS(address);
//
//        System.out.println(list);

        String json = "{\"status\":0,\"message\":\"query ok\",\"request_id\":\"11320884659242217338\",\"count\":100,\"data\":[{\"id\":\"5866905815035848227\",\"title\":\"故宫博物院\",\"address\":\"北京市东城区景山前街4号\",\"category\":\"旅游景点:国家级景点\",\"type\":0,\"location\":{\"lat\":39.91799,\"lng\":116.397027},\"adcode\":110101,\"province\":\"北京市\",\"city\":\"北京市\",\"district\":\"东城区\"},{\"id\":\"8578655022962124975\",\"title\":\"故宫博物院(北院区)\",\"address\":\"北京市海淀区南沙河东路上庄家园小区东530米\",\"category\":\"文化场馆:博物馆\",\"type\":0,\"location\":{\"lat\":40.09818,\"lng\":116.225789},\"adcode\":110108,\"province\":\"北京市\",\"city\":\"北京市\",\"district\":\"海淀区\"},{\"id\":\"1579932424162376709\",\"title\":\"故宫博物院-东华门\",\"address\":\"北京市东城区故宫东门外\",\"category\":\"旅游景点:风景名胜\",\"type\":0,\"location\":{\"lat\":39.91511,\"lng\":116.401518},\"adcode\":110101,\"province\":\"北京市\",\"city\":\"北京市\",\"district\":\"东城区\"},{\"id\":\"14084173762517668344\",\"title\":\"沈阳故宫博物院\",\"address\":\"辽宁省沈阳市沈河区沈阳路171号\",\"category\":\"文化场馆:博物馆\",\"type\":0,\"location\":{\"lat\":41.798456,\"lng\":123.455654},\"adcode\":210103,\"province\":\"辽宁省\",\"city\":\"沈阳市\",\"district\":\"沈河区\"},{\"id\":\"15593732622386939217\",\"title\":\"故宫博物院南门-入口\",\"address\":\"北京市东城区东华门路与西华门大街交叉口西北方向111米\",\"category\":\"室内及附属设施:通行设施类:门/出入口\",\"type\":0,\"location\":{\"lat\":39.91351,\"lng\":116.39729},\"adcode\":110101,\"province\":\"北京市\",\"city\":\"北京市\",\"district\":\"东城区\"},{\"id\":\"14351038601527681821\",\"title\":\"故宫博物院-西华门\",\"address\":\"北京市东城区景山前街4号故宫内(西华门大街与南长街交汇处东侧)\",\"category\":\"旅游景点:其它旅游景点\",\"type\":0,\"location\":{\"lat\":39.914809,\"lng\":116.392894},\"adcode\":110101,\"province\":\"北京市\",\"city\":\"北京市\",\"district\":\"东城区\"},{\"id\":\"6544748574915390813\",\"title\":\"故宫博物院-午门\",\"address\":\"北京市东城区故宫博物院内\",\"category\":\"旅游景点:风景名胜\",\"type\":0,\"location\":{\"lat\":39.913895,\"lng\":116.397161},\"adcode\":110101,\"province\":\"北京市\",\"city\":\"北京市\",\"district\":\"东城区\"},{\"id\":\"9643291457144910330\",\"title\":\"故宫博物院-御花园\",\"address\":\"北京市东城区景山前街4号故宫内\",\"category\":\"旅游景点:风景名胜\",\"type\":0,\"location\":{\"lat\":39.921488,\"lng\":116.396881},\"adcode\":110101,\"province\":\"北京市\",\"city\":\"北京市\",\"district\":\"东城区\"},{\"id\":\"15561927199645653337\",\"title\":\"故宫博物院东门-出口\",\"address\":\"北京市东城区东华门大街与故宫东门外交叉口西南方向10米\",\"category\":\"室内及附属设施:通行设施类:门/出入口\",\"type\":0,\"location\":{\"lat\":39.915124,\"lng\":116.401477},\"adcode\":110101,\"province\":\"北京市\",\"city\":\"北京市\",\"district\":\"东城区\"},{\"id\":\"4824149167922331316\",\"title\":\"故宫博物院-神武门\",\"address\":\"北京市东城区景山前街4号\",\"category\":\"旅游景点:国家级景点\",\"type\":0,\"location\":{\"lat\":39.922365,\"lng\":116.396781},\"adcode\":110101,\"province\":\"北京市\",\"city\":\"北京市\",\"district\":\"东城区\"}]}";

        SuggestionResp resp = new Gson().fromJson(json, SuggestionResp.class);
        System.out.println(resp);


    }


}
