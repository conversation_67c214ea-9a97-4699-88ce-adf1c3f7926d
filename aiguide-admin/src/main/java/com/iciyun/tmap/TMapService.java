package com.iciyun.tmap;

import com.iciyun.tmap.model.PlaceCloudDataListResp;
import com.iciyun.tmap.model.SearchByPolygonResp;
import com.iciyun.tmap.model.SearchResp;
import com.iciyun.tmap.model.SuggestionResp;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.QueryMap;

import java.util.Map;

public interface TMapService {

    @GET("ws/place/v1/suggestion")
    Call<SuggestionResp> suggestion(@QueryMap Map<String, Object> req);

    @GET("/ws/place/v1/search")
    Call<SearchResp> search(@QueryMap Map<String, Object> req);

    @GET("/ws/place/v1/search_by_polygon")
    Call<SearchByPolygonResp> searchByPolygon(@QueryMap Map<String, Object> req);

    @GET("/place_cloud/data/list")
    Call<PlaceCloudDataListResp> placeCloudDataList(@QueryMap Map<String, Object> req);

}
