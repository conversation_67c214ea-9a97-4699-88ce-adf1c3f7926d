package com.iciyun.tmap;

import com.iciyun.common.core.domain.R;
import com.iciyun.tmap.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

@Slf4j
@Service
public class TMapClient {

    @Value("${tmap.apiKey}")
    private String key;

    private static TMapService tMapService;

    private static final Retrofit retrofit = new Retrofit.Builder()
            .baseUrl("https://apis.map.qq.com/")
            .addConverterFactory(GsonConverterFactory.create())
            .build();

    static {
        if (tMapService == null) {
            tMapService = retrofit.create(TMapService.class);
        }
    }

    public R<SuggestionResp> suggestion(SuggestionReq req) throws Exception {
        req.setKey(key);

        Call<SuggestionResp> call = tMapService.suggestion(req.toMap());

        Response<SuggestionResp> response = call.execute();
        if (response.isSuccessful()) {
            SuggestionResp resp = response.body();
            return R.ok(resp);
        }
        return R.fail();
    }

    public SearchResp search(SearchReq req) throws Exception {
        req.setKey(key);

        Call<SearchResp> call = tMapService.search(req.toMap());

        Response<SearchResp> response = call.execute();
        if (response.isSuccessful()) {
            return response.body();
        }
        return null;
    }

    public SearchByPolygonResp searchByPolygon(SearchByPolygonReq req) throws Exception {
        req.setKey(key);

        Call<SearchByPolygonResp> call = tMapService.searchByPolygon(req.toMap());

        Response<SearchByPolygonResp> response = call.execute();
        if (response.isSuccessful()) {
            SearchByPolygonResp resp = response.body();
            return resp;
        }
        return null;
    }


    public PlaceCloudDataListResp placeClourdDataList(PlaceCloudDataListReq req) throws Exception {
        req.setKey(key);

        Call<PlaceCloudDataListResp> call = tMapService.placeCloudDataList(req.toMap());

        Response<PlaceCloudDataListResp> response = call.execute();
        if (response.isSuccessful()) {
            PlaceCloudDataListResp resp = response.body();
            return resp;
        }

        return null;

    }


}
