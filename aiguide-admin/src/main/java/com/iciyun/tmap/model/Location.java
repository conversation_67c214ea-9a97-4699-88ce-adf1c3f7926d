package com.iciyun.tmap.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class Location implements Serializable {

    private String lat;

    private String lng;

    public String getLat() {
        int lastIndexOf = Math.min(this.lat.length(), 10);
        return lat.substring(0, lastIndexOf);
    }

    public String getLng() {
        int lastIndexOf = Math.min(this.lng.length(), 10);
        return lng.substring(0, lastIndexOf);
    }
}
