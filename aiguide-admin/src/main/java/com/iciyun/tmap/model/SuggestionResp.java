package com.iciyun.tmap.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SuggestionResp extends BaseResp{

    private List<RespData> data;

    @Data
    public static class RespData {
        private String adcode;
        private String address;
        private String category;
        private String province;
        private String city;
        private String district;
        private String title;
        private String id;
        private Location location;

        private String category_code;

    }

}
