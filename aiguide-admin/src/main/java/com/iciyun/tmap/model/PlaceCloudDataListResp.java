package com.iciyun.tmap.model;

import com.iciyun.system.domain.bo.SupplementMapInfoCmd;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Objects;

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PlaceCloudDataListResp extends BaseResp{

    private Result result;

    @Data
    public static class Result {
        private String count;
        private String page_next;
        private List<RespData> data;
    }


    @Data
    public static class RespData {
        private String adcode;
        private String address;
        private String province;
        private String city;
        private String district;
        private String title;
        private String id;
        private String polygon;
        private String ud_id;   // 关联景区 ID
        private Location location;
        private Customize x;

        public String genKeywork(){
            if (Objects.nonNull(this.polygon) && !this.polygon.isEmpty()) {
                return Const.KEYWORK_DEFAULT;
            }
            return this.title;
        }

        public SupplementMapInfoCmd genSupplementMapInfoCmd(){

            Adcode ad = new Adcode(this.adcode);
            return SupplementMapInfoCmd.builder()
                    .polygon(this.polygon)
                    .name(this.title)
                    .tmapScenicName(this.title)
                    .keyword(this.genKeywork())
                    .provinceName(this.province)
                    .cityName(this.city)
                    .districtName(this.district)
                    .provinceCode(ad.getProvinceCode())
                    .cityCode(ad.getCityCode())
                    .districtCode(ad.getDistrictCode())
                    .detailAddress(this.address)
                    .latitude(location.getLat())
                    .longitude(location.getLng())
                    .build();
        }

    }

    @Data
    public static class Customize{
        private String scenic_area_id; // 景区 ID
        private String update_time; // 景区名称
    }


}
