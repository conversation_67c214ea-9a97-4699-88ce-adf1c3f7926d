package com.iciyun.tmap.model;

import com.iciyun.common.constant.Constants;
import com.iciyun.system.domain.SysTouristLabel;
import com.iciyun.system.domain.bo.SupplementMapInfoCmd;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Objects;

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SearchByPolygonResp extends BaseResp{

    private List<RespData> data;

    @Data
    public static class RespData {
        private String address;
        private String category;
        private String title;
        private String id;
        private Location location;
        private AdInfo ad_info;
        private String category_code;


        public SupplementMapInfoCmd genSupplementMapInfoCmd(){

            AdInfo adInfo = this.getAd_info();
            String adcode = adInfo.getAdcode();
            Adcode ad = new Adcode(adcode);

            return SupplementMapInfoCmd.builder()
                    .name(this.title)
                    .provinceName(adInfo.getProvince()).cityName(adInfo.getCity()).districtName(adInfo.getDistrict())
                    .provinceCode(ad.getProvinceCode()).cityCode(ad.getCityCode()).districtCode(ad.getDistrictCode())
                    .longitude(this.location.getLng()).latitude(this.location.getLat())
                    .poiId(this.getId())
                    .tmapScenicName(this.getTitle()).keyword(this.getTitle())
                    .category(this.getCategory()).categoryCode(Integer.parseInt(this.getCategory_code()))
                    .build();

        }

        public SupplementMapInfoCmd genSupplementMapInfoCmd(String scenicSpotName){

            AdInfo adInfo = this.getAd_info();
            String adcode = adInfo.getAdcode();
            Adcode ad = new Adcode(adcode);

            if (Objects.isNull(this.category_code) || this.category_code.isEmpty()){
                this.category_code = String.valueOf(Constants.CATEGORY_CODE_DEFAULT);
            }
            String dealLabelName = SysTouristLabel.dealLabelName(scenicSpotName, this.getTitle());

            return SupplementMapInfoCmd.builder()
                    .name(dealLabelName)
                    .provinceName(adInfo.getProvince()).cityName(adInfo.getCity()).districtName(adInfo.getDistrict())
                    .provinceCode(ad.getProvinceCode()).cityCode(ad.getCityCode()).districtCode(ad.getDistrictCode())
                    .longitude(this.location.getLng()).latitude(this.location.getLat())
                    .poiId(this.getId())
                    .tmapScenicName(this.getTitle()).keyword(this.getTitle())
                    .category(this.getCategory()).categoryCode(Integer.parseInt(this.category_code))
                    .build();

        }



    }

}
