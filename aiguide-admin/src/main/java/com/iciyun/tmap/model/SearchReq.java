package com.iciyun.tmap.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SearchReq extends BaseReq{

    public static final String FILTER_DEFAULT = "category=旅游景点";

    private String keyword;
    private String boundary;
    private String filter;
    private String page_size;
    private String page_index;


    @Override
    public Map<String, Object> toMap() {

        Map<String, Object> map = new HashMap<>();
        map.put("key", getKey());
        map.put("keyword", keyword);
        map.put("boundary", boundary);
        map.put("added_fields", "category_code");

        if (Objects.nonNull(page_index) && !page_index.isEmpty()) {
            map.put("page_index", page_index);
        }

        if (Objects.nonNull(page_size) && !page_size.isEmpty()) {
            map.put("page_size", page_size);
        }

        if (Objects.nonNull(filter) && !filter.isEmpty()) {
            map.put("filter", filter);
        }


        return map;
    }
}
