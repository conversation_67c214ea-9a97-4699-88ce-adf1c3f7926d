package com.iciyun.tmap.model;

import cn.hutool.core.util.StrUtil;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PlaceCloudDataListReq extends BaseReq{

    private static final int PAGE_SIZE_DEFAULT = 200;

    private String table_id;
    private String page_next;


    @Override
    public Map<String, Object> toMap() {

        Map<String, Object> map = new HashMap<>();
        map.put("key", getKey());
        map.put("page_size", PAGE_SIZE_DEFAULT);
        map.put("table_id", table_id);

        if (StrUtil.isNotBlank(this.page_next)) {
            map.put("page_next", page_next);
        }

        return map;
    }
}
