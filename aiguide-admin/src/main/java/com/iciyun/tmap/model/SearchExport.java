package com.iciyun.tmap.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * map.put("title", respData.getTitle());
 *                     map.put("tel", respData.getTel());
 *                     map.put("address", respData.getAddress());
 *                     map.put("category", respData.getCategory());
 *                     map.put("province", respData.getAd_info().getProvince());
 *                     map.put("city", respData.getAd_info().getCity());
 *                     map.put("district", respData.getAd_info().getDistrict());
 *                     map.put("adcode", respData.getAd_info().getAdcode());
 */

@Data
public class SearchExport {

    @ExcelProperty("名称")
    private String title;
    @ExcelProperty("电话")
    private String tel;
    @ExcelProperty("地址")
    private String address;
    @ExcelProperty("分类")
    private String category;
    @ExcelProperty("省份")
    private String province;
    @ExcelProperty("省份编码")
    private String provinceCode;
    @ExcelProperty("城市")
    private String city;
    /**
     * 城市编码
     */
    @ExcelProperty("城市编码")
    private String cityCode;

    @ExcelProperty("区县")
    private String district;
    /**
     * 区县编码
     */
    @ExcelProperty("区县编码")
    private String districtCode;



}
