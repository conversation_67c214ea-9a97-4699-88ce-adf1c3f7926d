package com.iciyun.tmap.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SearchByPolygonReq extends BaseReq{

    private String keyword;
    private String polygon;
    private String filter;
    private String page_size;
    private String page_index;


    @Override
    public Map<String, Object> toMap() {

        Map<String, Object> map = new HashMap<>();
        map.put("key", getKey());
        map.put("page_index", page_index);
        map.put("page_size", Const.PAGE_SIZE_DEFAULT);
        map.put("keyword", keyword);
        map.put("added_fields", "category_code");
        if (Objects.nonNull(polygon) && !polygon.isEmpty()) {
            map.put("polygon", polygon);
        }
        if (Objects.nonNull(filter) && !filter.isEmpty()) {
            map.put("filter", filter);
        }

        return map;
    }
}
