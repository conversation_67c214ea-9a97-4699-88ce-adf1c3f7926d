package com.iciyun.tmap.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SuggestionReq extends BaseReq{

    private String keyword;
    private String filter;
    @Override
    public Map<String, Object> toMap() {

        Map<String, Object> map = new HashMap<>();
        map.put("key", getKey());
        map.put("keyword", keyword);
        map.put("added_fields", "category_code");
        if (Objects.nonNull(filter) && !filter.isEmpty()) {
            map.put("filter", filter);
        }
        return map;
    }
}
