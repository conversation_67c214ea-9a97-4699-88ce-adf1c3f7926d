package com.iciyun.aiguide.controller.api;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.iciyun.common.core.controller.BaseController;
import com.iciyun.common.core.domain.AjaxResult;
import com.iciyun.common.core.page.TableDataInfo;
import com.iciyun.common.utils.SecurityUtils;
import com.iciyun.system.domain.ScenicDoc;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.dto.ScenicDocPageParam;
import com.iciyun.system.service.IScenicDocService;
import com.iciyun.system.service.IScenicSpotCustomizeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 景区文档管理
 *
 * <AUTHOR> on 2025-06-12 18:38.
 */
@Slf4j
@RestController
@RequestMapping("/guide/scenicDoc")
public class ScenicDocCtrl extends BaseController {

    @Autowired
    IScenicDocService scenicDocService;

    @Autowired
    IScenicSpotCustomizeService scenicSpotCustomizeService;

    /**
     * 分页列表
     * @param scenicDoc
     * @return
     */
    @GetMapping("/pageList")
    public TableDataInfo pageList(ScenicDocPageParam scenicDocPageParam) {
        startPage();
        scenicDocPageParam.setUserId(SecurityUtils.getUserId()+"");
        List<ScenicDoc> list = scenicDocService.pageList(scenicDocPageParam);
        return getDataTable(list);
    }

    /**
     * 列表
     * @param scenicDoc
     * @return
     */
    @GetMapping("/list")
    public AjaxResult list(ScenicDocPageParam scenicDocPageParam) {
        scenicDocPageParam.setUserId(SecurityUtils.getUserId()+"");
        List<ScenicDoc> list = scenicDocService.pageList(scenicDocPageParam);
        return success(list);
    }


    /**
     * 保存/更新 文档
     */
    @PostMapping(value = "/saveOrUpdate")
    public AjaxResult saveOrUpdate(@RequestBody List<ScenicDoc> scenicDocList) {
        ScenicSpot spot = null;

        if (CollUtil.isNotEmpty(scenicDocList)) {
            for (ScenicDoc scenicDoc : scenicDocList) {
                if (spot==null){
                    spot = scenicSpotCustomizeService.getById(scenicDoc.getScenicId().intValue());
                }
                scenicDoc.setScenicName(spot.getName());
                if (scenicDoc.getId()==null){
                    scenicDoc.setCreateTime(LocalDateTime.now());
                }
                if (StrUtil.isNotEmpty(scenicDoc.getFileName())){
                    String extName = FileUtil.extName(scenicDoc.getFileName());
                    scenicDoc.setExtName(extName);
                }
                scenicDoc.setUserId(SecurityUtils.getUserId()+"");

                scenicDocService.saveOrUpdate(scenicDoc);
            }
        }

        return success();
    }


    /**
     * 删除 文档
     * @param id
     * @return
     */
    @DeleteMapping(value = "/del")
    public AjaxResult del(Long id) {

        scenicDocService.removeById(id);

        return success();
    }








}
