package com.iciyun.aiguide.controller;

import com.alibaba.fastjson2.JSON;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Result;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyV3Result;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.iciyun.common.annotation.Anonymous;
import com.iciyun.common.constant.Constants;
import com.iciyun.common.core.controller.BaseController;
import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.system.domain.*;
import com.iciyun.system.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Anonymous
@RequiredArgsConstructor
@RestController
@RequestMapping("/wxpay")
public class WxPayNotifyCtrl extends BaseController {

    private final RedisCache redisCache;
    private final ISysUserService userService;
    private final IRechargeConfigService rechargeConfigService;
    private final IPaymentOrderService paymentOrderService;
    private final WxPayService wxPayService;
    private final ITokenDetailService tokenDetailService;
    private final IGuidePayOrderService guidePayOrderService;
    private final IRefundOrderService refundOrderService;
    private final IGuidePayOrderItemService guidePayOrderItemService;

    /**
     * 订单支付回执
     * @param notifyContent
     */
    @PostMapping("/notify/order")
    public String notifyOrder(@RequestBody String notifyContent, @RequestHeader("Wechatpay-Timestamp") String timeStamp, @RequestHeader("Wechatpay-Nonce") String nonce,
                              @RequestHeader("Wechatpay-Signature") String signature, @RequestHeader("Wechatpay-Serial") String serial) {
        log.info("订单支付回执内容：{}", notifyContent);
        SignatureHeader header = new SignatureHeader();
        header.setTimeStamp(timeStamp);
        header.setNonce(nonce);
        header.setSignature(signature);
        header.setSerial(serial);
        try {
            WxPayNotifyV3Result wxPayNotifyV3Result = wxPayService.parseOrderNotifyV3Result(notifyContent, header);
            WxPayNotifyV3Result.DecryptNotifyResult result = wxPayNotifyV3Result.getResult();
            String tradeState = result.getTradeState();
            // 支付成功
            if ("SUCCESS".equals(tradeState)) {
                String outTradeNo = result.getOutTradeNo();
                String transactionId = result.getTransactionId();

                PaymentOrder paymentOrder = paymentOrderService.lambdaQuery().eq(PaymentOrder::getPaymentOrderId, outTradeNo).one();
                paymentOrder.paySuccess(transactionId);
                paymentOrderService.updateById(paymentOrder);
                log.info("支付成功更新支付订单已完成：{}", paymentOrder.getPaymentOrderId());

                GuidePayOrderUpdate guidePayOrderUpdate = new GuidePayOrderUpdate();
                guidePayOrderUpdate.setOrderId(paymentOrder.getOrderId());
                guidePayOrderUpdate.setOpenId(paymentOrder.getOpenId());
                guidePayOrderUpdate.setPayStatue(PaymentOrderStatus.SUCCESS.name());
                guidePayOrderUpdate.setOrderStatus(PaymentOrderStatus.SUCCESS.name());
                guidePayOrderService.updateGuidePayOrder(guidePayOrderUpdate);
                log.info("支付成功更新订单已完成：{}", paymentOrder.getPaymentOrderId());

                GuidePayOrder guidePayOrder = guidePayOrderService.lambdaQuery().eq(GuidePayOrder::getOrderId, paymentOrder.getOrderId()).one();



                // 获取当前时间（带时区，防止服务器时区影响）
                ZonedDateTime now = ZonedDateTime.now(ZoneId.systemDefault());
                // 计算当天的 23:59:59
                ZonedDateTime endOfDay = now.toLocalDate().atTime(23, 59, 59).atZone(now.getZone());
                // 计算剩余秒数
                long secondsEndOfDay = Duration.between(now, endOfDay).getSeconds();

                // 订单ID
                String orderId = guidePayOrder.getOrderId();
                List<GuidePayOrderItem> list = guidePayOrderItemService.lambdaQuery().eq(GuidePayOrderItem::getOrderId, orderId).list();
                GuidePayOrderItem guidePayOrderItem = list.stream().findFirst().get();

                String redisKey = String.format("user:%s:scenic:%s", paymentOrder.getUserId(), guidePayOrderItem.getScenicId());
                redisCache.setCacheObject(redisKey, now.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME), (int) secondsEndOfDay, TimeUnit.SECONDS);

                String eyeRedisKey = String.format("eye:user:%s:scenic:%s", paymentOrder.getUserId(), guidePayOrderItem.getScenicId());
                redisCache.setCacheObject(eyeRedisKey, now.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME), (int) secondsEndOfDay, TimeUnit.SECONDS);





//                String userId = paymentOrder.getUserId();
//                SysUser sysUser = userService.selectUserById(Long.parseLong(userId));
//                String rechargeConfigRaw = paymentOrder.getRechargeConfigRaw();
//                RechargeConfig rechargeConfig = JSON.parseObject(rechargeConfigRaw, RechargeConfig.class);
//
//                TokenDetail tokenDetail = new TokenDetail();
//                tokenDetail.initByRecharge(paymentOrder, rechargeConfig, sysUser);
//                tokenDetailService.save(tokenDetail);

            }


        } catch (WxPayException e) {
            log.error("订单支付回执异常：", e);
            return WxPayNotifyResponse.fail("失败");
        }

        return WxPayNotifyResponse.success("成功");
    }


    /**
     * 订单退款回执
     * @param notifyContent
     */
    @PostMapping("/notify/refund")
    public String notifyRefund(@RequestBody String notifyContent, @RequestHeader("Wechatpay-Timestamp") String timeStamp, @RequestHeader("Wechatpay-Nonce") String nonce,
                               @RequestHeader("Wechatpay-Signature") String signature, @RequestHeader("Wechatpay-Serial") String serial) {
        SignatureHeader header = new SignatureHeader();
        header.setTimeStamp(timeStamp);
        header.setNonce(nonce);
        header.setSignature(signature);
        header.setSerial(serial);

        try {

            WxPayRefundNotifyV3Result result = wxPayService.parseRefundNotifyV3Result(notifyContent, header);
            WxPayRefundNotifyV3Result.DecryptNotifyResult decryptNotifyResult = result.getResult();
            String outTradeNo = decryptNotifyResult.getOutTradeNo();
            String outRefundNo = decryptNotifyResult.getOutRefundNo();
            String refundStatus = decryptNotifyResult.getRefundStatus();
            String refundId = decryptNotifyResult.getRefundId();

            PaymentOrder paymentOrder = paymentOrderService.lambdaQuery().eq(PaymentOrder::getPaymentOrderId, outRefundNo).one();
            paymentOrder.syncRefund(refundId, refundStatus);
            paymentOrderService.updateById(paymentOrder);

            if ("SUCCESS".equals(refundStatus)) {
                refundOrderService.updateRefundOrder(paymentOrder.getOrderId());
            }

        } catch (WxPayException e) {
            log.error("订单退款回执异常：", e);
            throw new RuntimeException(e);
        }

        return WxPayNotifyResponse.success("成功");
    }



}


