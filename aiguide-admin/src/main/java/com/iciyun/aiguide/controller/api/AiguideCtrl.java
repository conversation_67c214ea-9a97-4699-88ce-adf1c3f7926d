package com.iciyun.aiguide.controller.api;

import com.iciyun.common.annotation.Anonymous;
import com.iciyun.common.core.controller.BaseController;
import com.iciyun.common.core.domain.AjaxResult;
import com.iciyun.common.core.domain.R;
import com.iciyun.common.core.domain.entity.*;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.oss.OssService;
import com.iciyun.system.domain.GlobalVariablesSingleton;
import com.iciyun.system.domain.SysChatUser;
import com.iciyun.system.service.IGuideService;
import com.iciyun.system.service.IGuideTkService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 *  coze api
 */
@RestController
@RequestMapping("/guide/user")
@Slf4j
public class AiguideCtrl extends BaseController {

    @Autowired
    private IGuideService iGuideService;

    @Autowired
    private IGuideTkService guideTkService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private OssService ossService;

    //cozeToken
    private static String cozeTokenKey = "chat:cozeToken";

    //14 分钟
    private static int cozeTokenTime = 14;

    /**
     * 获取 token
     */
    @GetMapping(value = "/getToken")
    public AjaxResult getToken() {
        String coziToken = guideTkService.getToken();
        if (coziToken == null) {
            return error("获取token失败");
        }
        GlobalVariablesSingleton.getInstance().setCozeToken(coziToken);
        return success(coziToken);
    }

    /**
     * 创建会话
     */
    @PostMapping(value = "/createChat")
    public AjaxResult createChat(@Validated @RequestBody CreateChatCmd cmd) {
        if (cmd.getUserId() == null) {
            return error("用户ID不能为空");
        }
        SysChatUser curChatUser = new SysChatUser();
        curChatUser.setSysChatUserId(Long.parseLong(cmd.getUserId()));
        curChatUser.setBotId(cmd.getBotId());
        return success(iGuideService.createSession(cmd.getBotId(), curChatUser, null));
    }

    @PostMapping(value = "/streamChat")
    public Flux<String> streamChat(@Validated @RequestBody StreamChatCmd cmd) {
        return iGuideService.streamChat(cmd);
    }

    /**
     * 用户发起非流式对话
     */
    @PostMapping(value = "/userChat")
    public AjaxResult userChat(@Validated @RequestBody UserChatCmd cmd) {
        if (cmd.getUserId() == null) {
            return error("用户ID不能为空");
        }
        return success(iGuideService.noStreamChat(cmd, null));
    }

    /**
     * 非流式对话状态查看
     */
    @GetMapping(value = "/noStreamChatStatus")
    public AjaxResult noStreamChatStatus(@Validated @RequestBody UserChatStatueCmd cmd) {
        return success(iGuideService.noStreamChatStatus(cmd));
    }

    /**
     * 回答用户发起非流式对话
     */
    @GetMapping(value = "/answerUser")
    public AjaxResult answerUser(@Validated @RequestBody UserChatStatueCmd cmd) {
        return success(iGuideService.noStreamChatAnswer(cmd));
    }

    /**
     * 提交工具执行结果 非流式会话 相当于 在用户发送消息时，调用了 插件 ，例如 语速慢一点，则，在查询对话的结果时，会执行。
     */
    @PostMapping(value = "/toolOutputs")
    public AjaxResult userToolOutputs(@Validated @RequestBody UserToolOutputdCmd cmd) {
        return success(iGuideService.userToolOutputs(cmd));
    }

    /**
     * 取消进行中的对话
     * 对话的运行状态。取值为：
     * created：对话已创建。
     * in_progress：智能体正在处理中。
     * completed：智能体已完成处理，本次对话结束。
     * failed：对话失败。
     * requires_action：对话中断，需要进一步处理。
     * canceled：对话已取消。
     */
    @PostMapping(value = "/userCancel")
    public AjaxResult userCancel(@Validated @RequestBody UserChatStatueCmd cmd) {
        return success(iGuideService.userCancel(cmd));
    }

    /**
     * 创建一条消息，并将其添加到指定的会话中<实际就是，发送一条对话到指定的会话中>
     * <p>
     * return:
     * <p>
     * id 为 对话ID
     */
    @PostMapping(value = "/userCreateMessage")
    public AjaxResult userCreateMessage(@Validated @RequestBody UserCreateMessageCmd cmd) {
        return success(iGuideService.userCreateMessage(cmd));
    }

    /**
     * 查询消息列表，包含 用户 和 bot 发的所有消息， 有分页
     */
    @PostMapping(value = "/userGetMessages")
    public AjaxResult userGetMessages(@Validated @RequestBody UserGetMessagesCmd cmd) {
        return success(iGuideService.userGetMessages(cmd));
    }

    /**
     * 查看指定消息的详细信息
     */
    @GetMapping(value = "/userGetMsg")
    public AjaxResult userGetMsg(@Validated @RequestBody GetMessageCmd cmd) {
        return success(iGuideService.userGetMsg(cmd));
    }

    /**
     * 修改消息
     * 支持修改消息内容、附加内容和消息类型
     * <p>
     * 返回修改的消息
     */
    @PostMapping(value = "/userModifyMsg")
    public AjaxResult userModifyMsg(@Validated @RequestBody ModifyMessageCmd cmd) {
        return success(iGuideService.userModifyMsg(cmd));
    }

    /**
     * 删除消息
     * 返回删除的消息
     */
    @PostMapping(value = "/userDelMsg")
    public AjaxResult userDelMsg(@Validated @RequestBody GetMessageCmd cmd) {
        return success(iGuideService.userDelMsg(cmd));
    }

    /**
     * 上传文件，用于对话中的文件
     */
    @PostMapping(value = "/userUploadFile")
    public AjaxResult userUploadFile(@RequestParam("file") MultipartFile file) {
        return success(iGuideService.userUploadFile(file));
    }

    /**
     * 查看文件
     */
    @GetMapping(value = "/getFileById")
    public AjaxResult getFileById(String fileId) {
        return success(iGuideService.getFileById(fileId));
    }

    /**
     * 查看音色列表
     * voice_list
     */
    @GetMapping(value = "/getVoices")
    public AjaxResult getVoices(GetVoicesCmd cmd) {
        return success(iGuideService.getVoices(cmd));
    }

    /**
     * 复刻音色
     * 1 上传音频文件，按照指定的音色生成
     * 2 指定一段文字，按照已复制的音色输出
     */
    @PostMapping(value = "/cloneVoice")
    public AjaxResult cloneVoice(CloneVoiceCmd cmd) {
        return success(iGuideService.cloneVoice(cmd));
    }


    @Autowired
    private HttpServletResponse response;

    /**
     * 语音合成
     * 将指定文本合成为音频文件。
     */
    @PostMapping(value = "/textSpeech")
    public AjaxResult textSpeech(TextSpeechCmd cmd) {
        iGuideService.textSpeech(cmd, null);
        return success();
    }

    /**
     * 语音识别
     * 将音频文件转录为文本。
     */
    @PostMapping(value = "/transcriptions")
    public R transcriptions(@RequestParam("file") MultipartFile file) {
        log.info("file : {}", file.getOriginalFilename());
        String cozeToken = redisCache.getCacheObject(cozeTokenKey);
        if (StringUtils.isBlank(cozeToken)) {
            cozeToken = guideTkService.getToken();
            redisCache.setCacheObject(cozeTokenKey, cozeToken, cozeTokenTime, TimeUnit.MINUTES);
        }
        return R.ok(iGuideService.transcriptions(file, cozeToken));
    }

    /**
     * 创建房间
     * 用于创建一个房间，并将智能体加入房间，然后才能调用 RTC SDK 和智能体开始语音通话。
     * 语音相关功能仅限专业版用户可用
     */
    @PostMapping(value = "/rooms")
    public AjaxResult rooms(RoomsCmd cmd) {
        return success(iGuideService.rooms(cmd));
    }

    /**
     * 上传阿里云
     */
    @Anonymous
    @PostMapping(value = "/upFiles")
    public AjaxResult upFiles(@RequestParam("file") MultipartFile file) {
        if (file == null) {
            return error("文件不能为空!");
        }
        String fileName = file.getOriginalFilename();
        try {
//            byte[] bytes = file.getBytes();
            return success(ossService.uploadByInputstream(fileName, file.getInputStream()));
        } catch (IOException e) {
            return error("上传失败!");
        }
    }


    /**
     * 批量上传附件
     * @param files
     * @return
     */
    @PostMapping(value = "/batchUpload")
    public R<Object> batchUpload(@RequestParam("file") MultipartFile[] files) {
        List<String> list = new ArrayList<>();
        try {
            for (MultipartFile file : files) {
                String fileName = file.getOriginalFilename();
                String url = ossService.uploadByInputstream(fileName, file.getInputStream());
                list.add(url);
            }
        } catch (IOException e) {
            log.error("批量上传文件异常", e);
        }
        return R.ok(list);
    }


}
