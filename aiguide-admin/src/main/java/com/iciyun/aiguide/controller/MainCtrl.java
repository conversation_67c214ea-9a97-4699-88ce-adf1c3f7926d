package com.iciyun.aiguide.controller;

import redis.clients.jedis.Jedis;

import java.util.Set;

public class MainCtrl {
    public static void main(String[] args) {
        // 启动应用程序
        Jedis jedis_test = new Jedis("10.10.1.86", 6379);
        if (jedis_test != null) {
            jedis_test.auth("QAZ123qaz.");  //如redis没有设置密码, 则无需添加此行
            System.out.println("连接Redis成功");
        }

        jedis_test.select(2);

        // get key

        String key = "scenic:*";
        Set<String> keys = jedis_test.keys(key);
        for (String k : keys) {
            String val = jedis_test.get(k);
            System.out.println(k + " : " + val);
        }

        System.out.printf("keys：" + keys.size());
    }
}
