package com.iciyun.aiguide.controller.user;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.hutool.json.JSONUtil;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import com.iciyun.common.annotation.Anonymous;
import com.iciyun.common.core.controller.BaseController;
import com.iciyun.common.core.domain.AjaxResult;
import com.iciyun.common.core.domain.R;
import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.common.core.domain.entity.wx.ScenicSpotPayCmd;
import com.iciyun.common.core.domain.entity.wx.WxMiniAppCodeCmd;
import com.iciyun.common.core.domain.entity.wx.WxUserCmd;
import com.iciyun.common.core.page.TableDataInfo;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.common.cqe.ExplanationStyleView;
import com.iciyun.common.cqe.MineView;
import com.iciyun.common.cqe.SaveExplanationStyleCmd;
import com.iciyun.common.cqe.ToPayCmd;
import com.iciyun.common.enums.FreeTypeEnum;
import com.iciyun.common.enums.PartnerBusinessType;
import com.iciyun.common.utils.SecurityUtils;
import com.iciyun.common.utils.StringUtils;
import com.iciyun.common.utils.uuid.IdHutool;
import com.iciyun.system.domain.*;
import com.iciyun.system.domain.bo.*;
import com.iciyun.system.domain.qo.LabelsQuery;
import com.iciyun.system.domain.qo.ScenicSpotQuery;
import com.iciyun.system.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 小程序端接口
 */
@RestController
@RequestMapping("/guide/miniapp")
@Slf4j
@RequiredArgsConstructor
public class AiGuideMiniAppCtrl extends BaseController {

    private final ISysUserService userService;
    private final IRechargeConfigService rechargeConfigService;
    private final IPaymentOrderService paymentOrderService;

    private final ISysDictDataService sysDictDataService;

    private final ITokenDetailService tokenDetailService;
    private final WxMaService wxMaService;
    private final IScenicService scenicService;
    private final ISysTouristLabelService sysTouristLabelService;
    private final IScenicSpotCustomizeService scenicSpotCustomizeService;
    private final IDistrictService districtService;
    private final IUserRecService userRecService;
    private final IGuidePayOrderService guidePayOrderService;
    @Autowired
    private IBaseConfigService baseConfigService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private IdHutool idHutool;
    @Autowired
    private IScenicGuideService scenicGuideService;
    @Autowired
    private IUserSuggestionService userSuggestionService;
    @Autowired
    private IScenicHeadphoneService scenicHeadphoneService;
    @Autowired
    private IScenicLocationService scenicLocationService;
    @Autowired
    private IScenicRatioService scenicRatioService;

    @Autowired
    private IScenicAdminService scenicAdminService;

    @Autowired
    private ISysScenicStService sysScenicStService;

    /**
     * 加载我的信息
     *
     * @param userId
     * @return
     */
    @GetMapping(value = "/loadMine")
    public R<MineView> loadMine(Long userId) {
        SysUser loginUser = SecurityUtils.getLoginUser().getUser();
        SysUser sysUser = userService.selectUserById(loginUser.getUserId());
        MineView mineView = new MineView(sysUser);
        if(sysUser != null){
            Long ads = scenicAdminService.lambdaQuery()
                    .eq(ScenicAdmin::getBusinessType, PartnerBusinessType.COOPERATION.getCode())
                    .eq(ScenicAdmin::getUserPhone, sysUser.getUserName())
                    .count();
            mineView.setFlagOrderInfo(ads != null && ads > 0 ? true : false);
        }
        return R.ok(mineView);
    }


    /**
     * 加载讲解风格
     *
     * @return
     */
    @GetMapping(value = "/loadExplanationStyle")
    public R<ExplanationStyleView> loadExplanationStyle() {

//        List<Map<String, String>> roleTypes = RoleTypeEnum.toList();
//        List<Map<String, String>> guideStyles = GuideStyleEnum.toList();
//        List<Map<String, String>> hobbyTypes = HobbyTypeEnum.toList()  ;
//        Map<String, Object> map = Map.of("roleTypes", roleTypes, "guideStyles", guideStyles, "hobbyTypes", hobbyTypes);

        SysUser loginUser = SecurityUtils.getLoginUser().getUser();
        SysUser sysUser = userService.selectUserById(loginUser.getUserId());
        ExplanationStyleView view = new ExplanationStyleView(sysUser);
        return R.ok(view);
    }

    /**
     * 保存讲解风格
     *
     * @param cmd
     * @return
     */
    @PostMapping(value = "/saveExplanationStyle")
    public R<Void> saveExplanationStyle(@RequestBody SaveExplanationStyleCmd cmd) {

        try {
            SysUser loginUser = SecurityUtils.getLoginUser().getUser();
            SysUser sysUser = userService.selectUserById(loginUser.getUserId());
            sysUser.saveExplanationStyle(cmd);
            userService.updateUserProfile(sysUser);

        } catch (Exception e) {
            error("保存讲解风格异常");
            log.error("保存讲解风格异常:", e);
        }

        return R.ok();
    }

    /**
     * 加载充值页面信息
     *
     * @param userId
     * @return
     */
    @GetMapping("/rechargeView")
    public AjaxResult rechargeView(Long userId) {
        try {
            SysUser sysUser = userService.selectUserById(userId);
            List<RechargeConfig> list = rechargeConfigService.lambdaQuery().list();
            Map<String, Object> result = Map.of("user", sysUser, "rechargeConfigs", list);
            return success(result);
        } catch (Exception e) {
            error("加载充值页面信息异常");
            log.error("加载充值页面信息异常:", e);
            return error();
        }

    }

    /**
     * 去支付
     *
     * @return
     */
    @PostMapping("/toPay")
    public AjaxResult toPay(ToPayCmd cmd) {

        SysUser sysUser = userService.selectUserById(cmd.getUserId());
        String rechargeConfigId = cmd.getRechargeConfigId();
        RechargeConfig rechargeConfig = rechargeConfigService.lambdaQuery().eq(RechargeConfig::getId, rechargeConfigId).one();

        PlaceOrderRechargeCmd placeOrderRechargeCmd = new PlaceOrderRechargeCmd(sysUser, rechargeConfig);

        return paymentOrderService.placeOrderRecharge(placeOrderRechargeCmd);
    }

    /**
     * 账单查询
     *
     * @param tokenDetail
     * @return
     */
    @GetMapping("/queryBillList")
    public TableDataInfo queryBill(TokenDetail tokenDetail) {

        SysUser user = SecurityUtils.getLoginUser().getUser();

        startPage();
        List<TokenDetail> list = tokenDetailService.lambdaQuery()
                .eq(TokenDetail::getUserPhone, user.getPhonenumber())
                .orderByDesc(TokenDetail::getCreateTime)
                .list();

        return getDataTable(list);
    }

    /**
     * 景区列表
     */
    @Anonymous
    @GetMapping("/scenicSpot/list")
    public TableDataInfo queryScenicSpot4MiniApp(ScenicSpotMiniAppQuery query) {

        query.judgeIfCurrentLocation();
        Integer targetPosition = query.getTargetPosition();
        if (targetPosition == 1) {
            String cityCode = query.getCityCode();
            cityCode = StringUtils.isBlank(cityCode) ? "110100" : cityCode;
            District one = districtService.lambdaQuery().eq(District::getAdcode, cityCode).one();
            String[] xy = one.getXy();
            query.setCurrentCityX(xy[0]);
            query.setCurrentCityY(xy[1]);
        }

        startPage();

        List<ScenicSpot> list = scenicSpotCustomizeService.queryScenicSpot4MiniApp(query);
        return getDataTable(list);
    }

    /**
     * 推荐成功 人数
     */
    @PostMapping(value = "/getRecCount")
    public R getRecCount(@RequestBody WxUserCmd cmd) {
        if (StringUtils.isEmpty(cmd.getUserName())) {
            SysUser user = SecurityUtils.getLoginUser().getUser();
            cmd.setUserName(user.getUserName());
            cmd.setUserId(user.getUserId());
        }
        Map<String, Object> retMap = new HashMap<>();
        Long recCount = userRecService.lambdaQuery()
                .eq(UserRec::getRecUserName, cmd.getUserName())
                .count();
        retMap.put("recCount", recCount);
        //我的收益
        QueryTotalProfitQry qry = new QueryTotalProfitQry();
        qry.setUserId(cmd.getUserId());
        qry.setTransactionTypes(TransactionType.getTotalProfitQueryTypes());
        BigDecimal totalProfit = tokenDetailService.queryTotalProfit(qry);
        totalProfit = totalProfit == null ? BigDecimal.ZERO : totalProfit;
        retMap.put("earningsBean", totalProfit);
        return R.ok(retMap);
    }

    /**
     * 受邀好友
     */
    @PostMapping(value = "/getRecList")
    public TableDataInfo getRecList(@RequestBody WxUserCmd cmd) {
        startPage();
        if (StringUtils.isEmpty(cmd.getUserName())) {
            SysUser user = SecurityUtils.getLoginUser().getUser();
            cmd.setUserName(user.getUserName());
        }
        List<UserRecVo> list = userRecService.selectUserRecList(cmd.getUserName());
        return getDataTable(list);
    }

    /**
     * 查询随身讲景点
     * @param qry
     * @return
     */
    @PostMapping("/queryTouristAttractions")
    @Anonymous
    public R<SysTouristLabel> queryTouristAttractions(@RequestBody TouristAttractionsQry qry){

        Integer scenicSpotId = qry.getScenicSpotId();
        ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, scenicSpotId).one();

        log.info("查询景区[{}]内的随身听讲景点：{}", scenicSpot.getName(), JSONUtil.toJsonStr(qry));

        try {

            boolean isLogin = false;
            SysUser user = null;
            try {
                user = SecurityUtils.getLoginUser().getUser();
                isLogin = true;
            } catch (Exception e) {
                log.info("小程序下单当前用户未登录", e);
            }

            if (!isLogin) {
                String openId = qry.getOpenId();
                user =  userService.selectUserByOpenId(openId);
                if(user == null){
                    user = new SysUser(openId);
                    userService.insertUser(user);
                }
            }


            AtomicReference<SysTouristLabel> view = new AtomicReference<>(new SysTouristLabel());

            // 讲解点允许重复讲缓存key前缀
            String explainedIdCacheKeyPrefix = String.format("labels-repetition-true-id-%s-%s-", user.getUserId(), qry.getScenicSpotId());

            // 讲解点不允许重复讲解缓存key
            String scenicSpotRepetitionCacheKey = String.format("labels-repetition-false-%s-%s", user.getUserId(), qry.getScenicSpotId());
            List<Long> explainedIds = redisCache.getCacheObject(scenicSpotRepetitionCacheKey);
            if (CollectionUtils.isNotEmpty(explainedIds)) {
                LabelsQuery labelsQuery = LabelsQuery.builder().ids(explainedIds).build();
                List<SysTouristLabel> list = sysTouristLabelService.listQuery(labelsQuery);
                list.forEach(label -> {
                    Boolean repetition = label.getRepetition();
                    if (repetition) {
                        explainedIds.remove(label.getId());
                    }
                });
                redisCache.setCacheObject(scenicSpotRepetitionCacheKey, explainedIds, 12, TimeUnit.HOURS);
            }

            List<SysTouristLabel> list = null;
            String beaconCode = qry.getBeaconCode();
            if (StringUtils.isNotBlank(beaconCode)) {
                LabelsQuery labelsQuery = LabelsQuery.builder()
                        .touristId(Long.valueOf(qry.getScenicSpotId()))
                        .beaconCode(beaconCode)
                        .notInIds(explainedIds)
                        .build();
                list = sysTouristLabelService.listQuery(labelsQuery);
            } else {
                qry.setExplainedIds(explainedIds);
                list = sysTouristLabelService.queryNearestRepeatLabel(qry);
            }

            List<SysTouristLabel> nearestRepeatLabels = list
                    .stream()
                    .filter(item -> {
                        // 过滤出允许重复且不在复讲时间间隔内的讲解点
                        Long id = item.getId();
                        String explainedIdCacheKey = explainedIdCacheKeyPrefix + id;
                        String explainedId = redisCache.getCacheObject(explainedIdCacheKey);
                        return (Objects.isNull(explainedId) || explainedId.isBlank());
                    })
                    .toList();

            if (CollectionUtils.isNotEmpty(nearestRepeatLabels)) {
                SysUser finalUser = user;
                nearestRepeatLabels.stream().findFirst().ifPresent(label -> {

                    Boolean explainStatus = label.getExplainStatus();
                    Boolean repetition = label.getRepetition();
                    Long id = label.getId();

                    if (explainStatus && repetition) {
                        redisCache.setCacheObject(explainedIdCacheKeyPrefix + id, id.toString(), label.getIntervalTime(), TimeUnit.SECONDS);
                    } else {
                        List<Long> explainedIds_ = redisCache.getCacheObject(scenicSpotRepetitionCacheKey);
                        explainedIds_ = CollectionUtils.isEmpty(explainedIds_) ? new ArrayList<>() : explainedIds_;
                        if (!explainedIds_.contains(id)) {
                            explainedIds_.add(id);
                        }
                        redisCache.setCacheObject(scenicSpotRepetitionCacheKey, explainedIds_, 12, TimeUnit.HOURS);
                    }

                    sysScenicStService.addScenicStByType(finalUser.getUserId(), String.valueOf(label.getTouristId()), label.getTouristName(), 0);

                    view.set(label);

                });

            }

            SysTouristLabel sysTouristLabel = view.get();
            if (Objects.isNull(sysTouristLabel.getLabelName())) {
                log.info("景区[{}]内暂无符合随身听条件的讲景点", scenicSpot.getName());
            } else {
                log.info("景区[{}]内符合随身听条件的讲景点：{}", scenicSpot.getName(), sysTouristLabel.getLabelName());
            }

            return R.ok(view.get());

        } catch (Exception e) {
            log.info("查询景区[{}]内的随身听讲景点异常：", scenicSpot.getName(), e);
            return R.fail("查询景区[{}]内的随身听讲景点异常：" + scenicSpot.getName());
        }

    }

    /**
     * 查询系统配置
     *
     * @return
     */
    @Anonymous
    @GetMapping("/baseConfig/queryOne")
    public R<BaseConfig> baseConfigQueryOne() {
        try {
            BaseConfig baseConfig = baseConfigService.queryOne();
            return R.ok(baseConfig);
        } catch (Exception e) {
            log.error("查询奖励消耗配置失败", e);
            return R.fail("查询配置失败。");
        }
    }

    /**
     * 用户景区消耗游豆
     *
     * @return
     */
    @PostMapping("/scenicSpot/payBean")
    public R scenicSpotPayBean(@RequestBody ScenicSpotPayCmd scenicSpot) {
        Map<String, Object> retMap = new HashMap<>();
        try {
            SysUser user = SecurityUtils.getLoginUser().getUser();
            user = userService.selectUserById(user.getUserId());
            String redisKey = String.format("user:%s:scenic:%s", user.getUserId(), scenicSpot.getScenicId());
            if (redisCache.getCacheObject(redisKey) != null) {
                return R.ok();
            }

            BigDecimal needToken = scenicSpot.getNeedToken();
            if( needToken.compareTo(BigDecimal.ZERO) > 0 ){
                //校验游豆余额
                if (user.getTokenBalance().compareTo(needToken) < 0) {
                    retMap.put("verify", true);
                    retMap.put("message", "当前景点需消耗["+needToken.intValue()
                            +"]游豆，您的游豆余额仅剩["+user.getTokenBalance().intValue()
                            +"]个，可通过推荐好友获取更多游豆。");
                    return R.fail(retMap);
                }

                //扣除游豆.
                AddTokenDetailCmd cmd = new AddTokenDetailCmd();
                cmd.setUserId(user.getUserId());
                cmd.setUserPhone(user.getPhonenumber());
                cmd.setAmountIncurred(needToken);
                cmd.setChangeType(ChangeType.OUT);
                cmd.setTranscationType(TransactionType.USE);
                cmd.setScenicId(scenicSpot.getScenicId());
                R<Void> r = userService.updateTokenBalance(cmd);
                if (!R.isSuccess(r)) {
                    log.info("扣除游豆余额失败！");
                    retMap.put("verify", false);
                    retMap.put("message", "系统检测到游豆扣除失败，建议稍后重试。");
                    return R.fail(retMap);
                }
            }

            // 获取当前时间（带时区，防止服务器时区影响）
            ZonedDateTime now = ZonedDateTime.now(ZoneId.systemDefault());
            // 计算当天的 23:59:59
            ZonedDateTime endOfDay = now.toLocalDate().atTime(23, 59, 59).atZone(now.getZone());
            // 计算剩余秒数
            Long secondsEndOfDay = Duration.between(now, endOfDay).getSeconds();
            redisCache.setCacheObject(redisKey, now.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME), secondsEndOfDay.intValue(), TimeUnit.SECONDS);
            return R.ok();
        } catch (Exception e) {
            log.error("景区支付游豆异常", e);
            retMap.put("verify", false);
            retMap.put("message", "系统检测到游豆扣除失败，建议稍后重试。");
            return R.fail(retMap);
        }
    }

    /**
     * 校验是否需消耗游豆
     *
     * @return
     */
    @PostMapping("/scenicSpot/verifyPayBean")
    @Anonymous
    public R scenicSpotVerifyPayBean(@RequestBody ScenicSpotPayCmd scenicSpotPay) {
        Map<String, Object> retMap = new HashMap<>();
        BaseConfig baseConfig = baseConfigService.queryOne();
        retMap.put("lecturesNum", baseConfig.getLecturesNum());

        try {
            SysUser sysUser = userService.selectUserByOpenId(scenicSpotPay.getOpenId());
            //查询是否是景区采集员，如果是，直接放行
            ScenicAdmin sa = scenicAdminService.queryByScenicId(sysUser.getUserName(), scenicSpotPay.getScenicId());
            if(sa != null){
                retMap.put("payVerify", true);
                retMap.put("verify", true);
                return R.ok(retMap);
            }

            //查询免费次数
            Map<String, Integer> freeMap = getFreeTime(sysUser.getUserId(), scenicSpotPay.getScenicId(), baseConfig);
            retMap.put("lecturesNum", freeMap.get("lecturesNum"));
            retMap.put("cooperLecturesNum", freeMap.get("cooperLecturesNum"));
            String eyeKey = String.format("eye:user:%s:scenic:%s", sysUser.getUserId(), scenicSpotPay.getScenicId());
            if( redisCache.getCacheObject(eyeKey) != null ){
                retMap.put("payVerify", true);
                return R.ok(retMap);
            }
            retMap.put("payVerify", false);
            String redisKey = String.format("user:%s:scenic:%s", sysUser.getUserId(), scenicSpotPay.getScenicId());
            if( redisCache.getCacheObject(redisKey) != null ){
                retMap.put("verify", true);
                return R.ok(retMap);
            }

            ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, scenicSpotPay.getScenicId()).one();

            int num;
            boolean b = false;
            BigDecimal needToken = scenicSpot.getNeedToken();
            if( needToken != null ){
                num = needToken.intValue();
                if( needToken.compareTo(BigDecimal.ZERO) == 0 ){
                    b = true;
                }
            }else{
                switch (scenicSpot.getLevel()) {
                    case 5 -> num = baseConfig.getFiveExpend();
                    case 4 -> num = baseConfig.getFourExpend();
                    default -> num = baseConfig.getThreeExpend();
                };
            }

            retMap.put("num", num);
            retMap.put("verify", b);
            return R.ok(retMap);
        } catch (Exception e) {
            log.error("校验是否需消耗游豆异常", e);
            retMap.put("payVerify", false);
            retMap.put("verify", false);
            return R.fail(retMap);
        }
    }

    /**
     * 免费次数查询
     */
    public Map<String, Integer> getFreeTime(Long userId, Integer scenicId, BaseConfig baseConfig){
        Map<String, Integer> map = new HashMap<>();
        String key = String.format("freetimes:%s:%s:%s", FreeTypeEnum.YD.getCode(), userId, scenicId);
        Integer freeTimes =  redisCache.getCacheObject(key);
        map.put("lecturesNum", freeTimes != null ? freeTimes :
                baseConfig.getLecturesNum() != null ? baseConfig.getLecturesNum() : 5);
        key = String.format("freetimes:%s:%s:%s", FreeTypeEnum.HZ.getCode(), userId, scenicId);
        freeTimes =  redisCache.getCacheObject(key);
        map.put("cooperLecturesNum", freeTimes != null ? freeTimes :
                baseConfig.getCooperLecturesNum() != null ? baseConfig.getCooperLecturesNum() : 3);
        return map;
    }

    /**
     * 保存导览图
     */
    @PostMapping(value = "/scenicGuide/add")
    public R addScenicGuide(@RequestBody ScenicGuide scenicGuide) {
        try {
            SysUser user = SecurityUtils.getLoginUser().getUser();
            ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, scenicGuide.getScenicId()).one();
            scenicGuide.setScenicName(scenicSpot.getName());
            scenicGuide.setScenicAddress(scenicSpot.getAddress());
            scenicGuide.setUserId(user.getUserId().intValue());
            scenicGuide.setUserPhone(user.getPhonenumber());
            scenicGuide.setGuideCode(idHutool.genCode("SG"));
            scenicGuide.setCreateTime(new Date());
            scenicGuide.setUpdateTime(new Date());
            scenicGuideService.save(scenicGuide);
            return R.ok();
        }catch (Exception e){
            log.error("保存导览图异常", e);
            return R.fail("保存导览图失败。");
        }
    }

    /**
     * 建言提交
     */
    @PostMapping(value = "/userSuggestion/add")
    public R addUserSuggestion(@RequestBody UserSuggestion userSuggestion) {
        try {
            SysUser user = SecurityUtils.getLoginUser().getUser();
            userSuggestion.setUserId(user.getUserId());
            userSuggestion.setUserPhone(user.getPhonenumber());
            userSuggestionService.save(userSuggestion);
            return R.ok();
        }catch (Exception e){
            log.error("建言提交异常", e);
            return R.fail("建言提交失败。");
        }
    }

    /**
     * 查询导游相关费用
     *
     * @return
     */
    @Anonymous
    @GetMapping("/scenicSpot/queryPrice")
    public R<Map> queryPrice(Integer id) {
        try {
            Map<String, Object> map = new HashMap<>();
            ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, id).one();
            scenicSpot.handleServiceActivity();
            map.put("scenicSpot", scenicSpot);
            ScenicHeadphone qo = new ScenicHeadphone();
            qo.setScenicId(id);
            List<ScenicHeadphone> list = scenicHeadphoneService.queryList(qo);
            map.put("headphoneList", list);
            ScenicLocation scenicLocation = new ScenicLocation();
            scenicLocation.setLocationName("设备");
            scenicLocation.setScenicId(id);
            ScenicLocation queryLocation = scenicLocationService.queryByLocationName(scenicLocation);
            map.put("location", queryLocation);
            return R.ok(map);
        } catch (Exception e) {
            log.error("查询导游相关费用失败", e);
            return R.fail("查询服务费失败。");
        }
    }

    /**
     * 根据 code 获取 openId
     * @return
     */
    @Anonymous
    @PostMapping("/code2Session")
    public R<Object> code2Session(@RequestBody WxMiniAppCodeCmd cmd){
        try {
            WxMaJscode2SessionResult result = wxMaService.jsCode2SessionInfo(cmd.getCode());
            String openId = result.getOpenid();
            return R.ok(openId);
        } catch (WxErrorException e) {
            log.error("code2Session异常", e);
            return R.fail("code2Session失败");
        }
    }

    /**
     * POI数据采集
     * @param cmd
     * @return
     */
    @PostMapping("/poiMark")
    public R<Void> poiMark(@RequestBody PoiMarkCmd cmd){
        try {
            log.info("POI数据采集请求: {}", JSONUtil.toJsonStr(cmd));
            SysTouristLabel condition = new SysTouristLabel();
            condition.setTouristId(cmd.getTouristId());
            condition.setLabelName(cmd.getLabelName());
            List<SysTouristLabel> sysTouristLabels = sysTouristLabelService.selectSyncTouris(condition);
            if (CollectionUtils.isEmpty(sysTouristLabels)) {

                ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, cmd.getTouristId()).one();
                SysTouristLabel touristLabel = new SysTouristLabel(cmd, scenicSpot);
                sysTouristLabelService.insertSysTouristLabel(touristLabel);

            }

            if (CollectionUtils.isNotEmpty(sysTouristLabels)) {
                sysTouristLabels.forEach(item -> {
                    item.setLongitude(cmd.getLongitude());
                    item.setLatitude(cmd.getLatitude());
                    item.setPolygon(cmd.getPolygon());
                    sysTouristLabelService.updateSysTouristLabel(item);
                });
            }

            return R.ok();
        } catch (Exception e) {
            log.error("POI数据采集异常", e);
            return R.fail("POI数据采集失败");
        }
    }

    /**
     * 小程序下单
     * @param cmd
     * @return
     */
    @Anonymous
    @PostMapping("/placeOrder")
    public R<WxPayUnifiedOrderV3Result.JsapiResult> placeOrder(@RequestBody PlaceOrderCmd cmd) {

        try {
            boolean isLogin = false;
            SysUser user = null;
            try {
                user = SecurityUtils.getLoginUser().getUser();
                isLogin = true;
            } catch (Exception e) {
                log.info("小程序下单当前用户未登录", e);
            }

            if (!isLogin) {
                String openId = cmd.getOpenId();
                user =  userService.selectUserByOpenId(openId);
                if(user == null){
                    user = new SysUser(openId);
                    userService.insertUser(user);
                }
            }

            cmd.setOpenId(user.getOpenId());
            cmd.setUserId(user.getUserId());

            GuidePayOrder guidePayOrder = guidePayOrderService.lambdaQuery().eq(GuidePayOrder::getOrderId, cmd.getOrderId()).one();
            cmd.setAmount(guidePayOrder.getOrderAmount());

            return paymentOrderService.placeOrder(cmd);

        } catch (Exception e) {
            log.error("小程序下单异常", e);
            return R.fail("小程序下单异常");
        }

    }

    /**
     * 合作景区， 免费次数计算， 每调用一次， 次数减一， 如果次数为0 ，则需要用户支付
     * 次数有效时间到 晚上12 点，
     */
    @Anonymous
    @PostMapping("/cooperationFreeTimes")
    public R<Integer> cooperationFreeTimes(@RequestBody FreeTimesCmd cmd) {
        //判断用户
        SysUser user = userService.selectUserByOpenId(cmd.getOpenId());
        if(user == null){
            user = new SysUser(cmd.getOpenId());
            userService.insertUser(user);
        }
        //key = 免费类型：用户id: 景区id
        String key = String.format("freetimes:%s:%s:%s", cmd.getFreeType(), user.getUserId(), cmd.getScenicId());
        Integer freeTimes =  redisCache.getCacheObject(key);
        if(freeTimes != null){
            if(freeTimes <= 0){
                return R.ok(0);
            }
            //次数减1
            log.info("用户免费次数扣减==免费类型：{}, 用户ID：{}, 景区ID：{}", cmd.getFreeType(), user.getUserId(), cmd.getScenicId());
            redisCache.decr(key);
        } else {
            //查询景区 免费次数
            BaseConfig baseConfig = baseConfigService.queryOne();
            if(baseConfig != null){
                if(FreeTypeEnum.YD.getCode().equals(cmd.getFreeType())){
                    freeTimes = baseConfig.getLecturesNum() == null ? 5 : baseConfig.getLecturesNum();
                } else if(FreeTypeEnum.HZ.getCode().equals(cmd.getFreeType())){
                    freeTimes = baseConfig.getCooperLecturesNum() == null ? 3 : baseConfig.getCooperLecturesNum();
                }
            }
            // 获取当前时间（带时区，防止服务器时区影响）
            ZonedDateTime now = ZonedDateTime.now(ZoneId.systemDefault());
            // 计算当天的 23:59:59
            ZonedDateTime endOfDay = now.toLocalDate().atTime(23, 59, 59).atZone(now.getZone());
            // 计算剩余秒数
            long secondsEndOfDay = Duration.between(now, endOfDay).getSeconds();
            //设置次数
            redisCache.setCacheObject(key, freeTimes, (int) secondsEndOfDay, TimeUnit.SECONDS);
            //次数减1
            log.info("用户免费次数扣减==免费类型：{}, 用户ID：{}, 景区ID：{}", cmd.getFreeType(), user.getUserId(), cmd.getScenicId());
            redisCache.decr(key);
        }
        Integer retTime =  redisCache.getCacheObject(key);
        return R.ok(retTime);
    }

    /**
     * 讲解包
     * @param touristId
     * @return
     */
    @Anonymous
    @GetMapping("/touristPackage")
    public R<TouristPackageVO> touristPackage(@RequestParam Integer touristId){
        ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, touristId).one();
        TouristPackageVO touristPackageVO = new TouristPackageVO(scenicSpot);
        return R.ok(touristPackageVO);
    }

    /**
     * 游玩路线
     * @param touristId
     * @return
     */
    @Anonymous
    @GetMapping("/touristRoutes")
    public R<Object> touristRoutes(@RequestParam Integer touristId){
        ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery().eq(ScenicSpot::getId, touristId).one();
        List<TouristRoute> touristRoutes = scenicSpot.getTouristRoutes();
        return R.ok(touristRoutes);
    }

    /**
     * 查看景区
     *
     * @param id
     * @return
     */
    @GetMapping("/look")
    @Anonymous
    public R<ScenicSpot> look(Integer id) {

        try {
            ScenicSpotQuery query = ScenicSpotQuery.builder().id(id).build();
            ScenicSpot scenicSpot = scenicSpotCustomizeService.getSimpleOne(query);

            Long count = scenicRatioService.lambdaQuery().eq(ScenicRatio::getScenicId, scenicSpot.getId()).count();
            scenicSpot.setCooperate(count > 0);

            LabelsQuery labelsQuery = LabelsQuery.builder().touristId(Long.valueOf(scenicSpot.getId())).build();
            List<SysTouristLabel> sysTouristLabels = sysTouristLabelService.listQuery(labelsQuery);

            List<SysTouristLabel> distinctSysTouristLabels = sysTouristLabels.stream()
                    .filter(sysTouristLabel -> Objects.nonNull(sysTouristLabel.getLongitude())) // 过滤掉经度为空的数据
                    .filter(SysTouristLabel::getShowStatus) // 过滤掉显示状态为否的数据
                    .collect(Collectors.toMap(
                            SysTouristLabel::getLongitude,                      // 使用经度作为键
                            Function.identity(),                                // 使用原始对象作为值
                            (oldValue, newValue) -> oldValue,                   // 如果键重复，保留旧值
                            LinkedHashMap::new                                 // 保持插入顺序
                    )).values().stream().toList();

            scenicSpot.setSysTouristLabels(distinctSysTouristLabels);

            //查询景区数据采集信息
            scenicSpot.setScenicInfoFlag(false);
            try {
                SysUser user = SecurityUtils.getLoginUser().getUser();
                if (user != null) {
                    List<ScenicAdmin> adminList = scenicAdminService.lambdaQuery()
                            .eq(ScenicAdmin::getBusinessCode, scenicSpot.getScenicSpotId())
                            .eq(ScenicAdmin::getUserPhone, user.getUserName())
                            .eq(ScenicAdmin::getBusinessType,  PartnerBusinessType.SCENIC_INFO.getCode())
                            .list();
                    if (CollectionUtils.isNotEmpty(adminList)) {
                        scenicSpot.setScenicInfoFlag(true);
                    }
                }
            } catch (Exception e) {
                log.error("用户未登录！");
            }
            return R.ok(scenicSpot);
        } catch (Exception e) {
            log.error("查看景区失败", e);
            return R.fail("查看景区失败");
        }
    }


}
