package com.iciyun.aiguide.controller.api;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpUtil;
import com.google.common.collect.Maps;
import com.iciyun.amap.AMapUtils;
import com.iciyun.common.annotation.Anonymous;
import com.iciyun.common.constant.CacheConstants;
import com.iciyun.common.core.controller.BaseController;
import com.iciyun.common.core.domain.AjaxResult;
import com.iciyun.common.core.domain.R;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.common.utils.HelpMe;
import com.iciyun.system.alitts.AliTokenManager;
import com.iciyun.system.domain.PgInfoEntity;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.SysTouristLabel;
import com.iciyun.system.domain.bo.ScenicSpotBo;
import com.iciyun.system.service.*;
import com.iciyun.system.service.impl.AiMatchImgService;
import com.iciyun.system.service.impl.ChatService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 测试接口
 */
@Slf4j
@Anonymous
@RestController
@RequestMapping("/guide/user")
public class DemoCtrl extends BaseController {

    @Autowired
    private IPgInfoService pgInfoService;

    @Autowired
    DirectSqlQueryService directSqlQueryService;

    @Autowired
    ISysTouristLabelService sysTouristLabelService;

    @Autowired
    AliTokenManager aliTokenManager;

    @Autowired
    RedisCache redisCache;

    /**
     * 清空 redis 缓存
     * @param key
     * @return
     */
    @GetMapping("/clearRedisByKey")
    public AjaxResult clearRedisByKey(String key) {

        boolean flag = redisCache.deleteObject(key);

        return AjaxResult.success(flag);
    }



    /**
     * 清空 无法播放的音频
     * @param key
     * @return
     */
    @GetMapping("/clearValidMp3")
    public AjaxResult clearValidMp3() {

        ThreadUtil.execute(() -> {
            Collection<String> keys = redisCache.keys(CacheConstants.scenicPixKey + "*");
            for (String key : keys) {
                int length = key.split(":").length;
                if (key.contains("_") && length==4) {
                    Object object = redisCache.getCacheObject(key);
                    if (object instanceof String) {
                        String str = (String)object;
                        String[] strArr = str.split(",");

                        for (String aacUrl:strArr){
                            checkAudio(key,aacUrl);
                        }
                    }else {
                        redisCache.deleteObject(key);
                    }
                }else {
                    redisCache.deleteObject(key);
                }
            }
        });

        return AjaxResult.success();
    }


    private void checkAudio(String key,String aacUrl){

        String fileName = IdUtil.simpleUUID()+".aac";

        File file = new File(fileName);

        long size = 0;
        try {
            size = HttpUtil.downloadFile(aacUrl,file);
        } catch (Exception e) {
//            log.error("下载文件失败", e);
            if (redisCache.hasKey(key)){
                redisCache.deleteObject(key);
                log.info("删除 redis 中无法播放的音频 key:{}，aacUrl:{}",key,aacUrl);
                return;
            }
        }

        if (size > 0){
            FileUtil.del(file);
        }
    }


    public static void main(String[] args) {

        String str = "scenic:146_52c32184b4137f621a5d2c81846db684:7426725529589661723:63cc504fce03f9f5d363275f31d41de3";
        String[] strArr = str.split(":");

        System.out.println(strArr.length);

    }


    /**
     * 获取 redis 缓存
     * @param key
     * @return
     */
    @GetMapping("/getRedisValByKey")
    public AjaxResult getRedisValByKey(String key) {

        Object object = redisCache.getCacheObject(key);

        return AjaxResult.success(object);
    }



    @GetMapping("/getPgOne")
    public AjaxResult getPgOne() {
        List<PgInfoEntity> pgList = pgInfoService.getPgOne();
        return AjaxResult.success(pgList);
    }





    private static String cozeTokenKey = "chat:cozeToken";


    /**
     * 查询景区坐标点
     *
     * @return
     */
    @GetMapping("/geoQuery")
    public AjaxResult geoQuery() {

        ThreadUtil.execute(() -> {
            dealGeoQuery();
        });

        return AjaxResult.success();
    }


    private void dealGeoQuery() {
        String sql = "select id,name,detail_address from scenic_spot where latitude is null";
        List<ScenicSpot> list = directSqlQueryService.selectList(sql, ScenicSpot.class);
        for (ScenicSpot item : list) {
            List<String> tempList = AMapUtils.addressToGPS(item.getDetailAddress());
            if (CollUtil.isNotEmpty(tempList)) {
                String location = tempList.get(0);
                String temp[] = location.split(",");
                String latitude = temp[0];
                String longitude = temp[1];

                sql = "update scenic_spot set latitude = '" + latitude + "', longitude = '" + longitude + "' where id = " + item.getId();
                directSqlQueryService.exec(sql);

                log.info("当前景区：{}，执行的sql：{}", item.getName(), sql);

                ThreadUtil.safeSleep(1000);
            }
        }
    }


    @Autowired
    private IScenicSpotService scenicSpotService;

    /**
     * PG 向量查询
     *
     * @return
     */
    @GetMapping("/getPgVector")
    public AjaxResult getPgVector() {
        List<String> listf = new ArrayList<>();
        String a = "[0.010433565,0.034061383,0.01780621,-0.046170823,-0.054516647,-0.015099543,0.01595322,0.021234846,-9.416388e-05,-0.019213272,0.03528486,-0.10014519,0.0072252406,0.056981195,0.057860874,0.011354509,0.0003692538,0.006984087,-0.041598618,0.08132726,-0.0032530266,-0.011356956,0.013264088,0.04290018,0.034868807,-0.006677836,0.03709932,-0.0013226997,-0.050688624,-0.058148324,0.05615171,-0.015701266,-0.039474636,-0.04157513,-0.014527433,-0.020563344,-0.094900176,-0.015762128,0.0070609553,-0.026379053,-0.040407665,0.02830869,-0.042516053,-0.009851063,-0.027655065,-0.045763053,-0.02047716,0.07831747,-0.045031425,0.0092742285,0.027339455,-0.028897185,-0.028331526,-0.014172437,0.051196788,0.022926515,0.05133401,0.011296147,-0.026138498,0.07933603,0.07512294,0.029028041,0.010646618,-0.0035574364,0.08896277,0.011489194,0.0033087211,-0.020056494,-0.05299963,-0.04195466,0.06809021,-0.019656265,-0.0019021236,-0.024386307,-0.0023040217,-0.0446129,0.023017168,-0.0009137411,0.0015106412,0.008191451,-0.018528398,-0.015028796,-0.004189644,-0.034280006,0.016514247,0.025912495,0.07137848,-0.0046358113,-0.001293821,0.022831047,0.01603124,-0.02582553,-0.0233873,0.019295229,-0.0023755655,-0.044931363,0.027940165,0.011496524,-0.06310592,0.0130451415,-0.069694005,-0.020774283,-0.08543428,0.0032083432,0.014888739,-0.02527166,0.017649993,-0.040366698,-0.027485564,0.05126258,-0.023412457,-0.041830327,0.005084247,0.030856248,0.032944165,-0.03791685,-0.043549266,-0.015496109,-0.040912963,-0.0023746116,-0.026186105,0.012288522,-0.01448404,-0.018670458,0.0041236924,0.005069845,0.038055036,-0.034741763,-0.042892694,0.042289864,0.05103404,-0.06972912,0.03604907,0.025974616,-0.0021445507,0.013345063,-0.044267356,0.0010170239,0.029663635,0.02769223,-0.022473289,0.04168802,0.010708602,-0.053470045,0.018420262,-0.083847724,-0.019432915,0.046631332,0.007403493,0.014423129,-0.009939222,-0.07126123,0.022736229,-0.031712368,-0.013907501,0.042277347,-0.051502243,-0.025729826,-0.017082991,-0.0046690097,-0.01111596,-0.011940951,0.064712025,-0.018643508,0.015280595,0.001267469,0.036497,0.005773817,0.03909162,-0.042030126,0.037943896,0.038560625,-0.027262537,-0.00043025753,-0.02353706,0.017656393,0.060168136,-0.053324796,-0.07077335,0.019931052,-0.083859794,-0.076558955,0.07702783,-0.037471436,0.0060960506,0.05099213,0.0067304657,0.02485147,0.007381778,0.019020941,-0.04299936,-0.029319586,0.024985876,-0.042219546,0.008981969,0.03718044,0.07263716,-0.040329132,-0.03565967,0.0051823226,0.056402083,0.036017958,-0.082831174,-0.01043123,-0.004249604,0.0709217,0.046354122,-0.024740906,-0.00016024148,0.049724333,0.0042537684,0.039586373,0.028436977,-0.020841738,-0.035828337,0.00084775436,-0.0024347461,0.037345793,-0.005162515,-0.021130687,-0.00081378414,0.037359755,-0.021193624,0.025554754,0.004429915,-0.005137203,0.015782235,-0.04446971,-0.008797745,0.009655298,0.075408325,-0.00937148,-0.017889224,-0.03864448,-0.002814951,-0.017768906,-0.05392796,-0.019916028,-0.011771926,-0.002094714,-0.009620137,0.014245949,-0.015341615,-0.045953486,0.01871432,0.0077224164,-0.038105916,0.06917858,0.06790416,0.04814628,0.029210756,0.057894167,-0.05217514,-0.021591485,0.06420743,-0.011069801,0.0033742287,0.044183142,-0.015691193,0.0542336,0.006039727,-0.0043868334,-0.014451728,-0.018928919,-0.028732495,-0.01055208,-0.010802163,-0.002779372,0.0035412053,0.016700994,-0.003312281,-0.032110322,-0.00974166,0.054261055,0.044044554,-0.008677748,-0.06525595,-0.030366149,0.04392407,-0.02289124,0.038245644,0.06627876,0.009559866,-0.005405569,-0.00375389,0.067179084,-0.044430777,0.021694638,-0.015649384,0.060976878,0.019028272,0.02453354,-0.043322027,0.01847651,-0.112294815,-0.00768538,0.007817271,0.0065956344,-0.007534242,0.028252445,-0.0014654661,0.031185262,0.06921444,0.048451047,-0.057042774,-0.030749599,0.020815102,0.002957465,0.018234182,0.0054240073,-0.038295247,0.013586294,-0.05113109,-0.03164452,0.03621601,0.030225364,0.01845587,0.10324568,-0.01176,-0.03402889,-0.0036809258,-0.013778662,-0.030871915,0.00717903,0.03156386,0.0028818708,-0.010918947,-0.028341254,-0.044478316,0.013585773,0.03691295,0.02419077,0.0033968783,0.020208606,-0.023437649,0.010966821,0.0267908,0.013787896,0.008177853,0.0018399516,-0.058881555,0.014492143,0.070752524,0.009408856,-0.032774054,0.030110458,0.019223932,0.050690178,0.014549551,0.01330151,-0.043604694,0.024252333,-0.0065936875,-0.017642718,-0.0436662,-0.0064055,0.050121617,0.030089302,-0.0019415935,-0.050570894,0.037240993,-0.025002355,0.05534357,0.034969714,0.015507316,-0.036441218,0.013754673,-0.010943594,0.047005143,-0.048343025,-0.019085016,0.060943402,-0.026202532,0.015339855,-0.026160967,-0.02551048,-0.024278916,0.040672798,-0.013728509,-0.039116036,-0.04067609,-0.0052596447,0.029301329,0.025932377,-0.0762854,-0.010317986,0.046422604,0.010426644,-0.079112515,-0.014456448,0.023477685,-0.0040294644,0.011868411,0.07336632,-0.027147235,-0.053377707,-0.045506284,-0.047413968,-0.057193372,0.01892131,-0.036915325,0.07072671,-0.10145744,0.050628487,0.02939547,-0.024969187,-0.052680086,-0.021814125,0.027165007,-0.016204763,-0.0003290825,-0.052294504,0.012155082,-0.032751948,-0.02034309,-0.02536233,-0.020832822,0.064340904,0.022892656,0.014853605,0.021631556,0.051112805,-0.009162379,0.08883402,0.024158726,-0.037100226,-0.039566644,0.04174969,-0.03323101,-0.002930384,-0.0047892625,0.010515965,0.022906845,-0.021452067,0.011962499,-0.017543223,0.028893797,-0.014051306,-0.007228085,0.037233595,0.030974474,0.082169935,0.055563945,-0.06692955,0.06297007,0.02817749,0.0051847924,0.044391803,-0.013713548,0.0069818823,-0.015374522,0.031247973,0.053370163,0.050321262,-0.0026131617,-0.016857577,-0.021294272,-0.028169272,-0.017163433,-0.024977706,0.017259631,0.019854486,0.014686013,0.00788664,-0.017487649,-0.022473468,0.038648654,0.007794441,-0.03988602,-0.013319367,0.06546939,-0.032863375,-0.030923322,-0.0016076548,0.04713165,0.0074921506,-0.04115391,-0.03944479,0.055327065,0.022650989,-0.043168448,-0.0708278,0.0099725695,0.02190859,0.03380482,0.020115182,-0.0062335953,-0.0397933,-0.022468202,0.045883525,0.070882946,-0.07541256,-0.006459012,0.0372903,-0.0005975307,-0.0014409565,-0.018756764,-0.032515902,0.0023364061,-0.017197624,0.037395656,0.019334279,0.017675728,-0.036633644,-0.026271846,0.075362705,0.055896662,0.08846356,-0.043058213,-0.05017738,0.024409296,-0.023158256,-0.024787758,-0.011658898,-0.044310723,0.037705448,0.014653421,0.05352162,-0.05318901,-0.011018432,0.021319833,-0.016534789,0.0171135,0.062809974,0.02548146,-0.008926871,0.021566644,-0.017608365,-0.06913557,-0.030748248,-0.030561164,0.039710265,0.00075427035,0.028014854,-0.009257735,0.05208174,-0.016221082,-0.04556014,-0.051810447,-0.040644486,-0.01997884,0.078180335,0.0052475603,0.05144007,0.021865953,0.03383281,0.0034037144,0.0004201776,-0.04162062,-0.009391234,0.020129211,-0.060027212,-0.008587219,0.044668294,-0.010089248,-0.036744937,0.034256473,0.012129928,-0.009685314,0.034774244,0.014615429,-0.00323286,0.0018003056,-0.08332136,-0.031719033,0.0020912432,0.026480284,-0.04022785,0.017558636,-0.005056476,0.028700257,0.049871404,0.05601608,-0.012925339,-0.0029341257,0.000120623175,-0.032783832,0.047591742,0.034050588,0.008469007,0.06865104,-0.02922865,-0.026306285,-0.03658487,0.0024444624,0.013025772,-0.010726418,-0.0481187,0.034298494,0.060736366,0.022037087,0.04868766,-0.0024749124,-0.01049214,0.011337068,-0.015375881,-0.013100974,0.008683494,0.0036522981,-0.035595376,-0.073987044,-0.013542718,-0.0147521775,0.077390686,0.022184918,-0.03812318,-0.06206431,0.00797667,-0.06645994,-0.025591195,-0.00075455615,-0.046794385,-0.026966238,0.010622975,0.025561677,-0.007078173,-0.031767033,-0.019190582,0.009694512,0.03241602,0.041251235,-0.0067750667,-0.01065118,-0.04013191,-0.06574305,-0.042969845,0.035721865,0.05708836,-0.025718452,0.0037908012,-0.06409286,0.011257831,-0.0019796873,-0.07702867,0.02883729,0.01596626,-0.04282025,-0.029562498,-0.031405002,-0.0031558136,0.000799249,-0.07599976,0.05750882,-0.020838276,0.0037304556,0.041536514,0.021919286,-0.016122123,-0.05880201,-0.0007517948,-0.0134985065,-0.035921726,-0.010123,-0.024596956,-0.01271387,0.02536943,-0.023995075,0.0044355732,0.0043135067,0.021514224,0.0065179197,0.030463673,0.035812777,-0.02410015,-0.0022831461,0.07442781,-0.02299152,-0.010107775,-0.013512093,-0.042226084,0.069142595,0.020398736,0.02067128,-0.04345044,-0.046537135,-0.009309428,0.022501327,0.03021397,-0.0064722416,0.003003927,0.008271389,0.026412018,-0.045329947,0.008813812,-0.018831396,0.020463286,0.0069633946,0.052475728,-0.03003185,-0.0531821,0.00018227544,0.032621365,-0.031038042,0.0004874633,-0.0061810543,0.07588368,-0.0013804315,-0.042963665,0.0016646951,0.017435709,-0.007855231,0.06419883,0.029219266,0.034674015,0.008927576,0.037103247,0.09109629,0.053890776,-0.01813243,-0.016208794,0.0058937944,0.052836627,-0.019686637,0.045993637,0.01759208,-0.057420522,-0.018339466,0.049773082,0.029272798,-0.024488682,-0.03175733,0.0500027,-0.040385444,0.008341005,0.0010808471,0.026993327,0.0055161268,-0.00018506123,0.056322947,-0.056654375,-0.012463065,0.010685301,-0.031552408,0.00467922,0.0017128921,0.041039813,0.089940056,0.0012605346,-0.05398297,-0.01964543,0.007486113,-0.041516922,-0.0074874144,0.003055262,-0.005272552,-0.014886978,-0.03594377,-0.008395176,0.038084675,0.017779963,0.036842916,-0.067421064,-0.018395927,-0.005366373,0.03908964,-0.0073278653,0.0038690863,0.03190373,0.009587197,-0.015863696,-0.040928666,-0.015485977,0.026920142,-0.056504,-0.02345501,-0.038164772,-0.036733203,-0.037606433]";
        List<ScenicSpotBo> scenicSpots = scenicSpotService.queryByFeatures(a, 5);
        return AjaxResult.success(scenicSpots);
    }

    @Autowired
    private AiMatchImgService aiMatchImgService;

    @Value("${ai.match_second.img_url}")
    private String aiMatchImgUrl;

    /**
     * AI img 匹配换取 label
     */
    @PostMapping("/getImgLabel")
    public R getImgLabel(@RequestParam("file") MultipartFile file, String scenic_code) throws Exception {

        File tempFile = HelpMe.saveFile(file);

        String r = aiMatchImgService.matchImg(aiMatchImgUrl, tempFile, scenic_code);

        FileUtil.del(tempFile);

        return R.ok(r);
    }

    @Autowired
    private AsyncService asyncService;

    /**
     * 删除用户的所有业务
     *
     * @param userName
     * @return
     */
    @GetMapping("/delBizByUserName")
    public R delBizByUserName(String userName, Integer scenicId) {
        asyncService.delBizByUserName(userName, scenicId);
        return R.ok();
    }

    /**
     * 删除用户的所有业务
     *
     * @param userId
     * @return
     */
    @GetMapping("/delBizByUserId")
    public R delBizByUserId(Long userId, Integer scenicId) {
        asyncService.delBizByUserId(userId, scenicId);
        return R.ok();
    }

    @Autowired
    private IGuideService guideService;

    /**
     * 图片上传
     *
     * @param
     * @return
     */
    @PostMapping("/upFileByHsOss")
    public R upFileByHsOss(@RequestParam("file") MultipartFile file) {
        String fileName = file.getOriginalFilename();
        try {
            String path = guideService.upHsOSSFiles(fileName, file.getBytes());
            return R.ok(path);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
