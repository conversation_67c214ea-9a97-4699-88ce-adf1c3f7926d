package com.iciyun.aiguide.controller.user;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iciyun.common.annotation.Anonymous;
import com.iciyun.common.constant.Constants;
import com.iciyun.common.constant.HttpStatus;
import com.iciyun.common.core.controller.BaseController;
import com.iciyun.common.core.domain.R;
import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.common.core.page.TableDataInfo;
import com.iciyun.common.enums.OrderItemEnum;
import com.iciyun.common.utils.SecurityUtils;
import com.iciyun.common.utils.poi.ExcelUtil;
import com.iciyun.system.domain.*;
import com.iciyun.system.service.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 扫码支付
 *
 * <AUTHOR>
 * @since 2025-05-08 16:38:53
 */
@RestController
@RequestMapping("/guidePayOrder")
public class GuidePayOrderCtrl extends BaseController {

    @Autowired
    private IGuidePayOrderService guidePayOrderService;

    @Autowired
    private IScenicLocationService scenicLocationService;

    @Autowired
    private IGuidePayOrderItemService guidePayOrderItemService;

    @Autowired
    private ICoopAgencyService coopAgencyService;

    @Autowired
    private IScenicRatioService scenicRatioService;

    /**
     * 查询景区二维码信息
     */
    @PostMapping("/getGuideCode")
    @Anonymous
    public R<ScenicLocation> getGuideCode(@RequestBody GuideCodeCmd cmd) {
        ScenicLocation ret = scenicLocationService.lambdaQuery()
                .eq(ScenicLocation::getLocationUrlCode, cmd.getLocationUrlCode())
                .last("limit 1")
                .one();
        return R.ok(ret);
    }

    /**
     * 扫码下单
     *
     * @return
     */
    @PostMapping("/addGuideOrder")
    @Anonymous
    public R<GuidePayOrderRet> guidePay(@RequestBody GuidePayOrderCmd cmd) {
        GuidePayOrderRet ret = guidePayOrderService.guidePay(cmd);
        return R.ok(ret);
    }

    /**
     * 查询订单的耳机
     */
    @PostMapping("/getHeadsetphone")
    @Anonymous
    public R<ScenicHeadphoneVo> getHeadsetphone(@RequestBody GuideHeadset cmd) {
        ScenicHeadphoneVo scenicHeadphone = guidePayOrderService.getHeadsetphone(cmd);
        return R.ok(scenicHeadphone);
    }

    /**
     * 耳机领取
     */
    @PostMapping("/collectHeadsetphone")
    @Anonymous
    public R collectHeadsetphone(@RequestBody GuidePayOrderRet cmd) {
        guidePayOrderService.collectHeadsetphone(cmd.getOrderId());
        return R.ok();
    }

    /**
     * 耳机申请退款
     */
    @PostMapping("/applyHeadsetphone")
    @Anonymous
    public R applyHeadsetphone(@RequestBody GuidePayOrderRet cmd) {
        guidePayOrderService.applyHeadsetphone(cmd.getOrderId());
        return R.ok();
    }

    /**
     * 平台登录- 查询所有机构
     */
    @PostMapping("/getAgencies")
    public R<List<CoopAgency>> getAgencies() {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<CoopAgency> ret = guidePayOrderService.getAgencies(user.getUserName());
        return R.ok(ret);
    }

    /**
     * 机构登录-获取机构ID
     */
    @PostMapping("/getAgencyId")
    public R<List<String>> getAgencyId() {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        return R.ok(guidePayOrderService.getSelectId(user.getUserName()));
    }


    /**
     * 营收- 查询合作景区
     * 平台 可以查询所有景区，
     * 机构 只能查询合作的景区
     */
    @PostMapping("/getScenics")
    public R<List<ScenicRatio>> getScenics(@RequestBody OperateIdCmd cmd) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        cmd.setUserName(user.getUserName());
        List<ScenicRatio> ret = guidePayOrderService.getScenics(cmd);
        return R.ok(ret);
    }

    /**
     * 营收- 查询order
     */
    @PostMapping("/getOrders")
    public R<GuideOrderVo> getOrders(@RequestBody GuideOrderQry qry) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        qry.setUserName(user.getUserName());
        GuideOrderVo ret = guidePayOrderService.getOrders(qry);
        return R.ok(ret);
    }

    /**
     * 营收- 查询order明细列表
     */
    @PostMapping("/getOrderItems")
    public R<List<GuidePayOrderItemVo>> getOrderItems(@RequestBody GuideOrderItemQry qry) {
        return R.ok(guidePayOrderService.getOrderItems(qry));
    }

    /**
     * 营收- 查询分页order
     */
    @GetMapping("/getOrdersByLimit")
    public TableDataInfo getOrdersByLimit(GuideOrderGetQry qry) {
        GuideOrderQry retQry = new GuideOrderQry();
        retQry.setAgencyIds(CollectionUtil.isNotEmpty(qry.getAgencyIds()) ? qry.getAgencyIds() : List.of(Constants.ALLSELECT));
        retQry.setScenicIds(CollectionUtil.isNotEmpty(qry.getScenicIds()) ? qry.getScenicIds() : List.of(Constants.SCENICSELECT));
        retQry.setItems(CollectionUtil.isNotEmpty(qry.getItems()) ? qry.getItems() : List.of(Constants.ALLSELECT));
        retQry.setTimeType(qry.getTimeType());
        if (qry.getBeginTime() != null) {
            retQry.setStartTime(LocalDateTime.ofInstant(qry.getBeginTime().toInstant(), ZoneId.systemDefault()));
        }
        if (qry.getEndTime() != null) {
            retQry.setEndTime(LocalDateTime.ofInstant(qry.getEndTime().toInstant(), ZoneId.systemDefault()));
        }
        retQry.setOrderSortCol(qry.getOrderSortCol());
        retQry.setOrderSortType(qry.getOrderSortType());

        //选择全部时，处理机构，景区id
        if (CollectionUtil.isNotEmpty(retQry.getAgencyIds()) &&
                Constants.ALLSELECT.equals(retQry.getAgencyIds().get(0))) {
            //查询机构
            List<CoopAgency> coopAgencys = coopAgencyService.lambdaQuery().list();
            retQry.setAgencyIds(coopAgencys.stream().map(CoopAgency::getAgencyId).collect(Collectors.toList()));
        }

        if (CollectionUtil.isNotEmpty(retQry.getScenicIds()) &&
                Constants.SCENICSELECT == retQry.getScenicIds().get(0)) {
            List<Pair> tuples = new ArrayList<>();
            List<String> agencyIds = retQry.getAgencyIds();
            for (String agencyId : agencyIds) {
                List<ScenicRatio> adminRatioList = scenicRatioService.lambdaQuery()
                        .eq(ScenicRatio::getAgencyId, agencyId)
                        .list();
                List<Integer> adminScenis = adminRatioList.stream().map(ScenicRatio::getScenicId).collect(Collectors.toList());
                for (Integer scenicId : adminScenis) {
                    Pair tuple = new Pair();
                    tuple.setAgencyId(agencyId);
                    tuple.setScenicId(scenicId);
                    tuples.add(tuple);
                }
            }
            retQry.setAgencyIds(null);
            retQry.setScenicIds(null);
            retQry.setTuples(tuples);
        }

        Page page = PageHelper.startPage(qry.getPageNum(), qry.getPageSize());
        GuideOrderVo vo = guidePayOrderService.getOrdersLimit(retQry);
        Map<Object, Object> retParamsMap = new HashMap<>();
        retParamsMap.put("startTime", vo.getStartTime());
        retParamsMap.put("endTime", vo.getEndTime());
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(vo.getItems());
        rspData.setRetParamsMap(retParamsMap);
        PageInfo<GuideOrderSt> pageInfo = new PageInfo<>(page);
        rspData.setTotal(pageInfo.getTotal());
        return rspData;
    }

    /**
     * 合作订单-导出
     */
    @PostMapping("/exportOrders")
    public void exportOrders(HttpServletResponse response, GuideOrderGetQry qry) {
        GuideOrderQry retQry = new GuideOrderQry();
        retQry.setAgencyIds(CollectionUtil.isNotEmpty(qry.getAgencyIds()) ? qry.getAgencyIds() : List.of(Constants.ALLSELECT));
        retQry.setScenicIds(CollectionUtil.isNotEmpty(qry.getScenicIds()) ? qry.getScenicIds() : List.of(Constants.SCENICSELECT));
        retQry.setItems(CollectionUtil.isNotEmpty(qry.getItems()) ? qry.getItems() : List.of(Constants.ALLSELECT));
        retQry.setTimeType(qry.getTimeType());
        if (qry.getBeginTime() != null) {
            retQry.setStartTime(LocalDateTime.ofInstant(qry.getBeginTime().toInstant(), ZoneId.systemDefault()));
        }
        if (qry.getEndTime() != null) {
            retQry.setEndTime(LocalDateTime.ofInstant(qry.getEndTime().toInstant(), ZoneId.systemDefault()));
        }
        //选择全部时，处理机构，景区id
        if (CollectionUtil.isNotEmpty(retQry.getAgencyIds()) &&
                Constants.ALLSELECT.equals(retQry.getAgencyIds().get(0))) {
            //查询机构
            List<CoopAgency> coopAgencys = coopAgencyService.lambdaQuery().list();
            retQry.setAgencyIds(coopAgencys.stream().map(CoopAgency::getAgencyId).collect(Collectors.toList()));
        }

        if (CollectionUtil.isNotEmpty(retQry.getScenicIds()) &&
                Constants.SCENICSELECT == retQry.getScenicIds().get(0)) {
            List<Pair> tuples = new ArrayList<>();
            List<String> agencyIds = retQry.getAgencyIds();
            for (String agencyId : agencyIds) {
                List<ScenicRatio> adminRatioList = scenicRatioService.lambdaQuery()
                        .eq(ScenicRatio::getAgencyId, agencyId)
                        .list();
                List<Integer> adminScenis = adminRatioList.stream().map(ScenicRatio::getScenicId).collect(Collectors.toList());
                for (Integer scenicId : adminScenis) {
                    Pair tuple = new Pair();
                    tuple.setAgencyId(agencyId);
                    tuple.setScenicId(scenicId);
                    tuples.add(tuple);
                }
            }
            retQry.setAgencyIds(null);
            retQry.setScenicIds(null);
            retQry.setTuples(tuples);
        }

        GuideOrderVo vo = guidePayOrderService.getOrdersLimit(retQry);
        ExcelUtil excelUtil = new ExcelUtil(GuideOrderSt.class);
        excelUtil.exportExcel(response, vo.getItems(), "合作订单");
    }

    /**
     * 营收- 查询order明细列表分页
     */
    @GetMapping("/getOrderItemsByLimit")
    public TableDataInfo getOrderItemsByLimit(GuideOrderItemQry qry) {
        startPage();
        List<GuidePayOrderItemVo> ret = guidePayOrderService.getOrderItems(qry);
        return getDataTable(ret);
    }

    /**
     * 订单查询
     */
    @GetMapping("/getOrderList")
    public TableDataInfo getOrderList(OrderInfoQry qry) {
        startPage();
        QueryWrapper<GuidePayOrderItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().isNotNull(GuidePayOrderItem::getOpenId);
        queryWrapper.lambda().eq(GuidePayOrderItem::getOrderItem, OrderItemEnum.AIGUIDE.getCode());
        if(StringUtils.isNotBlank(qry.getUserName())){
            queryWrapper.lambda().like(GuidePayOrderItem::getUserName, qry.getUserName());
        }
        if(StringUtils.isNotBlank(qry.getScenicName())){
            queryWrapper.lambda().like(GuidePayOrderItem::getScenicName, qry.getScenicName());
        }
        if(qry.getStartTime()!= null){
            queryWrapper.apply("to_char(create_time,'yyyy-MM-dd') >= to_char({0}::timestamp,'yyyy-MM-dd')", qry.getStartTime());
        }
        if(qry.getEndTime()!= null){
            queryWrapper.apply("to_char(create_time,'yyyy-MM-dd') <= to_char({0}::timestamp,'yyyy-MM-dd')", qry.getEndTime());
        }
        queryWrapper.lambda().orderByDesc(GuidePayOrderItem::getCreateTime);
        List<GuidePayOrderItem> itemOrders = guidePayOrderItemService.list(queryWrapper);
        return getDataTable(itemOrders);
    }


    /**
     * 订单查询-导出
     */
    @PostMapping("/exportOrderList")
    public void exportOrderList(HttpServletResponse response, OrderInfoQry qry) {
        QueryWrapper<GuidePayOrderItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().isNotNull(GuidePayOrderItem::getOpenId);
        queryWrapper.lambda().eq(GuidePayOrderItem::getOrderItem, OrderItemEnum.AIGUIDE.getCode());
        if(StringUtils.isNotBlank(qry.getUserName())){
            queryWrapper.lambda().like(GuidePayOrderItem::getUserName, qry.getUserName());
        }
        if(StringUtils.isNotBlank(qry.getScenicName())){
            queryWrapper.lambda().like(GuidePayOrderItem::getScenicName, qry.getScenicName());
        }
        if(qry.getStartTime()!= null){
            queryWrapper.apply("to_char(create_time,'yyyy-MM-dd') >= to_char({0}::timestamp,'yyyy-MM-dd')", qry.getStartTime());
        }
        if(qry.getEndTime()!= null){
            queryWrapper.apply("to_char(create_time,'yyyy-MM-dd') <= to_char({0}::timestamp,'yyyy-MM-dd')", qry.getEndTime());
        }
        queryWrapper.lambda().orderByDesc(GuidePayOrderItem::getCreateTime);
        List<GuidePayOrderItem> itemOrders = guidePayOrderItemService.list(queryWrapper);
        ExcelUtil excelUtil = new ExcelUtil(GuidePayOrderItem.class);
        excelUtil.exportExcel(response, itemOrders, "订单信息");
    }

    /**
     * 订单查询- 查询order明细列表
     */
    @PostMapping("/getOrderItem")
    public R<GuidePayOrderItemEntity> getOrderItem(@RequestBody GuideOrderItemId qry) {
        return R.ok(guidePayOrderService.getOrderItem(qry.getOrderId()));
    }

    /**
     * 平台查询所有机构
     */
    @PostMapping("/getAgenciesByPlat")
    public R<List<CoopAgency>> getAgenciesByPlat() {
        //查询所有机构
        List<CoopAgency> ret = coopAgencyService.lambdaQuery()
                .list();
        return R.ok(ret);
    }

    /**
     * 平台查询所有景区
     */
    @PostMapping("/getScenicsByPlat")
    public R<List<ScenicRatio>> getScenicsByPlat(@RequestBody ScenicIdCmd cmd) {
        QueryWrapper<ScenicRatio> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(cmd.getAgencyId())) {
            if(!Constants.ALLSELECT.equals(cmd.getAgencyId())){
                queryWrapper.lambda().eq(ScenicRatio::getAgencyId, cmd.getAgencyId());
            }
        }
        List<ScenicRatio> ret = scenicRatioService.list(queryWrapper);
        //根据景区去重
        if (CollectionUtil.isNotEmpty(ret)) {
            List<ScenicRatio> retList = ret.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                    () -> new TreeSet<>(Comparator.comparing(ScenicRatio::getScenicId))), ArrayList::new));
            return R.ok(retList);
        }
        return R.ok(ret);
    }


}
