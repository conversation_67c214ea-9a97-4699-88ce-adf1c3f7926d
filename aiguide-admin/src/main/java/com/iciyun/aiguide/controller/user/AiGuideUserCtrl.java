package com.iciyun.aiguide.controller.user;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.coze.openapi.client.chat.model.ChatEvent;
import com.google.common.collect.Maps;
import com.iciyun.common.annotation.Anonymous;
import com.iciyun.common.constant.CacheConstants;
import com.iciyun.common.core.controller.BaseController;
import com.iciyun.common.core.domain.AjaxResult;
import com.iciyun.common.core.domain.R;
import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.common.core.domain.entity.TextSpeechCmd;
import com.iciyun.common.core.domain.entity.UseTokenCmd;
import com.iciyun.common.core.domain.entity.scenic.ScenicTextSpeechCmd;
import com.iciyun.common.core.domain.entity.wx.*;
import com.iciyun.common.core.domain.model.LoginUser;
import com.iciyun.common.core.domain.model.UserStyleDto;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.common.enums.GuidePersonaEnum;
import com.iciyun.common.enums.HobbyTypeEnum;
import com.iciyun.common.utils.HelpMe;
import com.iciyun.common.utils.SecurityUtils;
import com.iciyun.common.utils.StringUtils;
import com.iciyun.common.utils.sign.Md5Utils;
import com.iciyun.common.utils.uuid.IdHutool;
import com.iciyun.event.StreamChatSyncCacheEvent;
import com.iciyun.event.StreamChatSyncEvent;
import com.iciyun.event.StreamChatSyncQueueService;
import com.iciyun.event.TokenUsedEvent;
import com.iciyun.framework.web.service.TokenService;
import com.iciyun.framework.web.service.WxLoginService;
import com.iciyun.oss.OssService;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.SysIbeaconCmd;
import com.iciyun.system.domain.SysTouristLabel;
import com.iciyun.system.domain.UseToken;
import com.iciyun.system.domain.qo.LabelsQuery;
import com.iciyun.system.domain.vo.BeaconInitVO;
import com.iciyun.system.mapper.ScenicSpotCustomizeMapper;
import com.iciyun.system.mapper.SysTouristLabelMapper;
import com.iciyun.system.service.*;
import io.reactivex.Flowable;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.compress.utils.Lists;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.io.*;
import com.iciyun.common.utils.uuid.UUID;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 微信用户
 */
@RestController
@RequestMapping("/guide/user")
@Slf4j
public class AiGuideUserCtrl extends BaseController {

    private final ExecutorService executorService = Executors.newFixedThreadPool(100);


    //cozeToken
    private static String cozeTokenKey = "chat:cozeToken";

    //14 分钟
    private static int cozeTokenTime = 14;

    @Autowired
    private IWxUserService iWxUserService;

    @Autowired
    private WxLoginService wxLoginService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private HttpServletResponse response;

    @Autowired
    private IScenicService iScenicService;

    @Autowired
    private IGuideService iGuideService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private IGuideTkService guideTkService;

    @Autowired
    private IGuideService guideService;

    @Autowired
    private IUseTokenService useTokenService;

    @Value("${user.code.expireTime}")
    private Integer codeTime;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private StreamChatSyncQueueService streamChatSyncQueueService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    ScenicSpotCustomizeMapper scenicSpotCustomizeMapper;

    @Autowired
    private ISysScenicStService sysScenicStService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    @Autowired
    IScenicSpotCustomizeService scenicSpotCustomizeService;

    @Autowired
    ScenicCacheManager scenicCacheManager;

    @Autowired
    SysTouristLabelMapper sysTouristLabelMapper;

    @Autowired
    private OssService ossService;

    /**
     * 微信小程序 ，获取手机号
     */
    @PostMapping("/getPhoneNumber")
    public R<Map<String, String>> getPhoneNumber(@RequestBody WxGetPhoneCmd cmd) {
        return R.ok(iWxUserService.getPhoneNumber(cmd.getCode()));
    }

    /**
     * 微信小程序 ，获取不限制的小程序码
     */
    @PostMapping("/getUnlimitedQRCode")
    public R<String> getUnlimitedQRCode(@RequestBody WxGetCodeCmd cmd) {
        return R.ok(iWxUserService.getUnlimitedQRCode(cmd));
    }


    /**
     * 获取二维码有效时间
     */
    @PostMapping("/getCodeTime")
    public R<Integer> getCodeTime() {
        return R.ok(codeTime);
    }

    /**
     * 微信小程序 用户登录
     * 返回 token
     */
    @PostMapping(value = "/wxLogin")
    public AjaxResult wxLogin(@RequestBody WxMiniAppCodeCmd cmd) {
        WxUserCmd wxCmd = new WxUserCmd();
        wxCmd.setUserName(cmd.getNumber());
        WxSysUserInfo wxUser = iWxUserService.getWxUserInfo(wxCmd);
        if (wxUser != null) {
            //判断用户是否已经有token
            String token = tokenService.createTokenByUser(wxUser);
            if (StringUtils.isNotEmpty(token)) {
                Map<String, String> map = new HashMap<>();
                map.put("token", token);
                return success(map);
            }
        }
        SysUser sysUser = iWxUserService.getUserToken(cmd);
        String token = wxLoginService.wxLogin(sysUser.getUserName());
        Map<String, String> map = new HashMap<>();
        map.put("token", token);
        return success(map);
    }

    /**
     * 刷新 token
     */
    @PostMapping(value = "/wxUserRefreshToken")
    public AjaxResult wxUserRefreshToken(@RequestBody WxRefreshCmd cmd) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        if (user.getUserName().equals(cmd.getNumber())) {
            tokenService.refreshToken(loginUser);
        }
        return success();
    }

    /**
     * 景区语音播报 返回 语音lsit
     */
    @PostMapping(value = "/scenicSpeechs")
    @Anonymous
    public R scenicSpeechs(@RequestBody ScenicTextSpeechCmd cmd) {
        SysUser user = null;
        try {
            user = SecurityUtils.getLoginUser().getUser();
        } catch (Exception e) {
//            log.info("小程序下单当前用户未登录", e);
        }
        if (user == null) {
            if (StringUtils.isNotEmpty(cmd.getOpenId())) {
                //未绑定用户
                SysUser miniUser = userService.selectUserByOpenId(cmd.getOpenId());
                if (miniUser == null) {
                    user = new SysUser(cmd.getOpenId());
                    userService.insertUser(user);
                    cmd.setUserId(user.getUserId());
                } else {
                    cmd.setUserId(miniUser.getUserId());
                }
            } else {
                //未登录用户，默认admin
                cmd.setUserId(1l);
            }
        } else {
            cmd.setUserId(SecurityUtils.getLoginUser().getUserId());
            cmd.setUserName(SecurityUtils.getLoginUser().getUser().getUserName());
        }
        if (StringUtils.isBlank(cmd.getScenicId()) || StringUtils.isBlank(cmd.getScenicName())) {
            return R.fail("景区ID或景区名不能为空");
        }
        Map<String, Object> map = iScenicService.scenicSpeechs(response, cmd);
        String audioUrl = map.get("audioUrl").toString();

        if (StrUtil.isNotEmpty(audioUrl)) {
            //用户与景区交互统计
            try {
                sysScenicStService.addScenicSt(cmd.getUserId(), cmd.getScenicId(), cmd.getScenicName(), "");
            } catch (Exception e) {
                log.error("用户与景区交互统计异常", e);
            }
        }
        return R.ok(map);
    }

    /**
     * 上传景区导览图
     */
    @PostMapping(value = "/upScenicImg")
    public AjaxResult upScenicImg(@RequestParam("file") MultipartFile file, String scenicId) {
        iScenicService.upScenicImg(file, scenicId);
        return success();
    }

    /**
     * 微信用户信息
     */
    @PostMapping(value = "/getWxUserInfo")
    public R<WxSysUserInfo> getWxUserInfo(@RequestBody WxUserCmd cmd) {
        if (StringUtils.isEmpty(cmd.getUserName())) {
            SysUser user = SecurityUtils.getLoginUser().getUser();
            cmd.setUserName(user.getUserName());
        }
        WxSysUserInfo sysUser = iWxUserService.getWxUserInfo(cmd);
        return R.ok(sysUser);
    }

    /**
     * 更新用户信息
     */
    @PostMapping(value = "/updateWxUser")
    public R<WxSysUserInfo> updateWxUser(@RequestBody UpdateWxUserCmd cmd) {
        if (StringUtils.isEmpty(cmd.getUserName())) {
            SysUser user = SecurityUtils.getLoginUser().getUser();
            cmd.setUserName(user.getUserName());
        }
        iWxUserService.updateWxUser(cmd);
        WxUserCmd wxUserCmd = new WxUserCmd();
        wxUserCmd.setUserName(cmd.getUserName());
        WxSysUserInfo sysUser = iWxUserService.getWxUserInfo(wxUserCmd);
        return R.ok(sysUser);
    }


    /**
     * 图片转标签
     *
     * @param cmd
     * @return
     */
    @Anonymous
    @PostMapping(value = "/img2Label")
    public R img2Label(@RequestBody Img2LabelCmd cmd) {

        String label = iGuideService.img2label(cmd.getTouristId(), cmd.getXy());

        return R.ok(label);
    }


    /**
     * AI对话流式2
     */
    @PostMapping(value = "/getAiStreamChat2", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatEvent> getAiStreamChat2(@RequestBody AiSpeechTextCmd cmd) {

        AiSpeechCmd aiSpeechCmd = new AiSpeechCmd();
        aiSpeechCmd.setUserId(cmd.getUserId());
        aiSpeechCmd.setUserName(cmd.getUserName());
        aiSpeechCmd.setVoiceId(cmd.getVoiceId());
        aiSpeechCmd.setText(cmd.getText());

        Flowable<ChatEvent> flowable = iScenicService.streamChat(aiSpeechCmd);

        Flux<ChatEvent> chatEvents = convertFlowableToFlux(flowable);

        return chatEvents;
    }

    private Flux<ChatEvent> convertFlowableToFlux(Flowable<ChatEvent> flowable) {
        // 使用Flux.from()将Flowable转换为Flux
        return Flux.from(flowable);
    }

    /**
     * AI对话流式
     */
    @PostMapping(value = "/getAiStreamChat")
    public void getAiStreamChat(@RequestBody AiSpeechTextCmd cmd, HttpServletResponse httpServletResponse) throws Exception {
        String text = cmd.getText();

        Response response = iScenicService.getAiStreamChat(text);

        HelpMe.convert(response, httpServletResponse);
    }


    /**
     * 文字转语音
     *
     * @param textSpeechCmd
     * @return
     */
    @PostMapping(value = "/text2speech")
    public R<String> text2speech(@RequestBody TextSpeechCmd textSpeechCmd) {

        String cozeToken = redisCache.getCacheObject(cozeTokenKey);
        if (org.apache.commons.lang3.StringUtils.isBlank(cozeToken)) {
            cozeToken = guideTkService.getToken();
            redisCache.setCacheObject(cozeTokenKey, cozeToken, cozeTokenTime, TimeUnit.MINUTES);
        }

        byte[] chatByte = guideService.textSpeechCoze(textSpeechCmd);
        //MP3 名称： 手机号 + chat.mp3
        String mp3Name = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli() + "/chat.mp3";
        String filePath = guideService.upOSSFiles(mp3Name, chatByte);
        if (textSpeechCmd.isCacheFlag()) {
            TextSpeechCmd.ScenicCache scenic = textSpeechCmd.getScenicCache();
            SysUser user = SecurityUtils.getLoginUser().getUser();
            String scenicId = scenic.getScenicId();
            String scenicLabel = scenic.getScenicLabel();
            String prompt = iScenicService.getSysChatUserStyle(user.getUserId());
            log.info("{}, {}, {}", scenicId, scenicLabel, prompt);
            String labelKay = CacheConstants.scenicPixKey + Md5Utils.hash(scenicId +
                    (StringUtils.isNotEmpty(scenicLabel) ? scenicLabel : "") +
                    prompt);
            log.info("labelKay: {}", labelKay);
            List<String> voicesPaths = new ArrayList<>();
            String voiceArrStr = redisCache.getCacheObject(labelKay);
            if (StringUtils.isNotEmpty(voiceArrStr)) {
                voicesPaths = CollectionUtil.newArrayList(voiceArrStr.split(","));
                voicesPaths.add(filePath);
            } else {
                voicesPaths = List.of(filePath);
            }
            String voices = StringUtils.join(voicesPaths, ",");
            redisCache.setCacheObject(labelKay, voices, 7, TimeUnit.DAYS);
        }
        return R.ok(filePath);
    }

    /**
     * 记录token 使用
     */
    @PostMapping(value = "/tokenUse")
    public R tokenUse(@RequestBody UseTokenCmd useTokenCmd) {
        UseToken useToken = new UseToken();
        useToken.setBizId(IdHutool.gen());
        useToken.setUseToken(useTokenCmd.getTokenCount());
        useToken.setInputCount(useTokenCmd.getInputCount());
        useToken.setOutputCount(useTokenCmd.getOutputCount());
        SysUser user = SecurityUtils.getLoginUser().getUser();
        useToken.setUserName(StringUtils.isNotEmpty(useTokenCmd.getUserName()) ? useTokenCmd.getUserName() : user.getUserName());
        useToken.setBotPlatform("coze");
        useToken.setBotId(useTokenCmd.getBotId());
        useToken.setConversationId(useTokenCmd.getConversationId());
        useToken.setUserInput(useTokenCmd.getInput());
        useToken.setCreateTime(LocalDateTime.now());
        useTokenService.save(useToken);
        return R.ok();
    }


    private final ConcurrentHashMap<String, Future<?>> tasks = new ConcurrentHashMap<>();

    // 根据任务ID取消任务
    private boolean cancelTask(String taskId) {
        Future<?> future = tasks.get(taskId);
        if (future != null) {
            future.cancel(true); // 尝试中断任务执行
            return true;
        }
        return false;
    }


    private String getLanguageStr(UserStyleDto userStyleDto) {
        /**
         * 中文 Chinese
         * 英语 English
         * 日语 Japanese
         * 西班牙语 Spanish
         * 俄语 Russian
         * 韩语 Korean
         * 泰语 Thai
         * 越南语 Vietnamese
         */
        String languageFlag = userStyleDto.getLanguageFlag();
        String str = switch (languageFlag) {
            case "English" -> "使用英文回复。";
            case "Japanese" -> "使用日语回复。";
            case "Spanish" -> "使用西班牙语回复。";
            case "Russian" -> "使用俄语回复。";
            case "Korean" -> "使用韩语回复。";
            case "Thai" -> "使用泰语回复。";
            case "Vietnamese" -> "使用越南语回复。";
            default -> "使用中文回复。";
        };
        return str;
    }


    @Anonymous
    @PostMapping(value = "/getAiStreamChatSync")
    public R<String> getAiStreamChatSync(@RequestBody AiSpeechTextCmd cmd) {
        log.info("AI对话流式异步请求开始:{}", JSON.toJSONString(cmd));
        SysUser user = null;
        try {
            user = SecurityUtils.getLoginUser().getUser();
        } catch (Exception e) {
//            log.info("小程序下单当前用户未登录", e);
        }
        if (user == null) {
            //用户未登录
            if (StringUtils.isNotEmpty(cmd.getOpenId())) {
                //未绑定用户
                user = userService.selectUserByOpenId(cmd.getOpenId());
                if (user == null) {
                    user = new SysUser(cmd.getOpenId());
                    userService.insertUser(user);
                }
            } else {
                //未登录用户，默认admin
                user = userService.selectUserById(1l);
            }
        }
        String syncId = IdUtil.getSnowflakeNextIdStr();
        syncId = cmd.isCacheFlag() + syncId;
        String finalSyncId = syncId;
        log.info("AI对话流式异步Id：{}", syncId);


        ScenicSpot one = scenicSpotCustomizeMapper.selectById(Integer.parseInt(cmd.getScenicId()));
        UserStyleDto userStyleDto = userService.getUserStyle(user.getUserId());

        //根据景区配置的风格和语言，对用户的风格和语言进行调整
        ScenicSpot.StyleAndLanguage styleAndLanguage = one.parseStyleAndLanguage();
        List<String> styleList = styleAndLanguage.getStyle();
        List<String> languageList = styleAndLanguage.getLanguage();

        if (!styleList.contains(userStyleDto.getGuideStyle())) {
            userStyleDto.setGuideStyle("CULTURE");
        }
        if (!languageList.contains(userStyleDto.getLanguageFlag())) {
            userStyleDto.setLanguageFlag("Chinese");
        }

        ScenicSpot scenicSpot = scenicSpotCustomizeService.
                lambdaQuery()
                .eq(ScenicSpot::getId, Integer.parseInt(cmd.getScenicId()))
                .one();

        SysUser finalUser = user;
        executorService.execute(() -> {
            //用户与景区交互统计
            try {
                sysScenicStService.addScenicSt(finalUser.getUserId(), cmd.getScenicId(), cmd.getScenicName(), cmd.getText());
            } catch (Exception e) {
                log.error("用户与景区交互统计异常", e);
            }
        });

        if (cmd.getType() == 2) {
            HobbyTypeEnum hobbyTypeEnum = HobbyTypeEnum.of(userStyleDto.getGuideStyle());

            List<String> tempList = Lists.newArrayList();

            tempList.add("现在我们来到{label}");
            tempList.add("现在我们来讲讲{label}");
            tempList.add("欢迎来到{label}");
            tempList.add("现在我要讲讲{label}");

            int index = RandomUtil.randomInt(0, tempList.size());

            Map<String, String> paramMap = Maps.newHashMap();
            paramMap.put("label", cmd.getScenicLabel());

            String temp = tempList.get(index);
            temp = StrUtil.format(temp, paramMap);

            String text = "请帮我介绍一下" + cmd.getScenicName() + "的" + cmd.getScenicLabel();
            text += "，请以 " + temp + " 开头进行回复。回复风格要求：" + hobbyTypeEnum.getDesc() + "，回复语言要求：";
            ;
            cmd.setText(text);
        } else if (cmd.getType() == 1) {
            HobbyTypeEnum hobbyTypeEnum = HobbyTypeEnum.of(userStyleDto.getGuideStyle());

            //景区简介
            String scenicName = cmd.getScenicName();

            String str = "请结合" + scenicName + "的特点，为我尽可能详细的介绍。不说废话，直接介绍。";
            str += "回复时请以：现在您可以点击图上的景点，让我进行深度讲解啦！结尾。回复风格要求：" + hobbyTypeEnum.getDesc() + "，回复语言要求：";

            cmd.setText(str);
        } else if (cmd.getType() == 3) {
            cmd.setText("介绍" + cmd.getScenicLabel() + "，");
        } else if (cmd.getType() == 0) {
            cmd.setText(cmd.getText() + " 回复语言要求：");
        }

        cmd.setText(cmd.getText() + getLanguageStr(userStyleDto));
        cmd.setVoiceId(userService.getVoiceId(userStyleDto));

        String oldSyncId = cmd.getOldSyncId();
        if (StrUtil.isNotEmpty(oldSyncId) && oldSyncId.startsWith("false")) {
            boolean flag = cancelTask(oldSyncId);
            log.info("尝试中断老任务，oldSyncId：{}", oldSyncId);
        }

        SysUser finalUser1 = user;
        UserStyleDto finalUserStyleDto = userStyleDto;
        UserStyleDto finalUserStyleDto1 = userStyleDto;
        Future<?> future = executorService.submit(() -> {
            AiSpeechCmd aiSpeechCmd = new AiSpeechCmd();
            aiSpeechCmd.setUserId(String.valueOf(finalUser.getUserId()));
            aiSpeechCmd.setUserName(finalUser.getUserName());
            aiSpeechCmd.setVoiceId(cmd.getVoiceId());
            aiSpeechCmd.setText(cmd.getText());
            aiSpeechCmd.setScenicName(cmd.getScenicName());
            aiSpeechCmd.setSyncId(finalSyncId);
            aiSpeechCmd.setScenicId(cmd.getScenicId());
            aiSpeechCmd.setScenicLabel(cmd.getScenicLabel());
            aiSpeechCmd.setType(cmd.getType());
            aiSpeechCmd.setUserStyleDto(finalUserStyleDto);
            aiSpeechCmd.setLabelType(cmd.getLabelType());
            aiSpeechCmd.setProvinceName(scenicSpot.getProvinceName());
            aiSpeechCmd.setDistrictName(scenicSpot.getDistrictName());


            String dataStart = CacheConstants.DATA_START;
            String eventDelta = CacheConstants.EVENT_DELTA;
            String eventDone = CacheConstants.EVENT_DONE;
            String eventChatCompleted = CacheConstants.CHAT_COMPLETED;
            String eventMessageCompleted = CacheConstants.MESSAGE_COMPLETED;

            long time1 = System.currentTimeMillis();

            Response response = null;
            try {
                response = iScenicService.streamChatResponse(aiSpeechCmd);
            } catch (Exception e) {
                log.error("AI对话流式异步请求扣子异常，异步Id:{}", finalSyncId);
                log.error("AI对话流式异步请求扣子异常", e);

                StreamChatSyncEvent event = StreamChatSyncEvent.builder()
                        .syncId(finalSyncId).status(eventDone)
                        .build();
                applicationEventPublisher.publishEvent(event);
                return;
            }
            if (response == null) {
                StreamChatSyncEvent event = StreamChatSyncEvent.builder()
                        .syncId(finalSyncId).status(eventDone)
                        .build();
                applicationEventPublisher.publishEvent(event);
                return;
            }

            // 获取响应实体
            BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream()));
            try {
                String line;
                String currentEventId = null;
                String eventType = null;

                List<String> punctuationMarks = Arrays.asList("。", "!", "！", "?", "？",
                        ",", "，", "~", "~ ", "；", ";", "：", ":", "•", "·");
                StringBuilder sb = new StringBuilder();
                boolean sentenceSegmentation = true;

                //循环检查中断状态
                while (!Thread.currentThread().isInterrupted() && (line = reader.readLine()) != null) {

                    if (line.startsWith("event:")) {
                        String[] parts = line.split(":", 2);
                        eventType = parts[1];
                        eventType = "event:" + eventType;
//                        log.info("Event type: {}", eventType);
                    } else if (line.startsWith("id:")) {
                        String[] parts = line.split(":", 2);
                        currentEventId = parts[1];
                    } else if (line.startsWith("data:")) {
                        String[] parts = line.split(":", 2);
                        String data = parts[1];
                        if (CacheConstants.EVENT_DELTA.equals(eventType)) {
                            cn.hutool.json.JSONObject json = JSONUtil.parseObj(data);
                            String content = json.getStr("content");
                            String chatId = json.getStr("chat_id");

                            String[] split = content.split("");
                            for (String s : split) {
                                sb.append(s);
                                String text = sb.toString().replace("*", "").replace("#", "");

                                if (!sentenceSegmentation && punctuationMarks.contains(s)) {
                                    long time2 = System.currentTimeMillis();
                                    long time = time2 - time1;
                                    sb.setLength(0);
                                    log.info("{} ms chat_id:{} content:{}", time, chatId, text);

                                    StreamChatSyncEvent event = StreamChatSyncEvent.builder()
                                            .syncId(finalSyncId).content(text).status(eventDelta)
                                            .voiceId(cmd.getVoiceId())
                                            .chatId(chatId)
                                            .scenicId(cmd.getScenicId()).scenicName(cmd.getScenicName())
                                            .scenicLabel(cmd.getScenicLabel()).cacheFlag(cmd.isCacheFlag())
                                            .userId(finalUser.getUserId())
                                            .userStyleDto(finalUserStyleDto)
                                            .build();
                                    applicationEventPublisher.publishEvent(event);
                                }
                                if (sentenceSegmentation && punctuationMarks.contains(s) && sb.length() > 5) {
                                    long time2 = System.currentTimeMillis();
                                    long time = time2 - time1;
                                    sb.setLength(0);
                                    sentenceSegmentation = false;
                                    log.info("第一句流式文本 --> {} ms chat_id:{} content:{}", time, chatId, text);

                                    StreamChatSyncEvent event = StreamChatSyncEvent.builder()
                                            .syncId(finalSyncId).content(text).status(eventDelta)
                                            .voiceId(cmd.getVoiceId())
                                            .chatId(chatId)
                                            .scenicId(cmd.getScenicId()).scenicName(cmd.getScenicName())
                                            .scenicLabel(cmd.getScenicLabel()).cacheFlag(cmd.isCacheFlag())
                                            .userId(finalUser.getUserId())
                                            .userStyleDto(finalUserStyleDto)
                                            .build();
                                    applicationEventPublisher.publishEvent(event);
                                }
                            }
                        } else if (CacheConstants.EVENT_DONE.equals(eventType)) {
                            StreamChatSyncEvent event = StreamChatSyncEvent.builder()
                                    .syncId(finalSyncId).status(eventDone)
                                    .build();
                            applicationEventPublisher.publishEvent(event);
                        } else if (CacheConstants.MESSAGE_COMPLETED.equals(eventType)) {
                            if (cmd.isCacheFlag()) {
                                String scenicId = cmd.getScenicId();
                                String scenicLabel = cmd.getScenicLabel();
                                String voiceId = cmd.getVoiceId();

                                String labelKay = scenicCacheManager.getCacheKey(finalUserStyleDto1, scenicId, scenicLabel, cmd.getVoiceId());

                                log.info("查询景区 label 缓存：{}", labelKay);

                                String voiceArrStr = redisCache.getCacheObject(labelKay);
                                if (StringUtils.isNotEmpty(voiceArrStr)) {
                                    log.info("缓存 {}, 已存在", labelKay);
                                } else {
                                    cn.hutool.json.JSONObject json = JSONUtil.parseObj(data);
//                                    log.info("message completed json : {}", json);
                                    String type = json.getStr("type");
                                    if (type.equals("answer")) {
                                        String content = json.getStr("content");
                                        StreamChatSyncCacheEvent event = StreamChatSyncCacheEvent.builder()
                                                .syncId(finalSyncId).content(content).status(eventMessageCompleted)
                                                .voiceId(cmd.getVoiceId())
                                                .scenicId(cmd.getScenicId()).scenicName(cmd.getScenicName())
                                                .scenicLabel(cmd.getScenicLabel()).cacheFlag(cmd.isCacheFlag())
                                                .question(cmd.getText())
                                                .userId(finalUser.getUserId())
                                                .userStyleDto(finalUserStyleDto)
                                                .type(cmd.getType())
                                                .scenicSpot(scenicSpot)
                                                .build();
                                        applicationEventPublisher.publishEvent(event);
                                    }

                                }
                            }
                        } else if (CacheConstants.CHAT_COMPLETED.equals(eventType)) {
                            cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(data);
//                            log.info("扣子对话已完成， completed json : {}", jsonObject);

                            String conversationId = jsonObject.getStr("conversation_id");
                            String botId = jsonObject.getStr("botId");

                            cn.hutool.json.JSONObject usage = jsonObject.getJSONObject("usage");
                            int tokenCount = usage.getInt("token_count");
                            int inputCount = usage.getInt("input_count");
                            int outputCount = usage.getInt("output_count");

                            UseToken useToken = new UseToken();
                            useToken.setBizId(IdHutool.gen());
                            useToken.setUseToken(tokenCount);
                            useToken.setInputCount(inputCount);
                            useToken.setOutputCount(outputCount);
                            useToken.setUserId(finalUser.getUserId());
                            useToken.setBotPlatform("coze");
                            useToken.setBotId(botId);
                            useToken.setConversationId(conversationId);
                            useToken.setUserInput(cmd.getText());
                            useToken.setCreateTime(LocalDateTime.now());

                            TokenUsedEvent event = TokenUsedEvent.builder().useToken(useToken).build();
                            log.info("游豆消耗使用开始");
                            applicationEventPublisher.publishEvent(event);

                        }
                    }
                }
            } catch (Exception e) {
                // 捕获中断异常，退出循环
                Thread.currentThread().interrupt(); // 重新设置中断状态
                log.error("AI对话流式异步请求处理扣子应答异常，异步Id:{}", finalSyncId);
                log.error("AI对话流式异步请求处理扣子应答异常", e);
            } finally {
                tasks.remove(finalSyncId);
            }
        });
        tasks.put(syncId, future);

        return R.ok(syncId);
    }


    /**
     * AI对话流式异步
     */
    @PostMapping(value = "/getAiStreamChatSync_Back")
    public R<String> getAiStreamChatSync_Back(@RequestBody AiSpeechTextCmd cmd) {
        log.info("AI对话流式异步请求开始:{}", JSON.toJSONString(cmd));
        SysUser user = null;
        try {
            user = SecurityUtils.getLoginUser().getUser();
        } catch (Exception e) {
            log.info("小程序下单当前用户未登录", e);
        }
        if (user == null) {
            //用户未登录
            user = userService.selectUserByOpenId(cmd.getOpenId());
        }
        String syncId = IdUtil.getSnowflakeNextIdStr();
        syncId = cmd.isCacheFlag() + syncId;
        String finalSyncId = syncId;
        log.info("AI对话流式异步Id：{}", syncId);

        String tempStr = iScenicService.getHobbyTypes(user.getUserId());

        SysUser finalUser = user;
        executorService.execute(() -> {
            //用户与景区交互统计
            try {
                sysScenicStService.addScenicSt(finalUser.getUserId(), cmd.getScenicId(), cmd.getScenicName(), cmd.getText());
            } catch (Exception e) {
                log.error("用户与景区交互统计异常", e);
            }
        });


        if (cmd.getType() == 2) {
            List<String> tempList = Lists.newArrayList();

            tempList.add("现在我们来到{label}");
            tempList.add("现在我们来讲讲{label}");
            tempList.add("欢迎来到{label}");
            tempList.add("现在我要讲讲{label}");

            int index = RandomUtil.randomInt(0, tempList.size());

            Map<String, String> paramMap = Maps.newHashMap();
            paramMap.put("label", cmd.getScenicLabel());

            String temp = tempList.get(index);
            temp = StrUtil.format(temp, paramMap);

            String text = cmd.getText();
            text += "，请以 " + temp + " 开头进行回复。";
            cmd.setText(text);
        } else if (cmd.getType() == 1) {
            //景区简介
            String scenicName = cmd.getScenicName();

            String str = "请结合" + scenicName + "的特点，为我尽可能详细的介绍。不说废话，直接介绍。";
            str += "回复时请以：现在您可以点击图上的景点，让我进行深度讲解啦！结尾。";

            cmd.setText(str);
        } else if (cmd.getType() == 3) {
            cmd.setText("请帮我介绍一下景区内的：" + cmd.getScenicLabel());
        }

        cmd.setText(cmd.getText() + " 我的偏好是：" + tempStr);

        String oldSyncId = cmd.getOldSyncId();
        if (StrUtil.isNotEmpty(oldSyncId) && oldSyncId.startsWith("false")) {
            boolean flag = cancelTask(oldSyncId);
            log.info("尝试中断老任务，oldSyncId：{}", oldSyncId);
        }

        Future<?> future = executorService.submit(() -> {
            AiSpeechCmd aiSpeechCmd = new AiSpeechCmd();
            aiSpeechCmd.setUserId(String.valueOf(finalUser.getUserId()));
            aiSpeechCmd.setUserName(finalUser.getUserName());
            aiSpeechCmd.setVoiceId(cmd.getVoiceId());
            aiSpeechCmd.setText(cmd.getText());
            aiSpeechCmd.setScenicName(cmd.getScenicName());
            aiSpeechCmd.setSyncId(finalSyncId);
            aiSpeechCmd.setScenicId(cmd.getScenicId());
            aiSpeechCmd.setScenicLabel(cmd.getScenicLabel());
            aiSpeechCmd.setType(cmd.getType());

            String dataStart = CacheConstants.DATA_START;
            String eventDelta = CacheConstants.EVENT_DELTA;
            String eventDone = CacheConstants.EVENT_DONE;
            String eventChatCompleted = CacheConstants.CHAT_COMPLETED;
            String eventMessageCompleted = CacheConstants.MESSAGE_COMPLETED;
            List<String> punctuationMarks = Arrays.asList("。", "!", "！", "?", "？",
                    ",", "，", "~", "~ ", "；", ";", "：", ":", "•", "·");
            StringBuilder sb = new StringBuilder();
            StringBuilder logSb = new StringBuilder();
            String current = "", previous = "";

            CloseableHttpResponse httpResponse = null;
            try {
                httpResponse = iScenicService.getAiStreamChat(aiSpeechCmd);
            } catch (Exception e) {
                log.error("AI对话流式异步请求扣子异常，异步Id:{}", finalSyncId);
                log.error("AI对话流式异步请求扣子异常", e);

                StreamChatSyncEvent event = StreamChatSyncEvent.builder()
                        .syncId(finalSyncId).status(eventDone)
                        .build();
                applicationEventPublisher.publishEvent(event);

                closeHttpResponse(httpResponse);

                return;
            }

            // 获取响应实体
            HttpEntity entity = httpResponse.getEntity();
            try {
                InputStream inputStream = entity.getContent();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
                String line;
                boolean sentenceSegmentation = true;
                //循环检查中断状态
                while (!Thread.currentThread().isInterrupted() && (line = reader.readLine()) != null) {

                    previous = current;
                    current = line;

                    if (current.startsWith(dataStart) && previous.equals(eventDelta)) {

                        String substring = current.substring(5);
                        Map<String, String> map = JSON.parseObject(substring, Map.class);
                        String content = map.get("content").trim();
                        String chatId = map.get("chat_id").trim();

                        String[] split = content.split("");
                        for (String s : split) {
                            logSb.append(s);
                            sb.append(s);
                            String text = sb.toString().replace("*", "").replace("#", "");

                            if (!sentenceSegmentation && punctuationMarks.contains(s)) {
                                StreamChatSyncEvent event = StreamChatSyncEvent.builder()
                                        .syncId(finalSyncId).content(text).status(eventDelta)
                                        .voiceId(cmd.getVoiceId())
                                        .chatId(chatId)
                                        .scenicId(cmd.getScenicId()).scenicName(cmd.getScenicName())
                                        .scenicLabel(cmd.getScenicLabel()).cacheFlag(cmd.isCacheFlag())
                                        .userId(finalUser.getUserId())
                                        .build();
                                applicationEventPublisher.publishEvent(event);

                                sb.setLength(0);
                            }

                            if (sentenceSegmentation && punctuationMarks.contains(s) && sb.length() > 5) {

                                StreamChatSyncEvent event = StreamChatSyncEvent.builder()
                                        .syncId(finalSyncId).content(text).status(eventDelta)
                                        .voiceId(cmd.getVoiceId())
                                        .chatId(chatId)
                                        .scenicId(cmd.getScenicId()).scenicName(cmd.getScenicName())
                                        .scenicLabel(cmd.getScenicLabel()).cacheFlag(cmd.isCacheFlag())
                                        .userId(finalUser.getUserId())
                                        .build();
                                applicationEventPublisher.publishEvent(event);

                                sb.setLength(0);

                                sentenceSegmentation = false;
                            }

                        }

                    }

                    if (current.startsWith(dataStart) && previous.equals(eventDone)) {
                        StreamChatSyncEvent event = StreamChatSyncEvent.builder()
                                .syncId(finalSyncId).status(eventDone)
                                .build();
                        applicationEventPublisher.publishEvent(event);
                    }

                    if (current.startsWith(dataStart) && previous.equals(eventMessageCompleted)) {
                        if (cmd.isCacheFlag()) {
                            String prompt = getSysChatUserStyle(finalUser.getUserId());
                            String labelKay = CacheConstants.scenicPixKey + Md5Utils.hash(cmd.getScenicId() +
                                    (StringUtils.isNotEmpty(cmd.getScenicLabel()) ? cmd.getScenicLabel() : "") +
                                    prompt);
                            String voiceArrStr = redisCache.getCacheObject(labelKay);
                            if (StringUtils.isNotEmpty(voiceArrStr)) {
                                log.info("cache {}, 已存在", labelKay);
                            } else {
                                String substring = current.substring(5);
                                JSONObject jsonObject = JSONObject.parseObject(substring);
                                String type = String.valueOf(jsonObject.get("type"));
                                if (type.equals("answer")) {
                                    String content = String.valueOf(jsonObject.get("content")).trim();
                                    content = content.replace("*", "").replace("#", "");

                                    StreamChatSyncEvent event = StreamChatSyncEvent.builder()
                                            .syncId(finalSyncId).content(content).status(eventMessageCompleted)
                                            .voiceId(cmd.getVoiceId())
                                            .scenicId(cmd.getScenicId()).scenicName(cmd.getScenicName())
                                            .scenicLabel(cmd.getScenicLabel()).cacheFlag(cmd.isCacheFlag())
                                            .userId(finalUser.getUserId())
                                            .build();
                                    applicationEventPublisher.publishEvent(event);
                                }
                            }
                        }
                    }

                    if (current.startsWith(dataStart) && previous.equals(eventChatCompleted)) {
                        log.info("扣子对话已完成");
                        String substring = current.substring(5);

                        JSONObject jsonObject = JSONObject.parseObject(substring);
                        String conversationId = String.valueOf(jsonObject.get("conversation_id"));
                        String botId = String.valueOf(jsonObject.get("bot_id"));

                        JSONObject usage = jsonObject.getJSONObject("usage");
                        String tokenCount = usage.getString("token_count");
                        String outputCount = usage.getString("output_count");
                        String inputCount = usage.getString("input_count");

                        UseToken useToken = new UseToken();
                        useToken.setBizId(IdHutool.gen());
                        useToken.setUseToken(Integer.parseInt(tokenCount));
                        useToken.setInputCount(Integer.parseInt(inputCount));
                        useToken.setOutputCount(Integer.parseInt(outputCount));
                        useToken.setUserId(finalUser.getUserId());
                        useToken.setBotPlatform("coze");
                        useToken.setBotId(botId);
                        useToken.setConversationId(conversationId);
                        useToken.setUserInput(cmd.getText());
                        useToken.setCreateTime(LocalDateTime.now());

                        TokenUsedEvent event = TokenUsedEvent.builder().useToken(useToken).build();
                        log.info("游豆消耗使用开始");
                        applicationEventPublisher.publishEvent(event);
                    }
                }

                log.info("AI对话流式异步请求处理扣子应答,异步Id:{},扣子应答内容:{}", finalSyncId, logSb.toString());

            } catch (Exception e) {
                // 捕获中断异常，退出循环
                Thread.currentThread().interrupt(); // 重新设置中断状态
                log.error("AI对话流式异步请求处理扣子应答异常，异步Id:{}", finalSyncId);
                log.error("AI对话流式异步请求处理扣子应答异常", e);
            } finally {
                tasks.remove(finalSyncId);
                closeHttpResponse(httpResponse);
            }
        });
        tasks.put(syncId, future);

        return R.ok(syncId);
    }

    private void closeHttpResponse(CloseableHttpResponse httpResponse) {
        try {
            httpResponse.close();
        } catch (IOException e) {
            log.error("关闭 httpResponse 失败！", e);
        }
    }

    public String getSysChatUserStyle(Long userId) {
        String prompt = "性别:%s,风格:%s,语言:%s";
        SysUser sysUserInfo = userService.selectUserById(userId);
        if (sysUserInfo != null) {
            String sexType = GuidePersonaEnum.of(sysUserInfo.getGuidePersona()).getDesc();
            //兴趣
            List<String> bu = new ArrayList<>();
            List<String> hobbyTypes = JSON.parseArray(sysUserInfo.getHobbyTypes(), String.class);
            hobbyTypes.forEach(e -> {
                bu.add(HobbyTypeEnum.of(e).getDesc());
            });
            String languageFlag = sysUserInfo.getLanguageFlag();
            prompt = String.format(prompt, sexType, StringUtils.join(bu, ","), languageFlag);
        }
        return prompt;
    }


    /**
     * AI对话流式异步轮询
     */
    @PostMapping(value = "/getAiStreamChatSyncRount")
    @Anonymous
    public R<StreamChatSyncQueueService.StreamChatSyncQueueData> getAiStreamChatSyncRount(@RequestBody StreamChatSyncQueueService.StreamChatSyncQueueRoundReq req) {

//        log.info("AI对话流式异步轮询请求开始:{}", req.getSyncId());
        StreamChatSyncQueueService.StreamChatSyncQueueData streamChatSyncQueueData = streamChatSyncQueueService.get(req.getSyncId());
        return R.ok(streamChatSyncQueueData);

    }

    @Autowired
    private ISysTouristLabelService iSysTouristLabelService;

    /**
     * 默认label 进行缓存
     */
    @PostMapping(value = "/cacheLabels")
    public R cacheLabels(@RequestBody CacheLabelsCmd cmd) {
        log.info("执行批量缓存开始");
        if (StringUtils.isEmpty(cmd.getUserName())) {
            cmd.setUserName("admin");
        }

        if (StringUtils.isEmpty(cmd.getUserId())) {
            cmd.setUserId("1");
        }
        if (StringUtils.isEmpty(cmd.getVoiceId())) {
            cmd.setVoiceId("12940156318210");
        }

        cmd.setCacheFlag(true);
        String scenicIntroduction = "请为我简要概括一下%s，不说废话，直接介绍。";
        String scenicLabelIntroduction = "请为我介绍一下%s的%s";
        SysTouristLabel sysTouristLabel = new SysTouristLabel();
        sysTouristLabel.setTouristId(Long.valueOf(cmd.getScenicId()));
        List<SysTouristLabel> labels = iSysTouristLabelService.selectSysTouristLabelList(sysTouristLabel);
        //根据景区分组
        Map<Long, List<SysTouristLabel>> scenicesMap = labels.stream()
                .collect(Collectors.groupingBy(SysTouristLabel::getTouristId));
        scenicesMap.forEach((k, v) -> {
            List<SysTouristLabel> tourists = v;
            SysTouristLabel scenic = v.get(0);

            //查询是否已经有缓存
            ScenicTextSpeechCmd scCacheCmd = new ScenicTextSpeechCmd();
            scCacheCmd.setUserName(cmd.getUserName());
            scCacheCmd.setScenicId(cmd.getScenicId());
            List<String> mp3Path = iScenicService.getLabelCache(scCacheCmd);
            if (CollectionUtil.isNotEmpty(mp3Path)) {
                log.info("{} 已缓存", cmd.getScenicId());
            } else {
                AiSpeechTextCmd scCmd = new AiSpeechTextCmd();
                scCmd.setUserId(cmd.getUserId());
                scCmd.setUserName(cmd.getUserName());
                scCmd.setVoiceId(cmd.getVoiceId());
                scCmd.setText(String.format(scenicIntroduction, scenic.getTouristName()));
                scCmd.setCacheFlag(cmd.isCacheFlag());
                scCmd.setScenicId(cmd.getScenicId());
                scCmd.setScenicName(scenic.getTouristName());
                scCmd.setScenicLabel(cmd.getScenicLabel());
                getAiStreamChatSync(scCmd);

                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
            tourists.forEach(t -> {
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                Long scenicId = t.getTouristId();
                String scenicName = t.getTouristName();
                String label = t.getLabelName();
                String question = String.format(scenicLabelIntroduction, scenicName, label);

                ScenicTextSpeechCmd cacheCmd = new ScenicTextSpeechCmd();
                cacheCmd.setScenicId(cmd.getScenicId());
                cacheCmd.setScenicLabel(label);
                cacheCmd.setUserName(cmd.getUserName());
                List<String> LabelPath = iScenicService.getLabelCache(cacheCmd);
                if (CollectionUtil.isNotEmpty(LabelPath)) {
                    log.info("{}, {} 已缓存", cmd.getScenicId(), label);
                } else {
                    AiSpeechTextCmd aiSpeechTextCmd = new AiSpeechTextCmd();
                    aiSpeechTextCmd.setUserId(cmd.getUserId());
                    aiSpeechTextCmd.setUserName(cmd.getUserName());
                    aiSpeechTextCmd.setVoiceId(cmd.getVoiceId());
                    aiSpeechTextCmd.setText(question);
                    aiSpeechTextCmd.setCacheFlag(cmd.isCacheFlag());
                    aiSpeechTextCmd.setScenicId(cmd.getScenicId());
                    aiSpeechTextCmd.setScenicName(scenicName);
                    aiSpeechTextCmd.setScenicLabel(label);
                    getAiStreamChatSync(aiSpeechTextCmd);

                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            });
        });
        return R.ok();
    }

    /**
     * 操作scenicLabel缓存
     */
    @Anonymous
    @GetMapping("/delLabelCache")
    public R doLabelCache(String scenicId, String scenicLabel, Long userId, boolean delFlag) {
        String prompt = getSysChatUserStyle(userId);
        String labelKay = CacheConstants.scenicPixKey + Md5Utils.hash(scenicId +
                (StringUtils.isNotBlank(scenicLabel) ? scenicLabel : "") +
                prompt);
        log.info("{}, {}, {}, labelKay: {}", scenicId, scenicLabel, prompt, labelKay);
        String voices = redisCache.getCacheObject(labelKay);
        if (StringUtils.isNotEmpty(voices)) {
            if (delFlag) {
                log.info("del: {}", labelKay);
                redisCache.deleteObject(labelKay);
            }
            log.info("{}, {}, {}, labelKay: {}", scenicId, scenicLabel, prompt, labelKay);
        } else {
            log.info("{} 不存在", labelKay);
        }
        return R.ok(labelKay);
    }

    /**
     * 根据景区id 获取信标，label
     */
    @Anonymous
    @PostMapping("/getLabelForIbeacon")
    public R<List<BeaconInitVO>> getLabelForIbeacon(@RequestBody(required = false) SysIbeaconCmd cmd) {
//        List<SysIbeacon> sysIbeacons = sysIbeaconService.lambdaQuery()
//                .eq(SysIbeacon::getScenicId, cmd.getScenicId())
//                .eq(SysIbeacon::getStatue, 0)
//                .list();

        LabelsQuery query = LabelsQuery.builder()
                .touristId(cmd.getScenicId())
                .queryType(LabelsQuery.QueryTypeEnum.beacon_all.name())
                .build();
        List<SysTouristLabel> sysTouristLabels = iSysTouristLabelService.listQuery(query);
        List<BeaconInitVO> beaconInitVOS = sysTouristLabels.stream().map(BeaconInitVO::new).toList();

        return R.ok(beaconInitVOS);
    }

    @Value("${cj_data.dir}")
    private String cjDataDir;

    /**
     * 采集图片上传本地
     */
    @Anonymous
    @PostMapping(value = "/upFileByScenic")
    public R upFileByScenic(@RequestParam("file") MultipartFile image,
                            @RequestParam("scenicName") String scenicName,
                            @RequestParam("labelName") String labelName) {
        if (image == null || StringUtils.isBlank(scenicName) || StringUtils.isBlank(labelName)) {
            return R.fail("文件等参数不能为空!");
        }
        //创建文件
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String formattedDate = now.format(formatter);
        String dir = cjDataDir + "/" + formattedDate + "/" + scenicName + "/" + labelName;
        File dirFile = new File(dir);
        boolean creatFlag = false;
        if (!dirFile.exists()) {
            boolean isCreated = dirFile.mkdir();
            if (isCreated) {
                creatFlag = true;
               log.info("创建数据采集文件夹");
            } else {
                log.error("创建数据采集文件夹失败");
            }
        } else {
            creatFlag = true;
        }
        if(creatFlag){
            //存储文件
            String fileName = image.getOriginalFilename();
            String newFileName = UUID.fourUUID() + fileName.substring(fileName.lastIndexOf("."));
            //将文件存储到本地磁盘中
            try {
                image.transferTo(new File(dir + "/" + newFileName));
            } catch (IOException e) {
               log.error("存储失败{}", e.getMessage());
            }
        }
        return R.ok();
    }
}
