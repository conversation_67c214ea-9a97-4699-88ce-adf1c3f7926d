package com.iciyun.aiguide.controller.api;

import com.iciyun.common.annotation.Anonymous;
import com.iciyun.common.core.domain.R;
import com.iciyun.common.utils.poi.ExcelUtil;
import com.iciyun.system.domain.SysBaseSt;
import com.iciyun.system.domain.SysScenicSt;
import com.iciyun.system.service.ISysBaseStService;
import com.iciyun.system.service.ISysScenicStService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 统计
 * <AUTHOR>
 * @since 2025-05-06 16:41:01
 */
@Anonymous
@RestController
@RequestMapping("/sysBaseSt")
public class SysBaseStCtrl {

    @Autowired
    private ISysBaseStService sysBaseStService;

    @Autowired
    private ISysScenicStService sysScenicStService;

    /**
     * 基础统计
     */
    @GetMapping("/baseSt")
    public R<List<SysBaseSt>> baseSt() {
        return R.ok(sysBaseStService.lambdaQuery().list());
    }

    /**
     * 基础统计 下载
     */
    @GetMapping("/baseStExport")
    public void baseStExport() {
        List<SysBaseSt> stList = sysBaseStService.lambdaQuery().list();
        ExcelUtil excelUtil = new ExcelUtil(SysBaseSt.class);
        excelUtil.exportExcel(stList, "基础统计");
    }

    /**
     * 用户与景区交互统计
     */
    @GetMapping("/scenicSt")
    public R<List<SysScenicSt>> scenicSt() {
        return R.ok(sysScenicStService.lambdaQuery().list());
    }

    /**
     * 用户与景区交互统计 下载
     */
    @GetMapping("/scenicStExport")
    public void scenicStExport() {
        List<SysScenicSt> stList = sysScenicStService.lambdaQuery().list();
        ExcelUtil excelUtil = new ExcelUtil(SysScenicSt.class);
        excelUtil.exportExcel(stList, "详情统计");
    }


}
