package com.iciyun.aiguide.controller.api;

import com.iciyun.common.annotation.Anonymous;
import com.iciyun.common.core.domain.R;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.service.IScenicSpotCustomizeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Anonymous
@Controller
@RequiredArgsConstructor
public class ScenicSpotTmpController {

    private final IScenicSpotCustomizeService scenicSpotService;

    /**
     * 景区列表页面
     * @param scenicSpot 搜索关键词
     * @return 景区列表视图
     */
    @Anonymous
    @GetMapping("/scenicSport/home")
    public String listScenicSpots(ScenicSpot scenicSpot) {
        return "home";
    }

}
