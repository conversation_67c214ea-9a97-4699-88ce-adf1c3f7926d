package com.iciyun.aiguide.controller.baidu;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import com.iciyun.common.annotation.Anonymous;
import com.iciyun.common.core.controller.BaseController;
import com.iciyun.common.core.domain.R;
import com.iciyun.common.core.domain.entity.CombinationCmd;
import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.common.utils.HelpMe;
import com.iciyun.oss.OssService;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.SysEyePicture;
import com.iciyun.system.mapper.ScenicSpotCustomizeMapper;
import com.iciyun.system.mapper.SysEyePictureMapper;
import com.iciyun.system.service.IScenicSpotService;
import com.iciyun.system.service.ISysScenicStService;
import com.iciyun.system.service.ISysUserService;
import com.iciyun.system.service.impl.AiMatchImgService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 百度动植物识别
 */
@RestController
@RequestMapping("/guide/imagerecognition")
@Slf4j
@RequiredArgsConstructor
@Anonymous
public class BaiduImageRecognitionCtrl extends BaseController {


    @Value("${baidu.apiKey}")
    String apiKey;
    @Value("${baidu.apiSecret}")
    String apiSecret;

    @Autowired
    RedisCache redisCache;

    @Autowired
    private OssService ossService;

    private static final String baidu_access_token = "baidu_access_token";

    @Autowired
    SysEyePictureMapper sysEyePictureMapper;

    @Autowired
    ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    IScenicSpotService scenicSpotService;

    @Autowired
    ScenicSpotCustomizeMapper scenicSpotCustomizeMapper;

    @Autowired
    AiMatchImgService aiMatchImgService;

    @Value("${ai.match_second.img_url}")
    private String aiMatchImgUrl;

    @Autowired
    ISysScenicStService iSysScenicStService;

    @Autowired
    ISysUserService userService;


    public static void main(String[] args) {
        String url = "http://localhost:9898/prod-api/guide/imagerecognition/combination";
        String json = """
                {
                  "imgUrl": "https://ai-guided.oss-cn-beijing.aliyuncs.com/2025/5/6/小鸟.jpeg",
                  "touristId": 432,
                  "touristName": "北京磁云数字科技有限公司",
                  "openId": "",
                  "userPhone": "17610993829"
                }
                """;

        Map<String, Object> map = new HashMap<>();
        map.put("touristId",432);
        map.put("touristName","北京磁云数字科技有限公司");
        map.put("userPhone","17610993829");
        map.put("file",new File("/Users/<USER>/Desktop/test/植物/矮牵牛.jpeg"));

        String str = HttpUtil.post(url,map);

        System.out.println(str);

    }



    private String innerQuery(File file,CombinationCmd cmd){

        long time1 = System.currentTimeMillis();

        String label = aiMatchImgService.matchImg(aiMatchImgUrl,file,cmd.getTouristId()+"");

        long time2 = System.currentTimeMillis();

        log.info("查询yolo模型，label:{},用时：{} ms",label,(time2-time1));

        return label;
    }


    private JSONObject outQuery(File file,CombinationCmd cmd){

        long time1 = System.currentTimeMillis();

        String base64 = Base64.getEncoder().encodeToString(FileUtil.readBytes(file));
        String url = "https://aip.baidubce.com/api/v1/solution/direct/imagerecognition/combination?access_token="+getToken();

        String json = """
                {
                	"image": "{base64}",
                	"scenes": ["animal","plant"],
                	"sceneConf": {
                		"animal": {
                			"top_num": "3",
                			"baike_num": "3"
                		},
                		"plant": {
                			"top_num": "3",
                			"baike_num": "3"
                		}
                	}
                }
                """;

        Map<String, String> param = Maps.newHashMap();
        param.put("base64", base64);

        json = StrUtil.format(json, param);

        String result = HttpUtil.post(url,json);

        long time2 = System.currentTimeMillis();

        log.info("调用百度API接口返回结果：{}，用时：{} ms",result,(time2-time1));

        JSONObject data = JSONUtil.parseObj(result);

        return data;
    }


    /**
     * 组合识别
     *
     * @param cmd
     * @return
     */
    @PostMapping("/combination")
    public R combination(CombinationCmd cmd) throws Exception{
        File file = HelpMe.saveFile(cmd.getFile());

        ScenicSpot scenicSpot = scenicSpotCustomizeMapper.selectById(cmd.getTouristId());

        /**
         *   开开眼优先识别（1：文化符号；2：动植物）
         */
        Integer identifyType = scenicSpot.getIdentifyType();

        String label = "";
        Integer labelType = 0;//0 文物  1 动植物

        try {
            if (identifyType==1){
                label = innerQuery(file,cmd);
                if (StrUtil.isEmpty(label)){
                    JSONObject data = outQuery(file, cmd);
                    label = getLabel(data);
                    labelType = 1;
                }
            }else if (identifyType==2){
                JSONObject data = outQuery(file, cmd);
                label = getLabel(data);
                if (StrUtil.isEmpty(label)){
                    label = innerQuery(file,cmd);
                }else {
                    labelType = 1;
                }
            }
        }catch (Exception e){
            log.error("识别失败",e);
        }

        //记录图片
        threadPoolTaskExecutor.execute(()->{
            insertData(file,cmd);
        });

        Map<String,Object> map = new HashMap<>();
        map.put("label",label);
        map.put("labelType",labelType);

        threadPoolTaskExecutor.execute(()->{
            Long userId;
            SysUser user = userService.selectUserByOpenId(cmd.getOpenId());
            if (user!=null){
                userId = user.getUserId();
            } else {
                userId = 1L;
            }
            iSysScenicStService.addScenicStByType(userId,cmd.getTouristId()+"",cmd.getTouristName(),1);
        });

        return R.ok(map);
    }


    private String getLabel(JSONObject data){
        String label = "";
        if (data.containsKey("error") || data.containsKey("error_code")){
            return label;
        }

        /**
         * 返回示例：
         *
         * {
         *   "result": {
         *     "plant": {
         *       "result": [
         *         {
         *           "score": 0.85687524,
         *           "name": "非植物",
         *           "baike_info": {
         *             "baike_url": "",
         *             "image_url": "",
         *             "description": ""
         *           }
         *         }
         *       ],
         *       "log_id": 1921845773436994800
         *     },
         *     "animal": {
         *       "result": [
         *         {
         *           "score": "0.994349",
         *           "name": "知更鸟",
         *           "baike_info": {
         *             "baike_url": "",
         *             "image_url": "",
         *             "description": ""
         *           }
         *         },
         *         {
         *           "score": "0.000979966",
         *           "name": "和平鸟",
         *           "baike_info": {
         *             "baike_url": "https://baike.baidu.com/item/%E5%92%8C%E5%B9%B3%E9%B8%9F/11014065",
         *             "image_url": "https://bkimg.cdn.bcebos.com/pic/3c6d55fbb2fb431691e176b622a4462308f7d304?x-bce-process=image/resize,m_lfit,w_536,limit_1/quality,Q_70",
         *             "description": "和平鸟（学名：Irena puella）中型鸟类，体长24~28厘米。眼红色，嘴、脚黑色。雄鸟从头顶到尾上覆羽等整个上体辉蓝色具紫色光泽，其余体羽黑色。雌鸟通体铜蓝色，仅飞羽黑褐色。常见的低地留鸟，见于西藏东南部近边境地区及云南南部的原始森林，高可至海拔1100米。"
         *           }
         *         },
         *         {
         *           "score": "0.000727272",
         *           "name": "北美知更鸟",
         *           "baike_info": {
         *             "baike_url": "https://baike.baidu.com/item/%E5%8C%97%E7%BE%8E%E7%9F%A5%E6%9B%B4%E9%B8%9F/8425822",
         *             "image_url": "https://bkimg.cdn.bcebos.com/pic/a8ad9413b2a927c3f7039e87?x-bce-process=image/resize,m_lfit,w_536,limit_1/quality,Q_70",
         *             "description": "北美知更鸟属于动物界脊索动物门鸫科游鸫属鸟类，分布范围十分广泛，在北美分布于加拿大、美国和墨西哥，范围十分广泛，是著名的鸟。一夫一妻制，雌鸟、雄鸟共同筑巢。候鸟，身长约25cm，翼展36-41cm，体重77g。幼雏外形相似，成年后雄鸟自颈部以下羽毛是土黄色或桔黄色，头部及背部为黑色。"
         *           }
         *         }
         *       ],
         *       "log_id": 1921845773645681700
         *     }
         *   },
         *   "log_id": 17470387130160382
         * }
         */

        try{
            JSONObject plant = JSONUtil.parseObj(data.getJSONObject("result").getJSONObject("plant").getJSONArray("result").get(0));
            JSONObject animal = JSONUtil.parseObj(data.getJSONObject("result").getJSONObject("animal").getJSONArray("result").get(0));

            log.info("plant:{}",plant);
            log.info("animal:{}",animal);

            String plantName = plant.getStr("name");
            String animalName = animal.getStr("name");

            if (!"非植物".equals(plantName) && !"非动物".equals(animalName)){
                label = animal.getDouble("score" )>plant.getDouble("score")?animalName:plantName;
            }else if (!"非动物".equals(animalName)){
                label = animalName;
            }else if (!"非植物".equals(plantName)){
                label = plantName;
            }
        }catch (Exception e){
            log.error("解析百度接口数据失败！",e);
        }

        return label;
    }

    private void insertData(File file,CombinationCmd cmd){
        if (StrUtil.isNotEmpty(cmd.getOpenId()) || StrUtil.isNotEmpty(cmd.getUserPhone())){
            String imgUrl = ossService.uploadByInputstream(file.getName(), FileUtil.getInputStream(file));

            SysEyePicture sysEyePicture = new SysEyePicture();
            BeanUtil.copyProperties(cmd,sysEyePicture,HelpMe.copyOptions());

            sysEyePicture.setImgUrl(imgUrl);
            sysEyePicture.setCreateTime(new Date());
            sysEyePictureMapper.insertSysEyePicture(sysEyePicture);

            log.info("删除文件：{}",file.getName());

            FileUtil.del(file);
        }
    }


    private String getToken(){
        Boolean flag = redisCache.hasKey(baidu_access_token);
        String accessToken = "";
        if (flag){
            accessToken = redisCache.getCacheObject(baidu_access_token);
            if (StrUtil.isEmpty(accessToken)){
                accessToken = createToken();
            }
        }else {
            accessToken = createToken();
        }

        return accessToken;
    }

    private String createToken(){
        String url = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={client_id}&client_secret={client_secret}";
        Map<String, String> param = Maps.newHashMap();
        param.put("client_id", apiKey);
        param.put("client_secret", apiSecret);

        url = StrUtil.format(url, param);

        String result = HttpUtil.post(url, "");

        JSONObject json = JSONUtil.parseObj(result);

        if (json.containsKey("access_token")) {
            String access_token = json.getStr("access_token");
            Long expires_in = json.getLong("expires_in");//单位是秒

            log.info("baidu access_token:{},expires_in:{}",access_token,expires_in);

            redisCache.setCacheObject(baidu_access_token,access_token,expires_in.intValue() - 100, TimeUnit.SECONDS);

            return access_token;
        }

        return "";
    }


    @Test
    public void test1() {
        String url = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={client_id}&client_secret={client_secret}";
        Map<String, String> param = Maps.newHashMap();
        param.put("client_id", "xv2poRaUF6svklXoYXHESx1K");
        param.put("client_secret", "q34cCG2uXRKdYiuIZUJJ47BqKdndZk6w");

        url = StrUtil.format(url, param);

        String result = HttpUtil.post(url, "");

        JSONObject json = JSONUtil.parseObj(result);

        if (json.containsKey("access_token")) {
            String access_token = json.getStr("access_token");
            Long expires_in = json.getLong("expires_in");

            System.out.println(access_token + ":" + expires_in);
        }

    }


    @Test
    public void test2() {
        // 请求url
        String url = "https://aip.baidubce.com/api/v1/solution/direct/imagerecognition/combination?access_token=24.041fa1a5fad6ed293208d6ad1099aa5b.2592000.1749123059.282335-118763621";
//        String imgUrl = "https://ai-guided.oss-cn-beijing.aliyuncs.com/2025/5/6/朱槿.jpeg";
        String imgUrl = "https://ai-guided.oss-cn-beijing.aliyuncs.com/2025/5/6/小鸟.jpeg";

        String json = """
                {
                	"imgUrl": "{imgUrl}",
                	"scenes": ["animal","plant"],
                	"sceneConf": {
                		"animal": {
                			"top_num": "3",
                			"baike_num": "3"
                		},
                		"plant": {
                			"top_num": "3",
                			"baike_num": "3"
                		}
                	}
                }
                """;

        Map<String, String> param = Maps.newHashMap();
        param.put("imgUrl", imgUrl);

        json = StrUtil.format(json, param);

        String result = HttpUtil.post(url,json);

        System.out.println(result);

    }

    @Test
    public void test3() {


    }


}
