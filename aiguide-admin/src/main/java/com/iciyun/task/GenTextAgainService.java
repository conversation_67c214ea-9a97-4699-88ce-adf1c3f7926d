package com.iciyun.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.iciyun.common.core.domain.LabelTextDto;
import com.iciyun.common.core.domain.entity.ChatRespDto;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.common.utils.HelpMe;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.SysTouristLabel;
import com.iciyun.system.domain.dto.*;
import com.iciyun.system.mapper.ScenicSpotCustomizeMapper;
import com.iciyun.system.mapper.SysTouristLabelMapper;
import com.iciyun.system.service.*;
import com.iciyun.system.service.impl.ChatService;
import com.iciyun.task.dto.DealOtherLanguageDto;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2025-06-13 16:55.
 */
@Slf4j
@Data
@Service
public class GenTextAgainService {

    @Autowired
    IScenicSpotCustomizeService scenicSpotCustomizeService;
    @Autowired
    ScenicSpotCustomizeMapper scenicSpotCustomizeMapper;
    @Autowired
    ISysUserService sysUserService;
    @Autowired
    ScenicCacheManager scenicCacheManager;
    @Autowired
    ScenicCacheService scenicCacheService;
    @Autowired
    SysTouristLabelMapper sysTouristLabelMapper;
    @Autowired
    ISysDictDataService sysDictDataService;
    @Autowired
    private ISysDictTypeService dictTypeService;
    @Autowired
    GenAudioBySexTask genAudioBySexTask;
    @Autowired
    ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Autowired
    DealOtherLanguageTask dealOtherLanguageTask;


    //处理景区简介
    public String dealGenTextAgain(ScenicSpot scenicSpot, GenTextAgainParam genTextAgainParam) {
        String oldLabelText = scenicSpot.getLabelText();
        String result = genTextAgainParam.getContent();
        ScenicSpot.StyleAndLanguage styleAndLanguage = scenicSpot.parseStyleAndLanguage();
        List<String> styleList = styleAndLanguage.getStyle();

//        -1 重新生成（整个景区的全部）  0 重新生成（某个景点的全部）   1 重新生成（针对某个风格）     2 再次保存（针对某个风格）
        Integer type = genTextAgainParam.getType();
        if (type == 1) {
            String newLabelText = scenicCacheService.justDoCacheWork(scenicSpot, null, genTextAgainParam);
            genTextAgainParam.setContent(newLabelText);
            result = newLabelText;
            threadPoolTaskExecutor.execute(() -> {
                dealNewLabelText(scenicSpot, oldLabelText, genTextAgainParam);
            });
        } else if (type == 2) {
            threadPoolTaskExecutor.execute(() -> {
                dealNewLabelText(scenicSpot, oldLabelText, genTextAgainParam);
            });
        } else if (type == 0) {
            threadPoolTaskExecutor.execute(() -> {
                List<String> languageList = Lists.newArrayList("Chinese");

                for (String style : styleList) {
                    for (String language : languageList) {
                        ScenicSpot temp = scenicSpotCustomizeMapper.selectById(scenicSpot.getId());

                        GenTextAgainParam param = new GenTextAgainParam();
                        BeanUtil.copyProperties(genTextAgainParam, param, HelpMe.copyOptions());
                        param.setLanguage(language);
                        param.setStyle(style);

                        String newLabelText = scenicCacheService.justDoCacheWork(temp, null, param);

                        param.setContent(newLabelText);
                        dealNewLabelText(temp, temp.getLabelText(), param);
                    }
                }
            });
        }

        return result;
    }


    //处理景区 label
    public String dealGenTextAgain(ScenicSpot scenicSpot, SysTouristLabel sysTouristLabel, GenTextAgainParam genTextAgainParam) {
        String oldLabelText = sysTouristLabel.getLabelText();
        String result = genTextAgainParam.getContent();
        ScenicSpot.StyleAndLanguage styleAndLanguage = scenicSpot.parseStyleAndLanguage();
        List<String> styleList = styleAndLanguage.getStyle();

//        -1 重新生成（整个景区的全部）  0 重新生成（某个景点的全部）   1 重新生成（针对某个风格）     2 再次保存（针对某个风格）
        Integer type = genTextAgainParam.getType();
        if (type == 1) {
            String newLabelText = scenicCacheService.justDoCacheWork(scenicSpot, sysTouristLabel.getLabelName(), genTextAgainParam);
            genTextAgainParam.setContent(newLabelText);
            result = newLabelText;

            threadPoolTaskExecutor.execute(() -> {
                dealNewLabelText(sysTouristLabel, oldLabelText, genTextAgainParam);
            });
        } else if (type == 2) {
            threadPoolTaskExecutor.execute(() -> {
                dealNewLabelText(sysTouristLabel, oldLabelText, genTextAgainParam);
            });
        } else if (type == 0) {
            threadPoolTaskExecutor.execute(() -> {

                List<String> languageList = Lists.newArrayList("Chinese");

                for (String style : styleList) {
                    for (String language : languageList) {
                        SysTouristLabel temp = sysTouristLabelMapper.selectSysTouristLabelById(sysTouristLabel.getId());

                        GenTextAgainParam param = new GenTextAgainParam();
                        BeanUtil.copyProperties(genTextAgainParam, param, HelpMe.copyOptions());
                        param.setLanguage(language);
                        param.setStyle(style);

                        String newLabelText = scenicCacheService.justDoCacheWork(scenicSpot, temp.getLabelName(), param);
                        param.setContent(newLabelText);
                        dealNewLabelText(temp, temp.getLabelText(), param);
                    }
                }
            });
        }

        return result;
    }



    private boolean labelTextDto_isOk(ScenicSpot.StyleAndLanguage styleAndLanguage,LabelTextDto labelTextDto){
        List<String> languageList = styleAndLanguage.getLanguage();
        List<String> styleList = styleAndLanguage.getStyle();

        String style = labelTextDto.getStyle();
        String language = labelTextDto.getLanguage();

        if (languageList.contains(language) && styleList.contains(style)){
            return true;
        }

        return false;
    }


    public void genAudioAgain(GenAudioAgainParam genAudioAgainParam){
        Integer scenicId = genAudioAgainParam.getScenicId();
        ScenicSpot scenicSpot = scenicSpotCustomizeMapper.selectById(scenicId);
        ScenicSpot.StyleAndLanguage styleAndLanguage = scenicSpot.parseStyleAndLanguage();

//        label ID：为 0 时表示景区简介，为 null 表示所有label，大于 0 表示某个label
        Long labelId = genAudioAgainParam.getLabelId();

        threadPoolTaskExecutor.execute(()->{

            if (labelId==null){
                //处理所有label
                {
                    //处理景区简介
                    String labelText = scenicSpot.getLabelText();
                    List<LabelTextDto> dtoList = HelpMe.parseLabelText(labelText);

                    for (LabelTextDto dto:dtoList){
                        if (labelTextDto_isOk(styleAndLanguage,dto)){
                            if (scenicSpot.getStyleAndLanguageSwitch()==0){
                                genAudioBySexTask.enqueue(dto);
                            }else if (scenicSpot.getStyleAndLanguageSwitch()==1){
                                genAudioBySexTask.enqueue_coze(dto);
                            }
                        }
                    }
                }

                SysTouristLabelQueryParam param = new SysTouristLabelQueryParam();
                param.setTouristIdList(Lists.newArrayList(Long.parseLong(scenicId+"")));
                List<SysTouristLabel> labelList = sysTouristLabelMapper.selectListByParam(param);
                for (SysTouristLabel sysTouristLabel : labelList) {
                    //处理某个label
                    String labelText = sysTouristLabel.getLabelText();
                    List<LabelTextDto> dtoList = HelpMe.parseLabelText(labelText);

                    for (LabelTextDto dto:dtoList){
                        if (labelTextDto_isOk(styleAndLanguage,dto)){
                            if (scenicSpot.getStyleAndLanguageSwitch()==0){
                                genAudioBySexTask.enqueue(dto);
                            }else if (scenicSpot.getStyleAndLanguageSwitch()==1){
                                genAudioBySexTask.enqueue_coze(dto);
                            }
                        }
                    }

                }

            }else if (labelId==0){
                //处理景区简介
                String labelText = scenicSpot.getLabelText();
                List<LabelTextDto> dtoList = HelpMe.parseLabelText(labelText);
                for (LabelTextDto dto:dtoList){
                    if (labelTextDto_isOk(styleAndLanguage,dto)){
                        if (scenicSpot.getStyleAndLanguageSwitch()==0){
                            genAudioBySexTask.enqueue(dto);
                        }else if (scenicSpot.getStyleAndLanguageSwitch()==1){
                            genAudioBySexTask.enqueue_coze(dto);
                        }
                    }
                }
            }else {
                //处理某个label
                SysTouristLabel sysTouristLabel = sysTouristLabelMapper.selectSysTouristLabelById(labelId);
                String labelText = sysTouristLabel.getLabelText();
                List<LabelTextDto> dtoList = HelpMe.parseLabelText(labelText);
                for (LabelTextDto dto:dtoList){
                    if (labelTextDto_isOk(styleAndLanguage,dto)){
                        if (scenicSpot.getStyleAndLanguageSwitch()==0){
                            genAudioBySexTask.enqueue(dto);
                        }else if (scenicSpot.getStyleAndLanguageSwitch()==1){
                            genAudioBySexTask.enqueue_coze(dto);
                        }
                    }
                }
            }

        });
    }

    public void genOtherLanguageAudio(GenOtherLanguageAudioParam genOtherLanguageAudioParam){
        Integer scenicId = genOtherLanguageAudioParam.getScenicId();
        ScenicSpot scenicSpot = scenicSpotCustomizeMapper.selectById(scenicId);

//        label ID：为 0 时表示景区简介，为 null 表示所有label，大于 0 表示某个label
        Long labelId = genOtherLanguageAudioParam.getLabelId();

        threadPoolTaskExecutor.execute(()->{

            if (labelId==null){
                //处理所有label
                {
                    //处理景区简介
                    String labelText = scenicSpot.getLabelText();
                    List<LabelTextDto> dtoList = HelpMe.parseLabelText(labelText);
                    LabelTextDto dto = filterDefault(dtoList);
                    if (dto!=null){
                        DealOtherLanguageDto dealOtherLanguageDto = new DealOtherLanguageDto();
                        dealOtherLanguageDto.setScenicId(scenicId);
                        dealOtherLanguageDto.setLabelTextDto(dto);
                        dealOtherLanguageDto.setSysTouristLabel(null);

                        //添加到任务队列
                        dealOtherLanguageTask.enqueue(dealOtherLanguageDto);
                    }
                }

                SysTouristLabelQueryParam param = new SysTouristLabelQueryParam();
                param.setTouristIdList(Lists.newArrayList(Long.parseLong(scenicId+"")));
                List<SysTouristLabel> labelList = sysTouristLabelMapper.selectListByParam(param);
                for (SysTouristLabel sysTouristLabel : labelList) {
                    //处理某个label
                    String labelText = sysTouristLabel.getLabelText();
                    List<LabelTextDto> dtoList = HelpMe.parseLabelText(labelText);
                    LabelTextDto dto = filterDefault(dtoList);
                    if (dto!=null){
                        DealOtherLanguageDto dealOtherLanguageDto = new DealOtherLanguageDto();
                        dealOtherLanguageDto.setScenicId(scenicId);
                        dealOtherLanguageDto.setLabelTextDto(dto);
                        dealOtherLanguageDto.setSysTouristLabel(sysTouristLabel);

                        //添加到任务队列
                        dealOtherLanguageTask.enqueue(dealOtherLanguageDto);

                    }
                }

            }else if (labelId==0){
                //处理景区简介
                String labelText = scenicSpot.getLabelText();
                List<LabelTextDto> dtoList = HelpMe.parseLabelText(labelText);
                LabelTextDto dto = filterDefault(dtoList);
                if (dto!=null){
                    DealOtherLanguageDto dealOtherLanguageDto = new DealOtherLanguageDto();
                    dealOtherLanguageDto.setScenicId(scenicId);
                    dealOtherLanguageDto.setLabelTextDto(dto);
                    dealOtherLanguageDto.setSysTouristLabel(null);

                    //添加到任务队列
                    dealOtherLanguageTask.enqueue(dealOtherLanguageDto);
                }
            }else {
                //处理某个label
                SysTouristLabel sysTouristLabel = sysTouristLabelMapper.selectSysTouristLabelById(labelId);
                String labelText = sysTouristLabel.getLabelText();
                List<LabelTextDto> dtoList = HelpMe.parseLabelText(labelText);
                LabelTextDto dto = filterDefault(dtoList);
                if (dto!=null){
                    DealOtherLanguageDto dealOtherLanguageDto = new DealOtherLanguageDto();
                    dealOtherLanguageDto.setScenicId(scenicId);
                    dealOtherLanguageDto.setLabelTextDto(dto);
                    dealOtherLanguageDto.setSysTouristLabel(sysTouristLabel);

                    //添加到任务队列
                    dealOtherLanguageTask.enqueue(dealOtherLanguageDto);
                }
            }

        });


    }




    private void dealNewLabelText(SysTouristLabel sysTouristLabel, String oldLabelText, GenTextAgainParam genTextAgainParam) {
        //该方法存在多线程并发调用的可能，会导致 labelText 数据相互覆盖，所以此处再查询一次，获取最新的 labelText 数据
        sysTouristLabel = sysTouristLabelMapper.selectSysTouristLabelById(sysTouristLabel.getId());
        oldLabelText = sysTouristLabel.getLabelText();

        List<LabelTextDto> labelTextDtoList = delOldLabelText(oldLabelText, genTextAgainParam);
        if (labelTextDtoList != null) {
            //添加新内容
            LabelTextDto labelTextDto = new LabelTextDto();
            labelTextDto.setLabelText(genTextAgainParam.getContent());
            labelTextDto.setLanguage(genTextAgainParam.getLanguage());
            labelTextDto.setStyle(genTextAgainParam.getStyle());
            labelTextDto.setTouristId(sysTouristLabel.getTouristId() + "");
            labelTextDto.setTouristName(sysTouristLabel.getTouristName());
            labelTextDto.setLabelName(sysTouristLabel.getLabelName());
            labelTextDto.setUpdateTimeStr(DateUtil.now());
            labelTextDtoList.add(labelTextDto);
            sysTouristLabel.setJoinCluster(sysTouristLabel.getJoinCluster());
            sysTouristLabel.setLabelText(JSONUtil.toJsonStr(labelTextDtoList));
            sysTouristLabelMapper.updateSysTouristLabel(sysTouristLabel);

            if (labelTextDto.getLanguage().equals("Chinese") && labelTextDto.getStyle().equals("CULTURE")){
                int id = sysTouristLabel.getTouristId().intValue();
                SysTouristLabel finalSysTouristLabel = sysTouristLabel;
                threadPoolTaskExecutor.execute(()->{
                    DealOtherLanguageDto dealOtherLanguageDto = new DealOtherLanguageDto();
                    dealOtherLanguageDto.setScenicId(id);
                    dealOtherLanguageDto.setLabelTextDto(labelTextDto);
                    dealOtherLanguageDto.setSysTouristLabel(finalSysTouristLabel);

                    //添加到任务队列
                    dealOtherLanguageTask.enqueue(dealOtherLanguageDto);
                });
            }

            ScenicSpot scenicSpot = scenicSpotCustomizeMapper.selectById(sysTouristLabel.getTouristId().intValue());

            //不直接生成，而是添加到任务队列
            if (scenicSpot.getStyleAndLanguageSwitch()==0){
                genAudioBySexTask.enqueue(labelTextDto);
            }else if (scenicSpot.getStyleAndLanguageSwitch()==1){
                genAudioBySexTask.enqueue_coze(labelTextDto);
            }

            //注释直接生成音频的逻辑
//            genAudioBySex(labelTextDto, "GIRL");
//            genAudioBySex(labelTextDto, "BOY");

        } else {
            log.info("讲解词相同，无需再次生成");
        }
    }

    private void dealNewLabelText(ScenicSpot scenicSpot, String oldLabelText, GenTextAgainParam genTextAgainParam) {
        //该方法存在多线程并发调用的可能，会导致 labelText 数据相互覆盖，所以此处再查询一次，获取最新的 labelText 数据
        scenicSpot = scenicSpotCustomizeMapper.selectById(scenicSpot.getId());
        oldLabelText = scenicSpot.getLabelText();

        List<LabelTextDto> labelTextDtoList = delOldLabelText(oldLabelText, genTextAgainParam);
        if (labelTextDtoList != null) {
            //添加新内容
            LabelTextDto labelTextDto = new LabelTextDto();
            labelTextDto.setLabelText(genTextAgainParam.getContent());
            labelTextDto.setLanguage(genTextAgainParam.getLanguage());
            labelTextDto.setStyle(genTextAgainParam.getStyle());
            labelTextDto.setTouristId(scenicSpot.getId() + "");
            labelTextDto.setTouristName(scenicSpot.getName());
            labelTextDto.setLabelName("");
            labelTextDto.setUpdateTimeStr(DateUtil.now());
            labelTextDtoList.add(labelTextDto);

            scenicSpot.setLabelText(JSONUtil.toJsonStr(labelTextDtoList));
            scenicSpot.setUpdateTime(new Date());
            scenicSpotCustomizeService.updateById(scenicSpot);

            if (labelTextDto.getLanguage().equals("Chinese") && labelTextDto.getStyle().equals("CULTURE")){
                Integer id = scenicSpot.getId();
                threadPoolTaskExecutor.execute(()->{
                    DealOtherLanguageDto dealOtherLanguageDto = new DealOtherLanguageDto();
                    dealOtherLanguageDto.setScenicId(id);
                    dealOtherLanguageDto.setLabelTextDto(labelTextDto);
                    dealOtherLanguageDto.setSysTouristLabel(null);

                    //添加到任务队列
                    dealOtherLanguageTask.enqueue(dealOtherLanguageDto);
                });
            }

            //不直接生成，而是添加到任务队列
            if (scenicSpot.getStyleAndLanguageSwitch()==0){
                genAudioBySexTask.enqueue(labelTextDto);
            }else if (scenicSpot.getStyleAndLanguageSwitch()==1){
                genAudioBySexTask.enqueue_coze(labelTextDto);
            }

            //注释直接生成音频的逻辑
//            genAudioBySex(labelTextDto, "GIRL");
//            genAudioBySex(labelTextDto, "BOY");
        } else {
            log.info("讲解词相同，无需再次生成");
        }
    }

    private List<LabelTextDto> delOldLabelText(String oldLabelText, GenTextAgainParam genTextAgainParam) {
        JSONArray arr = null;
        try {
            arr = JSONUtil.parseArray(oldLabelText);
        } catch (Exception e) {
        }
        List<LabelTextDto> dtoList = Lists.newArrayList();
        if (arr == null) return dtoList;
        for (Object obj : arr) {
            JSONObject jsonObject = JSONUtil.parseObj(obj);
            LabelTextDto dto = JSONUtil.toBean(jsonObject, LabelTextDto.class);
            if (genTextAgainParam.getStyle().equals(dto.getStyle()) && genTextAgainParam.getLanguage().equals(dto.getLanguage())) {
                /*if (genTextAgainParam.getContent().equals(dto.getLabelText())) {
                    //原内容与新内容一致
                    return null;
                }*/
            } else {
                dtoList.add(dto);
            }
        }
        return dtoList;
    }


    private LabelTextDto filterDefault(List<LabelTextDto> list) {
        for (LabelTextDto dto : list) {
            if (dto.getLanguage().equals("Chinese") && dto.getStyle().equals("CULTURE")) {
                return dto;
            }
        }
        return null;
    }




    private List<LabelTextDto> parseLabelText(String oldLabelText, GenTextAgainParam genTextAgainParam) {
        JSONArray arr = null;
        try {
            arr = JSONUtil.parseArray(oldLabelText);
        } catch (Exception e) {
        }
        List<LabelTextDto> dtoList = Lists.newArrayList();
        if (arr == null) return dtoList;
        for (Object obj : arr) {
            JSONObject jsonObject = JSONUtil.parseObj(obj);
            LabelTextDto dto = JSONUtil.toBean(jsonObject, LabelTextDto.class);
            if (genTextAgainParam.getStyle().equals(dto.getStyle()) && genTextAgainParam.getLanguage().equals(dto.getLanguage())) {
                /*if (genTextAgainParam.getContent().equals(dto.getLabelText())) {
                    //原内容与新内容一致
                    return null;
                }*/
            } else {
                dtoList.add(dto);
            }
        }
        return dtoList;
    }


}
