package com.iciyun.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import com.iciyun.common.constant.CacheConstants;
import com.iciyun.common.core.domain.LabelTextDto;
import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.common.core.domain.entity.wx.AiSpeechCmd;
import com.iciyun.common.core.domain.entity.wx.AiSpeechTextCmd;
import com.iciyun.common.core.domain.model.UserStyleDto;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.common.enums.GuidePersonaEnum;
import com.iciyun.common.enums.HobbyTypeEnum;
import com.iciyun.common.utils.StringUtils;
import com.iciyun.event.StreamChatSyncCacheEvent2;
import com.iciyun.event.StreamChatSyncQueueService;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.SysTouristLabel;
import com.iciyun.system.domain.dto.GenTextAgainParam;
import com.iciyun.system.mapper.ScenicSpotCustomizeMapper;
import com.iciyun.system.mapper.SysTouristLabelMapper;
import com.iciyun.system.service.*;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2025-05-30 10:48.
 */
@Slf4j
@Component
public class ScenicCacheService {

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private StreamChatSyncQueueService streamChatSyncQueueService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysScenicStService sysScenicStService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    @Autowired
    IScenicSpotCustomizeService scenicSpotCustomizeService;

    @Autowired
    ScenicCacheManager scenicCacheManager;

    @Autowired
    SysTouristLabelMapper sysTouristLabelMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private IScenicService iScenicService;

    @Autowired
    ScenicSpotCustomizeMapper scenicSpotCustomizeMapper;

    @Autowired
    LabelAudioCacheTask labelAudioCacheTask;

    @Autowired
    TouristDetailAudioCacheTask touristDetailAudioCacheTask;

    public void doCacheWork(ScenicSpot scenicSpot,List<LabelTextDto> labelTextDtoList) {
        List<UserStyleDto> list = userStyleDtoList();
        for (UserStyleDto userStyleDto : list) {
            Boolean flag = LabelTextDto.unExistKey(labelTextDtoList, scenicSpot.getId() + "", scenicSpot.getName(), "", userStyleDto.getLanguageFlag(), HobbyTypeEnum.of(userStyleDto.getGuideStyle()));
            if (flag){
                //需要生成景区简介
                justDoCacheWork(scenicSpot, null, userStyleDto,false);
            }else {
                log.info("简介解说词已存在，景区ID：[{}] 景区名称：[{}] , 风格：[{}]，语言：[{}]", scenicSpot.getId(),
                        scenicSpot.getName(), userStyleDto.getGuideStyle(), userStyleDto.getLanguageFlag());
            }
        }
    }


    public void doCacheWork_Default(ScenicSpot scenicSpot, String labelName) {
        List<UserStyleDto> list = userStyleDtoList_Default();
        for (UserStyleDto userStyleDto : list) {
            justDoCacheWork(scenicSpot, labelName, userStyleDto,true);
        }
    }


    public String justDoCacheWork(ScenicSpot scenicSpot, String labelName, GenTextAgainParam genTextAgainParam) {
        UserStyleDto userStyleDto = new UserStyleDto();
        userStyleDto.setLanguageFlag(genTextAgainParam.getLanguage());
        userStyleDto.setGuideStyle(genTextAgainParam.getStyle());

        if (StrUtil.isNotEmpty(labelName)) {
            //缓存景点
            AiSpeechTextCmd cmd = new AiSpeechTextCmd();
            cmd.setVoiceId("12940156318210");
            cmd.setCacheFlag(true);
            cmd.setScenicId(scenicSpot.getId() + "");
            cmd.setScenicName(scenicSpot.getName());
            cmd.setScenicLabel(labelName);
            cmd.setType(2);
            cmd.setUserStyleDto(userStyleDto);
            log.info("准备缓存 {} 的景点：{}, 风格：{}", scenicSpot.getName(), labelName,userStyleDto);

            return justDoCacheWork_GetFullContent(cmd);
        } else {
            //缓存景区简介
            AiSpeechTextCmd cmd = new AiSpeechTextCmd();
            cmd.setVoiceId("12940156318210");
            cmd.setCacheFlag(true);
            cmd.setScenicId(scenicSpot.getId() + "");
            cmd.setScenicName(scenicSpot.getName());
            cmd.setType(1);
            cmd.setUserStyleDto(userStyleDto);
            log.info("准备缓存 {} 简介... 风格：{}", scenicSpot.getName(),userStyleDto);

            return justDoCacheWork_GetFullContent(cmd);
        }
    }

    public String justDoCacheWork(ScenicSpot scenicSpot, String labelName, String language,String guideStyle) {
        GenTextAgainParam genTextAgainParam = new GenTextAgainParam();
        genTextAgainParam.setLanguage(language);
        genTextAgainParam.setStyle(guideStyle);
        return justDoCacheWork(scenicSpot,labelName,genTextAgainParam);
    }


    private void justDoCacheWork(ScenicSpot scenicSpot, String labelName, UserStyleDto userStyleDto,Boolean defaultFlag) {
        if (StrUtil.isNotEmpty(labelName)) {
            //缓存景点
            AiSpeechTextCmd cmd = new AiSpeechTextCmd();
            cmd.setVoiceId("12940156318210");
            cmd.setCacheFlag(true);
            cmd.setScenicId(scenicSpot.getId() + "");
            cmd.setScenicName(scenicSpot.getName());
            cmd.setScenicLabel(labelName);
            cmd.setType(2);
            cmd.setUserStyleDto(userStyleDto);
            cmd.setDefaultFlag(defaultFlag);
            log.info("准备缓存 {} 的景点：{}, 风格：{}", scenicSpot.getName(), labelName,userStyleDto);

            justDoCacheWork(cmd);
        } else {
            //缓存景区简介
            AiSpeechTextCmd cmd = new AiSpeechTextCmd();
            cmd.setVoiceId("12940156318210");
            cmd.setCacheFlag(true);
            cmd.setScenicId(scenicSpot.getId() + "");
            cmd.setScenicName(scenicSpot.getName());
            cmd.setType(1);
            cmd.setUserStyleDto(userStyleDto);
            cmd.setDefaultFlag(defaultFlag);
            log.info("准备缓存 {} 简介... 风格：{}", scenicSpot.getName(),userStyleDto);

            justDoCacheWork(cmd);
        }
    }


    private List<UserStyleDto> userStyleDtoList() {
        List<UserStyleDto> list = new ArrayList<>();

        //历史考证
        {
            UserStyleDto one = new UserStyleDto();
            one.setGuideStyle("HISTORY");
            one.setLanguageFlag("Chinese");
            list.add(one);
        }

        //聊天陪伴
        {
            UserStyleDto one = new UserStyleDto();
            one.setGuideStyle("CHAT");
            one.setLanguageFlag("Chinese");
            list.add(one);
        }

        //艺术创作
        {
            UserStyleDto one = new UserStyleDto();
            one.setGuideStyle("ART");
            one.setLanguageFlag("Chinese");
            list.add(one);
        }

        //亲子教育
        {
            UserStyleDto girl = new UserStyleDto();
            girl.setGuideStyle("PARENT_CHILD");
            girl.setLanguageFlag("Chinese");
            list.add(girl);
        }

        //自然漫步
        {
            UserStyleDto girl = new UserStyleDto();
            girl.setGuideStyle("NATURE");
            girl.setLanguageFlag("Chinese");

            list.add(girl);
        }

        //诗歌漫游
        {
            UserStyleDto girl = new UserStyleDto();
            girl.setGuideStyle("POETRY");
            girl.setLanguageFlag("Chinese");
            list.add(girl);
        }

        //剧本沉浸
        {
            UserStyleDto girl = new UserStyleDto();
            girl.setGuideStyle("SCRIPT_IMMERSION");
            girl.setLanguageFlag("Chinese");
            list.add(girl);
        }

        //文化内涵
        {
            UserStyleDto girl = new UserStyleDto();
            girl.setGuideStyle("CULTURE");
            girl.setLanguageFlag("Chinese");
            list.add(girl);
        }

        return list;
    }


    private List<UserStyleDto> userStyleDtoList_Default() {
        List<UserStyleDto> list = new ArrayList<>();

        //文化内涵
        {
            UserStyleDto girl = new UserStyleDto();
            girl.setGuideStyle("CULTURE");
            girl.setSexType("GIRL");
            girl.setLanguageFlag("Chinese");

            list.add(girl);
        }

        return list;
    }


    /**
     * AI对话流式异步 -- 仅用于后台数据缓存
     */
    public void justDoCacheWork(AiSpeechTextCmd cmd) {

        String syncId = IdUtil.getSnowflakeNextIdStr();
        syncId = cmd.isCacheFlag() + syncId;
        String finalSyncId = syncId;

        UserStyleDto userStyleDto = cmd.getUserStyleDto();

        SysUser user = userService.selectUserById(1l);

        ScenicSpot scenicSpot = scenicSpotCustomizeService.
                lambdaQuery()
                .eq(ScenicSpot::getId, Integer.parseInt(cmd.getScenicId()))
                .one();

        if (cmd.getType() == 2) {
            HobbyTypeEnum hobbyTypeEnum = HobbyTypeEnum.of(userStyleDto.getGuideStyle());

            List<String> tempList = Lists.newArrayList();

            tempList.add("现在我们来到{label}");
            tempList.add("现在我们来讲讲{label}");
            tempList.add("欢迎来到{label}");
            tempList.add("现在我要讲讲{label}");

            int index = RandomUtil.randomInt(0, tempList.size());

            Map<String, String> paramMap = Maps.newHashMap();
            paramMap.put("label", cmd.getScenicLabel());

            String temp = tempList.get(index);
            temp = StrUtil.format(temp, paramMap);

            String text = "请帮我介绍一下" + cmd.getScenicName() + "的" + cmd.getScenicLabel();
            text += "，请以 " + temp + " 开头进行回复。回复风格要求：" + hobbyTypeEnum.getDesc() + "，回复语言要求：";
            ;
            cmd.setText(text);
        } else if (cmd.getType() == 1) {
            HobbyTypeEnum hobbyTypeEnum = HobbyTypeEnum.of(userStyleDto.getGuideStyle());

            //景区简介
            String scenicName = cmd.getScenicName();

            String str = "请结合" + scenicName + "的特点，为我尽可能详细的介绍。不说废话，直接介绍。";
            str += "回复时请以：现在您可以点击图上的景点，让我进行深度讲解啦！结尾。回复风格要求：" + hobbyTypeEnum.getDesc() + "，回复语言要求：";

            cmd.setText(str);
        } else if (cmd.getType() == 3) {
            cmd.setText("介绍" + cmd.getScenicLabel() + "，");
        } else if (cmd.getType() == 0) {
            cmd.setText(cmd.getText() + " 回复语言要求：");
        }

        cmd.setText(cmd.getText() + getLanguageStr(userStyleDto));

        SysUser finalUser = user;
        UserStyleDto finalUserStyleDto = userStyleDto;

        if (cmd.isDefaultFlag()){
            ThreadUtil.safeSleep(1000);
        }else {
            int time = RandomUtil.randomInt(1000 * 3, 1000 * 20);
            ThreadUtil.safeSleep(time);
        }

        AiSpeechCmd aiSpeechCmd = new AiSpeechCmd();
        aiSpeechCmd.setUserId(String.valueOf(finalUser.getUserId()));
        aiSpeechCmd.setUserName(finalUser.getUserName());
        aiSpeechCmd.setVoiceId(cmd.getVoiceId());
        aiSpeechCmd.setText(cmd.getText());
        aiSpeechCmd.setScenicName(cmd.getScenicName());
        aiSpeechCmd.setSyncId(finalSyncId);
        aiSpeechCmd.setScenicId(cmd.getScenicId());
        aiSpeechCmd.setScenicLabel(cmd.getScenicLabel());
        aiSpeechCmd.setType(cmd.getType());
        aiSpeechCmd.setUserStyleDto(finalUserStyleDto);
        aiSpeechCmd.setLabelType(cmd.getLabelType());
        aiSpeechCmd.setProvinceName(scenicSpot.getProvinceName());
        aiSpeechCmd.setDistrictName(scenicSpot.getDistrictName());


        String dataStart = CacheConstants.DATA_START;
        String eventDelta = CacheConstants.EVENT_DELTA;
        String eventDone = CacheConstants.EVENT_DONE;
        String eventChatCompleted = CacheConstants.CHAT_COMPLETED;
        String eventMessageCompleted = CacheConstants.MESSAGE_COMPLETED;

        long time1 = System.currentTimeMillis();

        Response response = null;
        try {
            response = iScenicService.streamChatResponse(aiSpeechCmd);
        } catch (Exception e) {
            log.error("AI对话流式异步请求扣子异常", e);
            return;
        }
        if (response == null) {
            return;
        }

        // 获取响应实体
        BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream()));
        try {
            String line;
            String currentEventId = null;
            String eventType = null;

            List<String> punctuationMarks = Arrays.asList("。", "!", "！", "?", "？",
                    ",", "，", "~", "~ ", "；", ";", "：", ":", "•", "·");
            StringBuilder sb = new StringBuilder();
            boolean sentenceSegmentation = true;

            //循环检查中断状态
            while ((line = reader.readLine()) != null) {
                if (line.startsWith("event:")) {
                    String[] parts = line.split(":", 2);
                    eventType = parts[1];
                    eventType = "event:" + eventType;
                } else if (line.startsWith("id:")) {
                    String[] parts = line.split(":", 2);
                    currentEventId = parts[1];
                } else if (line.startsWith("data:")) {
                    String[] parts = line.split(":", 2);
                    String data = parts[1];
                    if (CacheConstants.EVENT_DELTA.equals(eventType)) {

                    } else if (CacheConstants.EVENT_DONE.equals(eventType)) {

                    } else if (CacheConstants.MESSAGE_COMPLETED.equals(eventType)) {
                        if (cmd.isCacheFlag()) {
                            String scenicId = cmd.getScenicId();
                            String scenicLabel = cmd.getScenicLabel();
                            String voiceId = cmd.getVoiceId();

                            cn.hutool.json.JSONObject json = JSONUtil.parseObj(data);
                            String type = json.getStr("type");
                            if (type.equals("answer")) {
                                String content = json.getStr("content");

                                if (cmd.getType()==1){
                                    this.saveLabelText(scenicSpot,finalUserStyleDto, content);
                                }else if (cmd.getType()==2){
                                    SysTouristLabel temp = sysTouristLabelMapper.selectByTouristIdAndLabel(cmd.getScenicLabel(), Long.parseLong(cmd.getScenicId()));
                                    if (temp != null) {
                                        this.saveLabelText(temp,HobbyTypeEnum.of(finalUserStyleDto.getGuideStyle()), finalUserStyleDto.getLanguageFlag(), content);
                                    }
                                }
                            }

                        }
                    } else if (CacheConstants.CHAT_COMPLETED.equals(eventType)) {

                    }
                }
            }
        } catch (Exception e) {
            log.error("处理 label 缓存请求异常", e);
        }

    }



    /**
     * AI对话流式异步 -- 仅用于后台数据缓存
     */
    private String justDoCacheWork_GetFullContent(AiSpeechTextCmd cmd) {

        String syncId = IdUtil.getSnowflakeNextIdStr();
        syncId = cmd.isCacheFlag() + syncId;
        String finalSyncId = syncId;

        UserStyleDto userStyleDto = cmd.getUserStyleDto();

        SysUser user = userService.selectUserById(1l);

        ScenicSpot scenicSpot = scenicSpotCustomizeService.
                lambdaQuery()
                .eq(ScenicSpot::getId, Integer.parseInt(cmd.getScenicId()))
                .one();

        if (cmd.getType() == 2) {
            HobbyTypeEnum hobbyTypeEnum = HobbyTypeEnum.of(userStyleDto.getGuideStyle());

            List<String> tempList = Lists.newArrayList();

            tempList.add("现在我们来到{label}");
            tempList.add("现在我们来讲讲{label}");
            tempList.add("欢迎来到{label}");
            tempList.add("现在我要讲讲{label}");

            int index = RandomUtil.randomInt(0, tempList.size());

            Map<String, String> paramMap = Maps.newHashMap();
            paramMap.put("label", cmd.getScenicLabel());

            String temp = tempList.get(index);
            temp = StrUtil.format(temp, paramMap);

            String text = "请帮我介绍一下" + cmd.getScenicName() + "的" + cmd.getScenicLabel();
            text += "，请以 " + temp + " 开头进行回复。回复风格要求：" + hobbyTypeEnum.getDesc() + "，回复语言要求：";
            ;
            cmd.setText(text);
        } else if (cmd.getType() == 1) {
            HobbyTypeEnum hobbyTypeEnum = HobbyTypeEnum.of(userStyleDto.getGuideStyle());

            //景区简介
            String scenicName = cmd.getScenicName();

            String str = "请结合" + scenicName + "的特点，为我尽可能详细的介绍。不说废话，直接介绍。";
            str += "回复时请以：现在您可以点击图上的景点，让我进行深度讲解啦！结尾。回复风格要求：" + hobbyTypeEnum.getDesc() + "，回复语言要求：";

            cmd.setText(str);
        } else if (cmd.getType() == 3) {
            cmd.setText("介绍" + cmd.getScenicLabel() + "，");
        } else if (cmd.getType() == 0) {
            cmd.setText(cmd.getText() + " 回复语言要求：");
        }

        cmd.setText(cmd.getText() + getLanguageStr(userStyleDto));

        SysUser finalUser = user;
        UserStyleDto finalUserStyleDto = userStyleDto;

        ThreadUtil.safeSleep(10);

        AiSpeechCmd aiSpeechCmd = new AiSpeechCmd();
        aiSpeechCmd.setUserId(String.valueOf(finalUser.getUserId()));
        aiSpeechCmd.setUserName(finalUser.getUserName());
        aiSpeechCmd.setVoiceId(cmd.getVoiceId());
        aiSpeechCmd.setText(cmd.getText());
        aiSpeechCmd.setScenicName(cmd.getScenicName());
        aiSpeechCmd.setSyncId(finalSyncId);
        aiSpeechCmd.setScenicId(cmd.getScenicId());
        aiSpeechCmd.setScenicLabel(cmd.getScenicLabel());
        aiSpeechCmd.setType(cmd.getType());
        aiSpeechCmd.setUserStyleDto(finalUserStyleDto);
        aiSpeechCmd.setLabelType(cmd.getLabelType());
        aiSpeechCmd.setProvinceName(scenicSpot.getProvinceName());
        aiSpeechCmd.setDistrictName(scenicSpot.getDistrictName());


        String dataStart = CacheConstants.DATA_START;
        String eventDelta = CacheConstants.EVENT_DELTA;
        String eventDone = CacheConstants.EVENT_DONE;
        String eventChatCompleted = CacheConstants.CHAT_COMPLETED;
        String eventMessageCompleted = CacheConstants.MESSAGE_COMPLETED;

        long time1 = System.currentTimeMillis();

        Response response = null;
        try {
            response = iScenicService.streamChatResponse(aiSpeechCmd);
        } catch (Exception e) {
            log.error("AI对话流式异步请求扣子异常", e);
            return "";
        }
        if (response == null) {
            return "";
        }

        // 获取响应实体
        BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream()));
        try {
            String line;
            String currentEventId = null;
            String eventType = null;

            //循环检查中断状态
            while ((line = reader.readLine()) != null) {
                if (line.startsWith("event:")) {
                    String[] parts = line.split(":", 2);
                    eventType = parts[1];
                    eventType = "event:" + eventType;
                } else if (line.startsWith("id:")) {
                    String[] parts = line.split(":", 2);
                    currentEventId = parts[1];
                } else if (line.startsWith("data:")) {
                    String[] parts = line.split(":", 2);
                    String data = parts[1];
                    if (CacheConstants.EVENT_DELTA.equals(eventType)) {

                    } else if (CacheConstants.EVENT_DONE.equals(eventType)) {

                    } else if (CacheConstants.MESSAGE_COMPLETED.equals(eventType)) {
                            cn.hutool.json.JSONObject json = JSONUtil.parseObj(data);
                            String type = json.getStr("type");
                            if (type.equals("answer")) {
                                String content = json.getStr("content");
                                return content;
                            }

                    } else if (CacheConstants.CHAT_COMPLETED.equals(eventType)) {

                    }
                }
            }
        } catch (Exception e) {
            log.error("处理 label 缓存请求异常", e);
        }

        return "";
    }


    public void saveLabelText(SysTouristLabel sysTouristLabel, HobbyTypeEnum style, String language, String labelText) {
        log.info("保存解说词，景区ID：[{}] 景区名称：[{}] labelName: [{}], 风格：[{}]，语言：[{}]，labelText：{}", sysTouristLabel.getTouristId(),
                sysTouristLabel.getTouristName(), sysTouristLabel.getLabelName(), style.getDesc(), language, labelText);

        LabelTextDto labelTextDto = new LabelTextDto();
        labelTextDto.setTouristId(sysTouristLabel.getTouristId() + "");
        labelTextDto.setTouristName(sysTouristLabel.getTouristName());
        labelTextDto.setLabelName(sysTouristLabel.getLabelName());
        labelTextDto.setLanguage(language);
        labelTextDto.setLabelText(labelText);
        labelTextDto.setStyle(style.getCode());
        labelTextDto.setUpdateTimeStr(DateUtil.now());

        JSONArray arr = null;
        String oldLabelText = sysTouristLabel.getLabelText();
        try {
            arr = JSONUtil.parseArray(oldLabelText);
        } catch (Exception e) {
        }
        if (ArrayUtil.isEmpty(arr)) {
            List<LabelTextDto> list = new ArrayList<>();
            list.add(labelTextDto);
            sysTouristLabel.setLabelText(JSONUtil.toJsonStr(list));
        } else {
            arr.add(labelTextDto);
            if (arr.size() == 8) {
                sysTouristLabel.setCacheOk(1);
            }
            sysTouristLabel.setLabelText(JSONUtil.toJsonStr(arr));
        }

        sysTouristLabelMapper.updateSysTouristLabel(sysTouristLabel);

        if (sysTouristLabel.getCacheOk()==1){
            labelAudioCacheTask.enqueue(sysTouristLabel);
        }
    }


    public void saveLabelText(ScenicSpot one, UserStyleDto userStyleDto, String labelText) {
        log.info("保存简介解说词，景区ID：[{}] 景区名称：[{}] , 风格：[{}]，语言：[{}]，labelText：{}", one.getId(),
                one.getName(), userStyleDto.getGuideStyle(), userStyleDto.getLanguageFlag(), labelText);

        Integer id = one.getId();

        LabelTextDto labelTextDto = new LabelTextDto();
        labelTextDto.setTouristId(id+"");
        labelTextDto.setTouristName(one.getName());
        labelTextDto.setLabelName("");
        labelTextDto.setLanguage(userStyleDto.getLanguageFlag());
        labelTextDto.setLabelText(labelText);
        labelTextDto.setStyle(userStyleDto.getGuideStyle());
        labelTextDto.setUpdateTimeStr(DateUtil.now());


        JSONArray arr = null;
        String oldLabelText = one.getLabelText();
        try {
            arr = JSONUtil.parseArray(oldLabelText);
        } catch (Exception e) {
        }
        if (ArrayUtil.isEmpty(arr)) {
            List<LabelTextDto> list = new ArrayList<>();
            list.add(labelTextDto);
            scenicSpotCustomizeMapper.updateLabelText(id,JSONUtil.toJsonStr(list));
        } else {
            arr.add(labelTextDto);
            if (arr.size() == 8) {
                scenicSpotCustomizeMapper.updateCacheOk(id,1);
            }
            scenicSpotCustomizeMapper.updateLabelText(id,JSONUtil.toJsonStr(arr));

            if (arr.size() == 8){
                touristDetailAudioCacheTask.enqueue(one);
            }
        }

    }


    private String getLanguageStr(UserStyleDto userStyleDto) {
        /**
         * 中文 Chinese
         * 英语 English
         * 日语 Japanese
         * 西班牙语 Spanish
         * 俄语 Russian
         * 韩语 Korean
         */
        String languageFlag = userStyleDto.getLanguageFlag();
        String str = switch (languageFlag) {
            case "English" -> "使用英文回复。";
            case "Japanese" -> "使用日语回复。";
            case "Spanish" -> "使用西班牙语回复。";
            case "Russian" -> "使用俄语回复。";
            case "Korean" -> "使用韩语回复。";
            default -> "使用中文回复。";
        };
        return str;
    }
}
