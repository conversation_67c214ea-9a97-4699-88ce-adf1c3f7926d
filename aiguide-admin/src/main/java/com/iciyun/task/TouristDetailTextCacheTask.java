package com.iciyun.task;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.iciyun.common.core.domain.LabelTextDto;
import com.iciyun.common.utils.ConcurrentQueueUtil;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.SysTouristLabel;
import com.iciyun.system.mapper.ScenicSpotCustomizeMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 景区简介生成 任务
 * <AUTHOR> on 2025-06-08 10:39.
 */
@Slf4j
@Component
public class TouristDetailTextCacheTask {


    @Autowired
    ScenicSpotCustomizeMapper scenicSpotCustomizeMapper;

    @Autowired
    ScenicCacheService scenicCacheService;

    ConcurrentQueueUtil<ScenicSpot> concurrentQueueUtil = new ConcurrentQueueUtil<ScenicSpot>();

    public void enqueue(ScenicSpot scenicSpot) {
        concurrentQueueUtil.enqueue(scenicSpot);
    }
    public void clear() {
        concurrentQueueUtil.clear();
    }

    public void doCache(){
        ScenicSpot one = null;

        if (concurrentQueueUtil.isEmpty()){
            one = scenicSpotCustomizeMapper.getOneNoCacheOk();
        }else {
            one = concurrentQueueUtil.dequeue();
        }
        if (one!=null){
            dealData(one);
        }

    }

    private void dealData(ScenicSpot one){
        List<LabelTextDto> labelTextDtoList = Lists.newArrayList();
        String labelText = one.getLabelText();
        if (StrUtil.isNotEmpty(labelText)) {
            JSONArray tempArr = JSONUtil.parseArray(labelText);
            labelTextDtoList = tempArr.stream().map(item -> {
                JSONObject obj = JSONUtil.parseObj(item);
                LabelTextDto labelTextDto = new LabelTextDto();
                labelTextDto.setTouristId(obj.getStr("touristId"));
                labelTextDto.setTouristName(obj.getStr("touristName"));
                labelTextDto.setLabelName(obj.getStr("labelName"));
                labelTextDto.setLabelText(obj.getStr("labelText"));
                labelTextDto.setKey(obj.getStr("key"));
                labelTextDto.setStyle(obj.getStr("style"));
                labelTextDto.setLanguage(obj.getStr("language"));
                return labelTextDto;
            }).collect(Collectors.toList());
        }
        scenicCacheService.doCacheWork(one, labelTextDtoList);
    }


}
