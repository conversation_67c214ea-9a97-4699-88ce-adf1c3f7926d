package com.iciyun.task;

import com.iciyun.system.service.IUsUserSortService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("uscTask")
@Slf4j
public class UserSortChangeTask {

    @Autowired
    private IUsUserSortService ususerSortService;

    /**
     * 定期修改用户排名
     */
    public void changeSort() {
        log.info("定时修改用户排名-start");
        ususerSortService.updateUserSort();
        log.info("定时修改用户排名-end");
    }
}
