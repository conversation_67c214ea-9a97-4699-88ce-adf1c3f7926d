package com.iciyun.task;

import com.iciyun.system.service.ISysBaseStService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("sysBaseStTask")
@Slf4j
public class SysBaseStTask {

    @Autowired
    private ISysBaseStService sysBaseStService;

    /**
     * 每日统计
     */
    public void dateStTask() {
        log.info("每日统计-start");
        try {
            sysBaseStService.dateStTask();
        } catch (Exception e) {
            log.error("每日统计异常", e);
        }
        log.info("每日统计-end");
    }
}
