package com.iciyun.task;

import com.iciyun.system.service.IGuidePayOrderService;
import com.iciyun.system.service.ISysBaseStService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("userOrderBingTask")
@Slf4j
public class UserOrderBingTask {

    @Autowired
    private IGuidePayOrderService guidePayOrderService;

    /**
     * 每日统计
     */
    public void bingOrder() {
        log.info("更新用户订单中的username-start");
        try {
            guidePayOrderService.bingOrder();
        } catch (Exception e) {
            log.error("更新用户订单中的username", e);
        }
        log.info("更新用户订单中的username-end");
    }
}
