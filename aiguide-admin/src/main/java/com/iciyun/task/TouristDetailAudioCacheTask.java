package com.iciyun.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.iciyun.common.core.domain.LabelTextDto;
import com.iciyun.common.core.domain.model.UserStyleDto;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.common.utils.ConcurrentQueueUtil;
import com.iciyun.common.utils.HelpMe;
import com.iciyun.common.utils.StringUtils;
import com.iciyun.common.utils.tts.TTSClient;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.SysTouristLabel;
import com.iciyun.system.mapper.ScenicSpotCustomizeMapper;
import com.iciyun.system.service.GenAudioService;
import com.iciyun.system.service.IGuideService;
import com.iciyun.system.service.ISysUserService;
import com.iciyun.system.service.ScenicCacheManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.EOFException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 景区简介音频 任务
 * <AUTHOR> on 2025-06-08 10:39.
 */
@Slf4j
@Component
public class TouristDetailAudioCacheTask {

    @Autowired
    ScenicSpotCustomizeMapper scenicSpotCustomizeMapper;
    @Autowired
    ScenicCacheService scenicCacheService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    ISysUserService sysUserService;
    @Autowired
    private GenAudioService genAudioService;
    @Autowired
    ScenicCacheManager scenicCacheManager;
    @Autowired
    GenAudioBySexTask genAudioBySexTask;

    TTSClient ttsClient = null;
    TTSClient ttsClient2 = null;

    ConcurrentQueueUtil<ScenicSpot> concurrentQueueUtil = new ConcurrentQueueUtil<ScenicSpot>();

    public void enqueue(ScenicSpot scenicSpot) {
        concurrentQueueUtil.enqueue(scenicSpot);
    }
    public void clear() {
        concurrentQueueUtil.clear();
    }
    public void doCache(){
        if (genAudioBySexTask.isWorking())return;//PC后台手动操作的优先
        if (ttsClient==null){
            ttsClient = new TTSClient("https://tts.ideepyou.com/tts");
        }

        ScenicSpot one = null;

        if (concurrentQueueUtil.isEmpty()){
            one = scenicSpotCustomizeMapper.getOneNoAudioCacheOk();
        }else {
            one = concurrentQueueUtil.dequeue();
        }

        if (one!=null){
            genAudio(one,1);
        }
    }

    public void doCache2(){
        if (genAudioBySexTask.isWorking())return;//PC后台手动操作的优先
        if (ttsClient2==null){
            ttsClient2 = new TTSClient("http://115.29.176.94:8006/tts");
        }

        ScenicSpot one = null;

        if (concurrentQueueUtil.isEmpty()){
            one = scenicSpotCustomizeMapper.getOneNoAudioCacheOk();
        }else {
            one = concurrentQueueUtil.dequeue();
        }

        if (one!=null){
            genAudio(one,2);
        }
    }


    private void genAudio(ScenicSpot one,Integer type) {
        List<LabelTextDto> list = parseLabelText(one);
        int successCount = 0;
        for (LabelTextDto item : list) {
            Boolean flag1 = genAudioBySex(item, "GIRL",type);
            if (flag1){
                successCount++;
            }
            Boolean flag2 = genAudioBySex(item, "BOY",type);
            if (flag2){
                successCount++;
            }
        }
        if (successCount==list.size()*2){
            //全部成功
            scenicSpotCustomizeMapper.updateAudioCacheOk(one.getId(),1);
        }
    }

    private Boolean genAudioBySex(LabelTextDto labelTextDto, String sexType,Integer type) {

        UserStyleDto userStyleDto = new UserStyleDto();
        userStyleDto.setSexType(sexType);
        userStyleDto.setLanguageFlag(labelTextDto.getLanguage());
        userStyleDto.setGuideStyle(labelTextDto.getStyle());

        String scenicId = labelTextDto.getTouristId();
        String scenicLabel = labelTextDto.getLabelName();
        String scenicName = labelTextDto.getTouristName();
        String voiceId = sysUserService.getVoiceId(userStyleDto);
        String content = labelTextDto.getLabelText();

        String labelKay = scenicCacheManager.getCacheKey(userStyleDto, scenicId, scenicLabel, voiceId);

        String voiceArrStr = redisCache.getCacheObject(labelKay);
        if (StrUtil.isNotEmpty(voiceArrStr)) {
            log.info("缓存 {}, 已存在", labelKay);
            return true;
        }
        if (content.contains("https://github.com/liu-qi-666/liu-qi-666.github.io")){
            return true;
        }
        if (content.length()>1000){
            return true;
        }

        content = HelpMe.replaceContent(content);

        ThreadUtil.safeSleep(1000);

        TTSClient ttsClientTemp = ttsClient;
        if (type==2){
            ttsClientTemp = ttsClient2;
        }

        String filePath = genAudioService.genAudio(ttsClientTemp,userStyleDto.getLanguageFlag(),voiceId,content);

        if (StrUtil.isNotEmpty(filePath)){
            log.info("缓存 [{}] 的 简介，key ：{}", scenicName, labelKay);

            redisCache.setCacheObject(labelKay, filePath);

            return true;
        }
        return false;
    }


    private List<LabelTextDto> parseLabelText(ScenicSpot one) {
        String labelText = one.getLabelText();
        List<LabelTextDto> list = Lists.newArrayList();
        if (StrUtil.isNotEmpty(labelText)) {
            JSONArray arr = JSONUtil.parseArray(labelText);
            list = arr.stream().map(item -> {
                LabelTextDto labelTextDto = new LabelTextDto();
                JSONObject obj = JSONUtil.parseObj(item);
                labelTextDto.setTouristId(obj.getStr("touristId"));
                labelTextDto.setTouristName(obj.getStr("touristName"));
                labelTextDto.setLabelName(obj.getStr("labelName"));
                labelTextDto.setLanguage(obj.getStr("language"));
                labelTextDto.setStyle(obj.getStr("style"));
                labelTextDto.setLabelText(obj.getStr("labelText"));
                return labelTextDto;
            }).collect(Collectors.toList());
        }
        return list;
    }


}
