# 项目相关配置
sysinfo:
  # 名称
  name: Ai-Guide
  # 版本
  version: 0.0.1
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/ugs/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /opt/deploy/uploadDir
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 9898
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.iciyun: debug
    org.springframework: warn
    #添加配置
    com.ying: debug
  my:
    log:
      path: /opt/deploy/logs
# 用户配置
user:
  password:
    default: 123456.
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 1
  code:
    # 验证码有效期（天）
    expireTime: 1

# Spring配置
spring:
  datasource:
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        master:
          url: ***********************************************************************
          username: ideepyou
          password: ZbNyE2FBZExpubLr
          driver-class-name: org.postgresql.Driver
        slave:
          url: *************************************************************************
          username: ideepyou
          password: ZbNyE2FBZExpubLr
          driver-class-name: org.postgresql.Driver
    druid:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: aiGuide
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages

  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 100MB
      # 设置总上传的文件大小
      max-request-size: 200MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false
  data:
    # redis 配置
    redis:
      # 地址
      host: redis-cnlfc6zgdtwwstluz.redis.ivolces.com
      # 端口，默认为6379
      port: 6379
      # 数据库索引
      database: 2
      # 密码
      password: iExYnmCsxE4uPi74
      # 连接超时时间
      timeout: 10s
      lettuce:
        pool:
          # 连接池中的最小空闲连接
          min-idle: 0
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池的最大数据库连接数
          max-active: 8
          # #连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyz
  # 令牌有效期 30天
  expireTime: 43200

# PageHelper分页插件
pagehelper:
  helperDialect: postgresql
  supportMethodsArguments: true
  params: count=countSql

# Springdoc配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
  group-configs:
    - group: 'default'
      display-name: '测试模块'
      paths-to-match: '/**'
      packages-to-scan: com.iciyun.web.controller.tool

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# MyBatis Plus配置
mybatis-plus:
  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级
  # 例如 com.**.**.mapper
  mapperPackage: com.iciyun.**.mapper
  # 对应的 XML 文件位置
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.iciyun.**.domain
  # 启动时是否检查 MyBatis XML 文件的存在，默认不检查
  checkConfigLocation: false
  configuration:
    # 自动驼峰命名规则（camel case）映射
    mapUnderscoreToCamelCase: true
    # MyBatis 自动映射策略
    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射
    autoMappingBehavior: PARTIAL
    # MyBatis 自动映射时未知列或未知属性处理策
    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息
    autoMappingUnknownColumnBehavior: NONE
    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl
    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl
    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl
    logImpl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    # 是否打印 Logo banner
    banner: false
    dbConfig:
      # 主键类型
      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID
      idType: ASSIGN_ID
      # 逻辑已删除值
      logicDeleteValue: 2
      # 逻辑未删除值
      logicNotDeleteValue: 0
      # 字段验证策略之 insert,在 insert 的时候的字段验证策略
      # IGNORED 忽略 NOT_NULL 非NULL NOT_EMPTY 非空 DEFAULT 默认 NEVER 不加入 SQL
      insertStrategy: NOT_NULL
      # 字段验证策略之 update,在 update 的时候的字段验证策略
      updateStrategy: NOT_NULL
      # 字段验证策略之 select,在 select 的时候的字段验证策略既 wrapper 根据内部 entity 生成的 where 条件
      where-strategy: NOT_NULL

amap:
  apiKey: bdc78bfe0ec2cbd2ed38b2a81ab269f5

tmap:
  apiKey: E4UBZ-B3QKL-JP6PS-MY6T4-K5NI5-X4BHR

# oss
oss:
  accessKeyId: AKLTZTViMGIwM2UyOTZjNDhlMWFhYzA4YzAyODJkZGVjMTM
  accessKeySecret: WWpCa05HUm1abVV3TmpNMk5ETXdaR0UyWXpaalptTXlNVEl6TTJVMk16TQ==
  region: cn-beijing
  endpoint: tos-cn-beijing.ivolces.com #改为使用内网地址
  endpoint_bf: tos-cn-beijing.volces.com #播放地址
  bucketName: ai-guide

# wx 小程序
wx:
  miniapp:
    appid: wx17624dfe6ba6fc66
    secret: 04d863ee2fa63f7e6aaef3ee911db5a5
    token: token
    aesKey: EncodingAESKey
    msgDataFormat: JSON
    config-storage:
      type: RedisTemplate
      redis:
        host: redis-cnlfc6zgdtwwstluz.redis.ivolces.com
        port: 6379
        password: iExYnmCsxE4uPi74
        database: 3
      httpClientType: OkHttp
  mp:
    appid: wx17624dfe6ba6fc66
    secret: 04d863ee2fa63f7e6aaef3ee911db5a5
    token: token
    aesKey: EncodingAESKey
    config-storage:
      type: RedisTemplate
      redis:
        host: redis-cnlfc6zgdtwwstluz.redis.ivolces.com
        port: 6379
        password: iExYnmCsxE4uPi74
        database: 3
      httpClientType: OkHttp
    notifyUrl: https://dev.b2b.iyunsulian.net/integration/wxmp/authNotify
    successUrl: https://dev.mobile.iyunsulian.net/#/login
    errorUrl: https://dev.mobile.iyunsulian.net/#/errorPage
  pay:
    appId: wx17624dfe6ba6fc66
    mchId: 1714969757 #商户id
    apiV3Key: AYfaQ4FpRVehv2TBSPehQ5sopv2DyAi0 #V3密钥
    certSerialNo: 3879EF176E3C2F3BE732115884C325B158BBB13F
    privateKeyPath: classpath:cert/pro/apiclient_key.pem #apiclient_key.pem证书文件的绝对路径或者以classpath:开头的类路径
    privateCertPath: classpath:cert/pro/apiclient_cert.pem #apiclient_cert.pem证书文件的绝对路径或者以classpath:开头的类路径
    notifyOrderUrl: https://ai.ideepyou.com/prod-api/wxpay/notify/order
    notifyRefundUrl: https://ai.ideepyou.com/prod-api/wxpay/notify/refund

# bot
coze:
  img2label:
    bot_id: 7485964668297871410
  jwt:
    oauth:
      base_url: https://api.coze.cn
      client_id: 1141689231679
      private_key: MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQC+bhaczD1NfLw++6o5Rh2CfV1d6o6hli9CyBBXuSm385I1vVy1umzjK+jyHlOwE8e3D/BQrTUpRaf6hYEIywKcWwSPDRM6Oo1ScBVV0xAa9BNXeMpbOolomuMpoQJXbvx2Rbg5N2s+kZHRFntYEo5NkwHz1ZAChDTuWw5dU8Lg4MjTIyaYfjVqT/fIUoTd2Ne6k3kGVIrsMu/ftZOlZmgBEqPAiSFlN8/SQo7+UORRBFlgtt69aOtZj30V+3qiZGJ2NG7EHNDnbye+0Tve+0QiJoVuiUTkRNiIiLgISD5fO0YO0DJz0lQHM2ZmC7k2Kfbg+HqbFpqDIPKSsq/ExfN5AgMBAAECggEAAT/HCPHi/mxi1PZMzMJLeYsLKSK9vUp9mbCEuHVj2p/w1lN0uK/QoQTiqLNSaOU2UhqY+ENw3VsZtPjCiJTKZKsnAfphJ0zWEv9aNBM+WM9QpBCW0OMqFhj/iFFhwCzbTukRpoyhiecBbkjGSytYUwCxhTGRG/0dzgCOuMXQ4E+5uolsZy/cP+AGyVZMHfda3yDnwfhOlR1o3CBWjZv/raDMVBzph47/1gZIIsB4eN5FS9AUOXEPJS/6W5xj01r93piN0E2Okp2PbIixO2rtjggUAC74U7jhYIoJ1PVtPt+wK5re/ZODchhBE7UnOB/4YV/uzDjSIJQIBVN7pA/5YQKBgQDmGUUp6eKKysqyXOr0SWC4yRLwLSiC/XUM+lv+gG0W5TryTXBsoK2uwk+t0Cf7papQoRxxNKwHBmujip+jH8xphYSjMQfS//7ejS9wjVjwLCds4vvBjbIKTRXtT6yheTGGlsryVtHI7GL71TB/zsyBwXEigeIH/2rfqEu+snduoQKBgQDT3bCqaaQs4JfWwLv6rgru+IrwbiRm9hDtxGUuppF1k7XvL7EHf/m5T+ohK+Sj9yDT89GHcODJ1kelpLLT02R919NV+CVO3l9TUNsyGdrMMkfyJLe2d2VaxKl2BKIPwpSw9SejYnMmI1MqOuNWOBf8BshnZR2Oh7nJUUW1LAsN2QKBgQCNRWSWeDeTuTA71BqZA1gz+5f6B+/AhLbR0gCbP+Q6U2EelPb6aqhYDIr5Dz0NHshzmoco5grcgU+i6CBc+c/51XT68MZ6AJxNrWc79jxtsN4/1xh03Hc8JdnZirpVpWAH3xsZML66Wo/nSBvvzFr5K3g3lIDfIi1OxjhDC7WSIQKBgQCr+T5nBXtTRjdaBXZQxCdx1OOKyAzaWpBhP9LV7DUUWIMMlQzAxruhFWI57NQZ2AaYpUgGBbUhgMMFjMvvr57Zm3AbT2KRQ2XjTjWu1FfK+mF2ByHshbwK0qmvd5FI86wYe9biA47ufSwLkmNoX/3wAF15uBUm1bsT09medxK9+QKBgQCkkiJcFNPMv7THNuRuJ2CyKNkqOnGy5tPl4V1veT/e0l1Z6+sNYWXTrzLYQQ2jMXMWqbCejsEIWEEHCOCgjGsiME+9g9h4ucVxnO8XTjHHUFiBEhzzkhjBTYIy+ZArLA3DtdlMUX8O/PV9It4j7Cdg5T4QKZSe3ZY0nsOXDPdIfQ==
      public_key_id: SgEKfphGQo2QS-uCsr97EjM3XLw1z1Vjh_Mb9YUI2Jo
  app:
    bot_id_common: 7496303824152477723

# img-match
ai:
  match_first:
    img_url: https://test.img-search.iciyun.net/query
    max_distance: 0.7
  match_second:
    img_url: https://image.ideepyou.com/api/v1/image_similarity
    max_distance: 0.1
  add_second:
    add_url: https://image.ideepyou.com/api/v1/add_scenic_area

# scenise
scenise:
  label:
    cache_time: 30

baidu:
  appId: 118994153
  apiKey: iR3UBwwSszWpWJchmXVjBYIs
  apiSecret: HEdRRoC21MAIOarRb238DVb5bD4Mk1Li

qr:
  url: https://test.guide.iciyun.net/prod-api/common/downloadTxt/test
  logoUrl: applet/ideepyou.jpg
  logoPath: /opt/deploy/downDir
  qrPath: /opt/deploy/uploadDir

order:
  agency_id: QD20250521000001

cj_data:
  dir: /opt/deploy/pic
