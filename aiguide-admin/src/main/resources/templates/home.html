<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>景区信息管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <!-- 引入腾讯地图API -->
<!--    <script src="https://map.qq.com/api/js?v=2.exp&key=E4UBZ-B3QKL-JP6PS-MY6T4-K5NI5-X4BHR"></script>-->
    <script charset="utf-8" src="https://map.qq.com/api/gljs?v=1.exp&key=E4UBZ-B3QKL-JP6PS-MY6T4-K5NI5-X4BHR"></script>

    <!-- Tailwind配置 -->
    <script>
        tailwind.config = {
          theme: {
            extend: {
              colors: {
                primary: '#165DFF',
                secondary: '#36CFC9',
                neutral: {
                  100: '#F5F7FA',
                  200: '#E5E6EB',
                  300: '#C9CDD4',
                  400: '#86909C',
                  500: '#4E5969',
                  600: '#272E3B',
                  700: '#1D2129',
                }
              },
              fontFamily: {
                inter: ['Inter', 'sans-serif'],
              },
            },
          }
        }
    </script>

    <style type="text/tailwindcss">
        @layer utilities {
          .content-auto {
            content-visibility: auto;
          }
          .shadow-soft {
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
          }
        }
    </style>
</head>
<body class="bg-neutral-100 font-inter text-neutral-700 min-h-screen flex flex-col">
<!-- 顶部导航 -->
<header class="bg-white shadow-md sticky top-0 z-50">
    <div class="container mx-auto px-4 py-4 flex justify-center">
        <div class="flex items-center space-x-2">
            <i class="fa fa-map-marker text-primary text-2xl"></i>
            <h1 class="text-xl font-bold text-neutral-700">景区信息管理系统</h1>
        </div>
    </div>
</header>

<!-- 主内容区 -->
<main class="flex-grow container mx-auto px-4 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 查询区域 -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-xl shadow-soft p-6 transition-all duration-300 hover:shadow-md">
                <h2 class="text-xl font-bold mb-4 flex items-center">
                    <i class="fa fa-search text-primary mr-2"></i>景区查询
                </h2>
                <!-- 修改后的搜索表单 -->
                <form id="searchForm" class="space-y-4" method="get">
                    <div>
                        <label for="scenicName" class="block text-sm font-medium text-neutral-500 mb-1">景区名称</label>
                        <div class="relative">
                            <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-neutral-400">
                              <i class="fa fa-map-marker"></i>
                            </span>
                            <input
                                    type="text"
                                    id="scenicName"
                                    name="scenicName"
                                    placeholder="请输入景区名称"
                                    class="pl-10 block w-full rounded-lg border border-neutral-200 bg-neutral-50 py-2.5 px-4 text-sm placeholder:text-neutral-400 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all"
                            >
                        </div>
                        <!-- 错误提示 -->
                        <p id="searchError" class="text-red-500 text-xs mt-1 hidden">请输入景区名称</p>
                    </div>
                    <div class="pt-2">
                        <button
                                type="button"
                                id="searchBtn"
                                class="w-full bg-primary text-white py-2.5 px-4 rounded-lg transition-all hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary/30 focus:ring-offset-2 flex items-center justify-center"
                        >
                            <i class="fa fa-search mr-2"></i>查询景区
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 表单区域 -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-xl shadow-soft p-6 transition-all duration-300 hover:shadow-md">
                <h2 class="text-xl font-bold mb-4 flex items-center">
                    <i class="fa fa-edit text-primary mr-2"></i>景区信息表单
                </h2>

                <!-- 状态提示 -->
                <div id="statusMessage" class="mb-4 hidden">
                    <div id="successMsg" class="hidden bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-center">
                        <i class="fa fa-check-circle mr-2"></i>
                        <span id="successText">操作成功!</span>
                    </div>
                    <div id="errorMsg" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-center">
                        <i class="fa fa-exclamation-circle mr-2"></i>
                        <span id="errorText">操作失败，请重试!</span>
                    </div>
                </div>

                <form id="scenicForm" class="space-y-4" method="post">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="scenicId" class="block text-sm font-medium text-neutral-500 mb-1">景区ID</label>
                            <div class="relative">
                              <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-neutral-400">
                                <i class="fa fa-id-card"></i>
                              </span>
                                <input
                                        type="text"
                                        id="scenicId"
                                        name="scenicId"
                                        placeholder="系统自动生成"
                                        disabled
                                        class="pl-10 block w-full rounded-lg border border-neutral-200 bg-neutral-50 py-2.5 px-4 text-sm placeholder:text-neutral-400 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all"
                                        th:value="${scenic?.id ?: ''}"
                                >
                            </div>
                        </div>
                        <div>
                            <label for="checkTime" class="block text-sm font-medium text-neutral-500 mb-1">景区校对时间</label>
                            <div class="relative">
                              <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-neutral-400">
                                <i class="fa fa-id-card"></i>
                              </span>
                                <input
                                        type="text"
                                        id="checkTime"
                                        name="checkTime"
                                        placeholder="景区校对时间"
                                        disabled
                                        class="pl-10 block w-full rounded-lg border border-neutral-200 bg-neutral-50 py-2.5 px-4 text-sm placeholder:text-neutral-400 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all"
                                        th:value="${scenic?.checkTime ?: ''}"
                                >
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">

                        <div>
                            <label for="formScenicName" class="block text-sm font-medium text-neutral-500 mb-1">景区名称</label>
                            <div class="relative">
                                  <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-neutral-400">
                                    <i class="fa fa-map-marker"></i>
                                  </span>
                                <input
                                        type="text"
                                        id="formScenicName"
                                        name="scenicName"
                                        placeholder="请输入景区名称"
                                        class="pl-10 block w-full rounded-lg border border-neutral-200 bg-white py-2.5 px-4 text-sm placeholder:text-neutral-400 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all"
                                        th:value="${scenic?.name ?: ''}"
                                >
                            </div>
                        </div>

                        <div>
                            <label for="tMapScenicName" class="block text-sm font-medium text-neutral-500 mb-1">腾讯地图景区名称</label>
                            <div class="relative">
                                  <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-neutral-400">
                                    <i class="fa fa-map-marker"></i>
                                  </span>
                                <input
                                        type="text"
                                        id="tMapScenicName"
                                        name="tMapScenicName"
                                        placeholder="请输入腾讯地图景区名称"
                                        class="pl-10 block w-full rounded-lg border border-neutral-200 bg-white py-2.5 px-4 text-sm placeholder:text-neutral-400 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all"
                                        th:value="${scenic?.tMapScenicName ?: ''}"
                                >
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="longitude" class="block text-sm font-medium text-neutral-500 mb-1">经度</label>
                            <div class="relative">
                              <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-neutral-400">
                                <i class="fa fa-compass"></i>
                              </span>
                                <input
                                        type="number"
                                        step="0.000001"
                                        id="longitude"
                                        name="longitude"
                                        placeholder="请输入经度"
                                        disabled
                                        class="pl-10 block w-full rounded-lg border border-neutral-200 bg-neutral-50 py-2.5 px-4 text-sm placeholder:text-neutral-400 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all"
                                        th:value="${scenic?.longitude ?: ''}"
                                >
                            </div>
                        </div>
                        <div>
                            <label for="latitude" class="block text-sm font-medium text-neutral-500 mb-1">纬度</label>
                            <div class="relative">
                  <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-neutral-400">
                    <i class="fa fa-compass"></i>
                  </span>
                                <input
                                        type="number"
                                        step="0.000001"
                                        id="latitude"
                                        name="latitude"
                                        placeholder="请输入纬度"
                                        disabled
                                        class="pl-10 block w-full rounded-lg border border-neutral-200 bg-neutral-50 py-2.5 px-4 text-sm placeholder:text-neutral-400 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all"
                                        th:value="${scenic?.latitude ?: ''}"
                                >
                            </div>
                        </div>
                    </div>

                    <!-- 新增的景区地址字段 -->
                    <div>
                        <label for="detailAddress" class="block text-sm font-medium text-neutral-500 mb-1">景区地址</label>
                        <div class="relative">
                            <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-neutral-400">
                              <i class="fa fa-map-pin"></i>
                            </span>
                            <input
                                    type="text"
                                    id="detailAddress"
                                    name="detailAddress"
                                    placeholder="请输入景区地址"
                                    disabled
                                    class="pl-10 block w-full rounded-lg border border-neutral-200 bg-neutral-50 py-2.5 px-4 text-sm placeholder:text-neutral-400 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all"
                            >
                        </div>
                    </div>

                    <div>
                        <label for="layerIndex" class="block text-sm font-medium text-neutral-500 mb-1">图层索引</label>
                        <div class="relative">
                        <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-neutral-400">
                          <i class="fa fa-layers"></i>
                        </span>
                            <input
                                    type="number"
                                    id="layerIndex"
                                    name="layerIndex"
                                    placeholder="请输入图层索引"
                                    class="pl-10 block w-full rounded-lg border border-neutral-200 bg-white py-2.5 px-4 text-sm placeholder:text-neutral-400 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all"
                                    th:value="${scenic?.layerIndex ?: ''}"
                            >
                        </div>
                    </div>



                    <!-- 地图预览 - 修改高度为 h-96 (384px) -->
                    <div class="mt-4">
                        <h3 class="text-sm font-medium text-neutral-500 mb-2">位置预览</h3>
                        <div id="mapContainer" class="h-[512px] bg-neutral-100 rounded-lg overflow-hidden border border-neutral-200 relative">
                            <div id="mapLoading" class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
                                <div class="text-center">
                                    <i class="fa fa-spinner fa-spin text-4xl text-primary mb-2"></i>
                                    <p>加载地图中...</p>
                                </div>
                            </div>
<!--                            <div id="emptyMapState" class="absolute inset-0 flex items-center justify-center text-neutral-400">-->
<!--                                <div class="text-center">-->
<!--                                    <i class="fa fa-map text-4xl mb-2"></i>-->
<!--                                    <p>输入经纬度后显示地图预览</p>-->
<!--                                </div>-->
<!--                            </div>-->
                            <!-- 腾讯地图将渲染在这里 -->
                        </div>
                    </div>

                    <div class="flex space-x-3 pt-2">
                        <button
                                type="button"
                                id="resetBtn"
                                class="flex-1 bg-neutral-100 text-neutral-700 py-2.5 px-4 rounded-lg transition-all hover:bg-neutral-200 focus:outline-none focus:ring-2 focus:ring-neutral-300 focus:ring-offset-2 flex items-center justify-center"
                        >
                            <i class="fa fa-refresh mr-2"></i>重置
                        </button>
                        <button
                                type="submit"
                                id="saveBtn"
                                class="flex-1 bg-primary text-white py-2.5 px-4 rounded-lg transition-all hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary/30 focus:ring-offset-2 flex items-center justify-center"
                        >
                            <i class="fa fa-save mr-2"></i>保存信息
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</main>

<!-- 页脚 -->
<footer class="bg-white border-t border-neutral-200 py-6 mt-12">
    <div class="container mx-auto px-4 text-center">
        <p class="text-neutral-500 text-sm">© 2025 景区信息管理系统. 保留所有权利</p>
    </div>
</footer>

<!-- JavaScript -->
<script>

    // 全局变量存储地图实例
    let map = null;
    let marker = null;

    // DOM 加载完成后执行
    document.addEventListener('DOMContentLoaded', () => {
      const searchForm = document.getElementById('searchForm');
      const scenicForm = document.getElementById('scenicForm');
      const searchBtn = document.getElementById('searchBtn');
      const resetBtn = document.getElementById('resetBtn');
      const saveBtn = document.getElementById('saveBtn');
      const statusMessage = document.getElementById('statusMessage');
      const successMsg = document.getElementById('successMsg');
      const errorMsg = document.getElementById('errorMsg');
      const successText = document.getElementById('successText');
      const errorText = document.getElementById('errorText');
      const mapPreview = document.getElementById('mapPreview');

      const searchError = document.getElementById('searchError');
      const scenicNameInput = document.getElementById('scenicName');
      const scenicIdInput = document.getElementById('scenicId');
      const formScenicNameInput = document.getElementById('formScenicName');
      const longitudeInput = document.getElementById('longitude');
      const latitudeInput = document.getElementById('latitude');
      const layerIndexInput = document.getElementById('layerIndex');
      const detailAddressInput = document.getElementById('detailAddress');

      const checkTimeInput = document.getElementById('checkTime');
      const tMapScenicNameInput = document.getElementById('tMapScenicName');

      // 初始化地图函数
      function initMap(longitude, latitude, zoom) {
        const mapContainer = document.getElementById('mapContainer');

<!--        // 隐藏空状态提示-->
<!--        document.getElementById('emptyMapState').classList.add('hidden');-->

<!--        // 显示加载状态-->
<!--        document.getElementById('mapLoading').classList.remove('hidden');-->

        // 如果地图已初始化，先销毁
        if (map) {
          map.destroy();
          map = null;
        }

            var center = new TMap.LatLng(latitude, longitude);
            //初始化地图
            map = new TMap.Map("mapContainer", {
                zoom: zoom,//设置地图缩放级别
                center: center,  //设置地图中心点坐标
                baseMap: {  // 设置卫星地图
                  type: 'satellite'
                }
            });

            //监听地图瓦片加载完成事件
            map.on("tilesloaded", function () {
              document.getElementById('mapLoading').classList.add('hidden');
            })

      }

      // 验证经纬度是否有效
      function isValidCoordinate(value) {
        return !isNaN(value) && value !== '';
      }

      // 初始化时检查是否有默认经纬度
      const initialLongitude = parseFloat(document.getElementById('longitude').value);
      const initialLatitude = parseFloat(document.getElementById('latitude').value);

      if (isValidCoordinate(initialLongitude) && isValidCoordinate(initialLatitude)) {
        initMap(initialLongitude, initialLatitude, layerIndexInput);
      }


      // 搜索按钮点击事件
      searchBtn.addEventListener('click', () => {
        const scenicName = scenicNameInput.value.trim();

        // 验证景区名称
        if (!scenicName) {
          searchError.classList.remove('hidden');
          scenicNameInput.classList.add('border-red-500');
          scenicNameInput.classList.remove('border-neutral-200');
          return;
        }

        // 清除错误提示
        searchError.classList.add('hidden');
        scenicNameInput.classList.remove('border-red-500');
        scenicNameInput.classList.add('border-neutral-200');

        // 显示加载状态
        searchBtn.disabled = true;
        searchBtn.innerHTML = '<i class="fa fa-spinner fa-spin mr-2"></i>查询中...';

        // 异步请求
        fetch(`/prod-api/scenicSpot/get?name=${encodeURIComponent(scenicName)}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(response => {
          if (!response.ok) {
            throw new Error('查询失败');
          }
          return response.json();
        })
        .then(data => {

          // 成功获取数据，填充表单
          if (data) {
            var sc = data.data
            scenicIdInput.value = sc.id || '';
            formScenicNameInput.value = sc.name || '';
            longitudeInput.value = sc.latitude || '';
            latitudeInput.value = sc.longitude || '';
            layerIndexInput.value = parseInt(sc.zoom) || '';
            detailAddressInput.value = sc.detailAddress || '';

            checkTimeInput.value = sc.checkTime || '';

            if (sc.tmapScenicName) {
                tMapScenicNameInput.value = sc.tmapScenicName || '';
            } else {
                tMapScenicNameInput.value = sc.name || '';
            }

            // 更新地图预览
            if (sc.longitude && sc.latitude) {
                initMap(sc.latitude, sc.longitude, parseInt(sc.zoom));
            } else {
                if (map) {
                  map.destroy();
                  map = null;
                }
            }

            showSuccess('查询成功');
          } else {
            showError('未找到景区数据');
          }
        })
        .catch(error => {
          console.error('查询错误:', error);
          showError('查询出错，请重试');
        })
        .finally(() => {
          // 恢复按钮状态
          searchBtn.disabled = false;
          searchBtn.innerHTML = '<i class="fa fa-search mr-2"></i>查询景区';
        });
      });

      // 输入框输入事件 - 清除错误提示
      scenicNameInput.addEventListener('input', () => {
        if (scenicNameInput.value.trim()) {
          searchError.classList.add('hidden');
          scenicNameInput.classList.remove('border-red-500');
          scenicNameInput.classList.add('border-neutral-200');
        }
      });

      // 保存按钮点击事件
    document.getElementById('saveBtn').addEventListener('click', () => {
      // 获取表单数据
      const formData = {
        id: document.getElementById('scenicId').value,
        name: document.getElementById('formScenicName').value,
        longitude: document.getElementById('latitude').value,
        latitude: document.getElementById('longitude').value,
        zoom: document.getElementById('layerIndex').value,
        detailAddress: document.getElementById('detailAddress').value,
        tmapScenicName: document.getElementById('tMapScenicName').value
      };

      // 简单验证
      if (!formData.name) {
        showError('景区名称不能为空');
        return;
      }

<!--      if (!formData.longitude || !formData.latitude) {-->
<!--        showError('经纬度不能为空');-->
<!--        return;-->
<!--      }-->

      // 显示加载状态
      const saveBtn = document.getElementById('saveBtn');
      saveBtn.disabled = true;
      saveBtn.innerHTML = '<i class="fa fa-spinner fa-spin mr-2"></i>保存中...';

      // 异步调用后端API
      fetch('/prod-api/scenicSpot/checkEdit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })
      .then(response => {
        if (!response.ok) throw new Error('保存失败');
        return response.json();
      })
      .then(savedScenic => {
        showSuccess('保存成功');

        // 保存成功后，触发搜索事件
        document.getElementById('scenicName').value = formData.name;
        document.getElementById('searchBtn').click();
      })
      .catch(error => {
        console.error('保存错误:', error);
        showError('保存失败，请重试');
      })
      .finally(() => {
        // 恢复按钮状态
        saveBtn.disabled = false;
        saveBtn.innerHTML = '<i class="fa fa-save mr-2"></i>保存信息';
      });
    });


      // 显示成功消息
      function showSuccess(message) {
        hideStatusMessages();
        successText.textContent = message;
        successMsg.classList.remove('hidden');
        statusMessage.classList.remove('hidden');

        // 3秒后自动隐藏
        setTimeout(() => {
          hideStatusMessages();
        }, 3000);
      }

      // 显示错误消息
      function showError(message) {
        hideStatusMessages();
        errorText.textContent = message;
        errorMsg.classList.remove('hidden');
        statusMessage.classList.remove('hidden');

        // 3秒后自动隐藏
        setTimeout(() => {
          hideStatusMessages();
        }, 3000);
      }

      // 隐藏所有状态消息
      function hideStatusMessages() {
        successMsg.classList.add('hidden');
        errorMsg.classList.add('hidden');
        statusMessage.classList.add('hidden');
      }

      // 检查URL参数中是否有查询结果
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.get('success') === 'true') {
        showSuccess('景区信息保存成功');
      }
    });
</script>
</body>
</html>