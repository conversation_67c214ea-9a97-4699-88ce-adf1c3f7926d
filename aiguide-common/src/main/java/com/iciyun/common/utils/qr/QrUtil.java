package com.iciyun.common.utils.qr;

import cn.hutool.core.io.FileUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageConfig;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * 二维码工具类
 */
public class QrUtil {

    public static BufferedImage generateQR(String content, int size) throws WriterException {
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
        hints.put(EncodeHintType.MARGIN, 1);

        BitMatrix matrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, size, size, hints);
        return MatrixToImageWriter.toBufferedImage(matrix);
    }

    /**
     * 带LOGO的二维码
     */
    public static File generateQRWithLogo(String data, int size, String logoPath, String qrPath) {
        File file = QrCodeUtil.generate(//
                data, //二维码内容
                QrConfig.create().setImg(logoPath).setWidth(size).setHeight(size), //附带logo
                FileUtil.file(qrPath) //写出到的文件
        );

        return file;
    }

    /**
     * 带文字描述的二维码
     */
    public static void addText(BufferedImage image, String text) {
        Graphics2D g = (Graphics2D) image.getGraphics();
        g.setFont(new Font("微软雅黑", Font.BOLD, 20));
        g.setColor(Color.BLACK);

        // 计算文字位置
        FontMetrics metrics = g.getFontMetrics();
        int textWidth = metrics.stringWidth(text);
        int x = (image.getWidth() - textWidth)/2;
        int y = image.getHeight() - metrics.getHeight() + 30;

        g.drawString(text, x, y);
        g.dispose();
    }

    public static void generateQRWithLogo(String data, String logoPath, int size) throws Exception {
        QrCodeUtil.generate(//
                data, //二维码内容
                QrConfig.create().setImg(logoPath).setWidth(size).setHeight(size), //附带logo
                FileUtil.file("D:\\mydata\\qr_with_logo.png") //写出到的文件
        );
    }

    public static void main(String[] args) {
        String content = "https://test.guide.iciyun.net/prod-api/common/downloadTxt/test?scenicId=7&locationCode=A";
        String logoPath  = "D:\\mydata\\logo.png";
        try {
            generateQRWithLogo(content, logoPath, 600);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
