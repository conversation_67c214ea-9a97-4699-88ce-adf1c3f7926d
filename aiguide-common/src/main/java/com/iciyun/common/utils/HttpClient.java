package com.iciyun.common.utils;

import okhttp3.ConnectionPool;
import okhttp3.ConnectionSpec;
import okhttp3.Dns;
import okhttp3.OkHttpClient;
import org.jetbrains.annotations.NotNull;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> on 2025-05-09 14:07.
 */
@SuppressWarnings("ALL")
public class HttpClient {
    private static final OkHttpClient INSTANCE = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .connectionSpecs(Arrays.asList(ConnectionSpec.MODERN_TLS, ConnectionSpec.COMPATIBLE_TLS))//启用连接验证功能,帮助客户端在重新建立连接时减少不必要的验证步骤，加快连接速度。
            .retryOnConnectionFailure(true)// 自动重试失败连接
            .connectionPool(new ConnectionPool(50, 30, TimeUnit.MINUTES))
            .pingInterval(5,TimeUnit.MINUTES)// 添加心跳检测机制（每5分钟发送PING帧）
            .dns(new Dns() {
                // 实现带缓存的DNS解析器
                private final Map<String, List<InetAddress>> cache = new ConcurrentHashMap<>();
                @NotNull
                @Override
                public List<InetAddress> lookup(String hostname) throws UnknownHostException {
                    return cache.computeIfAbsent(hostname, h -> {
                        try {
                            return Dns.SYSTEM.lookup(hostname);
                        } catch (UnknownHostException e) {
                            throw new RuntimeException(e);
                        }
                    });
                }
            })
            .build();

    public static OkHttpClient getInstance() {
        return INSTANCE;
    }

    public static void shutdown() {
        INSTANCE.dispatcher().executorService().shutdown();
        INSTANCE.connectionPool().evictAll();
    }

}

