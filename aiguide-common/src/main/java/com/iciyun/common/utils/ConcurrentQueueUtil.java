package com.iciyun.common.utils;

import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR> on 2025-06-07 23:41.
 */
public class ConcurrentQueueUtil<T> {
    private final ConcurrentLinkedQueue<T> queue = new ConcurrentLinkedQueue<>();
    private final ReentrantLock enqueueLock = new ReentrantLock();
    private final ReentrantLock dequeueLock = new ReentrantLock();

    /**
     * 线程安全入队操作
     *
     * @param item 要入队的元素
     */
    public void enqueue(T item) {
        enqueueLock.lock();
        try {
            queue.offer(item);
        } finally {
            enqueueLock.unlock();
        }
    }

    /**
     * 线程安全出队操作
     *
     * @return 出队的元素，队列为空时返回null
     */
    public T dequeue() {
        dequeueLock.lock();
        try {
            return queue.poll();
        } finally {
            dequeueLock.unlock();
        }
    }

    public void clear(){
        queue.clear();
    }

    /**
     * 获取队列当前大小
     *
     * @return 队列元素数量
     */
    public int size() {
        return queue.size();
    }

    /**
     * 检查队列是否为空
     *
     * @return 队列为空返回true，否则返回false
     */
    public boolean isEmpty() {
        return queue.isEmpty();
    }
}
