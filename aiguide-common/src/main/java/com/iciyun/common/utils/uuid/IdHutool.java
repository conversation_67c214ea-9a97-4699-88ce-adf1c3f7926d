package com.iciyun.common.utils.uuid;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 使用 hutool 生成id
 */
@Component
public class IdHutool {

    @Autowired
    private RedisTemplate redisTemplate;

    private static Long proInsIdLast = 900000000000L;

    private static String incrKey = "incr:";

    public static Long genLong() {
        return IdUtil.getSnowflake().nextId();
    }

    public static String gen() {
        return IdUtil.getSnowflake().nextIdStr();
    }

    /**
     * 标志 + 年月日时分秒 + 6位随机数
     */
    public static String gen(String bizFlag) {
        return bizFlag + DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss") + IdUtil.getSnowflakeNextId() % 1000000;
    }

    /**
     * 标志 + 年月日时分秒 +  自增数
     */
    public String genIncr(String bizFlag) {
        return bizFlag + DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss") + getTaskId();
    }

    /**
     * 生成一个长度为12的纯数字字符串作为任务id，规则如下
     * 1.以9开头
     * 2.后11位数字从1开始自增
     *
     * @return 如："00000000001"
     */
    public String getTaskId() {
        String key = incrKey + DateUtil.format(LocalDateTime.now(), "yyyyMMdd");
        long incr = redisTemplate.opsForValue().increment(key, 1);
        redisTemplate.expire(key, 2, TimeUnit.DAYS);
        return String.valueOf(incr + proInsIdLast).substring(1, 11);
    }

    /**
     * 标志 + 年月日 +  6位自增数
     */
    public String genCode(String code) {
        String dateStr = DateUtil.format(LocalDateTime.now(), "yyyyMMdd");
        String redisKey = code + dateStr;
        long sequence = redisTemplate.opsForValue().increment(redisKey, 1);
        redisTemplate.expire(redisKey, 2, TimeUnit.DAYS);
        return code + dateStr + String.format("%06d", sequence);
    }


    public static void main(String[] args) {
        System.out.println(gen("UU"));
    }
}
