package com.iciyun.common.utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;

/**
 * <AUTHOR> on 2025-06-09 11:24.
 */
public class JavaLineCounter {

    public static void main(String[] args) {
        String directoryPath = "/Users/<USER>/Desktop/project/aiguide-back";
        File directory = new File(directoryPath);

        if (!directory.exists() || !directory.isDirectory()) {
            System.out.println("指定的路径不存在或不是目录");
            return;
        }

        System.out.println("开始统计Java文件代码行数...");
        System.out.println("--------------------------------");

        int totalLines = countJavaFilesLines(directory, 0);

        System.out.println("--------------------------------");
        System.out.println("总计: " + totalLines + " 行代码");
    }

    private static int countJavaFilesLines(File directory, int totalLines) {
        File[] files = directory.listFiles();

        if (files == null) {
            return totalLines;
        }

        for (File file : files) {
            if (file.isDirectory()) {
                totalLines = countJavaFilesLines(file, totalLines);
            } else if (file.getName().endsWith(".java")) {
                int lines = countFileLines(file);
                System.out.println(file.getAbsolutePath() + ": " + lines + " 行");
                totalLines += lines;
            }
        }

        return totalLines;
    }

    private static int countFileLines(File file) {
        int lines = 0;

        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            while (reader.readLine() != null) {
                lines++;
            }
        } catch (IOException e) {
            System.err.println("读取文件 " + file.getName() + " 时出错: " + e.getMessage());
        }

        return lines;
    }

}
