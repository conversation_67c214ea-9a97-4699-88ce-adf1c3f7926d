package com.iciyun.common.utils.http;

import com.alibaba.fastjson2.JSONObject;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.CharEncoding;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.*;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.ContentBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class HttpClientUtil {

    @Autowired
    private static CloseableHttpClient closeableHttpClient;

    public static String get(String url, Map<String, Object> urlParam, Map<String, String> header, boolean ssl) {
        return get(url, urlParam, header, CharEncoding.UTF_8, ssl);
    }

    public static String get(String url, Map<String, Object> urlParams, Map<String, String> headers, String charSet, boolean ssl) {
        HttpGet httpGet = new HttpGet(charSet == null ? addParams(url, urlParams) : addParamsWithCharSet(url, urlParams, charSet));
        return getResponse(httpGet, charSet, headers, ssl);
    }

    // 以请求体JSON发送数据
    public static String postJson(String url, Map<String, Object> urlParams, Map<String, String> headers, String data, boolean ssl) {
        HttpPost httpPost = new HttpPost(addParams(url, urlParams));
        if(StringUtils.isNotBlank(data)){
            httpPost.setEntity(new StringEntity(data, ContentType.APPLICATION_JSON));
        }
        return getResponse(httpPost, CharEncoding.UTF_8, headers, ssl);
    }

    // 以表单的形式发送数据
    public static String postForm(String url, Map<String, Object> urlParams, Map<String, String> headers, Map<String, String> data, boolean ssl) {
        HttpPost httpPost = new HttpPost(addParams(url, urlParams));
        ContentType contentType = ContentType.create("application/x-www-form-urlencoded", Consts.UTF_8);
        if (Objects.isNull(headers)) {
            headers = new HashMap<>();
        }
        headers.put("Content-Type", contentType.toString());
        List<NameValuePair> list = new ArrayList<>();
        for (Map.Entry<String, String> entry : data.entrySet()) {
            list.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
        }
        if (list.size() > 0) {
            UrlEncodedFormEntity entity = null;
            try {
                entity = new UrlEncodedFormEntity(list, CharEncoding.UTF_8);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            httpPost.setEntity(entity);
        }
        return getResponse(httpPost, CharEncoding.UTF_8, headers, ssl);
    }

    /**
     * 文件上传
     *
     * @param url url
     * @return
     */
    public static String uploadFile(String url, Map<String, String> headers, MultipartFile multipartFile, boolean ssl) {
        HttpPost httpPost = new HttpPost(url);
        //文件请求
        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
        builder.setCharset(Charset.forName("UTF-8"));
        //文件名
        String fileName = multipartFile.getOriginalFilename();
        InputStream inputStream = null;
        try {
            //得到流
            inputStream = multipartFile.getInputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }
        //添加到 请求体中。注意这个参数名：fileParName，应该为file
        builder.addBinaryBody(fileName, inputStream);// 文件流
        HttpEntity httpEntity = builder.build();
        httpPost.setEntity(httpEntity);

        return getResponse(httpPost, CharEncoding.UTF_8, headers, ssl);
    }

    /**
     * multipart/form-data向接口上传文件
     */
    public static String postFileMultiPart(String url, Map<String, ContentBody> reqParam,
                                           Map<String, String> headers, boolean ssl) {
        // 创建http
        HttpPost httppost = new HttpPost(url);
        MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
        multipartEntityBuilder.setCharset(Charset.forName("UTF-8"));
        for (Map.Entry<String, ContentBody> param : reqParam.entrySet()) {
            multipartEntityBuilder.addPart(param.getKey(), param.getValue());
        }
        HttpEntity reqEntity = multipartEntityBuilder.build();
        httppost.setEntity(reqEntity);

        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(5000) // 设置连接超时时间
                .setConnectionRequestTimeout(1000) // 设置从连接池获取连接的超时时间
                .setSocketTimeout(10000) // 设置请求获取数据的超时时间
                .build();
        httppost.setConfig(requestConfig);

        return getResponse(httppost, CharEncoding.UTF_8, headers, ssl);
    }


    // 获取响应数据
    private static String getResponse(HttpRequestBase httpRequestBase, String charSet, Map<String, String> headers, boolean ssl) {
        CloseableHttpClient httpClient = null;
        try {
            httpClient = ssl ? closeableHttpClient : HttpClients.createDefault();
            httpRequestBase.setConfig(getRequestConfig());
            if (headers.size() > 0) {
                httpRequestBase.setHeaders(getHeaders(headers));
            }
            CloseableHttpResponse response = httpClient.execute(httpRequestBase);
            int statusCode = response.getStatusLine().getStatusCode();
            if (HttpStatus.SC_OK == statusCode) {
                HttpEntity entity = response.getEntity();
                String res = EntityUtils.toString(entity, charSet);
                EntityUtils.consume(entity);
                return res;
            } else if (HttpStatus.SC_UNAUTHORIZED == statusCode) {
                JSONObject ret = new JSONObject();
                ret.put("code", statusCode);
                ret.put("msg", "未授权");
                return ret.toJSONString();
            } else if (4100 == statusCode) {
                JSONObject ret = new JSONObject();
                ret.put("code", statusCode);
                ret.put("msg", "The token you entered is incorrect");
            } else {
                HttpEntity entity = response.getEntity();
                String res = EntityUtils.toString(entity, charSet);
                log.error("接口{},请求失败，返回：{}", httpRequestBase.getURI(), res);
                return res;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (Objects.nonNull(httpClient)) {
                    httpClient.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        throw new RuntimeException("调用失败");
    }

    private static RequestConfig getRequestConfig() {
        return RequestConfig.custom().setConnectTimeout(6000 * 2).setConnectionRequestTimeout(6000 * 2).setSocketTimeout(6000 * 2).build();
    }

    private static String addParams(String url, Map<String, Object> params) {
        return addParamsWithCharSet(url, params, CharEncoding.UTF_8);
    }

    private static String addParamsWithCharSet(String url, Map<String, Object> params, String charSet) {
        if (params == null || params.isEmpty()) {
            return url;
        }
        StringBuilder sb = new StringBuilder();
        try {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                sb.append("&").append(entry.getKey()).append("=");
                if (entry.getValue() instanceof String) {
                    sb.append(charSet == null ? entry.getValue() : URLEncoder.encode((String) entry.getValue(), charSet));
                } else {
                    sb.append(entry.getValue());
                }
            }
            if (!url.contains("?")) {
                sb.deleteCharAt(0).insert(0, "?");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return url + sb;
    }

    // 设置请求头
    private static Header[] getHeaders(Map<String, String> header) {
        if (header.size() == 0) {
            return new Header[]{};
        }
        List<Header> headers = new ArrayList<>();
        for (String key : header.keySet()) {
            headers.add(new BasicHeader(key, header.get(key)));
        }
        return headers.toArray(new Header[]{});
    }

    //post 返回 io 流
    public static byte[] postJsonRtIo(String url,
                                      Map<String, Object> urlParams,
                                      Map<String, String> headers,
                                      String data,
                                      boolean ssl) {
        HttpPost httpPost = new HttpPost(addParams(url, urlParams));
        httpPost.setEntity(new StringEntity(data, ContentType.APPLICATION_JSON));
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(50000) // 设置连接超时时间
                .setConnectionRequestTimeout(10000) // 设置从连接池获取连接的超时时间
                .setSocketTimeout(20000) // 设置请求获取数据的超时时间
                .build();
        httpPost.setConfig(requestConfig);
        return getResponseIo(httpPost, headers, ssl);
    }


    //post 返回  浏览器下载
    public static void postJsonRtBrowse(HttpServletResponse response,
                                        String url,
                                        Map<String, Object> urlParams,
                                        Map<String, String> headers,
                                        String data,
                                        boolean ssl) {
        HttpPost httpPost = new HttpPost(addParams(url, urlParams));
        httpPost.setEntity(new StringEntity(data, ContentType.APPLICATION_JSON));
        getResponseBrowse(response, httpPost, headers, ssl);
    }

    public static byte[] getResponseIo(HttpRequestBase httpRequestBase,
                                       Map<String, String> headers,
                                       boolean ssl) {
        CloseableHttpClient httpClient = null;
        try {
            httpClient = ssl ? closeableHttpClient : HttpClients.createDefault();
            httpRequestBase.setConfig(getRequestConfig());
            if (headers.size() > 0) {
                httpRequestBase.setHeaders(getHeaders(headers));
            }
            CloseableHttpResponse httpResponse = httpClient.execute(httpRequestBase);
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            log.info("生成语音返回状态码：{}", statusCode);
            HttpEntity entity = httpResponse.getEntity();
            byte[] ioData = EntityUtils.toByteArray(entity);
            return ioData;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("生成语音请求失败，返回：{}", e.getMessage());
        } finally {
            try {
                if (Objects.nonNull(httpClient)) {
                    httpClient.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
       return null;
    }

    //post 浏览器 下载
    public static void getResponseBrowse(HttpServletResponse response,
                                         HttpRequestBase httpRequestBase,
                                         Map<String, String> headers,
                                         boolean ssl) {
        CloseableHttpClient httpClient = null;
        try {
            httpClient = ssl ? closeableHttpClient : HttpClients.createDefault();
            httpRequestBase.setConfig(getRequestConfig());
            if (headers.size() > 0) {
                httpRequestBase.setHeaders(getHeaders(headers));
            }
            CloseableHttpResponse httpResponse = httpClient.execute(httpRequestBase);
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            log.info("生成语音返回状态码：{}", statusCode);
            HttpEntity entity = httpResponse.getEntity();
            // 设置编码
            response.setCharacterEncoding("UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("chat.mp3", "UTF-8"));
            // 获取文件流并写入到response的输出流中
            try (OutputStream outputStream = response.getOutputStream()) {
                entity.writeTo(outputStream);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (Objects.nonNull(httpClient)) {
                    httpClient.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    //post 返回 io 流
    public static String postJsonRtLocal(String url,
                                         String filepath,
                                         Map<String, Object> urlParams,
                                         Map<String, String> headers,
                                         String data, boolean ssl) {
        HttpPost httpPost = new HttpPost(addParams(url, urlParams));
        httpPost.setEntity(new StringEntity(data, ContentType.APPLICATION_JSON));
        return getResponseIoLocal(httpPost, filepath, headers, ssl);
    }

    public static List<JSONObject> postTextJsonRtBrowse(String url,
                                                        Map<String, Object> urlParams,
                                                        Map<String, String> headers,
                                                        String data,
                                                        boolean ssl) {
        log.info("开始请求接口url:{},请求参数 {}", url, data);
        HttpPost httpPost = new HttpPost(addParams(url, urlParams));
        httpPost.setEntity(new StringEntity(data, ContentType.APPLICATION_JSON));
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(5000) // 设置连接超时时间
                .setConnectionRequestTimeout(1000) // 设置从连接池获取连接的超时时间
                .setSocketTimeout(10000) // 设置请求获取数据的超时时间
                .build();
        httpPost.setConfig(requestConfig);
        return getResponseTextBrowse(httpPost, headers, ssl);
    }

    private static List<JSONObject> getResponseTextBrowse(HttpRequestBase httpRequestBase,
                                                          Map<String, String> headers,
                                                          boolean ssl) {
        CloseableHttpClient httpClient = null;
        //11.读取输入流中的返回值
        List<JSONObject> bu = new ArrayList<>();
        try {
            httpClient = ssl ? closeableHttpClient : HttpClients.createDefault();
            httpRequestBase.setConfig(getRequestConfig());
            if (headers.size() > 0) {
                httpRequestBase.setHeaders(getHeaders(headers));
            }
            CloseableHttpResponse httpResponse = httpClient.execute(httpRequestBase);
            HttpEntity entity = httpResponse.getEntity();
            log.info("大模型回复 code：{}", httpResponse.getStatusLine().getStatusCode());
            // 将响应实体转换为字节数组
            //10.获取输入流
            BufferedReader reader = new BufferedReader(new InputStreamReader(entity.getContent(), StandardCharsets.UTF_8));
            String line;
            while ((line = reader.readLine()) != null) {
                if (StringUtils.isBlank(line) || "event: ping".equals(line)) {
                    continue;
                }
                System.out.println(line);
                if (line.startsWith("data:")) {
                    if (line.contains("DONE")) {
                        continue;
                    }
                    line = line.substring("data:".length());
                    bu.add(JSONObject.parse(line));
                } else if (StringUtils.isNotBlank(line) && line.contains("code")) {
                    bu.add(JSONObject.parse(line));
                }

            }
            log.info("大模型已回复");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (Objects.nonNull(httpClient)) {
                    httpClient.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return bu;
    }

    public static String getResponseIoLocal(HttpRequestBase httpRequestBase,
                                            String filepath,
                                            Map<String, String> headers,
                                            boolean ssl) {
        CloseableHttpClient httpClient = null;
        try {
            httpClient = ssl ? closeableHttpClient : HttpClients.createDefault();
            httpRequestBase.setConfig(getRequestConfig());
            if (headers.size() > 0) {
                httpRequestBase.setHeaders(getHeaders(headers));
            }
            CloseableHttpResponse httpResponse = httpClient.execute(httpRequestBase);
            HttpEntity entity = httpResponse.getEntity();
            if (StringUtils.isNotBlank(filepath)) {
                // 将响应实体转换为字节数组
                byte[] ioData = EntityUtils.toByteArray(entity);
                // 创建本地文件
                File destinationFile = new File(filepath);
                if (!destinationFile.exists()) {
                    //先得到文件的上级目录，并创建上级目录，在创建文件
                    destinationFile.getParentFile().mkdir();
                    try {
                        //创建文件
                        destinationFile.createNewFile();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                try (FileOutputStream fos = new FileOutputStream(destinationFile)) {
                    // 将图片数据写入本地文件
                    fos.write(ioData);
                }
                log.info("音频已成功保存到：" + filepath);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (Objects.nonNull(httpClient)) {
                    httpClient.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return "success";
    }

    public static List<JSONObject> postEventStream(String urlStr, String json, String token) {
        log.info("开始请求接口url:{},请求参数{}", urlStr, json);
        InputStream is = null;
        //11.读取输入流中的返回值
        List<JSONObject> bu = new ArrayList<>();
        try {
            //1.设置URL
            URL url = new URL(urlStr);
            //2.打开URL连接
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(5000); //连接超时时间5秒
            conn.setReadTimeout(10000); //读取数据的超时时间10秒
            //3.设置请求方式
            conn.setRequestMethod("POST");
            //4.设置Content-Type
            conn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
            conn.setRequestProperty("Authorization", "Bearer " + token);
            //5.设置Accept
            conn.setRequestProperty("Accept", "text/event-stream");
            //6.设置DoOutput
            conn.setDoOutput(true);
            //7.设置DoInput
            conn.setDoInput(true);
            //8.获取输出流
            OutputStream os = conn.getOutputStream();
            //9.写入参数（json格式）
            os.write(json.getBytes("utf-8"));
            os.flush();
            os.close();
            //10.获取输入流
            is = conn.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
            String line;
            while ((line = reader.readLine()) != null) {
                log.info("line: {}", line);
                if (StringUtils.isBlank(line) || "event: ping".equals(line)) {
                    continue;
                }
                if (line.startsWith("data:")) {
                    if (line.contains("DONE")) {
                        continue;
                    }
                    line = line.substring("data:".length());
                    bu.add(JSONObject.parse(line));
                } else if (StringUtils.isNotBlank(line) && line.contains("code")) {
                    bu.add(JSONObject.parse(line));
                }
            }
        } catch (IOException e) {
            log.error("请求模型接口异常", e);
            throw new RuntimeException("调用失败");
        } finally {
            if (!Objects.isNull(is)) {
                try {
                    //12.关闭输入流
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

        }
        return bu;
    }


    public static CloseableHttpResponse post4Response(String url,
                                     Map<String, Object> urlParams,
                                     Map<String, String> headers,
                                     String data,
                                     boolean ssl) {
        log.info("开始请求接口url:{},请求参数 {}", url, data);
        HttpPost httpPost = new HttpPost(addParams(url, urlParams));
        httpPost.setEntity(new StringEntity(data, ContentType.APPLICATION_JSON));
        httpPost.setConfig(config);

        CloseableHttpClient httpClient = createOptimizedClient();

        if (headers.size() > 0) {
            httpPost.setHeaders(getHeaders(headers));
        }
        try {
            CloseableHttpResponse result = httpClient.execute(httpPost);
            log.info("请求结果：{}",result);
            return result;
        } catch (IOException e) {
            log.error("请求失败！", e);
            throw new RuntimeException(e);
        }finally {

        }

    }


    private static PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager();
    static {
        connManager.setMaxTotal(200);
        connManager.setDefaultMaxPerRoute(50);
        connManager.setValidateAfterInactivity(30_000);
    }

    private static RequestConfig config = RequestConfig.custom()
            .setConnectionRequestTimeout(5000)
            .setConnectTimeout(5000)
            .setSocketTimeout(15000)
            .build();

    private static CloseableHttpClient createOptimizedClient() {

        return HttpClients.custom()
                .setConnectionManager(connManager)
                .setDefaultRequestConfig(config)
                .evictIdleConnections(60, TimeUnit.SECONDS) // 定期清理空闲连接
                .build();
    }


}
