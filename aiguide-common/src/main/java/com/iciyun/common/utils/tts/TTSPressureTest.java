package com.iciyun.common.utils.tts;

import java.util.concurrent.*;

public class TTSPressureTest {
    private final TTSClient ttsClient;
    private final int concurrencyLevel;
    private final int requestsPerThread;
    private final ConcurrentHashMap<String, Long> stats;

    public TTSPressureTest(int concurrencyLevel, int requestsPerThread) {
        this.ttsClient = new TTSClient();
        this.concurrencyLevel = concurrencyLevel;
        this.requestsPerThread = requestsPerThread;
        this.stats = new ConcurrentHashMap<>();
        stats.put("success", 0L);
        stats.put("failure", 0L);
        stats.put("totalTime", 0L);
        stats.put("responseTimes", 0L);
        stats.put("<100ms", 0L);
        stats.put("100-200ms", 0L);
        stats.put("200-500ms", 0L);
        stats.put("500-1000ms", 0L);
        stats.put("1000-2000ms", 0L);
        stats.put(">2000ms", 0L);
    }

    public void startPressureTest() {
        ExecutorService executorService = Executors.newFixedThreadPool(concurrencyLevel);
        int totalRequests = concurrencyLevel * requestsPerThread;
        CountDownLatch latch = new CountDownLatch(totalRequests); // 等待所有请求完成
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < concurrencyLevel; i++) {
            executorService.execute(() -> {
                try {
                    for (int j = 0; j < requestsPerThread; j++) {
                        ttsClient.synthesizeAndSave("你好，欢迎来到北京，我是你的导游小明，很高兴为你服务。", stats, latch);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        }

        try {
            latch.await(); // 等待所有请求完成
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        executorService.shutdown();
        printStatistics(totalTime);
    }

    private void printStatistics(long totalTime) {
        long success = stats.get("success");
        long failure = stats.get("failure");
        long totalRequests = success + failure;
        double successRate = totalRequests > 0 ? (double) success / totalRequests * 100 : 0;
        double averageResponseTime = stats.get("responseTimes") > 0 ? (double) stats.get("totalTime") / stats.get("responseTimes") : 0;
        double throughput = totalRequests / ((double) totalTime / 1000);

        System.out.println("\n=== 压测结果 ===");
        System.out.println("总请求数: " + totalRequests);
        System.out.println("成功请求数: " + success);
        System.out.println("失败请求数: " + failure);
        System.out.println("成功率: " + String.format("%.2f", successRate) + "%");
        System.out.println("平均响应时间: " + String.format("%.2f", averageResponseTime) + "ms");
        System.out.println("吞吐率: " + String.format("%.2f", throughput) + " requests/sec");

        System.out.println("\n=== 响应时间分布 ===");
        printTimeRange("<100ms", stats.get("<100ms"));
        printTimeRange("100-200ms", stats.get("100-200ms"));
        printTimeRange("200-500ms", stats.get("200-500ms"));
        printTimeRange("500-1000ms", stats.get("500-1000ms"));
        printTimeRange("1000-2000ms", stats.get("1000-2000ms"));
        printTimeRange(">2000ms", stats.get(">2000ms"));
    }

    private void printTimeRange(String range, Long count) {
        System.out.println(range + ": " + count);
    }

    public static void main(String[] args) {
        // 设置并发数和每个线程的请求数量
        int concurrencyLevel = 100;
        int requestsPerThread = 1;

        TTSPressureTest pressureTest = new TTSPressureTest(concurrencyLevel, requestsPerThread);
        pressureTest.startPressureTest();
    }
}