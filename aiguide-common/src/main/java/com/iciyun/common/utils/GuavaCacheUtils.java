package com.iciyun.common.utils;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> on 2025/6/19 15:40.
 */
public class GuavaCacheUtils<K, V>  {
    private final LoadingCache<K, V> cache;

    public GuavaCacheUtils(long expireAfterWrite, int maximumSize,TimeUnit timeUnit) {
        this.cache = CacheBuilder.newBuilder()
                // 设置写入后过期时间
                .expireAfterWrite(expireAfterWrite, timeUnit)
                // 设置访问后过期时间
//                .expireAfterAccess(expireAfterAccess, timeUnit)
                // 设置写入后刷新时间
//                .refreshAfterWrite(refreshAfterWrite, timeUnit)
                // 设置缓存最大容量
                .maximumSize(maximumSize)
                // 设置并发级别（可选）
                .concurrencyLevel(4)
                // 设置缓存统计（可选）
                .recordStats()
                // 设置缓存加载器（可选）
                .build(new CacheLoader<K, V>() {
                    @Override
                    public V load(K key) throws Exception {
                        // 在这里实现缓存未命中时的加载逻辑
                        return null;
                    }
                });
    }

    /**
     * 获取缓存值
     *
     * @param key 缓存键
     * @return 缓存值
     * @throws ExecutionException 如果加载缓存失败
     */
    public V getCache(K key) throws ExecutionException {
        return cache.get(key);
    }

    /**
     * 获取缓存值（如果缓存存在）
     *
     * @param key 缓存键
     * @return 缓存值（如果存在），不存在返回 null
     */
    public V getIfPresent(K key) {
        return cache.getIfPresent(key);
    }

    /**
     * 设置缓存值
     *
     * @param key   缓存键
     * @param value 缓存值
     */
    public void putCache(K key, V value) {
        cache.put(key, value);
    }

    /**
     * 删除缓存值
     *
     * @param key 缓存键
     */
    public void invalidateCache(K key) {
        cache.invalidate(key);
    }

    /**
     * 删除所有缓存值
     */
    public void invalidateAllCache() {
        cache.invalidateAll();
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public String getCacheStats() {
        return cache.stats().toString();
    }

    /**
     * 关闭缓存和线程池
     */
    public void close() {
        cache.cleanUp();
    }

    public static void main(String[] args) {
        // 示例用法
        GuavaCacheUtils<String, String> cacheUtils = new GuavaCacheUtils<>(10 , 100,TimeUnit.MINUTES);

        // 添加缓存
        cacheUtils.putCache("key1", "value1");

        try {
            // 获取缓存（如果存在）
            String presentValue = cacheUtils.getIfPresent("key11");
            System.out.println("Present value: " + presentValue);

            // 删除缓存
            cacheUtils.invalidateCache("key1");

            // 获取缓存统计信息
            System.out.println("Cache stats: " + cacheUtils.getCacheStats());

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭缓存和线程池
            cacheUtils.close();
        }
    }
}
