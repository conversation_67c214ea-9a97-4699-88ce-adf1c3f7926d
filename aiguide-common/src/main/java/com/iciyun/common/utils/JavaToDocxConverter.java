package com.iciyun.common.utils;

/**
 * <AUTHOR> on 2025-06-09 11:51.
 */

import org.apache.poi.xwpf.usermodel.*;

import java.io.*;
import java.nio.file.*;

public class JavaToDocxConverter {

    public static void main(String[] args) {

        String sourceDir = "/Users/<USER>/Desktop/project/aiguide-back";
        String outputFile = "/Users/<USER>/Desktop/test/深度游DeepYou小程序源码.docx";


        try (XWPFDocument document = new XWPFDocument()) {
            processDirectory(new File(sourceDir), document);

            try (FileOutputStream out = new FileOutputStream(outputFile)) {
                document.write(out);
                System.out.println("成功生成文档: " + outputFile);
            }
        } catch (IOException e) {
            System.err.println("处理文件时出错: " + e.getMessage());
        }
    }

    private static void processDirectory(File directory, XWPFDocument document) throws IOException {
        File[] files = directory.listFiles();
        if (files == null) return;

        for (File file : files) {
            if (file.isDirectory()) {
                processDirectory(file, document);
            } else if (file.getName().endsWith(".java")) {
                addJavaFileToDocument(file, document);
            }
        }
    }

    private static void addJavaFileToDocument(File javaFile, XWPFDocument document) throws IOException {
        // 添加文件名作为标题
        XWPFParagraph titlePara = document.createParagraph();
        XWPFRun titleRun = titlePara.createRun();
        titleRun.setText(javaFile.getName());
        titleRun.setBold(true);
        titleRun.setFontSize(14);

        // 按行写入文件内容
        try (BufferedReader reader = new BufferedReader(new FileReader(javaFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                XWPFParagraph para = document.createParagraph();
                XWPFRun run = para.createRun();
                run.setText(line);
                run.setFontFamily("Courier New");
                run.setFontSize(10);
            }
        }

        // 添加分页符
        document.createParagraph().createRun().addBreak(BreakType.PAGE);
    }
}

