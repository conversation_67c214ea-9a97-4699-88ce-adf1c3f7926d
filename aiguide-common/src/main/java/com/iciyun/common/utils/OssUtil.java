package com.iciyun.common.utils;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.common.comm.SignVersion;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> on 2025-05-12 23:34.
 */
@Slf4j
public class OssUtil {

    public static OSS ossClient = null;

    public static void init(String accessKeyId,String accessKeySecret,String endpoint,String region) {
        // 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
        DefaultCredentialProvider credentialsProvider = CredentialsProviderFactory.newDefaultCredentialProvider(accessKeyId, accessKeySecret);

        // 创建OSSClient实例。
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);
        // 连接与超时配置
        clientBuilderConfiguration.setMaxConnections(100);
        clientBuilderConfiguration.setConnectionTimeout(3000);
        clientBuilderConfiguration.setSocketTimeout(60000);

        ossClient = OSSClientBuilder.create()
                .endpoint(endpoint)
                .credentialsProvider(credentialsProvider)
                .clientConfiguration(clientBuilderConfiguration)
                .region(region)
                .build();

        log.info("初始化 ossClient 成功！");
    }

}
