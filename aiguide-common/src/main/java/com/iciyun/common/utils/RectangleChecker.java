package com.iciyun.common.utils;

/**
 * <AUTHOR> on 2025-04-02 14:10.
 */
public class RectangleChecker {

    /**
     * 判断一个点是否在矩形区域内。
     *
     * @param topLeftLeft  矩形左上角坐标 (x, y)
     * @param bottomRight  矩形右下角坐标 (x, y)
     * @param point        要判断的点坐标 (x, y)
     * @return 如果点在矩形内，返回 true；否则返回 false
     */
    public static boolean isPointInRectangle(double[] topLeft, double[] bottomRight, double[] point) {
        double x1 = topLeft[0];
        double y1 = topLeft[1];
        double x2 = bottomRight[0];
        double y2 = bottomRight[1];
        double x = point[0];
        double y = point[1];

        // 判断点是否在矩形内
        return x >= x1 && x <= x2 && y >= y1 && y <= y2;
    }

    public static void main(String[] args) {
        // 示例：测试点是否在矩形区域内
        double[] topLeft = {1.0, 5.0};    // 矩形左上角坐标
        double[] bottomRight = {4.0, 2.0}; // 矩形右下角坐标
        double[] point1 = {3.0, 3.0};     // 测试点 1
        double[] point2 = {5.0, 5.0};     // 测试点 2

        // 判断点 1 是否在矩形内
        boolean result1 = isPointInRectangle(topLeft, bottomRight, point1);
        System.out.println("点 (" + point1[0] + ", " + point1[1] + ") 是否在矩形内: " + result1);

        // 判断点 2 是否在矩形内
        boolean result2 = isPointInRectangle(topLeft, bottomRight, point2);
        System.out.println("点 (" + point2[0] + ", " + point2[1] + ") 是否在矩形内: " + result2);
    }
}
