package com.iciyun.common.utils;

public class HaversineFormula {

    private static final double EARTH_RADIUS = 6371000; // 地球半径（米）

    // 计算两个经纬度坐标之间的距离（米）
    public static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final int R = 6371000; // 地球半径（米）
        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2) +
                Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
                        Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;

    }

    public static void main(String[] args) {
        // 测试数据：北京和上海的经纬度
//        double lat1 = 39.9042;  // 北京纬度
//        double lon1 = 116.4074; // 北京经度
//        double lat2 = 31.2304;  // 上海纬度
//        double lon2 = 121.4737; // 上海经度

        double lat1 = 35.514104;
        double lon1 = 112.582705;
        double lat2 = 35.514052;
        double lon2 = 112.582732;

        // 计算距离
        double distance = HaversineFormula.calculateDistance(lat1, lon1, lat2, lon2);

        System.out.printf("计算距离: %.6f 米%n", distance);

        // 验证示例：相同位置的距离应为0
        double sameLocationDistance = HaversineFormula.calculateDistance(lat1, lon1, lat1, lon1);
        System.out.printf("相同位置的距离: %.10f 公里%n", sameLocationDistance);

    }

}
