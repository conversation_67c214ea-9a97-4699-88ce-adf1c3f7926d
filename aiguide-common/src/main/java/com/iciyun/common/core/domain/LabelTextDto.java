package com.iciyun.common.core.domain;

import com.iciyun.common.enums.HobbyTypeEnum;
import com.iciyun.common.utils.sign.Md5Utils;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> on 2025-05-17 19:55.
 */
@Data
public class LabelTextDto implements Serializable {

    private String touristId;
    private String touristName;
    private String labelName;
    /**
     * 中文 Chinese
     * 英语 English
     * 日语 Japanese
     * 西班牙语 Spanish
     * 俄语 Russian
     * 韩语 Korean
     */
    private String language;
    /**
     * HobbyTypeEnum：
     *     HISTORY("HISTORY", "历史考证"),
     *     SCRIPT_IMMERSION("SCRIPT_IMMERSION", "剧本沉浸"),
     *     POETRY("POETRY", "诗歌漫游"),
     *     NATURE("NATURE", "自然漫步"),
     *     ART("ART", "艺术创作"),
     *     CULTURE("CULTURE", "文化内涵"),
     *     CHAT("CHAT", "聊天陪伴"),
     *     PARENT_CHILD("PARENT_CHILD", "亲子教育"),
     */
    private String style;
    private String labelText;
    private String key;

    private String updateTimeStr;

    private Boolean showStatus = false;

    public String getKey() {
        String temp = touristId+"_"+touristName+"_"+labelName+"_"+language+"_"+style;
        return Md5Utils.hash(temp);
    }

    public static Boolean unExistKey(List<LabelTextDto> list, String touristId, String touristName, String labelName, String language, HobbyTypeEnum style){
        String temp = touristId+"_"+touristName+"_"+labelName+"_"+language+"_"+style.getCode();
        String tempKey = Md5Utils.hash(temp);

        for (LabelTextDto item:list){
            if (item.getKey().equals(tempKey))return false;
        }

        return true;
    }
}
