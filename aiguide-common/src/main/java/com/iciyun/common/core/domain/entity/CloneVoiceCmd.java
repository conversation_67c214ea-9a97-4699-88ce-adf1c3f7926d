package com.iciyun.common.core.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CloneVoiceCmd implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 传入文件的音频格式，例如 pcm, m4a, mp3
     */
    private String audioFormat;

    /**
     * 音频文件
     */
    private MultipartFile file;

    /**
     * 音频文件中的语种，支持以下语种：
     * zh：中文
     * en：英文
     * ja：日语
     * es：西班牙语
     * id：印度尼西亚语
     * pt：葡萄牙语
     */
    private String language;

    /**
     * 预览音频的文案。
     */
    private String previewText;

    /**
     * text
     * 音频文件对应的文案。需要和音频文件中人声朗读的内容大致一致，扣子平台服务会对比音频与该文本的差异，
     * 若差异过大会报错 1109 WERError。最大长度为 1024 字节。
     */
    private String text;

    /**
     * 音色 ID
     */
    private String voiceId;

    /**
     * 此音色的名称，长度限制为 128 字节。
     */
    private String voiceName;


}
