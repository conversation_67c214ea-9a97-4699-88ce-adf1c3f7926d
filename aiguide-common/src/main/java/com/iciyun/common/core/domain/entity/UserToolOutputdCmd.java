package com.iciyun.common.core.domain.entity;

import com.iciyun.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserToolOutputdCmd extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 会话ID
     */
    private String conversationId;

    //对话ID
    private String chatId;

    // 工具输出
    private List<ToolOutputdCmd> toolOutputs;


}
