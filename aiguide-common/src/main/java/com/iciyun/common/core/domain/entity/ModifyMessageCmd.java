package com.iciyun.common.core.domain.entity;

import com.iciyun.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ModifyMessageCmd implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 会话ID
     */
    private String conversationId;

    /**
     * 消息id
     */
    private String messageId;

    //发送的消息内容
    private String content;

    /**
     * 消息类型
     * text：文本
     * object_string：多模态内容，即文本和文件的组合、文本和图片的组合
     */
    private String contentType;

    //附加信息
    private Map<String, String> metaData;


}
