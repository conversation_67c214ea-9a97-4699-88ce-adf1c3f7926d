package com.iciyun.common.core.domain.entity;

import com.alibaba.fastjson2.JSON;
import com.iciyun.common.annotation.Excel;
import com.iciyun.common.annotation.Excel.ColumnType;
import com.iciyun.common.annotation.Excel.Type;
import com.iciyun.common.annotation.Excels;
import com.iciyun.common.core.domain.BaseEntity;
import com.iciyun.common.cqe.SaveExplanationStyleCmd;
import com.iciyun.common.enums.GuidePersonaEnum;
import com.iciyun.common.enums.HobbyTypeEnum;
import com.iciyun.common.enums.UserTypeEnum;
import com.iciyun.common.utils.SecurityUtils;
import com.iciyun.common.xss.Xss;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 用户对象 sys_user
 * 
 * <AUTHOR>
 */
public class SysUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @Excel(name = "用户序号", type = Type.EXPORT, cellType = ColumnType.NUMERIC, prompt = "用户编号")
    private Long userId;

    /** 部门ID */
    @Excel(name = "部门编号", type = Type.IMPORT)
    private Long deptId;

    /** 用户账号 */
    @Excel(name = "登录名称")
    private String userName;

    /** 用户昵称 */
    @Excel(name = "用户名称")
    private String nickName;

    /** 用户邮箱 */
    @Excel(name = "用户邮箱")
    private String email;

    /** 用户type */
    private String userType;

    /** 手机号码 */
    @Excel(name = "手机号码", cellType = ColumnType.TEXT)
    private String phonenumber;

    /** 用户性别 */
    @Excel(name = "用户性别", readConverterExp = "0=男,1=女,2=未知")
    private String sex;

    /** 用户头像 */
    private String avatar;

    /** 密码 */
    private String password;

    /** 帐号状态（0正常 1停用） */
    @Excel(name = "帐号状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 最后登录IP */
    @Excel(name = "最后登录IP", type = Type.EXPORT)
    private String loginIp;

    /** 最后登录时间 */
    @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Type.EXPORT)
    private Date loginDate;

    /** 部门对象 */
    @Excels({
        @Excel(name = "部门名称", targetAttr = "deptName", type = Type.EXPORT),
        @Excel(name = "部门负责人", targetAttr = "leader", type = Type.EXPORT)
    })
    private SysDept dept;

    /** 角色对象 */
    private List<SysRole> roles;

    /** 角色组 */
    private Long[] roleIds;

    /** 岗位组 */
    private Long[] postIds;

    /** 角色ID */
    private Long roleId;

    private String roleType;

    private String hobbyTypes;

    private String guideStyle;

    private String guidePersona;

    /**
     * 中文 Chinese
     * 英语 English
     * 日语 Japanese
     * 西班牙语 Spanish
     * 俄语 Russian
     * 韩语 Korean
     */
    private String languageFlag;

    private String openId;

    /**
     * 游豆余额
     */
    private BigDecimal tokenBalance;

    //推荐码
    private String userRecCode;

    /**
     * 游豆累计
     */
    private BigDecimal tokenBalanceTotal;

    private String guideStatus;

    /**
     * 用户管理景区
     */
    private List<UserScenic> sysUserScenics;


    public SysUser()
    {

    }

    public SysUser(String openId) {
        this.openId = openId;
        this.status = "0";
        this.delFlag = "0";
        this.nickName = "微信用户";
        this.setCreateBy("admin");
        this.setCreateTime(new Date());
        this.setUserType(UserTypeEnum.WXXCX.getCode());
        this.setPassword(SecurityUtils.encryptPassword("123456."));
        this.init();
    }

    public String getGuideStatus() {
        return guideStatus;
    }

    public void setGuideStatus(String guideStatus) {
        this.guideStatus = guideStatus;
    }

    public BigDecimal getTokenBalanceTotal() {
        return tokenBalanceTotal;
    }

    public void setTokenBalanceTotal(BigDecimal tokenBalanceTotal) {
        this.tokenBalanceTotal = tokenBalanceTotal;
    }

    public BigDecimal getTokenBalance() {
        return tokenBalance;
    }

    public void setTokenBalance(BigDecimal tokenBalance) {
        this.tokenBalance = tokenBalance;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getRoleType() {
        return roleType;
    }

    public void setRoleType(String roleType) {
        this.roleType = roleType;
    }

    public String getHobbyTypes() {
        return hobbyTypes;
    }

    public void setHobbyTypes(String hobbyTypes) {
        this.hobbyTypes = hobbyTypes;
    }

    public String getGuideStyle() {
        return guideStyle;
    }

    public void setGuideStyle(String guideStyle) {
        this.guideStyle = guideStyle;
    }

    public SysUser(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public boolean isAdmin()
    {
        return isAdmin(this.userId);
    }

    public static boolean isAdmin(Long userId)
    {
        return userId != null && 1L == userId;
    }

    public Long getDeptId()
    {
        return deptId;
    }

    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    @Xss(message = "用户昵称不能包含脚本字符")
    @Size(min = 0, max = 30, message = "用户昵称长度不能超过30个字符")
    public String getNickName()
    {
        return nickName;
    }

    public void setNickName(String nickName)
    {
        this.nickName = nickName;
    }

    @Xss(message = "用户账号不能包含脚本字符")
    @NotBlank(message = "用户账号不能为空")
    @Size(min = 0, max = 30, message = "用户账号长度不能超过30个字符")
    public String getUserName()
    {
        return userName;
    }

    public void setUserName(String userName)
    {
        this.userName = userName;
    }

    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
    public String getEmail()
    {
        return email;
    }

    public void setEmail(String email)
    {
        this.email = email;
    }

    @Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
    public String getPhonenumber()
    {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber)
    {
        this.phonenumber = phonenumber;
    }

    public String getSex()
    {
        return sex;
    }

    public void setSex(String sex)
    {
        this.sex = sex;
    }

    public String getAvatar()
    {
        return avatar;
    }

    public void setAvatar(String avatar)
    {
        this.avatar = avatar;
    }

    public String getPassword()
    {
        return password;
    }

    public void setPassword(String password)
    {
        this.password = password;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getLoginIp()
    {
        return loginIp;
    }

    public void setLoginIp(String loginIp)
    {
        this.loginIp = loginIp;
    }

    public Date getLoginDate()
    {
        return loginDate;
    }

    public void setLoginDate(Date loginDate)
    {
        this.loginDate = loginDate;
    }

    public SysDept getDept()
    {
        return dept;
    }

    public void setDept(SysDept dept)
    {
        this.dept = dept;
    }

    public List<SysRole> getRoles()
    {
        return roles;
    }

    public void setRoles(List<SysRole> roles)
    {
        this.roles = roles;
    }

    public Long[] getRoleIds()
    {
        return roleIds;
    }

    public void setRoleIds(Long[] roleIds)
    {
        this.roleIds = roleIds;
    }

    public Long[] getPostIds()
    {
        return postIds;
    }

    public void setPostIds(Long[] postIds)
    {
        this.postIds = postIds;
    }

    public Long getRoleId()
    {
        return roleId;
    }

    public void setRoleId(Long roleId)
    {
        this.roleId = roleId;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getUserRecCode() {
        return userRecCode;
    }

    public void setUserRecCode(String userRecCode) {
        this.userRecCode = userRecCode;
    }

    public String getGuidePersona() {
        return guidePersona;
    }

    public void setGuidePersona(String guidePersona) {
        this.guidePersona = guidePersona;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("userId", getUserId())
            .append("deptId", getDeptId())
            .append("userName", getUserName())
            .append("nickName", getNickName())
            .append("email", getEmail())
            .append("phonenumber", getPhonenumber())
            .append("sex", getSex())
            .append("avatar", getAvatar())
            .append("password", getPassword())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("loginIp", getLoginIp())
            .append("loginDate", getLoginDate())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("dept", getDept())
            .toString();
    }

    public String getLanguageFlag() {
        return languageFlag;
    }

    public void setLanguageFlag(String languageFlag) {
        this.languageFlag = languageFlag;
    }

    @Data
    @Builder
    public static class GuideStatus {
        /**
         * 景区列表引导状态
         */
        private boolean scenicSpotListStatus;
        /**
         * 景区导览引导状态
         */
        private boolean scenicSpotGuideStatus;
        /**
         * 常见问题引导状态
         */
        private boolean commonProblemStatus;

        public static GuideStatus genDefault(){
            return GuideStatus.builder()
                    .scenicSpotListStatus(false)
                    .scenicSpotGuideStatus(false)
                    .commonProblemStatus(false)
                    .build();
        }

    }



    public void init(){
//        this.roleType = RoleTypeEnum.ADULT.getCode();
//        this.guideStyle = GuideStyleEnum.CULTURE.getCode();
        this.hobbyTypes = JSON.toJSONString(HobbyTypeEnum.getDefaultHobby());
        this.guidePersona = GuidePersonaEnum.GIRL.getCode();
        this.tokenBalance = BigDecimal.ZERO;
        this.tokenBalanceTotal = BigDecimal.ZERO;
        GuideStatus guideStatus = GuideStatus.genDefault();
        this.guideStatus = JSON.toJSONString(guideStatus);
        this.languageFlag = "Chinese";
    }


    public void saveExplanationStyle(SaveExplanationStyleCmd cmd) {
//        this.roleType = cmd.getRoleType();
//        this.guideStyle = cmd.getGuideStyle();
        List<String> stringList = cmd.getHobbyTypes();
        Collections.sort(stringList);
        this.hobbyTypes = JSON.toJSONString(stringList);
        this.guidePersona = cmd.getGuidePerson();
        this.languageFlag = cmd.getLanguageFlag();
        this.setUpdateTime(new Date());
    }

    public List<UserScenic> getSysUserScenics() {
        return sysUserScenics;
    }

    public void setSysUserScenics(List<UserScenic> sysUserScenics) {
        this.sysUserScenics = sysUserScenics;
    }
}
