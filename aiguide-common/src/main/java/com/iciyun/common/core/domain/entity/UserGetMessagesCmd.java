package com.iciyun.common.core.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserGetMessagesCmd implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 会话ID
     */
    private String conversationId;

    /**
     * 消息列表的排序方式。
     * desc：（默认）按创建时间倒序
     * asc：按创建时间正序
     */
    private String order;

    //待查看的 Chat ID
    private String chatId;

    //查看指定位置之前的消息。
    //默认为 0，表示不指定位置。如需向前翻页，则指定为返回结果中的 first_id
    private String beforeId;

    /**
     * 查看指定位置之后的消息。
     * 默认为 0，表示不指定位置。如需向后翻页，则指定为返回结果中的 last_id。
     */
    private String afterId;

    //每次查询返回的数据量。默认为 50，取值范围为 1~50。
    private int limit;

}
