package com.iciyun.common.core.domain.entity;

import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RoomConifgCmd implements Serializable {
    private static final long serialVersionUID = 1L;

    private AudioConfig audioConfig;

    @Setter
    @Getter
    public static class AudioConfig {

        /**
         * 房间音频编码格式，支持设置为：
         * AACLC：AAC-LC 编码格式。
         * G711A：G711A 编码格式。
         * OPUS：（默认）Opus 编码格式。
         * G722：G.722 编码格式。
         */
        private String codec;
    }

    private videoConfig videoConfig;


    @Setter
    @Getter
    public static class videoConfig {

        /**
         * 房间视频编码格式，支持设置为：
         * H264：（默认）H264 编码格式。
         * BYTEVC1：火山引擎自研的视频编码格式。
         */
        private String codec;

        /**
         * 房间视频流类型，支持 main/screen, main:
         * 主流。包括：「由摄像头 / 麦克风通过内部采集机制，采集到的流。」和「通过自定义采集，采集到的流。」，screen：屏幕流
         */
        private String streamVideoType;

    }

}



