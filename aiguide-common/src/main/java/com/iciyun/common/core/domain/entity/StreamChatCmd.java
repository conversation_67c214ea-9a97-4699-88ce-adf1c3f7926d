package com.iciyun.common.core.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StreamChatCmd implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;


    //会话ID
    private String conversationId;

    //botId
    private String botId;

    private Boolean stream;

    private String question;

    /**
     * 附加参数，通常用于特殊场景下指定一些必要参数供模型判断，例如指定经纬度，并询问智能体此位置的天气。
     * 自定义键值对格式，其中键（key）仅支持设置为：
     * latitude：纬度，此时值（Value）为纬度值，例如 39.9800718。
     * longitude：经度，此时值（Value）为经度值，例如 116.309314。
     */
    private Map<String, String> extraParams;

    //附加信息
    private Map<String, String> metaData;

    //自定义变化，可以动态更改 systemPrompt 内容
    private Map<String, Object> custom_variables;


}
