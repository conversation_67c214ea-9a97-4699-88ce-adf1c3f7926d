package com.iciyun.common.core.domain.entity;

import com.iciyun.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class  ToolOutputdCmd implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 工具的执行结果  在 发起非流式对话 事件里，data 字段中的 tool_calls : type
     */
    private String output;

    //上报运行结果的 ID。 在 发起非流式对话 事件里，data 字段中的 tool_calls : tool_call_id 。
    private String tool_call_id;

}
