package com.iciyun.common.core.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GetVoicesCmd implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 查看音色列表时是否过滤掉系统音色。
     * true：过滤系统音色
     * false：（默认）不过滤系统音色
     */
    private Boolean filterSystemVoice;

    /**
     * 查询结果分页展示时，此参数用于设置查看的页码。最小值为 1，默认为 1。
     */
    private Integer pageNum;

    /**
     * 查询结果分页展示时，此参数用于设置每页返回的数据量。取值范围为 1~100，默认为 100。
     */
    private Integer pageSize;


}
