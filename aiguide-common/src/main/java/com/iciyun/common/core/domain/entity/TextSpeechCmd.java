package com.iciyun.common.core.domain.entity;

import com.iciyun.common.core.domain.model.UserStyleDto;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TextSpeechCmd implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 合成语音的文本，经由 UTF-8 编码。长度限制为 1024 字节
     */
    private String input;

    /**
     * 音色 ID
     */
    private String voiceId;

    /**
     * 音频文件的编码格式，支持设置为：
     * wav：返回二进制 wav 音频。
     * pcm：返回二进制 pcm 音频。
     * ogg_opus：返回多段含 opus 压缩分片音频。
     * mp3：（默认）返回二进制 mp3 音频。
     */
    private String responseFormat;

    /**
     * 语速，取值范围为 0.2~3，通常保留一位小数即可。
     * 其中 0.2 表示 0.2 倍速，3 表示 3 倍速。默认为 1，表示 1 倍速。
     */
    private Number speed;

    /**
     * 音频采样率，单位为 Hz。
     * 8000：8k
     * 16000：16k
     * 22050：22.05k
     * 24000：（默认）24k
     * 32000：32k
     * 44100：44.1k
     * 48000：48k
     */
    private Integer sampleRate;


    //是否缓存 true 缓存  false 不缓存
    private boolean cacheFlag;

    //
    private ScenicCache scenicCache;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ScenicCache implements Serializable {

        private static final long serialVersionUID = 1L;
        @NotBlank
        private String scenicId; // 景区id

        @NotBlank
        private String scenicName; // 景区名

        private String scenicLabel; // 景区label

        private Long userId;

        private String question;

        private String syncId;

        private String voiceId;

        private String text;

        private UserStyleDto userStyleDto;
    }
}
