package com.iciyun.common.core.domain.entity;

import com.iciyun.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserCreateMessageCmd implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 会话ID
     */
    private String conversationId;

    /**
     * 发送这条消息的实体
     *
     * user：代表该条消息内容是用户发送的。
     * assistant：代表该条消息内容是 Bot 发送的。
     */
    private String role;

    /**
     * 文本和文件消息
     *  "content": "[{\"type\":\"text\",\"text\":\"帮我看看这个PDF里有什么内容？\"},
     *              {\"type\":\"file\",\"file_url\":\"https://lf3-appstore-sign.oceancloudapi.com/ocean-cloud-tos/eaafba63-0d96-4ea6-b60c-fbadcf2c25e9.?lk3s=edeb9e45&x-expires=1718296132&x-signature=YtlsUsvSeLJi6x31I%2F4S9X53Y6Y%3D\"}]"
     */
    //发送的消息内容
    private String content;

    /**
     * 消息类型
     * text：文本
     * object_string：多模态内容，即文本和文件的组合、文本和图片的组合
     */
    private String contentType;

}
