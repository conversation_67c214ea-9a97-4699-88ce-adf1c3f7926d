package com.iciyun.common.core.domain.entity;

import com.iciyun.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CombinationCmd extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 图像Url
     */
    private String imgUrl;
    private String userPhone;
    private String openId;
    private Long touristId;
    private String touristName;

    private MultipartFile file;

}
