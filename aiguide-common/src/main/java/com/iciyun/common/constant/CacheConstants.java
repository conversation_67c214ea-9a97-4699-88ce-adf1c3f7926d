package com.iciyun.common.constant;

/**
 * 缓存的key 常量
 *
 * <AUTHOR>
 */
public class CacheConstants {
    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";

    /**
     * 限流 redis key
     */
    public static final String RATE_LIMIT_KEY = "rate_limit:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 景区缓存key
     */
    public static final String scenicPixKey = "scenic:";

    /**
     * 景区缓存key
     */
    public static final String userCodeKey = "sys:code:";

    //cozeToken
    public static final String cozeTokenKey = "chat:cozeToken";

    //14 分钟
    public static final int cozeTokenTime = 14;

    public static final String DATA_START = "data:";
    public static final String EVENT_DELTA = "event:conversation.message.delta";
    public static final String EVENT_DONE = "event:done";
    public static final String EVENT_WAIT = "event:wait";
    public static final String EVENT_PRE = "event:pre";
    public static final String CHAT_COMPLETED = "event:conversation.chat.completed";
    public static final String MESSAGE_COMPLETED = "event:conversation.message.completed";

    public static final String cacheScenicKey = "cache:scenic:";

    /**
     *  渠道下的景区 key
     */
    public static final String CHANNEL_SCENIC_KEY = "channel:";

    public static final String CHANNEL_SCENIC_FIELD_1 = "channelName";

    public static final String CHANNEL_SCENIC_FIELD_2 = "scienics";

}
