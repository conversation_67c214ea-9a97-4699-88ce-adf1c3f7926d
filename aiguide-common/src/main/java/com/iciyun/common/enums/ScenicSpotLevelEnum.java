package com.iciyun.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

@Getter
public enum ScenicSpotLevelEnum {

    FIVE("FIVE", "5A"),
    FOUR("FOUR", "4A"),
    THREE("THREE", "3A"),
//    TWO("FIVE", "5A"),
//    ONE("FIVE", "5A"),

    ;

    private final String code;
    private final String desc;

    ScenicSpotLevelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<Map<String, String>> toList() {
        return Arrays.stream(ScenicSpotLevelEnum.values()).map(hobby -> {
            Map<String, String> map = new HashMap<>();
            map.put("code", hobby.getCode());
            map.put("desc", hobby.getDesc());
            return map;
        }).toList();
    }

    public static ScenicSpotLevelEnum of(String code) {
        return Stream.of(ScenicSpotLevelEnum.values())
                .filter(statusEnum -> statusEnum.code.equals(code))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }


}
