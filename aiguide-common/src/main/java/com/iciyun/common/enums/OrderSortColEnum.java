package com.iciyun.common.enums;

/**
 * 订单排序字段
 *
 * <AUTHOR>
 */
public enum OrderSortColEnum {
    ALLAMOUNT("allAmount", "总收入"),
    ALLORDERNUM("allOrderNum", "订单量"),
    ALLPOINTNUM("allPointNum", "点位数"),
    AGENCYAMOUNT("agencyAmount", "机构收入"),

    ORDERAMOUNT("orderAmount", "订单收入"),
    ;

    private final String code;
    private final String info;

    OrderSortColEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
