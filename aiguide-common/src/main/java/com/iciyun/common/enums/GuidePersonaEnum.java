package com.iciyun.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

@Getter
public enum GuidePersonaEnum {

    BOY("BOY", "男"),
    GIRL("GIRL", "女"),
    ;

    private final String code;
    private final String desc;

    GuidePersonaEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<Map<String, String>> toList() {
        return Arrays.stream(GuidePersonaEnum.values()).map(style -> {
            Map<String, String> map = new HashMap<>();
            map.put("code", style.getCode());
            map.put("desc", style.getDesc());
            return map;
        }).toList();
    }

    public static GuidePersonaEnum of(String code) {
        return Stream.of(GuidePersonaEnum.values())
                .filter(guideStyleEnum -> guideStyleEnum.code.equals(code))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }

}
