package com.iciyun.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * 合作方类型
 *
 */
public enum PartnerBusinessType implements IEnum<Integer> {
    COOPERATION(1, "机构方"),
    PLATFORM(9, "平台"),

    SCENIC_INFO(4, "景区数据采集员"),
    ;

    private final Integer code;
    private final String info;

    PartnerBusinessType(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    @Override
    public Integer getValue() {
        return value();
    }

    public Integer value() {
        return code;
    }

}
