package com.iciyun.common.enums;

/**
 * 用户类型
 *
 * <AUTHOR>
 */
public enum FreeTypeEnum {
    YD("0", "游豆免费次数标识"),
    HZ("1", "合作景区免费次数标识"),

    ;

    private final String code;
    private final String info;

    FreeTypeEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
