package com.iciyun.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * 用户状态
 *
 * <AUTHOR>
 */
public enum ScenicStatus implements IEnum<Integer> {
    OK(0, "正常"), DISABLE(1, "停用"), DELETED(2, "删除");

    private final Integer code;
    private final String info;

    ScenicStatus(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    @Override
    public Integer getValue() {
        return value();
    }

    public Integer value() {
        return code;
    }

}
