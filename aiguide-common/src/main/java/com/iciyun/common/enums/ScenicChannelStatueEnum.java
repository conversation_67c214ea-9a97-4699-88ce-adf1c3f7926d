package com.iciyun.common.enums;

/**
 * 用户类型
 *
 * <AUTHOR>
 */
public enum ScenicChannelStatueEnum {
    ON("0", "渠道上线"),
    OFF("1", "渠道下线"),
    DEL("2", "渠道已删除");

    private final String code;
    private final String info;

    ScenicChannelStatueEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
