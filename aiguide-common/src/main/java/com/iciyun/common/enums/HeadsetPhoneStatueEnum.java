package com.iciyun.common.enums;

/**
 * 用户类型
 *
 * <AUTHOR>
 */
public enum HeadsetPhoneStatueEnum {
    NO_GET("0", "未领取"),
    GET("1", "已领取"),
    APPLY("2", "申请退款"),
    REFUND("3", "已退款");

    private final String code;
    private final String info;

    HeadsetPhoneStatueEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
