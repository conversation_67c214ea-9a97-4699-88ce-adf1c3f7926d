package com.iciyun.common.enums;

/**
 * 用户常量信息
 *
 * <AUTHOR>
 */
public enum AdminExceptionType {

    USER_LIST_NOT_EXIST(3000, "用户列表参数异常"),

    ;

    private final int code;

    private final String message;

    AdminExceptionType(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
