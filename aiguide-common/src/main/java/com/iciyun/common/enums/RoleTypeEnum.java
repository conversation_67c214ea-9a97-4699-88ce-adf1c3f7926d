package com.iciyun.common.enums;

import lombok.Getter;

import java.util.*;
import java.util.stream.Stream;

@Getter
public enum RoleTypeEnum {

    CHILD("11586117259522","CHILD", "孩子"),
    STUDENT("12940156318210","STUDENT", "学生"),
    ADULT("58859486031618","ADULT", "成人"),
    ELDER("11584796397826","ELDER", "长者"),
    ;

    private final String voiceId;
    private final String code;
    private final String desc;

    RoleTypeEnum(String voiceId, String code, String desc) {
        this.voiceId = voiceId;
        this.code = code;
        this.desc = desc;
    }

    public static List<Map<String, String>> toList() {

        return Arrays.stream(RoleTypeEnum.values()).map(roleType -> {
            Map<String, String> map = new HashMap<>();
            map.put("code", roleType.getCode());
            map.put("desc", roleType.getDesc());
            return map;
        }).toList();
    }

    public static RoleTypeEnum of(String code) {
        return Stream.of(RoleTypeEnum.values())
                .filter(roleTypeEnum -> roleTypeEnum.code.equals(code))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }

}
