package com.iciyun.common.enums;

import lombok.Getter;

import java.util.*;
import java.util.stream.Stream;

@Getter
public enum HobbyTypeEnum {

    HISTORY("HISTORY", "历史考证"),
    SCRIPT_IMMERSION("SCRIPT_IMMERSION", "剧本沉浸"),
    POETRY("POETRY", "诗歌漫游"),
    NATURE("NATURE", "自然漫步"),
    ART("ART", "艺术创作"),
    CULTURE("CULTURE", "文化内涵"),
    CHAT("CHAT", "聊天陪伴"),
    PARENT_CHILD("PARENT_CHILD", "亲子教育"),
//    GEOGRAPHY("GEOGRAPHY", "地理"),
    //    GS("GS", "故事"),
//    RW("RW", "人物"),
//    BUILDING("BUILDING", "建筑"),
    ;

    private final String code;
    private final String desc;

    HobbyTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<Map<String, String>> toList() {
        return Arrays.stream(HobbyTypeEnum.values()).map(hobby -> {
            Map<String, String> map = new HashMap<>();
            map.put("code", hobby.getCode());
            map.put("desc", hobby.getDesc());
            return map;
        }).toList();
    }

    public static List<String> getDefaultHobby() {

        List<String> defaultHobbies = new ArrayList<String>();
        defaultHobbies.add(CULTURE.getCode());

        return defaultHobbies;
    }

    public static HobbyTypeEnum of(String code) {

        return Stream.of(HobbyTypeEnum.values())
                .filter(hobbyTypeEnum -> hobbyTypeEnum.code.equals(code))
                .findFirst()
                .orElse(CULTURE);
    }

    public static void main(String[] args) {
        HobbyTypeEnum hobbyTypeEnum = HobbyTypeEnum.of("PARENT_CHILD");
        System.out.println(hobbyTypeEnum.desc);
    }

}
