package com.iciyun.common.enums;

/**
 * 景区类型
 *
 */
public enum ScenicTypeEnum {
    GENERAL("1", "一般景区"),
    MUSEUM("2", "博物馆"),
    ANIMAL("3", "动物园"),
    BOTANY("4", "植物园");

    private final String code;
    private final String info;

    ScenicTypeEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
