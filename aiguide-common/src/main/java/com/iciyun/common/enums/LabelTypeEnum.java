package com.iciyun.common.enums;

import lombok.Getter;

import java.util.stream.Stream;

@Getter
public enum LabelTypeEnum {

    POI("poi", ""),
    BEACON("beacon", "信标景点"),

    IMAGE("image", "照片入库"),
    ;

    private final String code;
    private final String desc;

    LabelTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }



    public static LabelTypeEnum of(String code) {
        return Stream.of(LabelTypeEnum.values())
                .filter(hobbyTypeEnum -> hobbyTypeEnum.code.equals(code))
                .findFirst()
                .orElse(null);
    }

    public static void main(String[] args) {
        LabelTypeEnum hobbyTypeEnum = LabelTypeEnum.of("PARENT_CHILD");
        System.out.println(hobbyTypeEnum.desc);
    }

}
