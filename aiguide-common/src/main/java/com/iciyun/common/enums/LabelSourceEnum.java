package com.iciyun.common.enums;

import lombok.Getter;

import java.util.stream.Stream;

@Getter
public enum LabelSourceEnum {

    MAP("map", "地图"),
    MINI("mini", "小程序采集"),
    PC("pc", "PC标记"),
    BEACON("beacon", "信标"),
    IMAGE("image", "图片转换"),
    ;

    private final String code;
    private final String desc;

    LabelSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }



    public static LabelSourceEnum of(String code) {
        return Stream.of(LabelSourceEnum.values())
                .filter(hobbyTypeEnum -> hobbyTypeEnum.code.equals(code))
                .findFirst()
                .orElse(null);
    }

    public static void main(String[] args) {
        LabelSourceEnum hobbyTypeEnum = LabelSourceEnum.of("PARENT_CHILD");
        System.out.println(hobbyTypeEnum.desc);
    }

}
