package com.iciyun.common.enums;

/**
 * 用户类型
 *
 * <AUTHOR>
 */
public enum OrderItemEnum {
    AIGUIDE("0", "AI导游服务"),
    HEADSETPHONE("1", "耳机服务");

    private final String code;
    private final String info;

    OrderItemEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
