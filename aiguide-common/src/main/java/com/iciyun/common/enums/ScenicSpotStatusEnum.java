package com.iciyun.common.enums;

import lombok.Getter;

import java.util.*;
import java.util.stream.Stream;

@Getter
public enum ScenicSpotStatusEnum {

    ON("0", "上线"),
    OFF("1", "下线"),

    ;

    private final String code;
    private final String desc;

    ScenicSpotStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<Map<String, String>> toList() {
        return Arrays.stream(ScenicSpotStatusEnum.values()).map(hobby -> {
            Map<String, String> map = new HashMap<>();
            map.put("code", hobby.getCode());
            map.put("desc", hobby.getDesc());
            return map;
        }).toList();
    }

    public static ScenicSpotStatusEnum of(String code) {
        return Stream.of(ScenicSpotStatusEnum.values())
                .filter(statusEnum -> statusEnum.code.equals(code))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }


}
