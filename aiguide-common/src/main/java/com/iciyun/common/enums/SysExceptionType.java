package com.iciyun.common.enums;

/**
 * 用户常量信息
 *
 * <AUTHOR>
 */
public enum SysExceptionType {

    USER_SORT_NOT_EXIST(4000, "用户排名不存在"),

    USER_SORT_FIRST(4001, "用户不存在付节点"),

    ;

    private final int code;

    private final String message;

    SysExceptionType(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
