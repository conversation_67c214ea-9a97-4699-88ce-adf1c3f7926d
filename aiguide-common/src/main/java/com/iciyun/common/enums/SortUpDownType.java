package com.iciyun.common.enums;

/**
 * 用户常量信息
 *
 * <AUTHOR>
 */
public enum SortUpDownType {

    UP(0, "向上"),

    DOWN(2, "向下"),

    NO_CHANGE(3, "不变"),

    ;

    private final int code;

    private final String message;

    SortUpDownType(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
