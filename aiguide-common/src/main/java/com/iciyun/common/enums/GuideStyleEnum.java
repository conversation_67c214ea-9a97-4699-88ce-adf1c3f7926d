package com.iciyun.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

@Getter
public enum GuideStyleEnum {

    LEISURE("LEISURE", "休闲观光"),
    CULTURE("CULTURE", "文化体验"),
    STUDY("STUDY", "研学考察"),
    FQYM("FQYM", "风趣幽默"),
    ;

    private final String code;
    private final String desc;

    GuideStyleEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<Map<String, String>> toList() {
        return Arrays.stream(GuideStyleEnum.values()).map(style -> {
            Map<String, String> map = new HashMap<>();
            map.put("code", style.getCode());
            map.put("desc", style.getDesc());
            return map;
        }).toList();
    }

    public static GuideStyleEnum of(String code) {
        return Stream.of(GuideStyleEnum.values())
                .filter(guideStyleEnum -> guideStyleEnum.code.equals(code))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }

}
