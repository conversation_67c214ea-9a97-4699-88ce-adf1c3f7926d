package com.iciyun.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

@Getter
public enum LabelStatusEnum {

    ZERO("0", "未审核"),
    ONE("1", "审核通过"),

    ;

    private final String code;
    private final String desc;

    LabelStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<Map<String, String>> toList() {
        return Arrays.stream(LabelStatusEnum.values()).map(hobby -> {
            Map<String, String> map = new HashMap<>();
            map.put("code", hobby.getCode());
            map.put("desc", hobby.getDesc());
            return map;
        }).toList();
    }

    public static LabelStatusEnum of(String code) {
        return Stream.of(LabelStatusEnum.values())
                .filter(statusEnum -> statusEnum.code.equals(code))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }


}
