<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iciyun.system.mapper.UserSuggestionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iciyun.system.domain.UserSuggestion">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="user_phone" property="userPhone" />
        <result column="type" property="type" />
        <result column="content" property="content" />
        <result column="url_list" property="urlList" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="queryList" parameterType="com.iciyun.system.domain.qo.UserSuggestionQo" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            user_suggestion us
        <where>
            <if test="userPhone != null and userPhone != ''">
                AND us.user_phone like concat('%', #{userPhone}, '%')
            </if>
            <if test="content != null and content != ''">
                AND us.content like concat('%', #{content}, '%')
            </if>
            <if test="beginTime != null"><!-- 开始时间检索 -->
                AND to_char(us.create_time,'yyy-MM-dd') &gt;= to_char(#{beginTime}::timestamp,'yyy-MM-dd')
            </if>
            <if test="endTime != null"><!-- 结束时间检索 -->
                AND to_char(us.create_time,'yyy-MM-dd') &lt;= to_char(#{endTime}::timestamp,'yyy-MM-dd')
            </if>
        </where>
        ORDER BY us.num DESC,us.id DESC
    </select>

    <select id="getMaxNum" resultType="int">
        SELECT "max"(us.num) FROM user_suggestion us
    </select>

    <update id="topSuggestion">
        UPDATE user_suggestion SET num = #{num} + 1 WHERE id = #{id}
    </update>

</mapper>
