<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iciyun.system.mapper.BaseConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iciyun.system.domain.BaseConfig">
        <id column="id" property="id" />
        <result column="register_reward" property="registerReward" />
        <result column="referral_reward" property="referralReward" />
        <result column="referral_first_reward" property="referralFirstReward" />
        <result column="lectures_num" property="lecturesNum" />
        <result column="five_expend" property="fiveExpend" />
        <result column="four_expend" property="fourExpend" />
        <result column="three_expend" property="threeExpend" />
        <result column="guide_reward" property="guideReward" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="cooper_lectures_num" property="cooperLecturesNum" />
    </resultMap>

    <select id="queryOne" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            base_config
        ORDER BY id DESC
        LIMIT 1
    </select>

</mapper>
