<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iciyun.system.mapper.SysTouristLabelMapper">

    <resultMap type="com.iciyun.system.domain.SysTouristLabel" id="SysTouristLabelResult">
        <result property="id" column="id"/>
        <result property="touristId" column="tourist_id"/>
        <result property="touristName" column="tourist_name"/>
        <result property="labelName" column="label_name"/>
        <result property="imageWidth" column="image_width"/>
        <result property="imageHeight" column="image_height"/>
        <result property="x1y1" column="x1y1"/>
        <result property="x2y2" column="x2y2"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="remark" column="remark"/>
        <result property="latitude" column="latitude"/>
        <result property="longitude" column="longitude"/>
        <result property="cityName" column="city_name"/>
        <result property="cityCode" column="city_code"/>
        <result property="joinCluster" column="join_cluster"/>
        <result property="labelText" column="label_text"/>
        <result property="cacheOk" column="cache_ok"/>
        <result property="audioCacheOk" column="audio_cache_ok"/>
        <result property="status" column="status"/>
        <result property="category" column="category"/>
        <result property="categoryCode" column="category_code"/>
        <result property="isImportant" column="is_important"/>
        <result property="radius" column="radius"/>
        <result property="repetition" column="repetition"/>
        <result property="intervalTime" column="interval_time"/>
        <result property="showStatus" column="show_status"/>
        <result property="explainStatus" column="explain_status"/>
        <result property="customizeCategoryCode" column="customize_category_code"/>
        <result property="note" column="note"/>
        <result property="labelSource" column="label_source"/>
        <result property="labelType" column="label_type"/>
        <result property="beaconCode" column="beacon_code"/>
        <result property="labelTextDefault" column="label_text_default"/>
    </resultMap>

    <sql id="selectSysTouristLabelVo">
        select id,
               tourist_id,
               tourist_name,
               label_name,
               image_width,
               image_height,
               x1y1,
               x2y2,
               create_by,
               create_time,
               remark,
               cache_ok,
               audio_cache_ok,
               label_text,
               label_text_default,
               note
        from sys_tourist_label
    </sql>

    <select id="labelCacheInfo" parameterType="com.iciyun.system.domain.dto.LabelCacheInfoDto">
        SELECT count(1) as cnt, 'cacheOk' as source
        FROM sys_tourist_label
        WHERE cache_ok = 1
        UNION
        SELECT count(1), 'total'
        FROM sys_tourist_label
        UNION
        SELECT count(1), 'audioCacheOk'
        FROM sys_tourist_label
        WHERE cache_ok = 1
          AND audio_cache_ok = 1
    </select>

    <select id="selectSysTouristLabelList" parameterType="com.iciyun.system.domain.SysTouristLabel"
            resultMap="SysTouristLabelResult">
        <include refid="selectSysTouristLabelVo"/>
        <where>
            <if test="touristId != null ">and tourist_id = #{touristId}</if>
            <if test="touristName != null  and touristName != ''">and tourist_name like concat('%', #{touristName},
                '%')
            </if>
            <if test="labelName != null  and labelName != ''">and label_name like concat('%', #{labelName}, '%')</if>
            <if test="imageWidth != null ">and image_width = #{imageWidth}</if>
            <if test="imageHeight != null ">and image_height = #{imageHeight}</if>
            <if test="x1y1 != null  and x1y1 != ''">and x1y1 = #{x1y1}</if>
            <if test="x2y2 != null  and x2y2 != ''">and x2y2 = #{x2y2}</if>
            <if test="cacheOk != null">and cache_ok = #{cacheOk}</if>
            <if test="audioCacheOk != null">and audio_cache_ok = #{audioCacheOk}</if>
        </where>
        ORDER BY id DESC
    </select>


    <select id="selectSysTouristLabelList_Simple" parameterType="com.iciyun.system.domain.SysTouristLabel"
            resultMap="SysTouristLabelResult">
        select id,
        tourist_id,
        label_name,
        note
        from sys_tourist_label
        <where>
            <if test="touristId != null ">and tourist_id = #{touristId}</if>
            <if test="labelName != null  and labelName != ''">and label_name like concat('%', #{labelName}, '%')</if>
        </where>
        ORDER BY id DESC
    </select>


    <select id="selectSysTouristLabelById" parameterType="Long" resultMap="SysTouristLabelResult">
        <include refid="selectSysTouristLabelVo"/>
        where id = #{id}
    </select>

    <sql id="Query_Condition">
        <where>
            <if test="id != null ">and t.id = #{id}</if>
            <if test="touristId != null  and touristId != ''">and t.tourist_id = #{touristId}</if>
            <if test="touristName != null  and touristName != ''">and t.tourist_name = #{touristName}</if>
            <if test="labelName != null  and labelName != ''">and label_name like concat('%', #{labelName}, '%')</if>
            <choose>
                <when test="queryCase == 0 ">
                    and label_name like concat('%', #{labelName}, '%')
                </when>
                <when test="queryCase == 1 ">
                    and label_name LIKE tourist_name || '%';
                </when>
            </choose>
        </where>
    </sql>

    <select id="selectSyncTouris" resultMap="SysTouristLabelResult">
        SELECT
        t.*
        FROM sys_tourist_label t
        <include refid="Query_Condition"/>

    </select>


    <select id="selectOne_unCache_asc" resultMap="SysTouristLabelResult">
        SELECT t.*
        FROM sys_tourist_label t
        where t.cache_ok = 0
        order by id asc limit 1
    </select>

    <select id="selectOne_unAudioCache_asc" resultMap="SysTouristLabelResult">
        SELECT t.*
        FROM sys_tourist_label t
        where t.audio_cache_ok = 0
          and t.cache_ok = 1
        order by id asc limit 1
    </select>

    <select id="selectOne_unAudioCache_desc" resultMap="SysTouristLabelResult">
        SELECT t.*
        FROM sys_tourist_label t
        where t.audio_cache_ok = 0
          and t.cache_ok = 1
        order by id desc limit 1
    </select>

    <select id="selectOne_unCache_desc" resultMap="SysTouristLabelResult">
        SELECT t.*
        FROM sys_tourist_label t
        where t.cache_ok = 0
        order by id desc limit 1
    </select>

    <!-- 查询随身讲景点 -->
    <select id="queryTouristAttractions" parameterType="com.iciyun.system.domain.bo.TouristAttractionsQry"
            resultMap="SysTouristLabelResult">
        <![CDATA[
        WITH filtered_points AS (SELECT T.*,
                                        ST_DistanceSphere(
                                                ST_SetSRID(ST_MakePoint(NULLIF(trim(T.LATITUDE), '')::NUMERIC,
                                                                        NULLIF(trim(T.LONGITUDE), '')::NUMERIC), 4326),
                                                ST_SetSRID(ST_MakePoint(#{lat}::numeric, #{lng}::numeric), 4326)
                                            ) AS distance
                                 FROM SYS_TOURIST_LABEL T
                                 WHERE T.TOURIST_ID = #{scenicSpotId}
                                   and T.LATITUDE IS NOT NULL)
        SELECT *
        FROM filtered_points
        WHERE distance <= #{radius}
        ORDER BY distance ASC LIMIT 1
        ]]>
    </select>
    <select id="selectOne" resultMap="SysTouristLabelResult">
        SELECT
        t.*
        FROM sys_tourist_label t
        <include refid="Query_Condition"/>
    </select>

    <select id="queryNearestNonRepeatLabel" parameterType="com.iciyun.system.domain.bo.TouristAttractionsQry"
            resultMap="SysTouristLabelResult">

        WITH filtered_points AS (
        SELECT
        T.*,
        CASE
        WHEN T.POLYGON IS NOT NULL AND TRIM(T.POLYGON) <![CDATA[ <> ]]> '' THEN
        ST_DistanceSphere(
        ST_SetSRID(ST_MakePoint(#{lng}::numeric, #{lat}::numeric), 4326),
        ST_SetSRID(
        ST_GeomFromText('POLYGON((' ||
        REGEXP_REPLACE(
        REGEXP_REPLACE(T.POLYGON, ';', ', ', 'g'),
        '([0-9\.]+),([0-9\.]+)', '\2 \1', 'g'
        ) || ', ' ||
        REGEXP_REPLACE(SPLIT_PART(T.POLYGON, ';', 1), '([0-9\.]+),([0-9\.]+)', '\2 \1', 'g') || '))'
        ),
        4326
        )
        )
        ELSE
        ST_DistanceSphere(
        ST_SetSRID(ST_MakePoint(#{lng}::numeric, #{lat}::numeric), 4326),
        ST_SetSRID(ST_MakePoint(NULLIF(trim(T.LONGITUDE), '')::NUMERIC, NULLIF(trim(T.LATITUDE), '')::NUMERIC), 4326)
        )
        END AS distance
        FROM SYS_TOURIST_LABEL T
        WHERE T.LATITUDE IS NOT NULL AND T.LONGITUDE IS NOT NULL AND T.TOURIST_ID = #{scenicSpotId}
        <if test=" explainedIds != null and explainedIds.size() > 0 ">
            AND T.ID NOT IN
            <foreach collection="explainedIds" item="item" index="index" open="(" separator="," close=")">
                #{item, jdbcType=BIGINT}
            </foreach>
        </if>

        )
        SELECT
        *
        FROM filtered_points
        WHERE distance <![CDATA[   <=     ]]> #{radius}
        ORDER BY distance ASC
        LIMIT 1

    </select>
    <select id="queryNearestRepeatLabel" resultType="com.iciyun.system.domain.SysTouristLabel">
        WITH filtered_points AS (SELECT
                                        t.id,
                                        t.tourist_id,
                                        t.tourist_name,
                                        t.label_name,
                                        t.show_status,
                                        t.explain_status,
                                        t.repetition,
                                        t.interval_time,
                                        t.radius,
                                        CASE WHEN T.POLYGON IS NOT NULL AND TRIM(T.POLYGON) <![CDATA[ <> ]]> '' THEN
                                                ST_DistanceSphere(
                                                        ST_SetSRID(ST_MakePoint(#{lng}::numeric, #{lat}::numeric), 4326),
                                                        ST_SetSRID(
                                                                ST_GeomFromText('POLYGON((' ||
                                                                                REGEXP_REPLACE(
                                                                                        REGEXP_REPLACE(T.POLYGON, ';', ', ', 'g'),
                                                                                        '([0-9\.]+),([0-9\.]+)',
                                                                                        '\2 \1', 'g'
                                                                                    ) || ', ' ||
                                                                                REGEXP_REPLACE(
                                                                                        SPLIT_PART(T.POLYGON, ';', 1),
                                                                                        '([0-9\.]+),([0-9\.]+)',
                                                                                        '\2 \1', 'g') || '))'
                                                                    ),
                                                                4326
                                                            )
                                                    )
                                            ELSE
                                                ST_DistanceSphere(
                                                        ST_SetSRID(ST_MakePoint(#{lng}::numeric, #{lat}::numeric), 4326),
                                                        ST_SetSRID(ST_MakePoint(NULLIF(trim(T.LONGITUDE), '')::NUMERIC,
                                                                                NULLIF(trim(T.LATITUDE), '')::NUMERIC),
                                                                   4326)
                                                    )
                                        END AS distance
                                 FROM SYS_TOURIST_LABEL T
                                 WHERE T.LATITUDE IS NOT NULL
                                    AND T.LONGITUDE IS NOT NULL
                                    AND T.TOURIST_ID = #{scenicSpotId}
                                    and t.explain_status = true
                                    <if test=" explainedIds != null and explainedIds.size() > 0 ">
                                        AND T.ID NOT IN
                                        <foreach collection="explainedIds" item="item" index="index" open="(" separator="," close=")">
                                            #{item, jdbcType=BIGINT}
                                        </foreach>
                                    </if>
        )
        SELECT tt.*
        FROM filtered_points tt
        WHERE tt.distance <![CDATA[   <=     ]]> tt.radius
        ORDER BY distance ASC LIMIT 20
    </select>

    <insert id="insertSysTouristLabel" parameterType="com.iciyun.system.domain.SysTouristLabel" useGeneratedKeys="true"
            keyProperty="id">
        insert into sys_tourist_label
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="touristId != null">tourist_id,</if>
            <if test="touristName != null">tourist_name,</if>
            <if test="labelName != null">label_name,</if>
            <if test="imageWidth != null">image_width,</if>
            <if test="imageHeight != null">image_height,</if>
            <if test="x1y1 != null">x1y1,</if>
            <if test="x2y2 != null">x2y2,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="cityCode != null">city_code,</if>
            <if test="cityName != null">city_Name,</if>
            <if test="polygon != null">polygon,</if>
            <if test="labelText != null">label_text,</if>
            <if test="cacheOk != null">cache_ok,</if>
            <if test="audioCacheOk != null">audio_cache_ok,</if>
            <if test="status != null">status,</if>
            <if test="category != null">category,</if>
            <if test="categoryCode != null">category_code,</if>

            <if test="radius != null">radius,</if>
            <if test="repetition != null">repetition,</if>
            <if test="intervalTime != null">interval_time,</if>
            <if test="showStatus != null">show_status,</if>
            <if test="explainStatus != null">explain_status,</if>
            <if test="customizeCategoryCode != null">customize_category_code,</if>
            <if test="note != null">note,</if>
            <if test="labelSource != null">label_source,</if>
            <if test="joinCluster != null">join_cluster,</if>
            <if test="labelType != null">label_type,</if>
            <if test="beaconCode != null">beacon_code,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="touristId != null">#{touristId},</if>
            <if test="touristName != null">#{touristName},</if>
            <if test="labelName != null">#{labelName},</if>
            <if test="imageWidth != null">#{imageWidth},</if>
            <if test="imageHeight != null">#{imageHeight},</if>
            <if test="x1y1 != null">#{x1y1},</if>
            <if test="x2y2 != null">#{x2y2},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="cityCode != null">#{cityCode},</if>
            <if test="cityName != null">#{cityName},</if>
            <if test="polygon != null">#{polygon},</if>
            <if test="labelText != null">#{labelText},</if>
            <if test="cacheOk != null">#{cacheOk},</if>
            <if test="audioCacheOk != null">#{audioCacheOk},</if>
            <if test="status != null">#{status},</if>
            <if test="category != null">#{category},</if>
            <if test="categoryCode != null">#{categoryCode},</if>

            <if test="radius != null">#{radius},</if>
            <if test="repetition != null">#{repetition},</if>
            <if test="intervalTime != null">#{intervalTime},</if>
            <if test="showStatus != null">#{showStatus},</if>
            <if test="explainStatus != null">#{explainStatus},</if>
            <if test="customizeCategoryCode != null">#{customizeCategoryCode},</if>
            <if test="note != null">#{note},</if>
            <if test="labelSource != null">#{labelSource},</if>
            <if test="joinCluster != null">#{joinCluster},</if>

            <if test="labelType != null">#{labelType},</if>
            <if test="beaconCode != null">#{beaconCode},</if>

        </trim>
    </insert>


    <update id="updateNote" parameterType="com.iciyun.system.domain.SysTouristLabel">
        update sys_tourist_label set note = #{note} where id = #{id}
    </update>

    <update id="updateSysTouristLabel" parameterType="com.iciyun.system.domain.SysTouristLabel">
        update sys_tourist_label
        <trim prefix="SET" suffixOverrides=",">
            <if test="touristId != null">tourist_id = #{touristId},</if>
            <if test="touristName != null">tourist_name = #{touristName},</if>
            <if test="labelName != null">label_name = #{labelName},</if>
            <if test="imageWidth != null">image_width = #{imageWidth},</if>
            <if test="imageHeight != null">image_height = #{imageHeight},</if>
            <if test="x1y1 != null">x1y1 = #{x1y1},</if>
            <if test="x2y2 != null">x2y2 = #{x2y2},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="cityCode != null">city_code = #{cityCode},</if>
            <if test="cityName != null">city_name = #{cityName},</if>
            <if test="longitude != null and longitude != '' ">longitude = #{longitude},</if>
            <if test="latitude != null and latitude != '' ">latitude = #{latitude},</if>
            <if test="tmapStatus != null">tmap_status = #{tmapStatus},</if>
            <if test="labelText != null">label_text = #{labelText},</if>
            <if test="labelTextDefault != null">label_text_default = #{labelTextDefault},</if>
            <if test="cacheOk != null">cache_ok = #{cacheOk},</if>
            <if test="audioCacheOk != null">audio_cache_ok = #{audioCacheOk},</if>
            <if test="status != null">status = #{status},</if>
            <if test="category != null">category = #{category},</if>
            <if test="categoryCode != null">category_code = #{categoryCode},</if>
            <if test="radius != null">radius = #{radius},</if>
            <if test="repetition != null">repetition = #{repetition},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="intervalTime != null">interval_time = #{intervalTime},</if>
            <if test="showStatus != null">show_status = #{showStatus},</if>
            <if test="explainStatus != null">explain_status = #{explainStatus},</if>
            <if test="customizeCategoryCode != null">customize_category_code = #{customizeCategoryCode},</if>
            <if test="note != null">note = #{note},</if>
            <if test="joinCluster != null">join_cluster = #{joinCluster},</if>
            <if test="labelSource != null">label_source = #{labelSource},</if>
            <if test="labelType != null">label_type = #{labelType},</if>
            <if test="beaconCode != null">beacon_code = #{beaconCode},</if>
            polygon = #{polygon},

        </trim>
        where id = #{id}
    </update>

    <update id="resetCache" parameterType="com.iciyun.system.domain.SysTouristLabel">
        update sys_tourist_label
        set cache_ok       = #{cacheOk},
            audio_cache_ok = #{audioCacheOk},
            label_text     = #{labelText}
        where id = #{id}
    </update>

    <delete id="deleteSysTouristLabelById" parameterType="Long">
        delete
        from sys_tourist_label
        where id = #{id}
    </delete>

    <delete id="deleteSysTouristLabelByIds" parameterType="String">
        delete from sys_tourist_label where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectListByParam" resultMap="SysTouristLabelResult">
        <include refid="selectSysTouristLabelVo"/>
        <where>
            <if test="touristIdList != null and touristIdList.size() > 0">
                AND tourist_id IN
                <foreach collection="touristIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cacheOk != null">
                AND cache_ok = #{cacheOk}
            </if>
            <if test="audioCacheOk != null">
                AND audio_cache_ok = #{audioCacheOk}
            </if>
        </where>
    </select>

    <select id="selectByLabel" resultMap="SysTouristLabelResult" parameterType="java.lang.String">
        select * from sys_tourist_label where label_name = #{labelName}
    </select>

    <select id="selectByTouristIdAndLabel" resultMap="SysTouristLabelResult">
        select * from sys_tourist_label where label_name = #{labelName} and tourist_id = #{touristId} limit 1
    </select>

    <sql id="Label_Query_Condition">
        <where>
            <if test="touristId != null  and touristId != ''">and t.tourist_id = #{touristId}</if>
            <if test="touristName != null  and touristName != ''">and t.tourist_name = #{touristName}</if>
            <if test="beaconCode != null  and beaconCode != ''">and t.beacon_code = #{beaconCode}</if>
            <if test="labelType != null  and labelType != ''">and t.label_type = #{labelType}</if>
            <if test="labelName != null  and labelName != ''">and t.label_name like concat('%', #{labelName}, '%')</if>
            <if test="categoryCode != null and categoryCode != '' ">and t.category_code = #{categoryCode}</if>
            <if test="customizeCategoryCode != null and customizeCategoryCode != '' ">and t.customize_category_code = #{customizeCategoryCode}</if>
            <if test=" ids != null and ids.size() > 0 ">
                AND T.ID IN
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item, jdbcType=BIGINT}
                </foreach>
            </if>
            <if test=" notInIds != null and notInIds.size() > 0 ">
                AND T.ID NOT IN
                <foreach collection="notInIds" item="item" index="index" open="(" separator="," close=")">
                    #{item, jdbcType=BIGINT}
                </foreach>
            </if>
            <choose>
                <when test="queryType == 'beacon_all'">
                    and t.beacon_code is NOT NULL
                </when>
            </choose>
        </where>
    </sql>

    <select id="listQuery" resultMap="SysTouristLabelResult">
        SELECT
            t."id",
            t.tourist_id,
            t.tourist_name,
            t.label_name,
            t.longitude,
            t.latitude,
            t.join_cluster,
            t.polygon,
            t.radius,
            t.repetition,
            t.interval_time,
            t.show_status,
            t.explain_status,
            t.category_code,
            t.customize_category_code,
            t.label_source,
            t.label_type,
            t.beacon_code
        FROM sys_tourist_label t
        <include refid="Label_Query_Condition" />
    </select>

</mapper>