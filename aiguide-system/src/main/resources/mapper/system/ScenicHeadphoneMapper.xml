<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iciyun.system.mapper.ScenicHeadphoneMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iciyun.system.domain.ScenicHeadphone">
        <id column="id" property="id" />
        <result column="scenic_id" property="scenicId" />
        <result column="scenic_name" property="scenicName" />
        <result column="model" property="model" />
        <result column="price" property="price" />
        <result column="locations" property="locations" />
        <result column="status" property="status" />
        <result column="agency_price" property="agencyPrice" />
        <result column="agency_id" property="agencyId" />
        <result column="agency_name" property="agencyName" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <select id="queryList" parameterType="com.iciyun.system.domain.ScenicHeadphone" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            scenic_headphone sh
        WHERE
            sh.status = 0
            and sh.scenic_id = #{scenicId}
    </select>

    <select id="queryByModel" parameterType="com.iciyun.system.domain.ScenicHeadphone" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            scenic_headphone sh
        WHERE
                sh.scenic_id = #{scenicId}
            AND sh.model = #{model}
    </select>

    <update id="upStatus">
        UPDATE scenic_headphone SET status = #{status} WHERE id = #{id}
    </update>

</mapper>
