<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--suppress MybatisXMapperXmlInspection -->
<mapper namespace="com.iciyun.system.mapper.ScenicDocMapper">

    <resultMap type="com.iciyun.system.domain.ScenicDoc" id="ScenicDocResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="scenicId" column="scenic_id"/>
        <result property="scenicName" column="scenic_name"/>
        <result property="url" column="url"/>
        <result property="fileName" column="file_name"/>
        <result property="extName" column="ext_name"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="pageList" parameterType="com.iciyun.system.domain.ScenicDoc" resultMap="ScenicDocResult">
        select * from scenic_doc where 1 = 1
        <if test="userId != null and userId != 0">
            AND user_id = #{userId}
        </if>
        <if test="scenicId != null and scenicId > 0">
            AND scenic_id = #{scenicId}
        </if>
        <if test="fileName != null and fileName != ''">
            AND file_name like concat('%', #{fileName}, '%')
        </if>
    </select>

</mapper>
