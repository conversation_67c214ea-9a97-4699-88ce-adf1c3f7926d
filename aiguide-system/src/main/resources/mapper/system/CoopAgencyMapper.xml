<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iciyun.system.mapper.CoopAgencyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iciyun.system.domain.CoopAgency">
        <id column="id" property="id" />
        <result column="agency_id" property="agencyId" />
        <result column="agency_name" property="agencyName" />
        <result column="agency_person" property="agencyPerson" />
        <result column="agency_statue" property="agencyStatue" />
        <result column="province_code" property="provinceCode" />
        <result column="province_name" property="provinceName" />
        <result column="city_code" property="cityCode" />
        <result column="city_name" property="cityName" />
        <result column="district_code" property="districtCode" />
        <result column="district_name" property="districtName" />
        <result column="detail_address" property="detailAddress" />
        <result column="agency_person_phone" property="agencyPersonPhone" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <select id="selectByUser" resultMap="BaseResultMap">
        SELECT
            ca.*
        FROM
            scenic_admin sa
                LEFT JOIN coop_agency ca ON sa.business_code = ca.agency_id
        where sa.business_type = 1
          and sa.user_phone = #{userName}
    </select>

    <select id="selectByScenic" resultMap="BaseResultMap">
        SELECT ca.*
        FROM
            scenic_admin sa
                LEFT JOIN coop_agency ca ON sa.business_code = ca.agency_id
                left join scenic_ratio sr on ca.agency_id = sr.agency_id
        WHERE
            sa.business_type = 1
          AND sa.user_phone = #{userName}
          and sr.scenic_id = #{scenicId}
    </select>

</mapper>
