<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iciyun.system.mapper.ScenicCommentaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iciyun.system.domain.ScenicCommentary">
        <id column="id" property="id" />
        <result column="scenic_id" property="scenicId" />
        <result column="scenic_name" property="scenicName" />
        <result column="label_name" property="labelName" />
        <result column="label_commentary" property="labelCommentary" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <select id="queryList" parameterType="com.iciyun.system.domain.ScenicCommentary" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            scenic_commentary sc
        <where>
            <if test="scenicId != null">
                sc.scenic_id = #{scenicId}
            </if>
            <if test="labelName != null and labelName != ''">
                sc.label_name = #{labelName}
            </if>
        </where>
        ORDER BY sc.id DESC
    </select>

</mapper>
