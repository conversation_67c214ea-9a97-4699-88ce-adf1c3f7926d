<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iciyun.system.mapper.SqlMapper">
    <select id="selectList" parameterType="java.lang.String" resultType="java.util.HashMap" useCache="false">
        ${sql}
    </select>

    <select id="selectMap" parameterType="java.lang.String" resultType="java.util.HashMap" useCache="false">
        ${sql}
    </select>

    <select id="selectStr" parameterType="java.lang.String" resultType="java.lang.String" useCache="false">
        ${sql}
    </select>

    <select id="selectCount" parameterType="java.lang.String" resultType="java.lang.Integer" useCache="false">
        ${sql}
    </select>

    <select id="selectInt" parameterType="java.lang.String" resultType="java.lang.Integer" useCache="false">
        ${sql}
    </select>

    <select id="selectBigDecimal" parameterType="java.lang.String" resultType="java.math.BigDecimal" useCache="false">
        ${sql}
    </select>

    <select id="selectLong" parameterType="java.lang.String" resultType="java.lang.Long" useCache="false">
        ${sql}
    </select>

    <insert id="insert" parameterType="java.lang.String">
        ${sql}
    </insert>

    <delete id="delete" parameterType="java.lang.String">
        ${sql}
    </delete>

    <update id="update" parameterType="java.lang.String">
        ${sql}
    </update>

    <update id="exec" parameterType="java.lang.String">
        ${sql}
    </update>
</mapper>
