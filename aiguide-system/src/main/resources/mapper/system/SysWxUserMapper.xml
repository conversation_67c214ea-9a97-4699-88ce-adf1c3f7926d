<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iciyun.system.mapper.SysWxUserMapper">

    <resultMap type="com.iciyun.common.core.domain.entity.SysWxUser" id="SysWxUserResult">
        <id property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="userType" column="user_type"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="selectWxUserList" resultMap="SysWxUserResult" parameterType="com.iciyun.common.core.domain.entity.SysWxUser">
        select user_id,
            user_name ,
            user_type,
            create_time
        from sys_user
        where del_flag = '0'
          and user_type != #{userType}
        <if test="userName!= null and userName!= ''"><!-- 用户名检索 -->
            AND user_name LIKE CONCAT('%', #{userName}, '%')
        </if>
        <if test="startTime != null "><!-- 开始时间检索 -->
            AND to_char(create_time,'yyyy-MM-dd') &gt;= to_char(#{startTime}::timestamp,'yyyy-MM-dd')
        </if>
        <if test="endTime != null "><!-- 结束时间检索 -->
            AND to_char(create_time,'yyyy-MM-dd') &lt;= to_char(#{endTime}::timestamp,'yyyy-MM-dd')
        </if>
        order by create_time desc
    </select>

    <select id="selectWxUserById" resultMap="SysWxUserResult" parameterType="java.lang.Long">
        select user_id,
            user_name,
            user_type,
            create_time
        from sys_user
        where del_flag = '0'
          and user_id = #{userId}
    </select>
</mapper> 