<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iciyun.system.mapper.UserRecMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iciyun.system.domain.UserRec">
        <id column="id" property="id" />
        <result column="biz_id" property="bizId" />
        <result column="create_time" property="createTime" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="rec_user_id" property="recUserId" />
        <result column="rec_user_name" property="recUserName" />
        <result column="rec_statue" property="recStatue" />
        <result column="rec_bean_count" property="recBeanCount" />
    </resultMap>

    <select id="selectUserRecList" resultType="com.iciyun.system.domain.UserRecVo">
        SELECT
            ur.user_name,
            su.nick_name,
            ur.create_time
        FROM
            user_rec ur
                LEFT JOIN sys_user su ON ur.user_name = su.user_name
        WHERE
            ur.rec_user_name = #{userName}
        ORDER BY
            ur.create_time DESC
    </select>

</mapper>
