<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iciyun.system.mapper.ScenicLocationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iciyun.system.domain.ScenicLocation">
        <id column="id" property="id" />
        <result column="scenic_id" property="scenicId" />
        <result column="scenic_name" property="scenicName" />
        <result column="location_code" property="locationCode" />
        <result column="location_name" property="locationName" />
        <result column="location_type" property="locationType" />
        <result column="location_url" property="locationUrl" />
        <result column="create_time" property="createTime" />
        <result column="location_url_code" property="locationUrlCode" />
    </resultMap>

    <select id="queryList" parameterType="com.iciyun.system.domain.ScenicLocation" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            scenic_location sl
        WHERE
            sl.scenic_id = #{scenicId}
    </select>

    <select id="queryByLocationName" parameterType="com.iciyun.system.domain.ScenicLocation" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            scenic_location sl
        WHERE
            sl.location_url_code is not null
            and sl.scenic_id = #{scenicId}
            <if test="locationName != null and locationName!= ''">
                AND sl.location_name = #{locationName}
            </if>
         limit 1
    </select>

</mapper>
