<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iciyun.system.mapper.SysUserMapper">

    <resultMap type="com.iciyun.common.core.domain.entity.SysUser" id="SysUserResult">
        <id     property="userId"       column="user_id"      />
        <result property="deptId"       column="dept_id"      />
        <result property="userName"     column="user_name"    />
        <result property="nickName"     column="nick_name"    />
        <result property="email"        column="email"        />
        <result property="phonenumber"  column="phonenumber"  />
        <result property="sex"          column="sex"          />
        <result property="avatar"       column="avatar"       />
        <result property="password"     column="password"     />
        <result property="status"       column="status"       />
        <result property="delFlag"      column="del_flag"     />
        <result property="loginIp"      column="login_ip"     />
        <result property="loginDate"    column="login_date"   />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <result property="remark"       column="remark"       />
        <result property="hobbyTypes"       column="hobby_types"       />
        <result property="guideStyle"       column="guide_style"       />
        <result property="roleType"       column="role_type"       />
        <result property="guidePersona"       column="guide_persona"       />
        <result property="languageFlag"       column="language_flag"       />
        <result property="openId"       column="open_id"       />
		<result property="userRecCode"       column="user_rec_code"       />
		<result property="tokenBalance"       column="token_balance"       />
		<result property="tokenBalanceTotal"       column="token_balance_total"       />
		<result property="guideStatus"       column="guide_status"       />
		<result property="userType"          column="user_type"          />
        <association property="dept"    javaType="com.iciyun.common.core.domain.entity.SysDept"         resultMap="deptResult" />
        <collection  property="roles"   javaType="java.util.List"  resultMap="RoleResult" />

    </resultMap>

	<resultMap id="deptResult" type="com.iciyun.common.core.domain.entity.SysDept">
        <id     property="deptId"    column="dept_id"     />
        <result property="parentId"  column="parent_id"   />
        <result property="deptName"  column="dept_name"   />
        <result property="ancestors" column="ancestors"   />
        <result property="orderNum"  column="order_num"   />
        <result property="leader"    column="leader"      />
        <result property="status"    column="dept_status" />
    </resultMap>
	
    <resultMap id="RoleResult" type="com.iciyun.common.core.domain.entity.SysRole">
        <id     property="roleId"       column="role_id"        />
        <result property="roleName"     column="role_name"      />
        <result property="roleKey"      column="role_key"       />
        <result property="roleSort"     column="role_sort"      />
        <result property="dataScope"    column="data_scope"     />
        <result property="status"       column="role_status"    />
    </resultMap>
	
	<sql id="selectUserVo">
        select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.status,u.language_flag,
               u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,u.hobby_types,u.guide_style,u.role_type,
               u.open_id,u.guide_persona,u.open_id, u.user_rec_code, u.token_balance, u.token_balance_total, u.guide_status, u.user_type,
        d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status,
        r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status
        from sys_user u
		    left join sys_dept d on u.dept_id = d.dept_id
		    left join sys_user_role ur on u.user_id = ur.user_id
		    left join sys_role r on r.role_id = ur.role_id
    </sql>

	<sql id="selectWxUserVo">
		select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password,
		       u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,
		       u.hobby_types,u.guide_style,u.role_type,u.open_id, u.user_rec_code, u.guide_persona, u.token_balance,
			   u.user_type,u.language_flag
		from sys_user u
	</sql>
    
    <select id="selectUserList" parameterType="com.iciyun.common.core.domain.entity.SysUser" resultMap="SysUserResult">
		select u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.sex,
		       u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_name, d.leader,
		       u.user_type,u.language_flag
		from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		where u.del_flag = '0'
		<if test="userId != null and userId != 0">
			AND u.user_id = #{userId}
		</if>
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
			AND to_char(u.create_time,'yyyy-MM-dd') &gt;= to_char(#{params.beginTime}::timestamp,'yyyy-MM-dd')
		</if>
		<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
			AND to_char(u.create_time,'yyyy-MM-dd') &lt;= to_char(#{params.endTime}::timestamp,'yyyy-MM-dd')
		</if>
		<if test="deptId != null and deptId != 0">
			AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
		</if>
		ORDER BY u.user_id DESC
	</select>
	
	<select id="selectAllocatedList" parameterType="com.iciyun.common.core.domain.entity.SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time,u.language_flag
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and r.role_id = #{roleId}
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		ORDER BY u.user_id DESC
	</select>
	
	<select id="selectUnallocatedList" parameterType="com.iciyun.common.core.domain.entity.SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and (r.role_id != #{roleId} or r.role_id IS NULL)
	    and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and ur.role_id = #{roleId})
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		ORDER BY u.user_id DESC
	</select>
	
	<select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
	    <include refid="selectUserVo"/>
		where u.user_name = #{userName} and u.del_flag = '0'
	</select>
	
	<select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_id = #{userId}
	</select>
	
	<select id="checkUserNameUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, user_name from sys_user where user_name = #{userName} and del_flag = '0' limit 1
	</select>
	
	<select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, phonenumber from sys_user where phonenumber = #{phonenumber} and del_flag = '0' limit 1
	</select>
	
	<select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, email from sys_user where email = #{email} and del_flag = '0' limit 1
	</select>
	
	<insert id="insertUser" parameterType="com.iciyun.common.core.domain.entity.SysUser" useGeneratedKeys="true" keyProperty="userId">
 		insert into sys_user(
 			<if test="userId != null and userId != 0">user_id,</if>
 			<if test="deptId != null and deptId != 0">dept_id,</if>
 			<if test="userName != null and userName != ''">user_name,</if>
 			<if test="nickName != null and nickName != ''">nick_name,</if>
 			<if test="email != null and email != ''">email,</if>
 			<if test="avatar != null and avatar != ''">avatar,</if>
 			<if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
 			<if test="sex != null and sex != ''">sex,</if>
 			<if test="password != null and password != ''">password,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			<if test="remark != null and remark != ''">remark,</if>
 			<if test="roleType != null and roleType != ''">role_Type,</if>
 			<if test="hobbyTypes != null and hobbyTypes != ''">hobby_Types,</if>
 			<if test="guideStyle != null and guideStyle != ''">guide_Style,</if>
 			<if test="openId != null and openId != ''">open_Id,</if>
 			<if test="tokenBalance != null ">token_balance,</if>
 			<if test="tokenBalanceTotal != null ">token_balance_total,</if>
			<if test="userType != null and userType != ''">user_type,</if>
			<if test="guidePersona != null and guidePersona != ''">guide_persona,</if>
			<if test="languageFlag != null and languageFlag != ''">language_flag,</if>
			<if test="guideStatus != null ">guide_status,</if>
 			create_time
 		)values(
 			<if test="userId != null and userId != ''">#{userId},</if>
 			<if test="deptId != null and deptId != ''">#{deptId},</if>
 			<if test="userName != null and userName != ''">#{userName},</if>
 			<if test="nickName != null and nickName != ''">#{nickName},</if>
 			<if test="email != null and email != ''">#{email},</if>
 			<if test="avatar != null and avatar != ''">#{avatar},</if>
 			<if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
 			<if test="sex != null and sex != ''">#{sex},</if>
 			<if test="password != null and password != ''">#{password},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
			<if test="roleType != null and roleType != ''">#{roleType},</if>
			<if test="hobbyTypes != null and hobbyTypes != ''">#{hobbyTypes},</if>
			<if test="guideStyle != null and guideStyle != ''">#{guideStyle},</if>
			<if test="openId != null and openId != ''">#{openId},</if>
			<if test="tokenBalance != null ">#{tokenBalance},</if>
			<if test="tokenBalanceTotal != null ">#{tokenBalanceTotal},</if>
			<if test="userType != null and userType != ''">#{userType},</if>
			<if test="guidePersona != null and guidePersona != ''">#{guidePersona},</if>
			<if test="languageFlag != null and languageFlag != ''">#{languageFlag},</if>
		<if test="guideStatus != null ">#{guideStatus},</if>
 			NOW()
 		)
	</insert>
	
	<update id="updateUser" parameterType="com.iciyun.common.core.domain.entity.SysUser">
 		update sys_user
 		<set>
 			<if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
 			<if test="userName != null and userName != ''">user_name = #{userName},</if>
 			<if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
 			<if test="email != null ">email = #{email},</if>
 			<if test="phonenumber != null ">phonenumber = #{phonenumber},</if>
 			<if test="sex != null and sex != ''">sex = #{sex},</if>
 			<if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
 			<if test="password != null and password != ''">password = #{password},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
 			<if test="loginDate != null">login_date = #{loginDate},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			<if test="remark != null">remark = #{remark},</if>
 			<if test="roleType != null">role_Type = #{roleType},</if>
 			<if test="hobbyTypes != null">hobby_Types = #{hobbyTypes},</if>
 			<if test="guideStyle != null">guide_Style = #{guideStyle},</if>
 			<if test="guidePersona != null">guide_persona = #{guidePersona},</if>
 			<if test="languageFlag != null">language_flag = #{languageFlag},</if>
 			<if test="tokenBalance != null">token_balance = #{tokenBalance},</if>
 			<if test="tokenBalanceTotal != null">token_balance_total = #{tokenBalanceTotal},</if>
 			update_time = NOW()
 		</set>
 		where user_id = #{userId}
	</update>
	
	<update id="updateUserStatus" parameterType="com.iciyun.common.core.domain.entity.SysUser">
 		update sys_user set status = #{status} where user_id = #{userId}
	</update>
	
	<update id="updateUserAvatar" parameterType="com.iciyun.common.core.domain.entity.SysUser">
 		update sys_user set avatar = #{avatar} where user_name = #{userName}
	</update>
	
	<update id="resetUserPwd" parameterType="com.iciyun.common.core.domain.entity.SysUser">
 		update sys_user set password = #{password} where user_name = #{userName}
	</update>
	
	<delete id="deleteUserById" parameterType="Long">
 		update sys_user set del_flag = '2' where user_id = #{userId}
 	</delete>
 	
 	<delete id="deleteUserByIds" parameterType="Long">
 		update sys_user set del_flag = '2' where user_id in
 		<foreach collection="array" item="userId" open="(" separator="," close=")">
 			#{userId}
        </foreach> 
 	</delete>

	<select id="selectWxUserInfo" parameterType="String" resultType="com.iciyun.common.core.domain.entity.wx.WxSysUserInfo">
		<include refid="selectWxUserVo"/>
		where u.user_name = #{userName}
		order by create_time desc
		limit 1
	</select>
	
	<update id="updateUserCode" >
		update sys_user set user_rec_code = #{userRecCode} where user_id = #{userId}
	</update>

	<select id="selectUserByCode" parameterType="String" resultType="com.iciyun.common.core.domain.entity.wx.WxSysUserInfo">
		<include refid="selectWxUserVo"/>
		where u.user_rec_code = #{userRecCode}
	</select>
	<select id="selectUserByIdLock" parameterType="Long" resultMap="SysUserResult">
		select
			u.*
		from sys_user u
		where u.user_id = #{userId}
		for update
	</select>

	<update id="updateWxUser" parameterType="String" >
		update sys_user set nick_name = #{nickName}, avatar = #{avatar} where user_name = #{userName}
	</update>
	
	<delete id="deleteUserByUserName" parameterType="String">
		delete from sys_user where user_name = #{userName}
	</delete>

	<select id="getTodayNewUserCount" resultType="java.lang.Integer">
		select count(1)
		  from sys_user
		  where to_char(create_time, 'yyyy-MM-dd') = to_char(now(), 'yyyy-MM-dd')
	</select>

    <select id="selectUserByOpenId" resultMap="SysUserResult">
		select
			u.*
		from sys_user u
		where u.open_id = #{value}
	</select>

    <update id="updateUserTypeByPhone">
		update sys_user set user_type = #{userType} where phonenumber = #{phone}
	</update>

	<select id="selectListByOpenIds" resultMap = "SysUserResult" parameterType="java.lang.String">
		select
			u.*
		from sys_user u
		where
			LENGTH( u.user_name)  > 0
		  and  u.open_id in
		<foreach collection="openIds" item="openId" open="(" separator="," close=")">
			#{openId}
		</foreach>
	</select>
	
</mapper> 