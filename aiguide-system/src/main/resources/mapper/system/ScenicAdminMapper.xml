<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iciyun.system.mapper.ScenicAdminMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iciyun.system.domain.ScenicAdmin">
        <id column="id" property="id" />
        <result column="business_type" property="businessType" />
        <result column="business_code" property="businessCode" />
        <result column="user_name" property="userName" />
        <result column="user_phone" property="userPhone" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <select id="queryList" parameterType="com.iciyun.system.domain.ScenicAdmin" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            scenic_admin sa
        WHERE
            sa.business_type = #{businessType}
            AND sa.business_code = #{businessCode}
    </select>

    <select id="queryByPhone" parameterType="com.iciyun.system.domain.ScenicAdmin" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            scenic_admin sa
        WHERE
            sa.business_type = #{businessType}
            AND sa.business_code = #{businessCode}
            AND sa.user_phone = #{userPhone}
    </select>

    <select id="queryByScenicId" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            scenic_admin sa
                LEFT JOIN scenic_spot ss ON sa.business_code = ss.scenic_spot_id
        where sa.business_type = 4
          and sa.user_phone = #{userPhone}
          and ss.id = #{scenicId}
        limit 1
    </select>

</mapper>
