<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iciyun.system.mapper.ScenicGuideMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iciyun.system.domain.ScenicGuide">
        <id column="id" property="id" />
        <result column="guide_code" property="guideCode" />
        <result column="user_id" property="userId" />
        <result column="user_phone" property="userPhone" />
        <result column="scenic_id" property="scenicId" />
        <result column="scenic_name" property="scenicName" />
        <result column="scenic_address" property="scenicAddress" />
        <result column="guide_url" property="guideUrl" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <update id="upScenic">
        UPDATE scenic_guide SET scenic_name = #{scenicName}, scenic_address = #{scenicAddress} WHERE Scenic_id = #{scenicId}
    </update>

    <select id="queryList" parameterType="com.iciyun.system.domain.qo.ScenicGuideQo" resultMap="BaseResultMap">
        SELECT
            *
        FROM
            scenic_guide sg
        <where>
            <if test="guideCode != null and guideCode != ''">
                AND sg.guide_code like concat('%', #{guideCode}, '%')
            </if>
            <if test="userPhone != null and userPhone != ''">
                AND sg.user_phone like concat('%', #{userPhone}, '%')
            </if>
            <if test="scenicName != null and scenicName != ''">
                AND sg.scenic_name like concat('%', #{scenicName}, '%')
            </if>
            <if test="scenicAddress != null and scenicAddress != ''">
                AND sg.scenic_address like concat('%', #{scenicAddress}, '%')
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                AND to_char(sg.create_time,'%Y%m%d') &gt;= to_char(#{beginTime},'%Y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                AND to_char(sg.create_time,'%Y%m%d') &lt;= to_char(#{endTime},'%Y%m%d')
            </if>
        </where>
        ORDER BY sg.id DESC
    </select>

    <update id="audit">
        UPDATE scenic_guide SET status = #{status} WHERE id = #{id}
    </update>

</mapper>
