<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iciyun.system.mapper.SysEyePictureMapper">

    <insert id="insertSysEyePicture" parameterType="com.iciyun.system.domain.SysEyePicture" useGeneratedKeys="true" keyProperty="id">
        insert into sys_eye_picture
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="touristId != null">tourist_id,</if>
            <if test="touristName != null">tourist_name,</if>
            <if test="userPhone != null">user_phone,</if>
            <if test="openId != null">open_id,</if>
            <if test="imgUrl != null">img_url,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="touristId != null">#{touristId},</if>
            <if test="touristName != null">#{touristName},</if>
            <if test="userPhone != null">#{userPhone},</if>
            <if test="openId != null">#{openId},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

</mapper>