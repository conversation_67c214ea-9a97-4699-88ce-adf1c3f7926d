<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iciyun.system.mapper.PaymentOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iciyun.system.domain.PaymentOrder">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="me_open_id" property="meOpenId" />
        <result column="payment_order_id" property="paymentOrderId" />
        <result column="payment_order_status" property="paymentOrderStatus" />
        <result column="actual_transaction_amount" property="actualTransactionAmount" />
        <result column="description" property="description" />
        <result column="effective_period" property="effectivePeriod" />
        <result column="time_expire" property="timeExpire" />
        <result column="channel_serial_number" property="channelSerialNumber" />
        <result column="success_time" property="successTime" />
        <result column="recharge_config_raw" property="rechargeConfigRaw" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

</mapper>
