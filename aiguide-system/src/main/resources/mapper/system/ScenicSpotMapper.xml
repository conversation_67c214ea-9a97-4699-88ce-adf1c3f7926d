<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iciyun.system.mapper.ScenicSpotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iciyun.system.domain.ScenicSpotBjYonghg">
        <id column="id" property="id" />
        <result column="label" property="label" />
        <result column="url" property="url" />
    </resultMap>

    <select id="queryByFeatures" resultType="com.iciyun.system.domain.bo.ScenicSpotBo">
        SELECT *,
               (1 - (embedding <![CDATA[<=>]]> #{targetFeatures}::vector)) as score
        FROM public.image_embedding
        ORDER BY score desc
        LIMIT ${topN}
    </select>

    <select id="queryByCosineDistance" resultType="com.iciyun.system.domain.bo.ScenicSpotBo">
        SELECT *, cosine_distance(embedding, #{targetFeatures}::vector) as score
        FROM public.image_embedding
        ORDER BY score asc
            LIMIT ${topN}
    </select>


</mapper>
