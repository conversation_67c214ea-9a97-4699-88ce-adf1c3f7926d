<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iciyun.system.mapper.SysTouristQuestionMapper">
    
    <resultMap type="com.iciyun.system.domain.SysTouristQuestion" id="SysTouristQuestionResult">
        <result property="id"    column="id"    />
        <result property="touristId"    column="tourist_id"    />
        <result property="questionId"    column="question_id"    />
        <result property="touristName"    column="tourist_name"    />
        <result property="userId"    column="user_id"    />
        <result property="type"    column="type"    />
        <result property="questionText"    column="question_text"    />
        <result property="answerText"    column="answer_text"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectSysTouristQuestionVo">
        select id, tourist_id, tourist_name, user_id, type, question_text, answer_text, create_by, create_time from sys_tourist_question
    </sql>

    <select id="selectSysTouristQuestionList" parameterType="com.iciyun.system.domain.SysTouristQuestion" resultMap="SysTouristQuestionResult">
        <include refid="selectSysTouristQuestionVo"/>
        <where>  
            <if test="touristId != null "> and tourist_id = #{touristId}</if>
            <if test="touristName != null  and touristName != ''"> and tourist_name like concat('%', #{touristName}, '%')</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="questionText != null  and questionText != ''"> and question_text = #{questionText}</if>
            <if test="answerText != null  and answerText != ''"> and answer_text = #{answerText}</if>
        </where>
        ORDER BY id
    </select>
    
    <select id="selectSysTouristQuestionById" parameterType="Long" resultMap="SysTouristQuestionResult">
        <include refid="selectSysTouristQuestionVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysTouristQuestion" parameterType="com.iciyun.system.domain.SysTouristQuestion" useGeneratedKeys="true" keyProperty="id">
        insert into sys_tourist_question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="touristId != null">tourist_id,</if>
            <if test="questionId != null">question_id,</if>
            <if test="touristName != null">tourist_name,</if>
            <if test="userId != null">user_id,</if>
            <if test="type != null">type,</if>
            <if test="questionText != null">question_text,</if>
            <if test="answerText != null">answer_text,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="touristId != null">#{touristId},</if>
            <if test="questionId != null">#{questionId},</if>
            <if test="touristName != null">#{touristName},</if>
            <if test="userId != null">#{userId},</if>
            <if test="type != null">#{type},</if>
            <if test="questionText != null">#{questionText},</if>
            <if test="answerText != null">#{answerText},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateSysTouristQuestion" parameterType="com.iciyun.system.domain.SysTouristQuestion">
        update sys_tourist_question
        <trim prefix="SET" suffixOverrides=",">
            <if test="touristId != null">tourist_id = #{touristId},</if>
            <if test="questionId != null">question_id = #{questionId},</if>
            <if test="touristName != null">tourist_name = #{touristName},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="questionText != null">question_text = #{questionText},</if>
            <if test="answerText != null">answer_text = #{answerText},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysTouristQuestionById" parameterType="Long">
        delete from sys_tourist_question where id = #{id}
    </delete>

    <delete id="deleteSysTouristQuestionByIds" parameterType="String">
        delete from sys_tourist_question where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>