<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iciyun.system.mapper.TokenDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iciyun.system.domain.TokenDetail">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="detail_id" property="detailId" />
        <result column="payment_order_id" property="paymentOrderId" />
        <result column="payment_amount" property="paymentAmount" />
        <result column="user_id" property="userId" />
        <result column="user_phone" property="userPhone" />
        <result column="amount_incurred" property="amountIncurred" />
        <result column="change_type" property="changeType" />
        <result column="transcation_type" property="transcationType" />
        <result column="transaction_time" property="transactionTime" />
    </resultMap>



    <select id="queryTotalProfit" resultType="java.math.BigDecimal">
        select
            sum(t.amount_incurred)
        from token_detail t
        where t.user_id = #{userId}
        and t.change_type = 'IN'
        and t.transcation_type in
        <foreach collection="transactionTypes" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>
