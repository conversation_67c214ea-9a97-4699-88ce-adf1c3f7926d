<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iciyun.system.mapper.GuidePayOrderItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iciyun.system.domain.GuidePayOrderItem">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="scenic_id" property="scenicId"/>
        <result column="scenic_name" property="scenicName"/>
        <result column="point_id" property="pointId"/>
        <result column="point_name" property="pointName"/>
        <result column="agency_raw" property="agencyRaw"/>
        <result column="order_item" property="orderItem"/>
        <result column="headset_model" property="headsetModel"/>
        <result column="headset_statue" property="headsetStatue"/>
        <result column="locations" property="locations"/>
        <result column="item_amount" property="itemAmount"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="create_time" property="createTime"/>
        <result column="order_statue" property="orderStatue"/>
        <result column="open_id" property="openId"/>
        <result column="user_name" property="userName"/>
    </resultMap>

    <select id="getOrderItemsBySelect" resultType="com.iciyun.system.domain.GuidePayOrderItemVo">
        SELECT
        r.agency_id,
        r.agency_name,
        r.agency_amount,
        o.*
        FROM
        guide_pay_order_item_ratio r
        LEFT JOIN guide_pay_order_item o ON r.order_id = o.order_id
        WHERE
        o.open_id IS NOT NULL
        and agency_amount > 0
        <if test="agencyId != null and agencyId != ''">
            AND r.agency_id = #{agencyId}
        </if>
        <if test="scenicId != null and scenicId != ''">
            AND o.scenic_id = #{scenicId}
        </if>
        <if test="orderItem != null and orderItem != ''">
            and o.order_item = #{orderItem}
            and r.order_item = #{orderItem}
        </if>
        <if test="startTime!= null ">
            AND o.create_time &gt;= #{startTime}
        </if>
        <if test="endTime!= null ">
            AND o.create_time &lt;= #{endTime}
        </if>
        <if test="orderIds != null ">
            AND o.order_id in (
            <foreach collection="orderIds" item="orderId" separator=",">
                #{orderId}
            </foreach>
            )
        </if>
        ORDER BY
        o.create_time DESC
    </select>

    <select id="getOrderItemsByLimit" resultType="com.iciyun.system.domain.GuideOrderSt">
        SELECT
        r.agency_id,
        r.agency_name,
        count(o.order_id) all_order_num,
        sum(o.item_amount) all_amount,
        SUM(r.agency_amount) agency_amount,
        o.scenic_id,
        o.scenic_name,
        o.order_item
        FROM
        guide_pay_order_item_ratio r
        LEFT JOIN
        (
        select * from guide_pay_order_item
        where open_id IS NOT NULL
        <if test="startTime!= null ">
            AND create_time &gt;= #{startTime}
        </if>
        <if test="endTime!= null ">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY
        create_time DESC
        ) o ON r.order_id = o.order_id and r.order_item = o.order_item
        WHERE
            o.open_id IS NOT NULL
        and agency_amount > 0
        <if test="orderItems != null ">
            and r.order_item in (
            <foreach collection="orderItems" item="orderItem" separator=",">
                #{orderItem}
            </foreach>
            )
        </if>
        <if test="orderItems != null ">
            and o.order_item in (
            <foreach collection="orderItems" item="orderItem" separator=",">
                #{orderItem}
            </foreach>
            )
        </if>
        <if test="agencyIds != null ">
            and r.agency_id in (
            <foreach collection="agencyIds" item="agencyId" separator=",">
                #{agencyId}
            </foreach>
            )
        </if>
        <if test="scenicIds != null ">
            AND o.scenic_id in (
            <foreach collection="scenicIds" item="scenicId" separator=",">
                #{scenicId}
            </foreach>
            )
        </if>
        <if test="pairs != null and !pairs.isEmpty()">
            and (r.agency_id, o.scenic_id) IN
            <foreach collection="pairs" item="pair" open="(" separator="," close=")">
                (#{pair.agencyId}, #{pair.scenicId})
            </foreach>
        </if>
        GROUP BY
        r.agency_id,
        r.agency_name,
        o.scenic_id,
        o.scenic_name,
        o.order_item

    </select>

    <select id="getOrdersBySelect" resultType="com.iciyun.system.domain.GuidePayOrderItemVo">
        select o.order_id, o.create_time
        from guide_pay_order_item_ratio r
        LEFT JOIN guide_pay_order_item o ON r.order_id = o.order_id
        where
        o.open_id IS NOT NULL
        <if test="orderItem != null ">
            and o.order_item = #{orderItem}
        </if>
        <if test="agencyId != null and agencyId != ''">
            AND r.agency_id = #{agencyId}
        </if>
        <if test="scenicId != null ">
            AND o.scenic_id = #{scenicId}
        </if>
        <if test="startTime!= null ">
            AND o.create_time &gt;= #{startTime}
        </if>
        <if test="endTime!= null ">
            AND o.create_time &lt;= #{endTime}
        </if>
    </select>

</mapper>
