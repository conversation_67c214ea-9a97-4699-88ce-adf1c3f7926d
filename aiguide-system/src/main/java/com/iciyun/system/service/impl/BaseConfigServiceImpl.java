package com.iciyun.system.service.impl;

import com.iciyun.system.domain.BaseConfig;
import com.iciyun.system.mapper.BaseConfigMapper;
import com.iciyun.system.service.IBaseConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07 16:49:25
 */
@Service
public class BaseConfigServiceImpl extends ServiceImpl<BaseConfigMapper, BaseConfig> implements IBaseConfigService {

    @Autowired
    private BaseConfigMapper baseConfigMapper;

    @Override
    public BaseConfig queryOne() {
        return baseConfigMapper.queryOne();
    }

}
