package com.iciyun.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iciyun.system.domain.ScenicDoc;
import com.iciyun.system.domain.dto.ScenicDocPageParam;
import com.iciyun.system.mapper.ScenicDocMapper;
import com.iciyun.system.service.IScenicDocService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02 14:44:00
 */
@Service
@RequiredArgsConstructor
public class ScenicDocServiceImpl extends ServiceImpl<ScenicDocMapper, ScenicDoc> implements IScenicDocService {

    private final ScenicDocMapper scenicDocMapper;


    @Override
    public List<ScenicDoc> pageList(ScenicDocPageParam scenicDocPageParam) {
        return scenicDocMapper.pageList(scenicDocPageParam);
    }
}
