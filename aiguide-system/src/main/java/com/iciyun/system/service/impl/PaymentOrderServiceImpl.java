package com.iciyun.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.binarywang.wxpay.bean.request.WxPayRefundV3Request;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayRefundV3Result;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.iciyun.common.core.domain.AjaxResult;
import com.iciyun.common.core.domain.R;
import com.iciyun.system.domain.PaymentOrder;
import com.iciyun.system.domain.PlaceOrderRechargeCmd;
import com.iciyun.system.domain.bo.PlaceOrderCmd;
import com.iciyun.system.domain.bo.RefundCmd;
import com.iciyun.system.mapper.PaymentOrderMapper;
import com.iciyun.system.service.IPaymentOrderService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

/**
 * <p>
 * 支付订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24 11:16:43
 */
@Slf4j
@Service
public class PaymentOrderServiceImpl extends ServiceImpl<PaymentOrderMapper, PaymentOrder> implements IPaymentOrderService {



    @Value("${wx.pay.notifyOrderUrl}")
    private String notifyOrderUrl;

    @Value("${wx.pay.notifyRefundUrl}")
    private String notifyRefundUrl;

    @Autowired
    private PaymentOrderMapper paymentOrderMapper;

    @Autowired
    private WxPayService wxPayService;

    @Override
    public AjaxResult placeOrderRecharge(PlaceOrderRechargeCmd placeOrderRechargeCmd) {

        PaymentOrder paymentOrder = new PaymentOrder(placeOrderRechargeCmd.getSysUser(), placeOrderRechargeCmd.getRechargeConfig());
        paymentOrderMapper.insert(paymentOrder);

        WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request();
        request.setAppid(wxPayService.getConfig().getAppId());
        request.setMchid(wxPayService.getConfig().getMchId());
        request.setDescription(paymentOrder.getDescription());
        request.setOutTradeNo(paymentOrder.getPaymentOrderId());
        request.setTimeExpire(paymentOrder.getTimeExpire().atOffset(ZoneOffset.ofHours(8)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX")));
        request.setNotifyUrl(notifyOrderUrl);
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        amount.setTotal(paymentOrder.getActualTransactionAmount().multiply(BigDecimal.valueOf(100)).intValue());
        request.setAmount(amount);
        WxPayUnifiedOrderV3Request.Payer payer = new WxPayUnifiedOrderV3Request.Payer();
        payer.setOpenid(paymentOrder.getOpenId());
        request.setPayer(payer);
        try {

            WxPayUnifiedOrderV3Result.JsapiResult result = wxPayService.createOrderV3(TradeTypeEnum.JSAPI, request);

            paymentOrder.placeSuccess();
            paymentOrderMapper.updateById(paymentOrder);

            return AjaxResult.success(result);

        } catch (WxPayException e) {
            log.error("微信支付下单异常", e);
            return AjaxResult.error();
        }

    }


    @Override
    public R<WxPayUnifiedOrderV3Result.JsapiResult> placeOrder(PlaceOrderCmd cmd) {
        PaymentOrder paymentOrder = new PaymentOrder(cmd);
        paymentOrderMapper.insert(paymentOrder);

        WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request();
        request.setAppid(wxPayService.getConfig().getAppId());
        request.setMchid(wxPayService.getConfig().getMchId());
        request.setDescription(paymentOrder.getDescription());
        request.setOutTradeNo(paymentOrder.getPaymentOrderId());
        request.setTimeExpire(paymentOrder.getTimeExpire().atOffset(ZoneOffset.ofHours(8)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX")));
        request.setNotifyUrl(notifyOrderUrl);
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        amount.setTotal(paymentOrder.getActualTransactionAmount().intValue());
        request.setAmount(amount);
        WxPayUnifiedOrderV3Request.Payer payer = new WxPayUnifiedOrderV3Request.Payer();
        payer.setOpenid(paymentOrder.getOpenId());
        request.setPayer(payer);
        try {

            WxPayUnifiedOrderV3Result.JsapiResult result = wxPayService.createOrderV3(TradeTypeEnum.JSAPI, request);

            paymentOrder.placeSuccess();
            paymentOrderMapper.updateById(paymentOrder);

            return R.ok(result);

        } catch (WxPayException e) {
            log.error("微信支付下单异常", e);
            return R.fail("微信支付下单异常");
        }

    }

    @Override
    public R<Object> refund(RefundCmd cmd) {

        PaymentOrder origin = paymentOrderMapper.selectOne(new QueryWrapper<PaymentOrder>().lambda().eq(PaymentOrder::getOrderId, cmd.getOrderId()));

        PaymentOrder refund = origin.refund(cmd);
        paymentOrderMapper.insert(refund);

        WxPayRefundV3Request request = new WxPayRefundV3Request();
        request.setOutTradeNo(refund.getOriginPaymentOrderId());
        request.setOutRefundNo(refund.getPaymentOrderId());
        request.setNotifyUrl(notifyRefundUrl);
        WxPayRefundV3Request.Amount amount = new WxPayRefundV3Request.Amount();
        amount.setTotal(origin.getActualTransactionAmount().intValue());
        amount.setRefund(refund.getActualTransactionAmount().intValue());
        amount.setCurrency("CNY");
        request.setAmount(amount);

        try {
            WxPayRefundV3Result result = wxPayService.refundV3(request);
        } catch (WxPayException e) {
            throw new RuntimeException(e);
        }

        return R.ok();
    }
}
