package com.iciyun.system.service;

import com.iciyun.system.domain.SysBaseSt;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

public interface AsyncTaskService {

    CompletableFuture<Integer> getTodayNewUserCount();

    CompletableFuture<Long> getAllUserCount();

    CompletableFuture<Long> getIssueBeanCount();

    CompletableFuture<Long> getUserUseBeanCount();

    void insertSysBaseSt(SysBaseSt sysBaseSt);
}
