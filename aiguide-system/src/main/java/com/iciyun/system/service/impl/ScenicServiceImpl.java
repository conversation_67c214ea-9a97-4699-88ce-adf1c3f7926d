package com.iciyun.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.coze.openapi.client.chat.CreateChatReq;
import com.coze.openapi.client.chat.model.ChatEvent;
import com.coze.openapi.client.connversations.message.model.Message;
import com.coze.openapi.service.service.CozeAPI;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.iciyun.common.constant.CacheConstants;
import com.iciyun.common.core.domain.LabelTextDto;
import com.iciyun.common.core.domain.entity.StreamChatCmd;
import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.common.core.domain.entity.TextSpeechCmd;
import com.iciyun.common.core.domain.entity.scenic.ScenicTextSpeechCmd;
import com.iciyun.common.core.domain.entity.wx.AiSpeechCmd;
import com.iciyun.common.core.domain.entity.wx.WxSysUserInfo;
import com.iciyun.common.core.domain.model.UserStyleDto;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.common.cqe.SaveScenicSpotBrowseHistoryCmd;
import com.iciyun.common.enums.*;
import com.iciyun.common.utils.SecurityUtils;
import com.iciyun.common.utils.sign.Md5Utils;
import com.iciyun.system.domain.AiguScenicVoice;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.SysChatUser;
import com.iciyun.system.domain.SysTouristLabel;
import com.iciyun.system.mapper.ScenicSpotCustomizeMapper;
import com.iciyun.system.mapper.SysTouristLabelMapper;
import com.iciyun.system.service.*;
import io.reactivex.Flowable;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

@Service
@Slf4j
public class ScenicServiceImpl implements IScenicService {

    @Autowired
    private IAiguScenicVoiceService iAiguScenicVoiceService;

    @Autowired
    private IGuideService guideService;

    //系统用户 user ,用于会话
    private static String sysChatUserKey = "sys:aiGuideUser";

    private static int sysChatUserKeyTime = 1;

    //景区介绍提示词
    private static String scenicIntroduction = "为我介绍一下%s，大概600字左右吧";

    private static String scenicLabelIntroduction = "请为我介绍一下%s的%s";

    // AI 导航的 botId
    @Value("${coze.app.bot_id_common}")
    private String botId;

    @Autowired
    private IGuideTkService guideTkService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private IGuideService iGuideService;

    //cozeToken
    private static String cozeTokenKey = "chat:cozeToken";

    //14 分钟
    private static int cozeTokenTime = 14;

    @Autowired
    private AiMatchImgService aiMatchImgService;

    @Autowired
    private ISysUserService userService;

    // AI img 匹配换取 label
    @Value("${ai.match_second.img_url}")
    private String aiMatchImgUrl;

    @Value("${ai.match_first.max_distance}")
    private float MATCH_THRESHOLD;

    private static String errTipPath = "https://ai-guided.oss-cn-beijing.aliyuncs.com/error/errTips.mp3";

    @Autowired
    private ISysDictTypeService dictTypeService;

    @Autowired
    ScenicCacheManager scenicCacheManager;

    @Autowired
    ScenicSpotCustomizeMapper scenicSpotCustomizeMapper;

    @Autowired
    SysTouristLabelMapper sysTouristLabelMapper;



    @Override
    public void setUserstSyle(String userName) {
        //清空 用户对话大模型的设置
    }

    //获取用户风格
    @Override
    public String getSysChatUserStyle(Long userId) {
        String prompt = "性别:%s,风格:%s,语言:%s";
        SysUser sysUserInfo = userService.selectUserById(userId);
        if (sysUserInfo != null) {
            String sexType = GuidePersonaEnum.of(sysUserInfo.getGuidePersona()).getDesc();
            //兴趣
            List<String> bu = new ArrayList<>();
            List<String> hobbyTypes = JSON.parseArray(sysUserInfo.getHobbyTypes(), String.class);
            hobbyTypes.forEach(e -> {
                bu.add(HobbyTypeEnum.of(e).getDesc());
            });
            String languageFlag = sysUserInfo.getLanguageFlag();
            prompt = String.format(prompt, sexType, com.iciyun.common.utils.StringUtils.join(bu, ","), languageFlag);
        }
        return prompt;
    }

    public String getHobbyTypes(Long userId) {
        Map<String, Object> map = new HashMap<>();
        SysUser sysUserInfo = userService.selectUserById(userId);
        if (sysUserInfo != null) {
            String sexType = GuidePersonaEnum.of(sysUserInfo.getGuidePersona()).getDesc();
            //兴趣
            List<String> bu = new ArrayList<>();
            List<String> hobbyTypes = JSON.parseArray(sysUserInfo.getHobbyTypes(), String.class);
            if (hobbyTypes == null) {
                hobbyTypes = HobbyTypeEnum.getDefaultHobby();
            }
            hobbyTypes.forEach(e -> {
                bu.add(HobbyTypeEnum.of(e).getDesc());
            });

            String result = StringUtils.join(bu, ",");

            log.info("性别: {}, 我的偏好: {}", sexType, result);

            return result;
        }
        return "";
    }


    public static void main(String[] args) {
        WxSysUserInfo sysUserInfo = new WxSysUserInfo();
        sysUserInfo.setGuidePersona("GIRL");
        sysUserInfo.setRoleType("CHILD");
        sysUserInfo.setGuideStyle("FQYM");
        sysUserInfo.setHobbyTypes("[\"GS\"]");
        if (sysUserInfo != null) {
            String prompt = "我期望找一个%s性导游，我对景点的%s更感兴趣；";
            String sexType = GuidePersonaEnum.of(sysUserInfo.getGuidePersona()).getDesc();
            //角色
            String roleType = RoleTypeEnum.of(sysUserInfo.getRoleType()).getDesc();
            //兴趣
            List<String> bu = new ArrayList<>();
            List<String> hobbyTypes = JSON.parseArray(sysUserInfo.getHobbyTypes(), String.class);
            hobbyTypes.forEach(e -> {
                bu.add(HobbyTypeEnum.of(e).getDesc());
            });
            //讲解风格
            String guideStyle = GuideStyleEnum.of(sysUserInfo.getGuideStyle()).getDesc();
            prompt = String.format(prompt, sexType, guideStyle, roleType, StringUtils.join(bu, ","));
            System.out.println(prompt);

            System.out.println(Md5Utils.hash(7 + "" + prompt));
        }

    }

    @Override
    public String scenicTextSpeech(HttpServletResponse response, ScenicTextSpeechCmd cmd) {
        //异步执行景区浏览记录
        SaveScenicSpotBrowseHistoryCmd historyCmd = new SaveScenicSpotBrowseHistoryCmd();
        historyCmd.setUserId(String.valueOf(cmd.getUserId()));
        historyCmd.setScenicId(cmd.getScenicId());
        historyCmd.setScenicName(cmd.getScenicName());
        historyCmd.setScenicLocation(cmd.getAddress());
        asyncService.insertScenicBrowseHistory(historyCmd);
        //根据景区， 用户设置的声音ID， 查询语音MP3
        AiguScenicVoice scenicVoice = null;
       /* AiguScenicVoice scenicVoice = iAiguScenicVoiceService.lambdaQuery()
                .eq(AiguScenicVoice::getScenicId, cmd.getScenicId())
                .eq(AiguScenicVoice::getScenicName, cmd.getScenicName())
                .last(" limit 1")
                .one();*/
        //获取当前用户的声音配置
        AtomicReference<String> voiceUrl = new AtomicReference<>("");
        if (scenicVoice != null) {
            String mp3Path = "";
            if (StringUtils.isNotEmpty(scenicVoice.getTextVoices())) {
                List<String> mp3List = Arrays.asList(scenicVoice.getTextVoices().split(","));
                mp3List.stream().forEach(e -> {
                    if (e.contains(cmd.getVoiceId())) {
                        voiceUrl.set(e);
                    }
                });
                mp3Path = voiceUrl.get();
                if (StringUtils.isNotBlank(mp3Path)) {
                    return mp3Path;
                }
            }
            // 没有当前用户需要的声音， 生成语音
            if (StringUtils.isNotBlank(scenicVoice.getContext())) {
                String cozeToken = redisCache.getCacheObject(cozeTokenKey);
                if (StringUtils.isBlank(cozeToken)) {
                    cozeToken = guideTkService.getToken();
                    redisCache.setCacheObject(cozeTokenKey, cozeToken, cozeTokenTime, TimeUnit.MINUTES);
                }
                if (StringUtils.isNotBlank(cozeToken)) {
                    //3 合成语音，上传oss
                    TextSpeechCmd textSpeechCmd = TextSpeechCmd.builder()
                            .input(scenicVoice.getContext())
                            .voiceId(cmd.getVoiceId())
                            .build();
                   /* String path = System.getProperty("user.dir") + "\\" + cmd.getScenicId() + "\\" +cmd.getVoiceId() + ".mp3";
                    String mp3 = guideService.textToSpeech(textSpeechCmd, cozeToken, path);
                    //MP3 名称
                    String mp3Name = cmd.getScenicId() + "/" + cmd.getVoiceId() + ".mp3";
                    File file = new File(path);
                    mp3Path = guideService.upOSSFiles(file, mp3Name);*/

                    String mp3Name = cmd.getScenicId() + "/" + cmd.getVoiceId() + ".mp3";
                    byte[] mp3 = guideService.textSpeech(textSpeechCmd, cozeToken);
                    mp3Path = guideService.upOSSFiles(mp3Name, mp3);

                    List<String> voices = new ArrayList<>();
                    //更新声音
                    if (StringUtils.isNotEmpty(scenicVoice.getTextVoices())) {
                        voices = Arrays.asList(scenicVoice.getTextVoices().split(","));
                    }
                    voices.add(mp3Path);
                    scenicVoice.setTextVoices(String.join(",", voices));
                    scenicVoice.setUpdateTime(LocalDateTime.now());
                    iAiguScenicVoiceService.updateById(scenicVoice);

                    return mp3Path;
                }
            }
        } else {
            Long userId = cmd.getUserId();
            //通过 Redis 查询 与 大模型对话
            String curChatUserKey = sysChatUserKey + ":" + userId;
            SysChatUser curChatUser = redisCache.getCacheObject(curChatUserKey);
            if (curChatUser == null) {
                //生成文本， 合成语音上传oss
                //1 获取token
                String cozeToken = redisCache.getCacheObject(cozeTokenKey);
                if (StringUtils.isBlank(cozeToken)) {
                    cozeToken = guideTkService.getToken();
                    redisCache.setCacheObject(cozeTokenKey, cozeToken, cozeTokenTime, TimeUnit.MINUTES);
                }
                if (cozeToken != null) {
                    curChatUser = curChatUser.builder()
                            .sysChatUserId(userId) //手机号
                            .botId(botId)
                            .style(getSysChatUserStyle(userId))
                            .build();
                    //会话Id
                    String conversationId = guideService.createSession(botId, curChatUser, cozeToken);
                    if (StringUtils.isNotBlank(conversationId)) {
                        curChatUser.setConversationId(conversationId);
                        redisCache.setCacheObject(curChatUserKey, curChatUser);
                    }
                }
            }
            //2 发起对话
            String cozeToken = redisCache.getCacheObject(cozeTokenKey);
            if (StringUtils.isBlank(cozeToken)) {
                cozeToken = guideTkService.getToken();
                redisCache.setCacheObject(cozeTokenKey, cozeToken, cozeTokenTime, TimeUnit.MINUTES);
            }
            if (cozeToken != null) {
                StreamChatCmd chatCmd = StreamChatCmd.builder()
                        .conversationId(curChatUser.getConversationId())
                        .userId(curChatUser.getSysChatUserId()) //手机号
                        .botId(curChatUser.getBotId())
                        .question(String.format(scenicIntroduction, cmd.getScenicName()))
                        .build();
                String text = guideService.streamChatJsonByHttp(chatCmd, cozeToken);
                if (StringUtils.isNotBlank(text)) {
                    //3 合成语音，上传oss
                    TextSpeechCmd textSpeechCmd = TextSpeechCmd.builder()
                            .input(text)
                            .voiceId(cmd.getVoiceId())
                            .build();
                    byte[] bytes = guideService.textSpeechCoze(textSpeechCmd);
                    if (bytes != null) {
                        //MP3 名称
                        String mp3Name = cmd.getScenicId() + "/" + curChatUser.getSysChatUserId() + ".mp3";
                        String filePath = guideService.upOSSFiles(mp3Name, bytes);

                        //保存
                        AiguScenicVoice scenicEtity = AiguScenicVoice.builder()
                                .scenicId(cmd.getScenicId())
                                .scenicName(cmd.getScenicName())
                                .context(text)
                                .scenicLocation(cmd.getAddress())
                                .statue(ScenicStatus.OK)
                                .createTime(LocalDateTime.now())
                                .textVoices(filePath)
                                .build();
                        iAiguScenicVoiceService.save(scenicEtity);

                        return filePath;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public Map<String,Object> scenicSpeechs(HttpServletResponse response, ScenicTextSpeechCmd cmd) {
        //异步执行景区浏览记录
        SaveScenicSpotBrowseHistoryCmd historyCmd = new SaveScenicSpotBrowseHistoryCmd();
        historyCmd.setUserId(String.valueOf(cmd.getUserId()));
        historyCmd.setScenicId(cmd.getScenicId());
        historyCmd.setScenicName(cmd.getScenicName());
        historyCmd.setScenicLocation(cmd.getAddress());
        asyncService.insertScenicBrowseHistory(historyCmd);

        Map<String,Object> result = Maps.newHashMap();

        ScenicSpot one = scenicSpotCustomizeMapper.selectById(Integer.parseInt(cmd.getScenicId()));
        UserStyleDto userStyleDto = userService.getUserStyle(cmd.getUserId());

        //根据景区配置的风格和语言，对用户的风格和语言进行调整
        ScenicSpot.StyleAndLanguage styleAndLanguage = one.parseStyleAndLanguage();
        List<String> styleList = styleAndLanguage.getStyle();
        List<String> languageList = styleAndLanguage.getLanguage();

        if (!styleList.contains(userStyleDto.getGuideStyle())) {
            userStyleDto.setGuideStyle("CULTURE");
        }
        if (!languageList.contains(userStyleDto.getLanguageFlag())) {
            userStyleDto.setLanguageFlag("Chinese");
        }

        String voiceId = userService.getVoiceId(userStyleDto);
        String scenicId = cmd.getScenicId();
        String scenicLabel = cmd.getScenicLabel();

        String labelKay = scenicCacheManager.getCacheKey(userStyleDto,scenicId,scenicLabel,voiceId);

        log.info("查询景区 label 缓存：{}",labelKay);

        String audioUrl = "";
        String audioText = "";


        String voices = redisCache.getCacheObject(labelKay);
        if (StringUtils.isNotBlank(voices)) {
            ArrayList<String> tempList = CollectionUtil.newArrayList(voices.split(","));
            //只返回第 1 条缓存
            audioUrl = tempList.get(0);

            SysTouristLabel sysTouristLabel = sysTouristLabelMapper.selectByTouristIdAndLabel(scenicLabel, Long.parseLong(scenicId));
            if (sysTouristLabel!=null){
                String labelText = sysTouristLabel.getLabelText();
                List<LabelTextDto> dtoList = parseLabelText(labelText);
                LabelTextDto dto = filterLabelTextDto(dtoList, userStyleDto.getLanguageFlag(), userStyleDto.getGuideStyle());
                if(dto != null){
                    audioText = dto.getLabelText();
                }
            }
        }

        result.put("audioUrl",audioUrl);
        result.put("audioText",audioText);
        return result;
    }


    private List<LabelTextDto> parseLabelText(String labelText) {
        JSONArray arr = null;
        try {
            arr = JSONUtil.parseArray(labelText);
        } catch (Exception e) {
        }
        List<LabelTextDto> dtoList = Lists.newArrayList();
        if (arr == null) return dtoList;
        for (Object obj : arr) {
            JSONObject jsonObject = JSONUtil.parseObj(obj);
            LabelTextDto dto = JSONUtil.toBean(jsonObject, LabelTextDto.class);
            dtoList.add(dto);
        }
        return dtoList;
    }


    private LabelTextDto filterLabelTextDto(List<LabelTextDto> list,String language,String style) {
        for (LabelTextDto dto : list) {
            if (dto.getLanguage().equals(language) && dto.getStyle().equals(style)) {
                return dto;
            }
        }
        return null;
    }


    @Override
    public void delScenic(String scenicId) {
        iAiguScenicVoiceService.lambdaUpdate()
                .eq(AiguScenicVoice::getScenicId, scenicId)
                .set(AiguScenicVoice::getStatue, ScenicStatus.DELETED)
                .update();
    }

    @Override
    public void updateScenicIntroduction(String scenicId, String introduction) {
        iAiguScenicVoiceService.lambdaUpdate()
                .eq(AiguScenicVoice::getScenicId, scenicId)
                .set(AiguScenicVoice::getContext, introduction)
                .set(AiguScenicVoice::getTextVoices, null)
                .update();
    }

    @Override
    public void upScenicImg(MultipartFile file, String scenicId) {

    }

    /**
     * 1 照片调取接口 换 label
     * 2 label + 文字 调取大模型 生成文本
     * 4 文本转语音返回
     */
    @Override
    public String getAiSpeech(HttpServletResponse response, AiSpeechCmd cmd) {

        String cozeToken = redisCache.getCacheObject(cozeTokenKey);
        if (StringUtils.isBlank(cozeToken)) {
            cozeToken = guideTkService.getToken();
            redisCache.setCacheObject(cozeTokenKey, cozeToken, cozeTokenTime, TimeUnit.MINUTES);
        }
        String label = "";

        log.info("label: {}", label);
        String text = "";
        if (StringUtils.isNotBlank(cmd.getText())) {
            text = cmd.getText();
            log.info("text_before: {}", text);
            if (text.contains("Undefined") || text.contains("undefined")) {
                text = "";
            }
        }
        if (cmd.getFileType() != null && cmd.getFileType() == 0 && cmd.getFile() != null && StringUtils.isBlank(cmd.getText())) {
            //语音换文字
            text = iGuideService.transcriptions(cmd.getFile(), cozeToken);
            if (StringUtils.isBlank(text)) {
                //异常提示语音
                return errTipPath;
            }
            log.info("text_coze: {}", text);
        }
        log.info("text: {}", text);
        //对话大模型 question
        String question = label + text;
        if (StringUtils.isNotBlank(question)) {
            //通过 Redis 查询 与 大模型对话的 系统用户
            String userId = cmd.getUserId();
            String userName = cmd.getUserName();
            if (StringUtils.isBlank(cmd.getUserName())) {
                SysUser user = SecurityUtils.getLoginUser().getUser();
                userName = user.getUserName();
            }

            String curChatUserKey = sysChatUserKey + ":" + userId;
            SysChatUser curChatUser = redisCache.getCacheObject(curChatUserKey);
            if (curChatUser == null) {
                curChatUser = SysChatUser.builder()
                        .sysChatUserId(Long.valueOf(userId))
                        .botId(botId)
                        .style(getSysChatUserStyle(Long.valueOf(userId)))
                        .build();
                //会话Id
                String conversationId = guideService.createSession(botId, curChatUser, cozeToken);
                curChatUser.setConversationId(conversationId);
                redisCache.setCacheObject(curChatUserKey, curChatUser);
            }
            //发起会话
            StreamChatCmd chatCmd = StreamChatCmd.builder()
                    .conversationId(curChatUser.getConversationId())
                    .userId(curChatUser.getSysChatUserId()) //手机号
                    .botId(curChatUser.getBotId())
                    .question(question)
                    .build();

            String answer = guideService.streamChatJsonByHttp(chatCmd, cozeToken);
            //合成语音
            if (StringUtils.isNotBlank(answer)) {
                //3 合成语音,返回流
                TextSpeechCmd textSpeechCmd = TextSpeechCmd.builder()
                        .input(answer)
                        .voiceId(cmd.getVoiceId())
                        .build();
                byte[] chatByte = guideService.textSpeechCoze(textSpeechCmd);
                //MP3 名称： 手机号 + chat.mp3
                String mp3Name = userName + "/chat.mp3";
                String filePath = guideService.upOSSFiles(mp3Name, chatByte);
                return filePath;
            }
        }
        return null;
    }


    @Override
    public Flowable<ChatEvent> streamChat(AiSpeechCmd cmd) {

        String cozeToken = redisCache.getCacheObject(cozeTokenKey);
        if (StringUtils.isBlank(cozeToken)) {
            cozeToken = guideTkService.getToken();
            redisCache.setCacheObject(cozeTokenKey, cozeToken, cozeTokenTime, TimeUnit.MINUTES);
        }

        CozeAPI coze = guideTkService.getCozeAPI();

        String userId = cmd.getUserId();

        String curChatUserKey = sysChatUserKey + ":" + userId;
        SysChatUser curChatUser = redisCache.getCacheObject(curChatUserKey);
        if (curChatUser == null) {
            curChatUser = SysChatUser.builder()
                    .sysChatUserId(Long.valueOf(userId))
                    .botId(botId)
                    .style(getSysChatUserStyle(Long.valueOf(userId)))
                    .build();
            //会话Id
            String conversationId = guideService.createSession(botId, curChatUser, cozeToken);
            curChatUser.setConversationId(conversationId);
            redisCache.setCacheObject(curChatUserKey, curChatUser);
        }

        CreateChatReq req =
                CreateChatReq.builder()
                        .botID(botId)
                        .userID(String.valueOf(curChatUser.getSysChatUserId()))
                        .conversationID(curChatUser.getConversationId())
                        .messages(Collections.singletonList(Message.buildUserQuestionText(cmd.getText())))
                        .build();

        Flowable<ChatEvent> resp = coze.chat().stream(req);
        coze.shutdownExecutor();
        return resp;
    }


    @Override
    @Retryable(value = {Exception.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2))
    public Response streamChatResponse(AiSpeechCmd cmd) {
        if (StrUtil.isEmpty(cmd.getText())) {
            return null;
        }

//        0 文物  1 动植物
        Integer labelType = cmd.getLabelType();

        String bot_1 = dictTypeService.dictVal("coze_agent", "bot_1");//智能导游 智能体
        String bot_2 = dictTypeService.dictVal("coze_agent", "bot_2");//动植物识别专家 智能体
        String bot_3 = dictTypeService.dictVal("coze_agent", "bot_3");//博物馆文物识别专家 智能体
        String bot_4 = dictTypeService.dictVal("coze_agent", "bot_4");//聊天专家 智能体 （用于按住说话陪聊）

        //0 按住说话  1 景区简介   2 景区label  3 AI眼睛照片
        Integer type = cmd.getType();
        String token = getToken();
        String conversationId = "";
        if (type == 0) {
            //按住说话，才维护用户的对话上下文，其它情况直接进行单次对话
            conversationId = getConversationId(cmd);
        }

        String useBotId = bot_1;
        if (type == 0) {
            useBotId = bot_4;
        } else if (type == 3) {
            if (labelType == 0) {
                useBotId = bot_3;
            } else if (labelType == 1) {
                useBotId = bot_2;
            }
        }

        String botId = dictTypeService.dictVal("agent_config", cmd.getScenicId());

        String finalBotId = StrUtil.isNotEmpty(botId) ? botId : useBotId;

        ChatService chatService = new ChatService(getToken(), finalBotId);

        Map<String, Object> custom_variables = new HashMap<>();

        String tempName = cmd.getProvinceName()+""+cmd.getDistrictName()+""+cmd.getScenicName();
        custom_variables.put("name", tempName);

        String typeStr = "";

        if (labelType==1){
            typeStr = "animal";
        }
        if (type==0){
            typeStr = "chat";
        }
        custom_variables.put("type", typeStr);



        try {
            return chatService.streamChatResponse(cmd.getUserId(), cmd.getText(), conversationId, custom_variables);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }


    private String getToken() {
        String cozeToken = redisCache.getCacheObject(cozeTokenKey);
        if (StringUtils.isBlank(cozeToken)) {
            cozeToken = guideTkService.getToken();
            redisCache.setCacheObject(cozeTokenKey, cozeToken, cozeTokenTime, TimeUnit.MINUTES);
        }
        return cozeToken;
    }

    private static final String user_conversation_id_key = "user_conversation_id_key_";//用户会话缓存key
    private static final Integer user_conversation_id_time = 14;//用户会话缓存时长（天）

    private String getConversationId(AiSpeechCmd cmd) {
        String key = user_conversation_id_key + cmd.getUserId()+"_"+cmd.getScenicId();
        String conversationId = redisCache.getCacheObject(key);
        if (StrUtil.isEmpty(conversationId)) {
            ChatService chatService = new ChatService(getToken(), botId);
            conversationId = chatService.genConversationId();
            redisCache.setCacheObject(key, conversationId, user_conversation_id_time, TimeUnit.DAYS);
        }
        return conversationId;
    }

    @Override
    public CloseableHttpResponse getAiStreamChat(AiSpeechCmd cmd) {

        String question = cmd.getText();
        if (StringUtils.isNotBlank(question)) {
            String botId = dictTypeService.dictVal("coze_agent", "bot_5");

            //发起会话
            StreamChatCmd chatCmd = StreamChatCmd.builder()
//                    .conversationId(curChatUser.getConversationId())
                    .userId(System.currentTimeMillis())
                    .botId(botId)
                    .question(question)
//                    .custom_variables(custom_variables)
                    .build();

            return guideService.streamChat(chatCmd, getToken());
        }
        return null;
    }

    public Response getAiStreamChat(String question) {
        if (StringUtils.isNotBlank(question)) {
            String botId = dictTypeService.dictVal("coze_agent", "bot_5");

            ChatService chatService = new ChatService(getToken(), botId);

            try {
                return chatService.streamChatResponse(String.valueOf(System.currentTimeMillis()),question,null,null);
            } catch (IOException e) {
                throw new RuntimeException(e);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    @Override
    public List<String> getLabelCache(ScenicTextSpeechCmd cmd) {

        Long userId = cmd.getUserId();
        //根据景区label 获取缓存
        String scenicId = cmd.getScenicId();
        String scenicLabel = cmd.getScenicLabel();

        // 判断是否存储
        String cacheScenicKey = CacheConstants.cacheScenicKey +
                Md5Utils.hash(scenicId + (StringUtils.isNotEmpty(scenicLabel) ? scenicLabel : ""));
        String syncIdForCache = redisCache.getCacheObject(cacheScenicKey);
        if (StringUtils.isNotBlank(syncIdForCache)) {
            log.info("缓存处理中...");
            return null;
        }

        String prompt = getSysChatUserStyle(userId);
        String labelKay = CacheConstants.scenicPixKey + Md5Utils.hash(scenicId +
                (StringUtils.isNotBlank(scenicLabel) ? scenicLabel : "") +
                prompt);
        log.info("{}, {}, {}, labelKay: {}", scenicId, scenicLabel, prompt, labelKay);
        String voices = redisCache.getCacheObject(labelKay);
        if (StringUtils.isNotBlank(voices)) {
            return CollectionUtil.newArrayList(voices.split(","));
        }
        return null;
    }
}
