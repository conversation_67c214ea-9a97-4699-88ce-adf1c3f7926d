package com.iciyun.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.coze.openapi.client.audio.speech.CreateSpeechReq;
import com.coze.openapi.client.audio.speech.CreateSpeechResp;
import com.coze.openapi.client.audio.transcriptions.CreateTranscriptionsReq;
import com.coze.openapi.client.audio.transcriptions.CreateTranscriptionsResp;
import com.coze.openapi.client.auth.OAuthToken;
import com.coze.openapi.client.chat.CreateChatReq;
import com.coze.openapi.client.chat.model.ChatEvent;
import com.coze.openapi.client.connversations.message.model.Message;
import com.coze.openapi.service.auth.JWTOAuth;
import com.coze.openapi.service.auth.JWTOAuthClient;
import com.coze.openapi.service.auth.TokenAuth;
import com.coze.openapi.service.service.CozeAPI;
import com.iciyun.common.core.domain.entity.UserChatStatueCmd;
import com.iciyun.common.utils.http.HttpClientUtil;
import com.iciyun.system.domain.GlobalVariablesSingleton;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Consts;
import org.apache.http.entity.mime.content.ContentBody;
import org.apache.http.entity.mime.content.FileBody;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.BodyExtractors;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.*;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class GuideTkMain {

    public static void getSYToken() {
        // The default access is api.coze.com, but if you need to access api.coze.cn,
        // please use base_url to configure the api endpoint to access
        String cozeAPIBase = "https://api.coze.cn";
        String jwtOauthClientID = "1143093201874";
        String jwtOauthPrivateKey = "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQDYlPQI0K03Juv2G/Bi9JOygF/q7QikvIGxGhSseXY7B1sWAxEXHrqWnIz+06rCAS0dsR4SYbqe3LLcjPW3YlkAcQfjK+En31ixmk83A1rVi1PCtu8zpZetyxxQHIkhaYIZqUs/x9QFAH1S2ThJJ5fCJiZhU+xHXwxh9hWF5w6Ms4qNAGoZwsKPfOFSSkxC4yvGbx5zmANmL0YDKDzAjeoKfrXlmXh05hGxH0fI3Z3I0MyXX08kqwlPk1XyB/6vq+amaJYDTl8J6z+I7AROzw3vMcV9f9CgkjfhR2tEwcJ7KhqZKQ/GezwG9OZoRf3/2XGhYLjjGbr/q/kC2PfbsON7AgMBAAECggEAL2DSS5VetwhoKOPuv67yGmPT17/3mpX0JwpmCF7fusT+q3+LPwwFVEo1287w9VStimfDBNFlMjhkVjPt0qqMfri7OXpRXFgS/nMbrhSpS7qZXaiIBjvMxQOAE5RSeUEr6TIsGG0918RGJhgHpJUpMRsIXVUvBGPqAQoEplCn0aZT5uySapCzAA7M/WIA6vMs5O2eVVToO9fIR0z0BN0j8hY60852xWFlCVQzoDjtnYx/D0njFqbYBIFWyuIlGntKO/z1emCUw/MfuEh/Yd1aRc8EXYrl93+JdYqwlfnQi9nUF/IstmO9B+A7mSLAcYQBxZ7In8J+BOZ6UGHgJb18mQKBgQD1TVbHyy507ERnmqKTYpuCDvUZAc1hajbaGrzxFvinrBn2XLAcdI0ilmaVAJSoH0RbZdYzGXqzWdU7ZvBGtO5US7kXhnQzbjU7p9FqVS0Xr2yGTh1ZwdmC55DGI+JcCb3lFxo3mQOIFrPPJ5Yc8xNkcaJKoLmHe9fHo30dh6+G8wKBgQDiBvf5Mb92GIqfn57TAouArjoLrWRuBrhAf0jjpsbXXk9gHitEM46Tk6jQ2F0j8BkEA35V2DWr/s6tmwxu/2KWwDtrG30OErDjJmRq2GXBllxKwUtidjvokgks83lnQKilOPLXtAkDAU+7z9GSrEBIfqILHDSU6k64cRP4jFljWQKBgQCCB8Ecn4O8zxFjqgDMB9VPGAasftwT622wJ8RIOFkO6JfKlE92hgHad8dg7LHgfNKLcGwfIj58m/4AQwzTLk23uXwgEFHAgYcOXGMR7py8lWz9tKKyVaOrVHzVy2fOSguzSI/JMP9CQGaIRP+50NBSyjmxPYmbW/aCCRPld2uAKQKBgQCUgCOX07mJK6KYMVU2KaviBZYZafhb2Ypgxb/r4f0uLsZTzj569qysBQ2a+Sxomiy1WF4+pGjO8gB0SzQmESCDDxV6z3qgLmONOgIruLVSNSm567lNnYTKzne80tJmpjKnKv1R/vUUxl3OdUGp7gXmG1z3k7ZwSGkkARcv63YnMQKBgQDrCSrBo7o0kjOIHaK9sKj98QuoUZ+kSTGdbk82IYlaCMQXTaY1rmDMebhAXJ0hfkbqS6VMStyraxsMJusk6Z6xc0AWqUJ/ZxlbRd3nGLt4LiDe0ubyh5PC+gWpF94X/ak91xH4XCzk6Xlw6JqK65XssVSACJAoPaGCIeSdpzsdSg==";
        String jwtOauthPublicKeyID = "9u5JWKoQQk4588tqerxsguOhFA-_pwW-ud7LPSSAXEQ";
        JWTOAuthClient oauth = null;

    /*
    The jwt oauth type requires using private to be able to issue a jwt token, and through
    the jwt token, apply for an access_token from the coze service. The sdk encapsulates
    this procedure, and only needs to use get_access_token to obtain the access_token under
    the jwt oauth process.
    Generate the authorization token
    The default ttl is 900s, and developers can customize the expiration time, which can be
    set up to 24 hours at most.
    * */
        try {
            oauth = new JWTOAuthClient.JWTOAuthBuilder()
                    .clientID(jwtOauthClientID)
                    .privateKey(jwtOauthPrivateKey)
                    .publicKey(jwtOauthPublicKeyID)
                    .baseURL(cozeAPIBase)
                    .build();
        } catch (Exception e) {
            e.printStackTrace();
            return;
        }

        try {
            OAuthToken resp = oauth.getAccessToken();
            System.out.println(resp);
        } catch (Exception e) {
            e.printStackTrace();
        }
    /*
    The jwt oauth process does not support refreshing tokens. When the token expires,
    just directly call get_access_token to generate a new token.
    * */
        CozeAPI coze = new CozeAPI.Builder().auth(new JWTOAuth(oauth)).baseURL(cozeAPIBase).build();
    }

    /**
     * token
     */
    public static void getToken() {
        // The default access is api.coze.com, but if you need to access api.coze.cn,
        // please use base_url to configure the api endpoint to access
        String cozeAPIBase = "https://api.coze.cn";
        String jwtOauthClientID = "1151600157462";
        String jwtOauthPrivateKey = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQC4eXAy50+7mHlOgILAyHpLL3LkKECqw/Pkg5MD3vI8juMJo+cWQUxpfNAoiUh3UishpHx8qiOACojX51HJPh50AG1U9yFUSdPPMt5V6iSvwkeWKh3ChAzpGpOKndo34FTDu4dPwXCTcaJ/yhpNW+HpZDnlbhPRJmyRTUOOXRbS4BarjMiRk2n6XNJlL8CIlOIKHtVZ0IIhnGd8ImVGsvanETVtmkkL9kDyyK+oOzjS+IctR0dsOtTNJ0STRzOMpE4i2QGulYl5THTK2BVSVoAtIP1umTabruA8hxmyHmS0aq8QFoMtexDZ2iaGru94+kOZubMudLCQHTS3Apbi0BHlAgMBAAECggEAHele9noINShXfxGaYez/22IRtIoMVkQYr1RgSa/kQkp+6MjBRxwGalJZJEGCVQqnQLi9kK87u1656pww6Dsgz/ljwPOjXVntNLsQTwj8st0DI4entKsZttBe3QqUYBwJUoV9PsLkYJv719Gq6yE7Y88jDh8HqpGa6AKJsjNI6J8zilsZ6mfstiLdxv5brmNTZi5wIPwEVbZAMKbBvhn2RK0wScfrXLMoK2q6iTZfpjy0SEi+R/yzD8W1YV1l8X8yaqDl/j7A8yUj+kzfBBiRLQEfiis88rxuEDWBIgd2hZewPh/2J69O2qy/sQUwtgEZLHWU2C2k7iRnMTs7X3kzMQKBgQDrejoOJRQqlEVQQDvxg7LhrYhHOFglh9dyfqNrO1FvyjKAbPT74XSDCufjcPuyUoC+7vZTo+wUOJcT9pXuzxdJjL99jo0xM8JBnw3+gJhMtSdIaKFG19OOR+U0LhL4J1uUZR0uo4lLG6UEtfDlsonK+yOm71IDfZmBPFOc3l/rPQKBgQDIjUYldW0wVzOvPtvSeFyLQauv9LrtwPaAfWgta9v+vzQJholy+TqRpju9xlx5RfqqpfQwfEoyrLUqW9h3/vc/fgmZ7HwMPmglCxPufgOA5P2iAjpNvlrceU92QIQa8T9KIY46a8WNkFkv49M1Vqm7Axk256+5fwj8qeEI3UPLyQKBgGLkMutr0PtMx4s7XrE/8OjsGtZmKEaitO1ll7XZ9IpxLSJYH86EUY0TEG2pLlsmYOFOs+5OYj/fwDrnDPJ80DfYPzc4nb0zEnrClHr0gIF+dp/nKQv6aeNP+EcgrmC/DqvUhxvmHCuJ0ZSX4DJm941iSQe7bAMBRW1L64VusnO1AoGAQn5csJTDdBCliUgdGCZiAKXwZ7GLvMKKuEVcIhst4MPCXBooPl+V5K6qBhzbkjX6i1Pkp6K98xlCI9454mc90OZYBpna8zBJ1WZb4GoVfUnvMiTs8dPYSltKVlnDsy8jnc+MWaBb6rjeaTtvTrMCKYg4dvTQVX9iGmu0ufyquskCgYB4os46eKZDawLMsNjmEedj0liyQFoe9/dg06rkYFX7XiqW9AAad0E1ESMkxFH8XWyciE5EXISalIic83MPXLJ4Mnrx6Y1R9a3DWwoFE7nx+TW9gROGccB6DnQ9wqadHiunFvMJ55J3uuhX97gAGS8qTvdrewLabrzqqWHD3N6xvg==";
        String jwtOauthPublicKeyID = "ZcLQcboJf3ZxudoeBglxtou9sa0upE5ToiNpSTwS1nk";
        JWTOAuthClient oauth = null;

    /*
    The jwt oauth type requires using private to be able to issue a jwt token, and through
    the jwt token, apply for an access_token from the coze service. The sdk encapsulates
    this procedure, and only needs to use get_access_token to obtain the access_token under
    the jwt oauth process.
    Generate the authorization token
    The default ttl is 900s, and developers can customize the expiration time, which can be
    set up to 24 hours at most.
    * */
        try {
            oauth = new JWTOAuthClient.JWTOAuthBuilder()
                    .clientID(jwtOauthClientID)
                    .privateKey(jwtOauthPrivateKey)
                    .publicKey(jwtOauthPublicKeyID)
                    .baseURL(cozeAPIBase)
                    .build();
        } catch (Exception e) {
            e.printStackTrace();
            return;
        }

        try {
            OAuthToken resp = oauth.getAccessToken();
            System.out.println(resp);
        } catch (Exception e) {
            e.printStackTrace();
        }
    /*
    The jwt oauth process does not support refreshing tokens. When the token expires,
    just directly call get_access_token to generate a new token.
    * */
        CozeAPI coze = new CozeAPI.Builder().auth(new JWTOAuth(oauth)).baseURL(cozeAPIBase).build();
    }

    public static void getProToken() {
        // The default access is api.coze.com, but if you need to access api.coze.cn,
        // please use base_url to configure the api endpoint to access
        String cozeAPIBase = "https://api.coze.cn";
        String jwtOauthClientID = "1141689231679";
        String jwtOauthPrivateKey = "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQC+bhaczD1NfLw++6o5Rh2CfV1d6o6hli9CyBBXuSm385I1vVy1umzjK+jyHlOwE8e3D/BQrTUpRaf6hYEIywKcWwSPDRM6Oo1ScBVV0xAa9BNXeMpbOolomuMpoQJXbvx2Rbg5N2s+kZHRFntYEo5NkwHz1ZAChDTuWw5dU8Lg4MjTIyaYfjVqT/fIUoTd2Ne6k3kGVIrsMu/ftZOlZmgBEqPAiSFlN8/SQo7+UORRBFlgtt69aOtZj30V+3qiZGJ2NG7EHNDnbye+0Tve+0QiJoVuiUTkRNiIiLgISD5fO0YO0DJz0lQHM2ZmC7k2Kfbg+HqbFpqDIPKSsq/ExfN5AgMBAAECggEAAT/HCPHi/mxi1PZMzMJLeYsLKSK9vUp9mbCEuHVj2p/w1lN0uK/QoQTiqLNSaOU2UhqY+ENw3VsZtPjCiJTKZKsnAfphJ0zWEv9aNBM+WM9QpBCW0OMqFhj/iFFhwCzbTukRpoyhiecBbkjGSytYUwCxhTGRG/0dzgCOuMXQ4E+5uolsZy/cP+AGyVZMHfda3yDnwfhOlR1o3CBWjZv/raDMVBzph47/1gZIIsB4eN5FS9AUOXEPJS/6W5xj01r93piN0E2Okp2PbIixO2rtjggUAC74U7jhYIoJ1PVtPt+wK5re/ZODchhBE7UnOB/4YV/uzDjSIJQIBVN7pA/5YQKBgQDmGUUp6eKKysqyXOr0SWC4yRLwLSiC/XUM+lv+gG0W5TryTXBsoK2uwk+t0Cf7papQoRxxNKwHBmujip+jH8xphYSjMQfS//7ejS9wjVjwLCds4vvBjbIKTRXtT6yheTGGlsryVtHI7GL71TB/zsyBwXEigeIH/2rfqEu+snduoQKBgQDT3bCqaaQs4JfWwLv6rgru+IrwbiRm9hDtxGUuppF1k7XvL7EHf/m5T+ohK+Sj9yDT89GHcODJ1kelpLLT02R919NV+CVO3l9TUNsyGdrMMkfyJLe2d2VaxKl2BKIPwpSw9SejYnMmI1MqOuNWOBf8BshnZR2Oh7nJUUW1LAsN2QKBgQCNRWSWeDeTuTA71BqZA1gz+5f6B+/AhLbR0gCbP+Q6U2EelPb6aqhYDIr5Dz0NHshzmoco5grcgU+i6CBc+c/51XT68MZ6AJxNrWc79jxtsN4/1xh03Hc8JdnZirpVpWAH3xsZML66Wo/nSBvvzFr5K3g3lIDfIi1OxjhDC7WSIQKBgQCr+T5nBXtTRjdaBXZQxCdx1OOKyAzaWpBhP9LV7DUUWIMMlQzAxruhFWI57NQZ2AaYpUgGBbUhgMMFjMvvr57Zm3AbT2KRQ2XjTjWu1FfK+mF2ByHshbwK0qmvd5FI86wYe9biA47ufSwLkmNoX/3wAF15uBUm1bsT09medxK9+QKBgQCkkiJcFNPMv7THNuRuJ2CyKNkqOnGy5tPl4V1veT/e0l1Z6+sNYWXTrzLYQQ2jMXMWqbCejsEIWEEHCOCgjGsiME+9g9h4ucVxnO8XTjHHUFiBEhzzkhjBTYIy+ZArLA3DtdlMUX8O/PV9It4j7Cdg5T4QKZSe3ZY0nsOXDPdIfQ==";
        String jwtOauthPublicKeyID = "SgEKfphGQo2QS-uCsr97EjM3XLw1z1Vjh_Mb9YUI2Jo";
        JWTOAuthClient oauth = null;

    /*
    The jwt oauth type requires using private to be able to issue a jwt token, and through
    the jwt token, apply for an access_token from the coze service. The sdk encapsulates
    this procedure, and only needs to use get_access_token to obtain the access_token under
    the jwt oauth process.
    Generate the authorization token
    The default ttl is 900s, and developers can customize the expiration time, which can be
    set up to 24 hours at most.
    * */
        try {
            oauth = new JWTOAuthClient.JWTOAuthBuilder()
                    .clientID(jwtOauthClientID)
                    .privateKey(jwtOauthPrivateKey)
                    .publicKey(jwtOauthPublicKeyID)
                    .baseURL(cozeAPIBase)
                    .build();
        } catch (Exception e) {
            e.printStackTrace();
            return;
        }

        try {
            OAuthToken resp = oauth.getAccessToken();
            System.out.println(resp.getAccessToken());
        } catch (Exception e) {
            e.printStackTrace();
        }
    /*
    The jwt oauth process does not support refreshing tokens. When the token expires,
    just directly call get_access_token to generate a new token.
    * */
        CozeAPI coze = new CozeAPI.Builder().auth(new JWTOAuth(oauth)).baseURL(cozeAPIBase).build();
    }

    /**
     * 创建会话
     * return:
     * {"code":0,"data":{"created_at":1741085080,"id":"7477903455970164736","last_section_id":"7477903455970164736","meta_data":{}},"detail":{"logid":"20250304184440B76DEE255AFE58B22CF9"},"msg":""}
     */
    public static void createChat() {
        String url = "https://api.coze.cn/v1/conversation/create";
        String userId = "";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + cozeToken);

        JSONObject dataJson = new JSONObject();
        dataJson.put("userId", userId);

        JSONArray messages = new JSONArray();
        JSONObject msg = new JSONObject();
        msg.put("content", "你是一名导游，我是一名小学生，我喜欢人物");
        msg.put("content_type", "text");
        msg.put("role", "user");
        msg.put("type", "question");

        messages.add(msg);

        JSONObject mateJson = new JSONObject();
        mateJson.put("bot_id", "7477414818966994981");
        mateJson.put("meta_data", dataJson);
        mateJson.put("messages", messages);


        String data = JSONObject.toJSONString(mateJson);

        String response = HttpClientUtil.postJson(url, null, headers, data, false);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer code = jsonObject.getInteger("code");
            if (code == 700012006) {
                //token 无效
                String token = cozeToken;
                if (StringUtils.isNotBlank(token)) {
                    GlobalVariablesSingleton.getInstance().setCozeToken(token);
                    //重试一次
                    createChat();
                }
            } else if (code == 0) {
                JSONObject resDataJson = jsonObject.getJSONObject("data");
                Long conversationId = resDataJson.getLong("id");
                log.info("创建会话成功，会话id：{}", conversationId);
                String meta_data = resDataJson.getString("meta_data");
            }
        }
    }

    /**
     * 非流式发起对话
     * return:
     * {"data":{"id":"7477903498768777242","conversation_id":"7477903455970164736","bot_id":"7477414818966994981","created_at":1741085088,"last_error":{"code":0,"msg":""},"status":"in_progress"},
     * "code":0,"msg":""}
     */

    public static String noStreamChat(Long userId, String question, Long chatId) {

        String url = "https://api.coze.cn/v3/chat";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + cozeToken);

        Map<String, Object> urlParams = new HashMap<>();
        urlParams.put("conversation_id", chatId);

        JSONObject dataJson = new JSONObject();
        dataJson.put("bot_id", "7477414818966994981");
        dataJson.put("user_id", userId);
        dataJson.put("stream", false);

        JSONArray messages = new JSONArray();
        JSONObject msg = new JSONObject();
        msg.put("content", question);
        msg.put("content_type", "text");
        msg.put("role", "user");
        msg.put("type", "question");

        messages.add(msg);

        dataJson.put("additional_messages", messages);


        String response = HttpClientUtil.postJson(url, null, headers, dataJson.toJSONString(), false);

        return null;
    }

    /**
     * return
     * {"code":0,
     * "data":{"bot_id":"7477414818966994981","completed_at":1741085092,"conversation_id":"7477903455970164736","created_at":1741085088,"id":"7477903498768777242",
     * "status":"completed","usage":{"input_count":2569,"output_count":36,"token_count":2605}},
     * "detail":{"logid":"20250305100829E7C21E601264FFD423F4"},
     * "msg":""}
     * 非流式对话状态查看
     * "conversation_id":"7477903455970164736"
     * "chat_id":"7477903498768777242"
     */
    public boolean noStreamChatStatus(UserChatStatueCmd cmd) {
        String url = "https://api.coze.cn/v3/chat/retrieve";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + cozeToken);

        Map<String, Object> urlParams = new HashMap<>();
        urlParams.put("conversation_id", cmd.getConversationId());
        urlParams.put("chat_id", cmd.getChatId());

        String response = HttpClientUtil.get(url, urlParams, headers, false);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer code = jsonObject.getInteger("code");
            if (code == 0) {
                JSONObject resDataJson = jsonObject.getJSONObject("data");
                String status = resDataJson.getString("status");
                if ("completed".equals(status)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 非流式对话回答详情
     * return
     * {"code":0,"data":[
     *      {"bot_id":"7477414818966994981","chat_id":"7477903498768777242","content":"{\"msg_type\":\"time_capsule_recall\",\"data\":\"{\\\"wraped_text\\\":\\\"\\\",\\\"origin_search_results\\\":\\\"[]\\\"}\",\"from_module\":null,\"from_unit\":null}","content_type":"text","conversation_id":"7477903455970164736","created_at":1741085088,"id":"7477903498768826394","role":"assistant","type":"verbose","updated_at":1741085088},
     *      {"bot_id":"7477414818966994981","chat_id":"7477903498768777242","content":"{\"msg_type\":\"knowledge_recall\",\"data\":\"{\\\"chunks\\\":[{\\\"slice\\\":\\\"首先，让我们来看看这个垃圾桶的设计。它分为两个部分：左边是黑色的“其他垃圾”桶，右边是蓝色的“可回收物”桶。每个垃圾桶上都有清晰的标识和图标，帮助大家更好地进行垃圾分类。\\\\n左侧的黑色垃圾桶用于收集“其他垃圾”。这类垃圾主要包括烟蒂、卫生纸、一次性餐具等不可回收的废弃物。请大家在使用时注意，将这些垃圾投入黑色垃圾桶中，确保它们得到妥善处理。\\\\n右侧的蓝色垃圾桶用于收集“可回收物”。这类垃圾主要包括废纸、塑料瓶、玻璃瓶、金属罐等可以回收再利用的物品。请大家在投放这些物品时，尽量保持它们的清洁，并将它们投入蓝色垃圾桶中，以便后续的回收处理。\\\\n为了方便大家使用，垃圾桶下方设有脚踏板。只需轻轻一踩，盖子就会自动打开，无需用手接触，既卫生又便捷。使用完毕后，盖子会自动关闭，保持环境整洁。\\\\n垃圾分类不仅有助于减少环境污染，还能提高资源的利用率。通过分类投放垃圾，我们可以将可回收物重新利用，减少对自然资源的消耗。同时，其他垃圾也能得到更有效的处理，减少对环境的污染。\\\\n小贴士：\\\\n1. “请勿乱扔垃圾”：请将垃圾投入相应的垃圾桶中，不要随意丢弃。\\\\n2. “保持环境卫生”：除了垃圾分类外，也请大家保持景区内的环境卫生，不随地吐痰，不乱涂乱画。\\\\n3. “爱护公共设施”：请爱护我们的垃圾桶和其他公共设施，共同维护美好的旅游环境。\\\\n希望大家在享受美景的同时，也能积极参与到环保行动中来。让我们一起努力，为保护环境贡献自己的一份力量！\\\\n如果有任何疑问或需要进一步的帮助，请随时联系景区工作人员。祝大家在景区度过愉快的时光！\\\\n4. yiyaoxiang\\\\n各位亲爱的游客朋友们，欢迎大家来到我们美丽的景区！今天我要向大家介绍的是我们景区内一个非常重要的设施——医药箱。这个医药箱是我们为了确保每一位游客的安全和健康而特别准备的。\\\",\\\"score\\\":0.83740234375,\\\"meta\\\":{\\\"dataset\\\":{\\\"id\\\":\\\"7477409075896352822\\\",\\\"name\\\":\\\"恭王府\\\"},\\\"document\\\":{\\\"id\\\":\\\"7477893672953774090\\\",\\\"name\\\":\\\"AI导游专有“景区”数据.docx\\\",\\\"format_type\\\":0,\\\"source_type\\\":0},\\\"link\\\":{\\\"title\\\":\\\"恭王府_AI导游专有“景区”数据.docx\\\",\\\"url\\\":\\\"\\\"},\\\"card\\\":{\\\"title\\\":\\\"AI导游专有“景区”数据.docx\\\",\\\"con\\\":\\\"首先，让我们来看看这个垃圾桶的设计。它分为两个部分：左边是黑色的“其他垃圾”桶，右边是蓝色的“可回收物”桶。每个垃圾桶上都有清晰的标识和图标，帮助大家更好地进行垃圾分类。\\\\n左侧的黑色垃圾桶用于收集“其他垃圾”。这类垃圾主要包括烟蒂、卫生纸、一次性餐具等不可回收的废弃物。请大家在使用时注意，将这些垃圾投入黑色垃圾桶中，确保它们得到妥善处理。\\\\n右侧的蓝色垃圾桶用于收集“可回收物”。这类垃圾主要包括废纸、塑料瓶、玻璃瓶、金属罐等可以回收再利用的物品。请大家在投放这些物品时，尽量保持它们的清洁，并将它们投入蓝色垃圾桶中，以便后续的回收处理。\\\\n为了方便大家使用，垃圾桶下方设有脚踏板。只需轻轻一踩，盖子就会自动打开，无需用手接触，既卫生又便捷。使用完毕后，盖子会自动关闭，保持环境整洁。\\\\n垃圾分类不仅有助于减少环境污染，还能提高资源的利用率。通过分类投放垃圾，我们可以将可回收物重新利用，减少对自然资源的消耗。同时，其他垃圾也能得到更有效的处理，减少对环境的污染。\\\\n小贴士：\\\\n1. “请勿乱扔垃圾”：请将垃圾投入相应的垃圾桶中，不要随意丢弃。\\\\n2. “保持环境卫生”：除了垃圾分类外，也请大家保持景区内的环境卫生，不随地吐痰，不乱涂乱画。\\\\n3. “爱护公共设施”：请爱护我们的垃圾桶和其他公共设施，共同维护美好的旅游环境。\\\\n希望大家在享受美景的同时，也能积极参与到环保行动中来。让我们一起努力，为保护环境贡献自己的一份力量！\\\\n如果有任何疑问或需要进一步的帮助，请随时联系景区工作人员。祝大家在景区度过愉快的时光！\\\\n4. yiyaoxiang\\\\n各位亲爱的游客朋友们，欢迎大家来到我们美丽的景区！今天我要向大家介绍的是我们景区内一个非常重要的设施——医药箱。这个医药箱是我们为了确保每一位游客的安全和健康而特别准备的。\\\",\\\"index\\\":\\\"1\\\"}}},{\\\"slice\\\":\\\"无论是在炎炎夏日，还是在凉爽的春秋季节，又或是寒冷的冬天，这台饮水机都会坚守在这里，为您的出行提供保障。希望它能成为您旅途中的一个小帮手，也希望大家在咱们景区游玩得开心、畅快，随时都能保持充足的水分和满满的活力。\\\\n\\\\n\\\",\\\"score\\\":0.625,\\\"meta\\\":{\\\"dataset\\\":{\\\"id\\\":\\\"7477409075896352822\\\",\\\"name\\\":\\\"恭王府\\\"},\\\"document\\\":{\\\"id\\\":\\\"7477893672953774090\\\",\\\"name\\\":\\\"AI导游专有“景区”数据.docx\\\",\\\"format_type\\\":0,\\\"source_type\\\":0},\\\"link\\\":{\\\"title\\\":\\\"恭王府_AI导游专有“景区”数据.docx\\\",\\\"url\\\":\\\"\\\"},\\\"card\\\":{\\\"title\\\":\\\"AI导游专有“景区”数据.docx\\\",\\\"con\\\":\\\"无论是在炎炎夏日，还是在凉爽的春秋季节，又或是寒冷的冬天，这台饮水机都会坚守在这里，为您的出行提供保障。希望它能成为您旅途中的一个小帮手，也希望大家在咱们景区游玩得开心、畅快，随时都能保持充足的水分和满满的活力。\\\",\\\"index\\\":\\\"2\\\"}}},{\\\"slice\\\":\\\"在景区的设计理念中，心心熊代表着爱与陪伴。我们希望每一位游客在游览景区的过程中，不仅能欣赏到美丽的自然风光，还能在与心心熊的互动中，感受到来自景区的关怀。无论是小朋友还是大朋友，都能在这里找到属于自己的快乐。当您玩累了，不妨来到心心熊身边，靠一靠，抱一抱，让它给予您片刻的温暖与慰藉。\\\\n而且，心心熊还承载着许多有趣的故事。在景区的不同季节，我们会为它换上特别的 “装扮”，就像是给它赋予了不同的性格和使命。在春节，它会穿上喜庆的红棉袄；在夏天，它会戴上清爽的遮阳帽。每一次的变化，都能给游客朋友们带来不一样的惊喜。\\\\n最后，希望这只可爱的心心熊能成为您此次景区之行中难忘的一部分，也希望您能带着从这里收获的爱与快乐，继续美好的生活旅程。祝大家玩得开心！\\\\n\\\\n8.yinshuiji\\\\n各位游客朋友们，现在看到的这台白色的饮水机，可是咱们景区贴心服务的一个小亮点哦。它来自宝贝尔品牌，在大家的游玩过程中，能为您随时提供便利的饮水服务。\\\\n这台饮水机有两个出水口，红色标识的是加热出水口，可以为大家提供热水。如果您想泡一杯热茶，舒缓一下游玩的疲惫，或者冲调一些热饮补充能量，用这个出水口就可以啦。蓝色标识的是制冷出水口，在炎热的天气里，能让您喝上一口清凉的饮用水，瞬间驱散暑气，恢复活力。\\\\n在使用饮水机时，大家也得留意一下上面的温馨提示哦，“高温警示，注意防烫”，尤其是带着小朋友的家长，一定要照看好孩子，避免被热水烫伤。下方还有一个储物柜，主要是用来放置一些清洁用品或者备用的零部件，以保证饮水机能够持续稳定地为大家服务。\\\\n咱们景区放置饮水机，就是考虑到大家在游玩途中，可能会口渴，而购买瓶装水有时候不太方便，也不够环保。有了这台饮水机，您只需要带上自己的水杯，就可以随时免费取水，既经济又环保。而且，我们的工作人员会定期对饮水机进行清洁和维护，确保水质干净卫生，让大家能放心饮用。\\\",\\\"score\\\":0.59912109375,\\\"meta\\\":{\\\"dataset\\\":{\\\"id\\\":\\\"7477409075896352822\\\",\\\"name\\\":\\\"恭王府\\\"},\\\"document\\\":{\\\"id\\\":\\\"7477893672953774090\\\",\\\"name\\\":\\\"AI导游专有“景区”数据.docx\\\",\\\"format_type\\\":0,\\\"source_type\\\":0},\\\"link\\\":{\\\"title\\\":\\\"恭王府_AI导游专有“景区”数据.docx\\\",\\\"url\\\":\\\"\\\"},\\\"card\\\":{\\\"title\\\":\\\"AI导游专有“景区”数据.docx\\\",\\\"con\\\":\\\"在景区的设计理念中，心心熊代表着爱与陪伴。我们希望每一位游客在游览景区的过程中，不仅能欣赏到美丽的自然风光，还能在与心心熊的互动中，感受到来自景区的关怀。无论是小朋友还是大朋友，都能在这里找到属于自己的快乐。当您玩累了，不妨来到心心熊身边，靠一靠，抱一抱，让它给予您片刻的温暖与慰藉。\\\\n而且，心心熊还承载着许多有趣的故事。在景区的不同季节，我们会为它换上特别的 “装扮”，就像是给它赋予了不同的性格和使命。在春节，它会穿上喜庆的红棉袄；在夏天，它会戴上清爽的遮阳帽。每一次的变化，都能给游客朋友们带来不一样的惊喜。\\\\n最后，希望这只可爱的心心熊能成为您此次景区之行中难忘的一部分，也希望您能带着从这里收获的爱与快乐，继续美好的生活旅程。祝大家玩得开心！\\\\n\\\\n8.yinshuiji\\\\n各位游客朋友们，现在看到的这台白色的饮水机，可是咱们景区贴心服务的一个小亮点哦。它来自宝贝尔品牌，在大家的游玩过程中，能为您随时提供便利的饮水服务。\\\\n这台饮水机有两个出水口，红色标识的是加热出水口，可以为大家提供热水。如果您想泡一杯热茶，舒缓一下游玩的疲惫，或者冲调一些热饮补充能量，用这个出水口就可以啦。蓝色标识的是制冷出水口，在炎热的天气里，能让您喝上一口清凉的饮用水，瞬间驱散暑气，恢复活力。\\\\n在使用饮水机时，大家也得留意一下上面的温馨提示哦，“高温警示，注意防烫”，尤其是带着小朋友的家长，一定要照看好孩子，避免被热水烫伤。下方还有一个储物柜，主要是用来放置一些清洁用品或者备用的零部件，以保证饮水机能够持续稳定地为大家服务。\\\\n咱们景区放置饮水机，就是考虑到大家在游玩途中，可能会口渴，而购买瓶装水有时候不太方便，也不够环保。有了这台饮水机，您只需要带上自己的水杯，就可以随时免费取水，既经济又环保。而且，我们的工作人员会定期对饮水机进行清洁和维护，确保水质干净卫生，让大家能放心饮用。\\\",\\\"index\\\":\\\"3\\\"}}},{\\\"slice\\\":\\\"恭王府西洋门\\\\n现存的西洋门\\\\n西洋门比较集中的清代园林,当属乾隆年间圆明园的西洋楼景区,尽管是西洋形态,但园林空间的处理仍是中国化的,主要表 现为以带有西洋门的院落划分景区。西洋楼景区的西洋门有两种,一种是独立设置的门,表示空间的开始和结束,如线法山东西两 侧的西洋门;一种是设在院墙上的门,是进出院落的通道,如观水法和万花镇的南门。但相对于其他西洋门,这些被学界公认为是 更倾向于西方风格,带有明显的巴洛克风格血统,并已被毁\\\\nhttps://www.pgm.org.cn/pgm/wfiz/201808/c0ade33830894af98cd3d7ec17727686.shtm\\\\n2/9\\\\n联通东西方的\\\\\\\"门\\\\\\\"--西洋门-文化和旅游部恭王府博物馆\\\\n2025/2/17 14:46\\\",\\\"score\\\":0.58056640625,\\\"meta\\\":{\\\"dataset\\\":{\\\"id\\\":\\\"7477409075896352822\\\",\\\"name\\\":\\\"恭王府\\\"},\\\"document\\\":{\\\"id\\\":\\\"7477413671636697142\\\",\\\"name\\\":\\\"联通东西方的“门”——西洋门.pdf\\\",\\\"format_type\\\":0,\\\"source_type\\\":0},\\\"link\\\":{\\\"title\\\":\\\"恭王府_联通东西方的“门”——西洋门.pdf\\\",\\\"url\\\":\\\"https://lf9-appstore-sign.oceancloudapi.com/ocean-cloud-tos/FileBizType.BIZ_BOT_DATASET/1106571495744154_1740971035953853210_WiesYQdxhq.pdf?lk3s=61a3dea3\\\\u0026x-expires=1743677088\\\\u0026x-signature=eLhOv1BlW9nPA0mIfwXVTnrz2aM%3D\\\"},\\\"card\\\":{\\\"title\\\":\\\"联通东西方的“门”——西洋门.pdf\\\",\\\"con\\\":\\\"恭王府西洋门\\\\n现存的西洋门\\\\n西洋门比较集中的清代园林,当属乾隆年间圆明园的西洋楼景区,尽管是西洋形态,但园林空间的处理仍是中国化的,主要表 现为以带有西洋门的院落划分景区。西洋楼景区的西洋门有两种,一种是独立设置的门,表示空间的开始和结束,如线法山东西两 侧的西洋门;一种是设在院墙上的门,是进出院落的通道,如观水法和万花镇的南门。但相对于其他西洋门,这些被学界公认为是 更倾向于西方风格,带有明显的巴洛克风格血统,并已被毁\\\\nhttps://www.pgm.org.cn/pgm/wfiz/201808/c0ade33830894af98cd3d7ec17727686.shtm\\\\n2/9\\\\n联通东西方的\\\\\\\"门\\\\\\\"--西洋门-文化和旅游部恭王府博物馆\\\\n2025/2/17 14:46\\\",\\\"index\\\":\\\"4\\\"}}},{\\\"slice\\\":\\\"\\\\u003ctable\\\\u003e\\\\u003ctr\\\\u003e\\\\u003ctd colspan=\\\\\\\"2\\\\\\\" rowspan=\\\\\\\"1\\\\\\\"\\\\u003e恭王府\\\\u003c/td\\\\u003e\\\\u003c/tr\\\\u003e\\\\u003ctr\\\\u003e\\\\u003ctd colspan=\\\\\\\"2\\\\\\\" rowspan=\\\\\\\"1\\\\\\\"\\\\u003e国家AAAAA级旅游景区\\\\u003c/td\\\\u003e\\\\u003c/tr\\\\u003e\\\\u003ctr\\\\u003e\\\\u003ctd colspan=\\\\\\\"1\\\\\\\" rowspan=\\\\\\\"1\\\\\\\"\\\\u003e级别\\\\u003c/td\\\\u003e\\\\u003ctd colspan=\\\\\\\"1\\\\\\\" rowspan=\\\\\\\"1\\\\\\\"\\\\u003eAAAAA\\\\u003c/td\\\\u003e\\\\u003c/tr\\\\u003e\\\\u003ctr\\\\u003e\\\\u003ctd colspan=\\\\\\\"1\\\\\\\" rowspan=\\\\\\\"1\\\\\\\"\\\\u003e所属地区\\\\u003c/td\\\\u003e\\\\u003ctd colspan=\\\\\\\"1\\\\\\\" rowspan=\\\\\\\"1\\\\\\\"\\\\u003e北京\\\\u003c/td\\\\u003e\\\\u003c/tr\\\\u003e\\\\u003ctr\\\\u003e\\\\u003ctd colspan=\\\\\\\"1\\\\\\\" rowspan=\\\\\\\"1\\\\\\\"\\\\u003e评定时间\\\\u003c/td\\\\u003e\\\\u003ctd colspan=\\\\\\\"1\\\\\\\" rowspan=\\\\\\\"1\\\\\\\"\\\\u003e2012年\\\\u003c/td\\\\u003e\\\\u003c/tr\\\\u003e\\\\u003c/table\\\\u003e\\\",\\\"score\\\":0.55078125,\\\"meta\\\":{\\\"dataset\\\":{\\\"id\\\":\\\"7477409075896352822\\\",\\\"name\\\":\\\"恭王府\\\"},\\\"document\\\":{\\\"id\\\":\\\"7477413671636025398\\\",\\\"name\\\":\\\"恭王府.docx\\\",\\\"format_type\\\":0,\\\"source_type\\\":0},\\\"link\\\":{\\\"title\\\":\\\"恭王府_恭王府.docx\\\",\\\"url\\\":\\\"\\\"},\\\"card\\\":{\\\"title\\\":\\\"恭王府.docx\\\",\\\"con\\\":\\\"恭王府\\\\n\\\\n\\\\n\\\\n国家AAAAA级旅游景区\\\\n\\\\n\\\\n\\\\n级别\\\\n\\\\nAAAAA\\\\n\\\\n\\\\n\\\\n所属地区\\\\n\\\\n北京\\\\n\\\\n\\\\n\\\\n评定时间\\\\n\\\\n2012年\\\",\\\"index\\\":\\\"5\\\"}}}]}\"}","content_type":"text","conversation_id":"7477903455970164736","created_at":1741085089,"id":"7477903507752861723","role":"assistant","type":"verbose","updated_at":1741085088},
     *      {"bot_id":"7477414818966994981","chat_id":"7477903498768777242","content":"请你明确一下关于“景区”具体的问题哦，比如景区内某个设施的信息、景区的规定等等，以便我更准确地为你作答。  ","content_type":"text","conversation_id":"7477903455970164736","created_at":1741085089,"id":"7477903507752992795","reasoning_content":"","role":"assistant","type":"answer","updated_at":1741085090},
     *      {"bot_id":"7477414818966994981","chat_id":"7477903498768777242","content":"{\"msg_type\":\"generate_answer_finish\",\"data\":\"{\\\"finish_reason\\\":0,\\\"FinData\\\":\\\"\\\"}\",\"from_module\":null,\"from_unit\":null}","content_type":"text","conversation_id":"7477903455970164736","created_at":1741085092,"id":"7477903514375782437","role":"assistant","type":"verbose","updated_at":1741085092},
     *      {"bot_id":"7477414818966994981","chat_id":"7477903498768777242","content":"介绍几个热门的景区","content_type":"text","conversation_id":"7477903455970164736","created_at":1741085092,"id":"7477903514375798821","role":"assistant","type":"follow_up","updated_at":1741085092},
     *      {"bot_id":"7477414818966994981","chat_id":"7477903498768777242","content":"中国有哪些5A级景区？","content_type":"text","conversation_id":"7477903455970164736","created_at":1741085092,"id":"7477903514375815205","role":"assistant","type":"follow_up","updated_at":1741085092},
     *      {"bot_id":"7477414818966994981","chat_id":"7477903498768777242","content":"分享一些景区拍照的小技巧","content_type":"text","conversation_id":"7477903455970164736","created_at":1741085092,"id":"7477903514375831589","role":"assistant","type":"follow_up","updated_at":1741085092}
     *      ],"
     *  detail":{"logid":"20250305101526F73BD4BB4E36C8010AF1"},"msg":""}
     *
     */


    /**
     * 合成语音
     *
     * @return
     */
    public static String textSpeech() {

        String url = "https://api.coze.cn/v1/audio/speech";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + cozeToken);

        JSONObject dataJson = new JSONObject();
        dataJson.put("input", "正在努力为您提供最佳讲解内容");
        dataJson.put("voice_id", "12940156318210");
        dataJson.put("response_format", "mp3");
        dataJson.put("speed", 1);
        String path = System.getProperty("user.dir") + "\\doc\\demo.mp3";
        String response = HttpClientUtil.postJsonRtLocal(url, path, null, headers, dataJson.toJSONString(), false);
        return null;
    }

    /**
     * 语音识别
     *
     * @return
     */
    public static String transcriptions() {

        String url = "https://api.coze.cn/v1/audio/transcriptions";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + cozeToken);

        String path = System.getProperty("user.dir") + "\\doc\\cozedemo.mp3";

        Map<String, ContentBody> reqParam = new HashMap<>();
        reqParam.put("file", new FileBody(new File(path)));

        String response = HttpClientUtil.postFileMultiPart(url, reqParam, headers, false);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            log.info("response:{}", response);
        }
        return null;
    }

    public static String streamChat() {

        JSONObject dataJson = new JSONObject();
        dataJson.put("bot_id", "7477425313061126156");
        dataJson.put("user_id", "gg");
        dataJson.put("stream", true);

        JSONArray messages = new JSONArray();
        JSONObject msg = new JSONObject();
        msg.put("content", "天气怎么样");
        msg.put("content_type", "text");
        msg.put("role", "user");
        msg.put("type", "question");

        messages.add(msg);

        dataJson.put("additional_messages", messages);

        String urlPost = "https://api.coze.cn/v3/chat?conversation_id=" + "7478629119559974964";

        WebClient webClient = WebClient.builder()
                .codecs(item -> item.defaultCodecs().maxInMemorySize(10 * 1024 * 1024))
                .build();
        Mono<String> ret = webClient
                .post()
                .uri(urlPost)
                .acceptCharset(Consts.UTF_8)
                .accept(MediaType.TEXT_EVENT_STREAM) // 用于服务器发送事件 (SSE)
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", "Bearer " + cozeToken)
                .bodyValue(dataJson)
                .retrieve()
                .bodyToMono(String.class)
                .onErrorResume(e -> {
                    // 错误处理：打印错误信息并返回错误提示
                    System.out.println("Error calling service2: " + e.getMessage());
                    return Mono.just("Error calling service2");
                })
                //.block()
                ;

        //System.out.println("Response: " + resp);

        //json 流式返回
    /*    List<JSONObject> bu = HttpClientUtil.postEventStream(urlPost, dataJson.toJSONString(), cozeToken);
        //log.info(bu.toString());

        List<JSONObject> contentJson = bu.stream()
                .filter(e -> "assistant".equals(e.getString("role"))
                        && "answer".equals(e.getString("type"))
                        && e.containsKey("created_at")
                ).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(contentJson)) {
            String content = contentJson.get(0).getString("content");
            log.info(content);
            return content;
        }*/

        return null;
    }

    public static String streamChatJsonByHttp() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + cozeToken);

        JSONObject dataJson = new JSONObject();
        dataJson.put("bot_id", "7477425313061126156");
        dataJson.put("user_id", "gg");
        dataJson.put("stream", true);

        JSONArray messages = new JSONArray();
        JSONObject msg = new JSONObject();
        msg.put("content", "介绍一下你吧");
        msg.put("content_type", "text");
        msg.put("role", "user");
        msg.put("type", "question");

        messages.add(msg);

        dataJson.put("additional_messages", messages);

        String urlPost = "https://api.coze.cn/v3/chat?conversation_id=" + "7486745743202484258";
        List<JSONObject> bu = HttpClientUtil.postTextJsonRtBrowse(urlPost, null, headers, dataJson.toJSONString(), false);

        List<JSONObject> contentJson = bu.stream()
                .filter(e -> "assistant".equals(e.getString("role"))
                        && "answer".equals(e.getString("type"))
                        && e.containsKey("created_at")
                ).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(contentJson)) {
            String content = contentJson.get(0).getString("content");
            log.info(content);
            return content;
        }
        return null;
    }

    /**
     * 使用coze api访问 流式会话
     */
    public static void cozeStreamChat() {
        TokenAuth authCli = new TokenAuth(cozeToken);
        // Init the Coze client through the access_token.
        CozeAPI coze =
                new CozeAPI.Builder()
                        .baseURL(com.coze.openapi.service.config.Consts.COZE_CN_BASE_URL)
                        .auth(authCli)
                        .readTimeout(10000)
                        .build();
        CreateChatReq req =
                CreateChatReq.builder()
                        .botID("7477425313061126156")
                        .userID("gg")
                        .stream(true)
                        .conversationID("7478629119559974964")
                        .messages(Collections.singletonList(Message.buildUserQuestionText("介绍一下恭王府")))
                        .build();

        Flowable<ChatEvent> resp = coze.chat().stream(req);
        Flux<ChatEvent> chatEventFlux = Flux.from(resp);
        chatEventFlux.log().subscribe();
        StringBuffer buffer = new StringBuffer();
      /*  resp.blockingForEach(
                event -> {
                    if (ChatEventType.CONVERSATION_MESSAGE_DELTA.equals(event.getEvent())) {
                        System.out.print(event.getMessage().getContent());
                        buffer.append(event.getMessage().getContent());
                    }
                    if (ChatEventType.CONVERSATION_CHAT_COMPLETED.equals(event.getEvent())) {
                        System.out.println("Token usage:" + event.getChat().getUsage().getTokenCount());
                    }
                });*/
        System.out.println("done");
        System.out.println(buffer.toString());
        coze.shutdownExecutor();
    }

    public static void cozeTextSpeech() {
        TokenAuth authCli = new TokenAuth(cozeToken);
        // Init the Coze client through the access_token.
        CozeAPI coze =
                new CozeAPI.Builder()
                        .baseURL(com.coze.openapi.service.config.Consts.COZE_CN_BASE_URL)
                        .auth(authCli)
                        .readTimeout(10000)
                        .build();

        String path = "https://ai-guided.oss-cn-beijing.aliyuncs.com/13700284369/chat.mp3";
        String voiceID = "12940156318210";
        String content = replaceEnter("");

        CreateSpeechReq req = CreateSpeechReq.builder()
                .input(content)
                .voiceID(voiceID)
                .sampleRate(24000)
                .build();

        CreateSpeechResp resp = coze.audio().speech().create(req);
        System.out.println(resp);
        try {
            byte[] bytes = resp.getResponse().bytes();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void transcriptionsCoze() {
        TokenAuth authCli = new TokenAuth(cozeToken);
        CozeAPI coze =
                new CozeAPI.Builder()
                        .baseURL(com.coze.openapi.service.config.Consts.COZE_CN_BASE_URL)
                        .auth(authCli)
                        .readTimeout(10000)
                        .connectTimeout(10000)
                        .build();
        String path = System.getProperty("user.dir") + "\\doc\\cozedemo.mp3";
        byte[] bytes = getFileBytes(path);
        CreateTranscriptionsReq req = CreateTranscriptionsReq.of("cozedemo.mp3", bytes);
        CreateTranscriptionsResp resp = coze.audio().transcription().create(req);
        System.out.println(resp);
    }

    private static byte[] getFileBytes(String file) {
        try {
            File f = new File(file);
            int length = (int) f.length();
            byte[] data = new byte[length];
            new FileInputStream(f).read(data);
            return data;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 替换 换行
     */
    public static String replaceEnter(String args){
        String s = "生活的每一瞬间都蕴含着诗意，晨曦穿透薄雾洒下的光辉，如同希望的画笔描绘出崭新的开始；黄昏时分天边绚丽的晚霞，恰似岁月写下的浪漫篇章；雨中静谧的小巷，那是时光留下的温柔足迹。自然是无尽的灵感源泉，巍峨山川似沉默的巨人守护着大地，潺潺溪流如灵动的琴弦弹奏着生命之歌，广袤森林像神秘的宝库蕴藏着无数奥秘。情感是一首悠扬的旋律，亲情是那永不落幕的主题曲，友情是轻快的间奏，爱情则是动人心弦的高潮。\n";
        String news = s.replaceAll("[\\s\\t\\n\\r]", "");
        System.out.println(news);
        return news;
    }

    public static void spline(){
        String str = "我没办法像人一样用眼睛去“看”东西呢。但要是在漕运历史文化游览区，那满眼都是历史的痕迹。能看到古旧的闸口，曾经它精准调控着水位，保障漕船顺利通行；还有岸边的拴船石桩，它们默默伫立，见证了无数漕船的来来去去。道路旁也许会有记载漕运故事的碑刻，上面的文字虽然有些磨损，却藏着往昔岁月的密码。而那些古街古巷，青石板路承载着昔日漕运带来的人来人往与繁华热闹。你呢，此刻在游览区看到了什么新鲜事儿，快和我说道说道呗？";
        String s = "生活的每一瞬间都蕴含着诗意，晨曦穿透薄雾洒下的光辉，如同希望的画笔描绘出崭新的开始；黄昏时分天边绚丽的晚霞，恰似岁月写下的浪漫篇章；雨中静谧的小巷，那是时光留下的温柔足迹。自然是无尽的灵感源泉，巍峨山川似沉默的巨人守护着大地，潺潺溪流如灵动的琴弦弹奏着生命之歌，广袤森林像神秘的宝库蕴藏着无数奥秘。情感是一首悠扬的旋律，亲情是那永不落幕的主题曲，友情是轻快的间奏，爱情则是动人心弦的高潮。";
        String[] split = s.split("[。；？！]");
        if (split.length > 0) {
            for (int i = 0; i < split.length; i++) {
                System.out.println(split[i]);
            }
        }
    }


    public static void main(String[] args) {
        //getSYToken();

        long start = System.currentTimeMillis();
        log.info("开始请求");

        getProToken();

        //createChat(); // 会话ID 7486742597088772115（故事）， 7486745743202484258 （人物）

        //textSpeech();

        //cozeTextSpeech();

        //transcriptions();

        //streamChat();

        //streamChatJsonByHttp(); //11457

//        cozeStreamChat(); //11459

        //replaceEnter("");

//        transcriptionsCoze();

        //spline();

        //read();

        long end = System.currentTimeMillis();
        log.info("time:{}", end - start);
    }

    private static final String cozeToken = "czs_qHhRfJYgI4aaDwhnra8bVj7PH5tJrhWAiG4IhT94QqHc9aJNktSvPD3bDizXjWTdW";


    public static void read(){
        try {
            InputStream inputStream = getResponseAsInputStream();
            String content = readContentFromPipedInputStream((PipedInputStream) inputStream);
            // log.info("response content: \n{}", content.replace("}", "}\n"));
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    public static InputStream getResponseAsInputStream() throws IOException, InterruptedException {

        PipedOutputStream pipedOutputStream = new PipedOutputStream();
        PipedInputStream pipedInputStream = new PipedInputStream(50 * 1024 * 1024);
        pipedInputStream.connect(pipedOutputStream);

        JSONObject dataJson = new JSONObject();
        dataJson.put("bot_id", "7477425313061126156");
        dataJson.put("user_id", "gg");
        dataJson.put("stream", true);

        JSONArray messages = new JSONArray();
        JSONObject msg = new JSONObject();
        msg.put("content", "天气怎么样");
        msg.put("content_type", "text");
        msg.put("role", "user");
        msg.put("type", "question");

        messages.add(msg);

        dataJson.put("additional_messages", messages);

        String urlPost = "https://api.coze.cn/v3/chat?conversation_id=" + "7478629119559974964";

        WebClient webClient = WebClient.builder()
                .codecs(item -> item.defaultCodecs().maxInMemorySize(10 * 1024 * 1024))
                .build();
        Flux<DataBuffer> body = webClient
                .post()
                .uri(urlPost)
                .acceptCharset(Consts.UTF_8)
                .accept(MediaType.TEXT_EVENT_STREAM) // 用于服务器发送事件 (SSE)
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", "Bearer " + cozeToken)
                .bodyValue(dataJson)
                .exchangeToFlux(clientResponse -> {
                    return clientResponse.body(BodyExtractors.toDataBuffers());
                })
                .doOnError(error -> {
                    log.error("error occurred while reading body", error);
                })
                .doFinally(s -> {
                    try {
                        pipedOutputStream.close();
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                })
                .doOnCancel(() -> {
                    log.error("Get request is cancelled");
                });
        log.info("Writing to output buffer");
        DataBufferUtils.write(body, pipedOutputStream)
                //.log("Writing to output buffer")
                .subscribe();
        return pipedInputStream;
    }

    private static String readContentFromPipedInputStream(PipedInputStream stream) throws IOException {
        StringBuffer contentStringBuffer = new StringBuffer();
        try {
            Thread pipeReader = new Thread(() -> {
                try {
                    contentStringBuffer.append(readContent(stream));
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });
            pipeReader.start();
            pipeReader.join();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            stream.close();
        }

        return String.valueOf(contentStringBuffer);
    }

    private static String readContent(InputStream stream) throws IOException {
        StringBuffer contentStringBuffer = new StringBuffer();
        while(stream.read() != -1){
            byte[] tmp = new byte[stream.available()];
            int byteCount = stream.read(tmp, 0, tmp.length);
            log.info("read {} from the stream", new String(tmp));
            contentStringBuffer.append(new String(tmp));
        }

        return String.valueOf(contentStringBuffer);
    }

}

