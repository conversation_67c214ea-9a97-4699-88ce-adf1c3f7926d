package com.iciyun.system.service;

import com.iciyun.system.domain.SysTouristLabel;
import com.iciyun.system.domain.bo.TouristAttractionsQry;
import com.iciyun.system.domain.qo.LabelsQuery;

import java.util.List;

/**
 * 景区标签Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface ISysTouristLabelService
{
    /**
     * 查询景区标签
     * 
     * @param id 景区标签主键
     * @return 景区标签
     */
    public SysTouristLabel selectSysTouristLabelById(Long id);

    /**
     * 查询景区标签列表
     * 
     * @param sysTouristLabel 景区标签
     * @return 景区标签集合
     */
    public List<SysTouristLabel> selectSysTouristLabelList(SysTouristLabel sysTouristLabel);

    /**
     * 新增景区标签
     * 
     * @param sysTouristLabel 景区标签
     * @return 结果
     */
    public int insertSysTouristLabel(SysTouristLabel sysTouristLabel);

    /**
     * 修改景区标签
     * 
     * @param sysTouristLabel 景区标签
     * @return 结果
     */
    public int updateSysTouristLabel(SysTouristLabel sysTouristLabel);

    /**
     * 批量删除景区标签
     * 
     * @param ids 需要删除的景区标签主键集合
     * @return 结果
     */
    public int deleteSysTouristLabelByIds(Long[] ids);

    /**
     * 删除景区标签信息
     * 
     * @param id 景区标签主键
     * @return 结果
     */
    public int deleteSysTouristLabelById(Long id);

    List<SysTouristLabel> selectSyncTouris(SysTouristLabel condition);
    /**
     * 查询随身讲景点
     * @param qry
     * @return
     */
    SysTouristLabel queryTouristAttractions(TouristAttractionsQry qry);

    SysTouristLabel selectOne(SysTouristLabel condition);

    SysTouristLabel queryNearestNonRepeatLabel(TouristAttractionsQry qry);

    List<SysTouristLabel> queryNearestRepeatLabel(TouristAttractionsQry qry);

    SysTouristLabel selectByLabel(String labelName);

    List<SysTouristLabel> listQuery(LabelsQuery labelsQuery);
}
