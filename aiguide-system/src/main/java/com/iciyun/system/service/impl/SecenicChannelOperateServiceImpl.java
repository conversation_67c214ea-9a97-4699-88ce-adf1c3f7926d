package com.iciyun.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iciyun.common.enums.PartnerBusinessType;
import com.iciyun.system.domain.SecenicChannelOperate;
import com.iciyun.system.mapper.SecenicChannelOperateMapper;
import com.iciyun.system.service.ISecenicChannelOperateService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 渠道下的运营对应的景区 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12 11:05:25
 */
@Service
public class SecenicChannelOperateServiceImpl extends ServiceImpl<SecenicChannelOperateMapper, SecenicChannelOperate> implements ISecenicChannelOperateService {

    @Override
    public void edit(SecenicChannelOperate secenicChannelOperate) {
        SecenicChannelOperate one = this.lambdaQuery()
                .eq(SecenicChannelOperate::getChannelCode, secenicChannelOperate.getChannelCode())
                .eq(SecenicChannelOperate::getOperateId, secenicChannelOperate.getOperateId())
                .eq(SecenicChannelOperate::getScenicId, secenicChannelOperate.getScenicId())
                .one();
        if (one != null) {
            this.updateById(secenicChannelOperate);
        } else {
            this.save(secenicChannelOperate);
        }
    }

    @Override
    public void del(Integer id) {
        this.removeById(id);
    }
}
