package com.iciyun.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.iciyun.common.enums.PartnerBusinessType;
import com.iciyun.common.utils.StringUtils;
import com.iciyun.common.utils.uuid.IdHutool;
import com.iciyun.system.domain.*;
import com.iciyun.system.mapper.ScenicOperateMapper;
import com.iciyun.system.service.IScenicLocationService;
import com.iciyun.system.service.IScenicOperateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 景区运营 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12 11:16:57
 */
@Service
public class ScenicOperateServiceImpl extends ServiceImpl<ScenicOperateMapper, ScenicOperate> implements IScenicOperateService {

    @Autowired
    private IScenicLocationService scenicLocationService;

    @Override
    public List<ScenicOperate> getlist(ScenicOperateCmd cmd) {
        QueryWrapper<ScenicOperate> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotEmpty(cmd.getOperateStatue())) {
            queryWrapper.lambda().eq(ScenicOperate::getOperateStatue, cmd.getOperateStatue());
        }
        if (StringUtils.isNotEmpty(cmd.getOperateName())) {
            queryWrapper.lambda().like(ScenicOperate::getOperateName, cmd.getOperateName());
        }
        if (StringUtils.isNotEmpty(cmd.getOperatePerson())) {
            queryWrapper.lambda().like(ScenicOperate::getOperatePerson, cmd.getOperatePerson());
        }
        if (StringUtils.isNotEmpty(cmd.getDetailAddress())) {
            queryWrapper.lambda().like(ScenicOperate::getDetailAddress, cmd.getDetailAddress());
        }
        if (StringUtils.isNotEmpty(cmd.getScenicName())) {
            //查询景区
            List<ScenicLocation> scenicList = scenicLocationService.lambdaQuery()
                    .like(ScenicLocation::getScenicName, cmd.getScenicName())
                    .list();
            queryWrapper.lambda().in(ScenicOperate::getOperateId, scenicList.stream().collect(Collectors.toList()));
        }

        List<ScenicOperate> scenicChannelList = this.list(queryWrapper);

        return scenicChannelList;
    }

    @Override
    public void edit(ScenicOperate scenicOperate) {
        ScenicOperate scenicDb = this.getById(scenicOperate.getId());
        if (scenicDb != null) {
            this.updateById(scenicOperate);
        } else {
            this.save(scenicOperate);
        }
    }

    @Override
    public void delete(Long id) {
        this.removeById(id);
    }
}
