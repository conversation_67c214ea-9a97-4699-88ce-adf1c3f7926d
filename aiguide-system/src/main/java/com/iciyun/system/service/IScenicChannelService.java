package com.iciyun.system.service;

import com.iciyun.system.domain.*;
import com.baomidou.mybatisplus.extension.service.IService;
import com.iciyun.system.domain.vo.ScenicChannelVO;

import java.util.List;

/**
 * <p>
 * 渠道信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06 10:44:43
 */
public interface IScenicChannelService extends IService<ScenicChannel> {

    List<ScenicChannelVO> getlist(ScenicChannelCmd cmd);

    List<ScenicLocation> getScenicByOperateId(ScenicLocation scenicLocation);

    List<ScenicOperate> getOperatelist();

}
