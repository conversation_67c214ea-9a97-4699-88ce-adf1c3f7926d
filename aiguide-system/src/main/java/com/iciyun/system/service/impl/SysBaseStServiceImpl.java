package com.iciyun.system.service.impl;

import com.iciyun.system.domain.SysBaseSt;
import com.iciyun.system.mapper.SysBaseStMapper;
import com.iciyun.system.service.AsyncTaskService;
import com.iciyun.system.service.ISysBaseStService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

/**
 * <p>
 * 基础统计 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06 16:41:01
 */
@Service
@Slf4j
public class SysBaseStServiceImpl extends ServiceImpl<SysBaseStMapper, SysBaseSt> implements ISysBaseStService {

    @Autowired
    private AsyncTaskService asyncTaskService;

    @Override
    public void dateStTask() {
        Integer todayUserCount = 0;
        Long allUserCount = 0L;
        Long issueBeanCount = 0L;
        Long userIssueBeanCount = 0L;
        try {
            //新增用户数量
            CompletableFuture<Integer> futureT = asyncTaskService.getTodayNewUserCount();
            //总用户数量
            CompletableFuture<Long> futureA = asyncTaskService.getAllUserCount();
            //发行bean数量
            CompletableFuture<Long> futureI = asyncTaskService.getIssueBeanCount();
            //用户总使用bean数量
            CompletableFuture<Long> futureU = asyncTaskService.getUserUseBeanCount();

            // 等待所有任务完成 只能在一个线程池中 才能控制住
            CompletableFuture<Void> allOf = CompletableFuture.allOf(futureT, futureA, futureI, futureU);
            // 提取结果
            allOf.join();
            todayUserCount = futureT.get();
            allUserCount = futureA.get();
            issueBeanCount = futureI.get();
            userIssueBeanCount = futureU.get();

            SysBaseSt sysBaseSt = SysBaseSt.builder()
                    .stDate(LocalDateTime.now().toLocalDate())
                    .newUserCount(todayUserCount)
                    .userCount(allUserCount)
                    .issueBeanCount(issueBeanCount)
                    .useBeanCount(userIssueBeanCount)
                    .residueBeanCount(issueBeanCount - userIssueBeanCount)  //用户总剩余bean数量
                    .createTime(LocalDateTime.now())
                    .build();

            asyncTaskService.insertSysBaseSt(sysBaseSt);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
