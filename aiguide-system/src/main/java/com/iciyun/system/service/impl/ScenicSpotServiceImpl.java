package com.iciyun.system.service.impl;

import com.iciyun.system.domain.bo.ScenicSpotBo;
import com.iciyun.system.mapper.ScenicSpotMapper;
import com.iciyun.system.service.IScenicSpotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ScenicSpotServiceImpl implements IScenicSpotService {

    @Autowired
    private ScenicSpotMapper scenicSpotMapper;

    @Override
    public List<ScenicSpotBo> queryByFeatures(String features, int topN) {
        return scenicSpotMapper.queryByCosineDistance(features, topN);
    }
}
