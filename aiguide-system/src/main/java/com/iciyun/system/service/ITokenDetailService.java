package com.iciyun.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.iciyun.system.domain.TokenDetail;
import com.iciyun.system.domain.bo.AddTokenDetailCmd;
import com.iciyun.system.domain.bo.QueryTotalProfitQry;

import java.math.BigDecimal;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25 10:27:41
 */
public interface ITokenDetailService extends IService<TokenDetail> {

    void addTokenDetail(AddTokenDetailCmd cmd);

    BigDecimal queryTotalProfit(QueryTotalProfitQry qry);
}
