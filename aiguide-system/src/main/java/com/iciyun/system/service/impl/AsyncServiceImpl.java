package com.iciyun.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.iciyun.common.constant.CacheConstants;
import com.iciyun.common.core.domain.R;
import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.common.core.domain.entity.TextSpeechCmd;
import com.iciyun.common.core.domain.entity.wx.WxSysUserInfo;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.common.cqe.SaveScenicSpotBrowseHistoryCmd;
import com.iciyun.common.enums.GuidePersonaEnum;
import com.iciyun.common.enums.HobbyTypeEnum;
import com.iciyun.common.utils.StringUtils;
import com.iciyun.common.utils.sign.Md5Utils;
import com.iciyun.common.utils.uuid.IdHutool;
import com.iciyun.system.domain.*;
import com.iciyun.system.domain.bo.AddTokenDetailCmd;
import com.iciyun.system.mapper.SysUserMapper;
import com.iciyun.system.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;


@Service
@Slf4j
public class AsyncServiceImpl implements AsyncService {

    @Autowired
    private IScenicBrowseHistoryService scenicBrowseHistoryService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SysUserMapper userMapper;

    @Value("${user.code.expireTime}")
    private Integer codeTime;

    @Autowired
    private IUserRecService userRecService;

    @Autowired
    private IBaseConfigService baseConfigService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ITokenDetailService tokenDetailService;

    @Autowired
    ScenicCacheManager scenicCacheManager;


    @Override
    @Async("threadPoolTaskExecutor")
    public void insertScenicBrowseHistory(SaveScenicSpotBrowseHistoryCmd cmd) {
        try {
            scenicBrowseHistoryService.saveScenicSpotBrowseHistory(cmd);
        } catch (Exception e) {
            log.error("保存景区浏览记录异常:", e);
        }
    }

    @Override
    @Async("threadPoolTaskExecutor")
    public void updateUserRecCode(String code, Long userId, String userName) {
        userMapper.updateUserCode(code, userId);
        String key = CacheConstants.userCodeKey + userName;
        redisCache.setCacheObject(key, code, codeTime, TimeUnit.DAYS);
    }

    @Override
    @Async("threadPoolTaskExecutor")
    public void bindCode(String recUserName, String userName, Long userId) {
        WxSysUserInfo wxSysUserInfo = userMapper.selectWxUserInfo(recUserName);
        if (wxSysUserInfo != null) {
            BaseConfig baseConfig = baseConfigService.queryOne();
            if (baseConfig != null) {
                Integer referralRewardCount = baseConfig.getReferralReward();
                if (referralRewardCount != null) {
                    // 增加推荐奖励
                    AddTokenDetailCmd cmd = new AddTokenDetailCmd();
                    cmd.setUserId(wxSysUserInfo.getUserId());
                    cmd.setChangeType(ChangeType.IN);
                    cmd.setTranscationType(TransactionType.RECOMMEND);
                    cmd.setUserPhone(wxSysUserInfo.getUserName());
                    cmd.setAmountIncurred(new BigDecimal(referralRewardCount));
                    UserRec userRec = new UserRec();
                    userRec.setBizId(IdHutool.gen(""));
                    try {
                        R ret = sysUserService.updateTokenBalance(cmd);
                        if (ret.getCode() == R.SUCCESS) {
                            userRec.setRecStatue("0");
                        } else {
                            userRec.setRecStatue("1");
                        }
                    } catch (Exception e) {
                        userRec.setRecStatue("1");
                        log.error("增加推荐奖励异常", e);
                    }
                    userRec.setUserId(userId);
                    //被推荐人
                    userRec.setUserName(userName);
                    //推荐人
                    userRec.setRecUserId(wxSysUserInfo.getUserId());
                    userRec.setRecUserName(wxSysUserInfo.getUserName());
                    userRec.setCreateTime(LocalDateTime.now());
                    userRec.setRecBeanCount(referralRewardCount);
                    userRecService.save(userRec);
                }
            }
        }
    }

    @Override
    @Async("threadPoolTaskExecutor")
    public void addRewardBySign(String userName, Long userId) {
        BaseConfig baseConfig = baseConfigService.queryOne();
        if (baseConfig != null) {
            Integer registerCount = baseConfig.getRegisterReward();
            if (registerCount != null) {
                // 增加注册奖励  userName
                AddTokenDetailCmd cmd = new AddTokenDetailCmd();
                cmd.setUserId(userId);
                cmd.setChangeType(ChangeType.IN);
                cmd.setTranscationType(TransactionType.REGISTER);
                cmd.setUserPhone(userName);
                cmd.setAmountIncurred(new BigDecimal(registerCount));
                try {
                    sysUserService.updateTokenBalance(cmd);
                } catch (Exception e) {
                    log.error("增加注册奖励异常", e);
                }
            }
        }
    }

    @Override
    public void doCache(TextSpeechCmd.ScenicCache scenicCache, String filePath) {
        String scenicId = scenicCache.getScenicId();
        String scenicLabel = scenicCache.getScenicLabel();
        String voiceId = scenicCache.getVoiceId();

        String labelKay = scenicCacheManager.getCacheKey(scenicCache.getUserStyleDto(),scenicId,scenicLabel,voiceId);

        log.info("进行景区 label 缓存：{}",labelKay);

//        redisCache.setCacheObject(labelKay, voices, scenicCacheManager.getCacheTime(), TimeUnit.DAYS);
        redisCache.setCacheObject(labelKay, filePath);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delBizByUserName(String userName, Integer scenicId) {

        WxSysUserInfo uxUser = userMapper.selectWxUserInfo(userName);
        if (uxUser != null) {
            //删除用户表
            userMapper.deleteUserByUserName(userName);
            //删除用户推荐表
            userRecService.remove(new QueryWrapper<UserRec>()
                    .lambda()
                    .eq(UserRec::getRecUserName, userName));
            //删除用户浏览表
            scenicBrowseHistoryService.remove(new QueryWrapper<ScenicBrowseHistory>()
                    .lambda()
                    .eq(ScenicBrowseHistory::getUserId, String.valueOf(uxUser.getUserId())));
            //删除用户的收益记录
            tokenDetailService.remove(new QueryWrapper<TokenDetail>()
                    .lambda()
                    .eq(TokenDetail::getUserPhone, uxUser.getUserName()));

            //删除用户的支付记录
            if(scenicId != null){
                String scenicKey = String.format("user:%s:scenic:%s", uxUser.getUserId(), scenicId);
                redisCache.deleteObject(scenicKey);
                String eyeRedisKey = String.format("eye:user:%s:scenic:%s", uxUser.getUserId(), scenicId);
                redisCache.deleteObject(eyeRedisKey);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delBizByUserId(Long userId, Integer scenicId) {
        SysUser uxUser = userMapper.selectUserById(userId);
        if (uxUser != null) {
            //删除用户表
            userMapper.deleteUserByUserName(uxUser.getUserName());
            //删除用户推荐表
            userRecService.remove(new QueryWrapper<UserRec>()
                    .lambda()
                    .eq(UserRec::getRecUserName, uxUser.getUserName()));
            //删除用户浏览表
            scenicBrowseHistoryService.remove(new QueryWrapper<ScenicBrowseHistory>()
                    .lambda()
                    .eq(ScenicBrowseHistory::getUserId, String.valueOf(uxUser.getUserId())));
            //删除用户的收益记录
            tokenDetailService.remove(new QueryWrapper<TokenDetail>()
                    .lambda()
                    .eq(TokenDetail::getUserPhone, uxUser.getUserName()));

            //删除用户的支付记录
            if(scenicId != null){
                String scenicKey = String.format("user:%s:scenic:%s", uxUser.getUserId(), scenicId);
                redisCache.deleteObject(scenicKey);
                String eyeRedisKey = String.format("eye:user:%s:scenic:%s", uxUser.getUserId(), scenicId);
                redisCache.deleteObject(eyeRedisKey);
            }
        }
    }

    public String getSysChatUserStyle(Long userId) {
        String prompt = "性别:%s,风格:%s,语言:%s";
        SysUser sysUserInfo = userService.selectUserById(userId);
        if (sysUserInfo != null) {
            String sexType = GuidePersonaEnum.of(sysUserInfo.getGuidePersona()).getDesc();
            //兴趣
            List<String> bu = new ArrayList<>();
            List<String> hobbyTypes = JSON.parseArray(sysUserInfo.getHobbyTypes(), String.class);
            hobbyTypes.forEach(e -> {
                bu.add(HobbyTypeEnum.of(e).getDesc());
            });
            String languageFlag = sysUserInfo.getLanguageFlag();
            prompt = String.format(prompt, sexType, StringUtils.join(bu, ","), languageFlag);
        }
        return prompt;
    }
}
