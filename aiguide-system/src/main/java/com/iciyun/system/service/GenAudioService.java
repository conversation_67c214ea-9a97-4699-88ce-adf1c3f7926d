package com.iciyun.system.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpUtil;
import com.coze.openapi.client.audio.common.AudioFormat;
import com.coze.openapi.client.audio.speech.CreateSpeechReq;
import com.coze.openapi.client.audio.speech.CreateSpeechResp;
import com.coze.openapi.service.service.CozeAPI;
import com.google.common.collect.Lists;
import com.iciyun.common.constant.CacheConstants;
import com.iciyun.common.core.domain.entity.TextSpeechCmd;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.common.utils.HelpMe;
import com.iciyun.common.utils.tts.TTSClient;
import com.iciyun.system.alitts.AliTokenManager;
import com.iciyun.system.alitts.SpeechSynthesizerRestful;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> on 2025/6/19 18:10.
 */
@Slf4j
@Service
public class GenAudioService {

    @Autowired
    ISysDictDataService sysDictDataService;
    @Autowired
    AliTokenManager aliTokenManager;
    @Autowired
    private ISysDictTypeService dictTypeService;
    @Autowired
    private IGuideService guideService;

    @Autowired
    private IGuideTkService guideTkService;

    @Autowired
    private RedisCache redisCache;

    //cozeToken
    private static String cozeTokenKey = "chat:cozeToken";

    //14 分钟
    private static int cozeTokenTime = 14;

    /**
     * Retryable，spring 重试机制
     *
     * @param cmd
     * @return
     */
    @Retryable(value = {Exception.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2))
    public byte[] ttsCoze(String voiceId, String content, float speed) {
        long time1 = System.currentTimeMillis();
        String cozeToken = redisCache.getCacheObject(CacheConstants.cozeTokenKey);
        if (org.apache.commons.lang3.StringUtils.isBlank(cozeToken)) {
            cozeToken = guideTkService.getToken();
            redisCache.setCacheObject(CacheConstants.cozeTokenKey, cozeToken, CacheConstants.cozeTokenTime, TimeUnit.MINUTES);
        }

        CozeAPI coze = guideTkService.getCozeAPI(cozeToken);

        CreateSpeechReq req = CreateSpeechReq.builder()
                .input(content)
                .voiceID(voiceId)
                .sampleRate(24000)
                .speed(speed)
                .responseFormat(AudioFormat.WAV)
                .readTimeout(20000)
                .connectTimeout(20000)
                .writeTimeout(20000)
                .build();
        CreateSpeechResp resp = coze.audio().speech().create(req);

        long time2 = System.currentTimeMillis();

        log.info("调用 coze tts 接口, 文本：{}，用时：{}  返回结果：{}", content, (time2 - time1), resp);

        try {
            byte[] bytes = resp.getResponse().bytes();
            return bytes;
        } catch (Exception e) {
            log.error("调用 coze speech 接口失败！", e);
            throw new RuntimeException(e);
        }
    }


    private String genAudioFilePath(String projectAppKey, String content, String voice) {
        LocalDateTime now = LocalDateTime.now();

        SpeechSynthesizerRestful spspeechSynthesizerRestful = new SpeechSynthesizerRestful(projectAppKey, aliTokenManager.getToken());

        File file = new File(IdUtil.simpleUUID() + ".wav");

        spspeechSynthesizerRestful.genAudio(content, file, voice);

        byte[] chatByte = IoUtil.readBytes(FileUtil.getInputStream(file), true);

        String fileName = "chat.wav";
        double second = HelpMe.getWavDurationFromBytes(chatByte);
        if (second>0){
            fileName = "chat_second_" + second + ".wav";
        }

        String mp3Name = "cache/mp3/" + now.getYear() + "/" + now.getMonthValue() + "/" + now.getDayOfMonth() + "/" + LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli() + "/" + fileName;

        FileUtil.del(file);

        return guideService.upOSSFiles(mp3Name, chatByte);
    }


    public String genAudio(TTSClient ttsClient, String languageFlag, String voiceId, String content) {
        String audioFilePath = "";

        long time1 = System.currentTimeMillis();

        String projectAppKey_1 = dictTypeService.dictVal("language_config", "projectAppKey_1");
        String projectAppKey_2 = dictTypeService.dictVal("language_config", "projectAppKey_2");
        String projectAppKey_3 = dictTypeService.dictVal("language_config", "projectAppKey_3");
        String projectAppKey_4 = dictTypeService.dictVal("language_config", "projectAppKey_4");

        /**
         * 中文 Chinese
         * 英语 English
         * 日语 Japanese
         * 西班牙语 Spanish
         * 俄语 Russian (仅有女生音色)
         * 韩语 Korean (仅有女生音色)
         * 泰语 Thai (仅有女生音色)
         * 越南语 Vietnamese (仅有女生音色)
         */

        if ("Russian".equals(languageFlag)) {
            audioFilePath = genAudioFilePath(projectAppKey_2, content, "masha");
        } else if ("Korean".equals(languageFlag)) {
            audioFilePath = genAudioFilePath(projectAppKey_1, content, "Kyong");
        } else if ("Thai".equals(languageFlag)) {
            audioFilePath = genAudioFilePath(projectAppKey_3, content, "Waan");
        } else if ("Vietnamese".equals(languageFlag)) {
            audioFilePath = genAudioFilePath(projectAppKey_4, content, "Tien");
        } else if ("Spanish".equals(languageFlag)) {
            try {
                byte[] chatByte = this.ttsCoze(voiceId, HelpMe.truncateStr(content, 1020), 1.0F);

                String fileName = "chat.wav";
                double second = HelpMe.getWavDurationFromBytes(chatByte);
                if (second>0){
                    fileName = "chat_second_" + second + ".wav";
                }

                long time2 = System.currentTimeMillis();

                String mp3Name = "temp/mp3/guide/" + LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli() + "/" + fileName;
                audioFilePath = guideService.upOSSFiles(mp3Name, chatByte);

                long time3 = System.currentTimeMillis();

                log.info("换音频用时：{}ms 上传 oss 用时：{}ms 音频链接: {} 文本：{}", time2 - time1, time3 - time2, audioFilePath, content);

            } catch (Exception e) {
                log.error("文本换音频异常", e);
            }
        } else {
            try {
                String tempLanguage = TTSClient.中英混合;
                String speed = "1.0";
                if ("English".equals(languageFlag)) {
                    tempLanguage = TTSClient.英文;
                    speed = "0.9";
                } else if ("Japanese".equals(languageFlag)) {
                    tempLanguage = TTSClient.日文;
                    speed = "0.9";
                }

                byte[] chatByte = ttsClient.GPT_SoVITS(voiceId, content, speed, tempLanguage);

                String fileName = "chat.wav";
                double second = HelpMe.getWavDurationFromBytes(chatByte);
                if (second>0){
                    fileName = "chat_second_" + second + ".wav";
                }

                long time2 = System.currentTimeMillis();

                String mp3Name = "cache/mp3/guide/" + LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli() + "/" + fileName;
                audioFilePath = guideService.upOSSFiles(mp3Name, chatByte);

                long time3 = System.currentTimeMillis();

                log.info("换音频用时：{}ms 上传 oss 用时：{}ms 音频链接: {} 文本：{}", time2 - time1, time3 - time2, audioFilePath, content);

            } catch (Exception e) {
                log.error("文本换音频异常", e);
            }

        }

        return audioFilePath;
    }


    public String genAudioByCozeWithLength(String languageFlag, String voiceId, String content) {

        /**
         * 历史考证-男  这个音色 coze 大部分情况下返回失败，原因未知，当遇到这个音色值时，直接走本地的 TTS
         *   7468512265151528987
         */
        if ("7468512265151528987".equals(voiceId)){
            return genAudio(languageFlag, voiceId, content);
        }

        /**
         * 日文，coze 也不支持：
         *      文化内涵-男-音色 7426725529589645339
         *      文化内涵-女-音色 7426725529589661723
         * 直接走本地的 TTS
         */
        if ("Japanese".equals(languageFlag)){
            return genAudio(languageFlag, voiceId, content);
        }

        if (content.length() > 1000) {
            List<String> strList = HelpMe.splitStringByLength(content, 1000);

            log.info("文本太长：{}，进行切分处理，切分长度为1000，切分后块数：{}", content.length(), strList.size());

            String uuid = IdUtil.fastSimpleUUID();

            List<File> fileList = Lists.newArrayList();
            int i = 0;
            for (String str : strList) {
                i++;
                String audioFilePath = genAudioByCoze(languageFlag, voiceId, str);
                String fileName = uuid + "_" + i + ".wav";
                File file = new File(fileName);

                HttpUtil.downloadFile(audioFilePath, file);
                fileList.add(file);
            }

            File fullFile = null;
            try {
                fullFile = HelpMe.mergeWavFiles(fileList);
            } catch (IOException e) {
                log.error("coze 合并 wav 文件失败！{}", strList,e);
                return "";
            }

            byte[] chatByte = IoUtil.readBytes(FileUtil.getInputStream(fullFile), true);

            String fileName = "chat.wav";
            double second = HelpMe.getWavDurationFromBytes(chatByte);
            if (second>0){
                fileName = "chat_second_" + second + ".wav";
            }

            String mp3Name = "cache/mp3/guide/" + LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli() + "/" + fileName;
            String audioFilePath = guideService.upOSSFiles(mp3Name, chatByte);

            for (File file : fileList) {
                file.delete();
            }
            fullFile.delete();

            return audioFilePath;
        } else {

            String audioFilePath = genAudioByCoze(languageFlag, voiceId, content);
            return audioFilePath;
        }

    }

    public static void main(String[] args) {

        List<String> strList = Lists.newArrayList();
        strList.add("https://ai-guided.oss-cn-beijing.aliyuncs.com/temp/mp3/guide/1751934297590/chat_1.0.wav");
        strList.add("https://ai-guided.oss-cn-beijing.aliyuncs.com/temp/mp3/guide/1751934317068/chat_1.0.wav");
        strList.add("https://ai-guided.oss-cn-beijing.aliyuncs.com/temp/mp3/guide/1751934338098/chat_1.0.wav");
        strList.add("https://ai-guided.oss-cn-beijing.aliyuncs.com/temp/mp3/guide/1751934359181/chat_1.0.wav");

        List<File> fileList = Lists.newArrayList();
        int i = 0;
        for (String str : strList) {
            i++;
            String fileName = "/Users/<USER>/Desktop/temp/_" + i + ".wav";
            File file = new File(fileName);

            HttpUtil.downloadFile(str, file);
            fileList.add(file);
        }

        File fullFile = null;
        try {
            fullFile = HelpMe.mergeWavFiles(fileList);
        } catch (IOException e) {
            log.error("coze 合并 wav 文件失败！{}", strList,e);
        }

        System.out.println(fullFile.getAbsolutePath());

    }


    public String genAudioByCoze(String languageFlag, String voiceId, String content) {
        String audioFilePath = "";

        long time1 = System.currentTimeMillis();

        String projectAppKey_1 = dictTypeService.dictVal("language_config", "projectAppKey_1");
        String projectAppKey_2 = dictTypeService.dictVal("language_config", "projectAppKey_2");
        String projectAppKey_3 = dictTypeService.dictVal("language_config", "projectAppKey_3");
        String projectAppKey_4 = dictTypeService.dictVal("language_config", "projectAppKey_4");

        /**
         * 中文 Chinese
         * 英语 English
         * 日语 Japanese
         * 西班牙语 Spanish
         * 俄语 Russian (仅有女生音色)
         * 韩语 Korean (仅有女生音色)
         * 泰语 Thai (仅有女生音色)
         * 越南语 Vietnamese (仅有女生音色)
         */

        if ("Russian".equals(languageFlag)) {
            audioFilePath = genAudioFilePath(projectAppKey_2, content, "masha");
        } else if ("Korean".equals(languageFlag)) {
            audioFilePath = genAudioFilePath(projectAppKey_1, content, "Kyong");
        } else if ("Thai".equals(languageFlag)) {
            audioFilePath = genAudioFilePath(projectAppKey_3, content, "Waan");
        } else if ("Vietnamese".equals(languageFlag)) {
            audioFilePath = genAudioFilePath(projectAppKey_4, content, "Tien");
        } else {
            try {
                byte[] chatByte = this.ttsCoze(voiceId, content, 1.0F);

                String fileName = "chat.wav";
                double second = HelpMe.getWavDurationFromBytes(chatByte);
                if (second>0){
                    fileName = "chat_second_" + second + ".wav";
                }

                long time2 = System.currentTimeMillis();

                String mp3Name = "temp/mp3/guide/" + LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli() + "/" + fileName;
                audioFilePath = guideService.upOSSFiles(mp3Name, chatByte);

                long time3 = System.currentTimeMillis();

                log.info("换音频用时：{}ms 上传 oss 用时：{}ms 音频链接: {} 文本：{}", time2 - time1, time3 - time2, audioFilePath, content);

            } catch (Exception e) {
                log.error("文本换音频异常", e);
            }

        }

        return audioFilePath;

    }

    public String genAudio(String languageFlag, String voiceId, String content) {
        TTSClient ttsClient = new TTSClient();
        if (content.length() > 1600) {
            List<String> strList = HelpMe.splitStringByLength(content, 1600);

            log.info("文本太长：{}，进行切分处理，切分长度为1600，切分后块数：{}", content.length(), strList.size());

            String uuid = IdUtil.fastSimpleUUID();

            List<File> fileList = Lists.newArrayList();
            int i = 0;
            for (String str : strList) {
                i++;
                String audioFilePath = genAudio(ttsClient, languageFlag, voiceId, str);
                String fileName = uuid + "_" + i + ".wav";
                File file = new File(fileName);

                HttpUtil.downloadFile(audioFilePath, file);
                fileList.add(file);
            }

            File fullFile = null;
            try {
                fullFile = HelpMe.mergeWavFiles(fileList);
            } catch (IOException e) {
                log.error("GPT_SoVITS 合并 wav 文件失败 {}",strList,e);
                return "";
            }

            String fileName = "chat.wav";
            byte[] chatByte = IoUtil.readBytes(FileUtil.getInputStream(fullFile), true);
            double second = HelpMe.getWavDurationFromBytes(chatByte);
            if (second>0){
                fileName = "chat_second_" + second + ".wav";
            }

            String mp3Name = "cache/mp3/guide/" + LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli() + "/" + fileName;
            String audioFilePath = guideService.upOSSFiles(mp3Name, chatByte);

            for (File file : fileList) {
                file.delete();
            }
            fullFile.delete();

            return audioFilePath;
        } else {

            String audioFilePath = genAudio(ttsClient, languageFlag, voiceId, content);
            return audioFilePath;
        }

    }

}
