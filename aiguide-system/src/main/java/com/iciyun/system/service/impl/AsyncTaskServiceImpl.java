package com.iciyun.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.common.enums.UserTypeEnum;
import com.iciyun.system.domain.ChangeType;
import com.iciyun.system.domain.ScenicChannel;
import com.iciyun.system.domain.SysBaseSt;
import com.iciyun.system.domain.TokenDetail;
import com.iciyun.system.mapper.SysBaseStMapper;
import com.iciyun.system.service.AsyncTaskService;
import com.iciyun.system.service.ISysBaseStService;
import com.iciyun.system.service.ISysUserService;
import com.iciyun.system.service.ITokenDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

@Service
@Slf4j
public class AsyncTaskServiceImpl implements AsyncTaskService {

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ITokenDetailService tokenDetailService;

    @Autowired
    private SysBaseStMapper sysBaseStMapper;

    @Override
    @Async("threadPoolTaskExecutor")
    public CompletableFuture<Integer> getTodayNewUserCount() {
        try {
            Integer count = userService.getTodayNewUserCount();
            return CompletableFuture.completedFuture(count);
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            return CompletableFuture.failedFuture(e); // 返回异常结果
        }
    }

    @Override
    @Async("threadPoolTaskExecutor")
    public CompletableFuture<Long> getAllUserCount() {
        try {
            SysUser sysUser = new SysUser();
            sysUser.setDelFlag("0");
            sysUser.setStatus("0");
            List<SysUser> users = userService.selectAllList(sysUser);
            long count = users.stream().count();
            return CompletableFuture.completedFuture(count);
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            return CompletableFuture.failedFuture(e); // 返回异常结果
        }

    }

    @Override
    @Async("threadPoolTaskExecutor")
    public CompletableFuture<Long> getIssueBeanCount() {
        try {
            QueryWrapper<TokenDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(TokenDetail::getChangeType, ChangeType.IN);
            queryWrapper.select("SUM(amount_incurred) as total");
            Map<String, Object> countObj = tokenDetailService.getMap(queryWrapper);
            if(CollectionUtil.isNotEmpty(countObj)){
                long total = (Long) countObj.get("total");
                return CompletableFuture.completedFuture(total);
            } else {
                return CompletableFuture.completedFuture(0L);
            }
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            return CompletableFuture.failedFuture(e); // 返回异常结果
        }
    }

    @Override
    @Async("threadPoolTaskExecutor")
    public CompletableFuture<Long> getUserUseBeanCount() {
        try {
            QueryWrapper<TokenDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(TokenDetail::getChangeType, ChangeType.OUT);
            queryWrapper.select("SUM(amount_incurred) as total");
            Map<String, Object> countObj = tokenDetailService.getMap(queryWrapper);
            if(CollectionUtil.isNotEmpty(countObj)){
                long total = (Long) countObj.get("total");
                return CompletableFuture.completedFuture(total);
            } else {
                return CompletableFuture.completedFuture(0L);
            }
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            return CompletableFuture.failedFuture(e); // 返回异常结果
        }
    }

    @Override
    public void insertSysBaseSt(SysBaseSt sysBaseSt) {
        sysBaseStMapper.insert(sysBaseSt);
    }
}
