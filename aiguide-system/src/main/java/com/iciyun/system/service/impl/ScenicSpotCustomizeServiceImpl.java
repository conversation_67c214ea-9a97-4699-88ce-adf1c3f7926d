package com.iciyun.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.bo.ScenicSpotEditCmd;
import com.iciyun.system.domain.bo.ScenicSpotMiniAppQuery;
import com.iciyun.system.domain.qo.ScenicSpotListQuery;
import com.iciyun.system.domain.qo.ScenicSpotQuery;
import com.iciyun.system.mapper.ScenicSpotCustomizeMapper;
import com.iciyun.system.service.IScenicGuideService;
import com.iciyun.system.service.IScenicSpotCustomizeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02 14:44:00
 */
@Service
@RequiredArgsConstructor
public class ScenicSpotCustomizeServiceImpl extends ServiceImpl<ScenicSpotCustomizeMapper, ScenicSpot> implements IScenicSpotCustomizeService {

    private final ScenicSpotCustomizeMapper scenicSpotCustomizeMapper;
    private final IScenicGuideService scenicGuideService;

    @Override
    public List<ScenicSpot> queryScenicSpot4MiniApp(ScenicSpotMiniAppQuery query) {

        return scenicSpotCustomizeMapper.queryScenicSpot4MiniApp(query);
    }

    @Override
    @Transactional
    public void edit(ScenicSpotEditCmd cmd) {

        ScenicSpot scenicSpot = scenicSpotCustomizeMapper.selectById(cmd.getId());
        scenicSpot.edit(cmd);
        scenicSpotCustomizeMapper.updateById(scenicSpot);

        if(scenicSpot.getServiceActivity() == null ){
            scenicSpotCustomizeMapper.upServiceActivityNull(cmd.getId());
        }
        if(scenicSpot.getNeedToken() == null ){
            scenicSpotCustomizeMapper.upNeedTokenNull(cmd.getId());
        }
        scenicGuideService.upScenic(cmd.getId(), cmd.getName(), scenicSpot.getAddress());
    }

    @Override
    public ScenicSpot getSimpleOne(ScenicSpotQuery query) {
        return scenicSpotCustomizeMapper.getSimpleOne(query);
    }

    @Override
    public List<ScenicSpot> selectByUser(ScenicSpotListQuery qry) {
        return scenicSpotCustomizeMapper.selectByUser(qry);
    }
}
