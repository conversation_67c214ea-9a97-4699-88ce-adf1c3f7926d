package com.iciyun.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iciyun.common.enums.PartnerBusinessType;
import com.iciyun.common.enums.ScenicSpotStatusEnum;
import com.iciyun.common.utils.qr.QrUtil;
import com.iciyun.system.domain.ScenicHeadphone;
import com.iciyun.system.domain.ScenicLocation;
import com.iciyun.system.domain.ScenicRatio;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.mapper.ScenicLocationMapper;
import com.iciyun.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

/**
 * <p>
 * 点位信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07 15:16:28
 */
@Service
public class ScenicLocationServiceImpl extends ServiceImpl<ScenicLocationMapper, ScenicLocation> implements IScenicLocationService {

    @Autowired
    private ScenicLocationMapper scenicLocationMapper;

    @Autowired
    private IGuideService guideService;

    @Value("${qr.url}")
    private String qrUrl;

    @Value("${qr.logoUrl}")
    private String logoUrl;

    @Value("${qr.logoPath}")
    private String logoPath;

    @Value("${qr.qrPath}")
    private String qrPath;

    @Override
    public List<ScenicLocation> queryList(ScenicLocation qo) {
        return scenicLocationMapper.queryList(qo);
    }

    @Override
    public ScenicLocation queryByLocationName(ScenicLocation scenicLocation) {
        return scenicLocationMapper.queryByLocationName(scenicLocation);
    }

    @Override
    public String generateQR(ScenicLocation scenicLocation) {

        //二维码内容
        String content = qrUrl + "?scenicId=" + scenicLocation.getScenicId() +
                "&locationCode=" + scenicLocation.getLocationCode();
        try {
            //下载logo
            String localLogoPath = logoPath + "/" + logoUrl;
            File file = new File(localLogoPath);
            if (!file.exists()) {
                boolean result = file.createNewFile();
                guideService.getOSSFiles(logoUrl, localLogoPath);
            }
            String qrName = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli() + ".png";
            String qrImgPath = qrPath + "/" + qrName;
            File image = QrUtil.generateQRWithLogo(content, 600, localLogoPath, qrImgPath);
            String qrUrl = guideService.upOSSFiles(image, qrName);
            //删除本地文件
            if (image.exists()) {
                image.delete();
            }
            return qrUrl;
        } catch (Exception e) {
            log.error("生成二维码失败", e);
        }
        return null;
    }

    @Override
    public void saveLocation(ScenicLocation scenicLocation) {
        this.save(scenicLocation);
    }

    @Override
    public void removeLocation(ScenicLocation scenicLocation) {
        //删除点位
        this.removeById(scenicLocation.getId());
    }
}
