package com.iciyun.system.service;

import com.iciyun.system.domain.ScenicChannelCmd;
import com.iciyun.system.domain.ScenicOperate;
import com.baomidou.mybatisplus.extension.service.IService;
import com.iciyun.system.domain.ScenicOperateCmd;

import java.util.List;

/**
 * <p>
 * 景区运营 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12 11:16:57
 */
public interface IScenicOperateService extends IService<ScenicOperate> {

    List<ScenicOperate> getlist(ScenicOperateCmd cmd);

    void edit(ScenicOperate scenicOperate);

    void delete(Long id);

}
