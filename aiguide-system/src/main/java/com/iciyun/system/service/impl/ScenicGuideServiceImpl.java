package com.iciyun.system.service.impl;

import com.iciyun.common.core.domain.R;
import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.common.utils.SecurityUtils;
import com.iciyun.system.domain.BaseConfig;
import com.iciyun.system.domain.ChangeType;
import com.iciyun.system.domain.ScenicGuide;
import com.iciyun.system.domain.TransactionType;
import com.iciyun.system.domain.bo.AddTokenDetailCmd;
import com.iciyun.system.domain.qo.ScenicGuideQo;
import com.iciyun.system.mapper.ScenicGuideMapper;
import com.iciyun.system.service.IBaseConfigService;
import com.iciyun.system.service.IScenicGuideService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iciyun.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-10 14:11:00
 */
@Service
public class ScenicGuideServiceImpl extends ServiceImpl<ScenicGuideMapper, ScenicGuide> implements IScenicGuideService {

    @Autowired
    private ScenicGuideMapper scenicGuideMapper;
    @Autowired
    private IBaseConfigService baseConfigService;
    @Autowired
    private ISysUserService userService;

    @Override
    public void upScenic(int scenicId, String scenicName, String scenicAddress) {
        ScenicGuide scenicGuide = new ScenicGuide();
        scenicGuide.setScenicId(scenicId);
        scenicGuide.setScenicName(scenicName);
        scenicGuide.setScenicAddress(scenicAddress);
        scenicGuide.setUpdateTime(new Date());
        scenicGuideMapper.upScenic(scenicGuide);
    }

    @Override
    public List<ScenicGuide> queryList(ScenicGuideQo qo) {
        return scenicGuideMapper.queryList(qo);
    }

    @Override
    @Transactional
    public void audit(ScenicGuide scenicGuide) {
        scenicGuideMapper.audit(scenicGuide);
        if( scenicGuide.getStatus() != 1 )
            return;

        SysUser user = SecurityUtils.getLoginUser().getUser();
        BaseConfig baseConfig = baseConfigService.queryOne();

        AddTokenDetailCmd cmd = new AddTokenDetailCmd();
        cmd.setUserId(user.getUserId());
        cmd.setUserPhone(user.getPhonenumber());
        cmd.setAmountIncurred(new BigDecimal(baseConfig.getGuideReward()));
        cmd.setChangeType(ChangeType.IN);
        cmd.setTranscationType(TransactionType.GUIDEMAP);
        userService.updateTokenBalance(cmd);
    }

}
