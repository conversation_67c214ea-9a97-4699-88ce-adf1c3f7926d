package com.iciyun.system.service.impl;

import com.iciyun.system.domain.ScenicCommentary;
import com.iciyun.system.mapper.ScenicCommentaryMapper;
import com.iciyun.system.service.IScenicCommentaryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 景区label解说词 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-29 17:10:34
 */
@Service
public class ScenicCommentaryServiceImpl extends ServiceImpl<ScenicCommentaryMapper, ScenicCommentary> implements IScenicCommentaryService {

    @Autowired
    private ScenicCommentaryMapper scenicCommentaryMapper;

    @Override
    public List<ScenicCommentary> queryList(ScenicCommentary qo) {
        return scenicCommentaryMapper.queryList(qo);
    }

    @Override
    public String queryByLabelName(Integer scenicId, String labelName) {
        ScenicCommentary commentary = new ScenicCommentary();
        commentary.setScenicId(scenicId);
        commentary.setLabelName(labelName);
        List<ScenicCommentary> list = scenicCommentaryMapper.queryList(commentary);
        if( CollectionUtils.isNotEmpty(list) ){
            return list.get(0).getLabelCommentary();
        }
        return labelName;
    }

}
