package com.iciyun.system.service;

import com.iciyun.system.domain.ScenicCommentary;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 景区label解说词 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-29 17:10:34
 */
public interface IScenicCommentaryService extends IService<ScenicCommentary> {

    List<ScenicCommentary> queryList(ScenicCommentary qo);

    String queryByLabelName(Integer scenicId, String labelName);
}
