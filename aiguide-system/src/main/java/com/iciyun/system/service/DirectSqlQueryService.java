package com.iciyun.system.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.PageUtil;
import com.alibaba.druid.DbType;
import com.alibaba.druid.sql.PagerUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.iciyun.system.mapper.SqlMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 直接 sql 查询
 *
 * <AUTHOR> on 2022/3/12 11:25.
 */
@Service
public class DirectSqlQueryService {

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    SqlMapper sqlMapper;


    /**
     * 分页查询
     * @param sql   原始 sql , 如： select * from sys_user
     * @param pageNo 当前页，从 1 开始
     * @param pageSize 每页大小，如 10
     * @param tClass  返回实体
     * @param <X>
     * @return
     */
    public <X> PageInfo<X> pageQuery(String sql, Integer pageNo, Integer pageSize, Class<X> tClass) {
        if (pageNo<=0){
            pageNo=1;
        }
        PageUtil.setOneAsFirstPageNo();
        int start = PageUtil.getStart(pageNo, pageSize);

        String countSql = PagerUtils.count(sql, DbType.mysql);
        String limitSql = sql + " limit " + start + "," + pageSize;

        Integer total = selectCount(countSql);
        List<X> dataList = selectList(limitSql, tClass);

        Page<X> page = new Page();
        page.addAll(dataList);
        page.setTotal(total);
        page.setPageNum(pageNo);
        page.setPageSize(pageSize);
        page.setPages(PageUtil.totalPage(total, pageSize));

        PageInfo<X> pageInfo = page.toPageInfo();

        return pageInfo;
    }


    /**
     * 分页查询
     * @param sql   原始 sql , 如： select * from sys_user
     * @param pageNo 当前页，从 1 开始
     * @param pageSize 每页大小，如 10
     * @return
     */
    public PageInfo pageQuery(String sql, Integer pageNo, Integer pageSize) {
        if (pageNo<=0){
            pageNo=1;
        }
        PageUtil.setOneAsFirstPageNo();
        int start = PageUtil.getStart(pageNo, pageSize);

        String countSql = PagerUtils.count(sql, DbType.mysql);
        String limitSql = sql + " limit " + start + "," + pageSize;

        Integer total = selectCount(countSql);
        List dataList = selectList(limitSql);

        Page page = new Page();
        page.addAll(dataList);
        page.setTotal(total);
        page.setPageNum(pageNo);
        page.setPageSize(pageSize);
        page.setPages(PageUtil.totalPage(total, pageSize));

        PageInfo pageInfo = page.toPageInfo();

        return pageInfo;
    }


    /**
     * @param sql：原始sql语句
     * @return：受影响的行数
     */
    public Integer insert(String sql) {
        return sqlMapper.insert(sql);
    }

    /**
     * @param sql：原始sql语句
     * @return：受影响的行数
     */
    public Integer delete(String sql) {
        return sqlMapper.delete(sql);
    }

    /**
     * @param sql：原始sql语句
     * @return：受影响的行数
     */
    public Integer update(String sql) {
        return sqlMapper.update(sql);
    }

    /**
     * 查询一个 List 集合
     *
     * @param sql 示例：select * from user;
     * @return
     */
    public List<Map<String, Object>> selectList(String sql) {
        List<Map<String, Object>> list = sqlMapper.selectList(sql);
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        } else {
            List<Map<String, Object>> result = new ArrayList<>();
            for (Map<String, Object> item : list) {
                result.add(MapUtil.toCamelCaseMap(item));
            }
            return result;
        }
    }

    /**
     * 查询一个 Map
     *
     * @param sql 示例：select * from user limit 1;
     * @return
     */
    public Map<String, Object> selectMap(String sql) {
        Map<String, Object> map = sqlMapper.selectMap(sql);
        return map == null ? new HashMap<>() : MapUtil.toCamelCaseMap(map);
    }

    /**
     * 查询一个 String 字段
     *
     * @param sql 示例：select name from user limit 1;
     * @return
     */
    public String selectStr(String sql) {
        return sqlMapper.selectStr(sql);
    }

    /**
     * 查询一个 Integer 字段
     *
     * @param sql 示例：select age from user limit 1;
     * @return
     */
    public Integer selectInt(String sql) {
        return sqlMapper.selectInt(sql);
    }

    /**
     * 查询一个 Long 字段
     *
     * @param sql 示例：select id from user limit 1;
     * @return
     */
    public Long selectLong(String sql) {
        return sqlMapper.selectLong(sql);
    }


    /**
     * 查询一个 BigDecimal 字段
     *
     * @param sql 示例：select amount from user limit 1;
     * @return
     */
    public BigDecimal selectBigDecimal(String sql) {
        return sqlMapper.selectBigDecimal(sql);
    }


    /**
     * 查询一个 List 集合，并转换为指定对象返回
     *
     * @param sql    示例：select * from user;
     * @param tClass User
     * @param <X>
     * @return
     */
    public <X> List<X> selectList(String sql, Class<X> tClass) {
        List<Map<String, Object>> list = selectList(sql);
        List<X> result = new ArrayList<>();

        for (Map<String, Object> map : list) {
            X bean = BeanUtil.toBean(MapUtil.toCamelCaseMap(map), tClass);
            result.add(bean);
        }

        return result;
    }

    /**
     * 查询一个 Map，并转换为指定对象返回
     *
     * @param sql    示例：select * from user limit 1;
     * @param tClass User
     * @param <X>
     * @return
     */
    public <X> X selectOne(String sql, Class<X> tClass) {
        Map<String, Object> map = selectMap(sql);
        if (map.size()==0){
            return null;
        }
        X bean = BeanUtil.mapToBean(MapUtil.toCamelCaseMap(map), tClass, true, null);
        return bean;
    }


    /**
     * 查询字符串列表
     *
     * @param sql 示例： select name from user;
     * @return
     */
    public List<String> selectStrList(String sql) {
        List<String> result = new ArrayList<>();
        List<HashMap> list = sqlMapper.selectList(sql);
        for (HashMap item : list) {
            if (item == null) {
                continue;
            }
            Collection valList = item.values();
            if (CollUtil.isNotEmpty(valList)) {
                result.add(valList.iterator().next().toString());
            }
        }
        return result;
    }


    /**
     * 查询总数
     *
     * @param sql 示例：select count(*) from user;
     * @return
     */
    public Integer selectCount(String sql) {

        return sqlMapper.selectCount(sql);
    }


    public void exec(String sql) {
         sqlMapper.exec(sql);
    }

}
