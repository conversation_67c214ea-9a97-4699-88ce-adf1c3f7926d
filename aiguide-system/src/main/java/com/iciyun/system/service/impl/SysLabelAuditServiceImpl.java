package com.iciyun.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iciyun.system.domain.SysLabelAudit;
import com.iciyun.system.mapper.SysLabelAuditMapper;
import com.iciyun.system.service.ISysLabelAuditService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02 14:44:00
 */
@Service
@RequiredArgsConstructor
public class SysLabelAuditServiceImpl extends ServiceImpl<SysLabelAuditMapper, SysLabelAudit> implements ISysLabelAuditService {

    private final SysLabelAuditMapper sysLabelAuditMapper;


}
