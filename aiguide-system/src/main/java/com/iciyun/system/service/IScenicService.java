package com.iciyun.system.service;

import com.coze.openapi.client.chat.model.ChatEvent;
import com.iciyun.common.core.domain.entity.scenic.ScenicTextSpeechCmd;
import com.iciyun.common.core.domain.entity.wx.AiSpeechCmd;
import com.iciyun.common.core.domain.model.UserStyleDto;
import io.reactivex.Flowable;
import jakarta.servlet.http.HttpServletResponse;
import okhttp3.Response;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface IScenicService {

    void setUserstSyle(String userName);

    String scenicTextSpeech(HttpServletResponse response, ScenicTextSpeechCmd cmd);

    Map<String,Object> scenicSpeechs(HttpServletResponse response, ScenicTextSpeechCmd cmd);

    //删除景区
    void delScenic(String scenicId);

    //修改景区介绍文本
    void updateScenicIntroduction(String scenicId, String introduction);

    void upScenicImg(MultipartFile file, String scenicId);

    String getAiSpeech(HttpServletResponse response, AiSpeechCmd cmd);

    CloseableHttpResponse getAiStreamChat(AiSpeechCmd cmd);

    Response getAiStreamChat(String question);

    Response streamChatResponse(AiSpeechCmd cmd);

    Flowable<ChatEvent> streamChat(AiSpeechCmd cmd);

    String getSysChatUserStyle(Long userId);

    String getHobbyTypes(Long userId);

    List<String> getLabelCache(ScenicTextSpeechCmd cmd);
}
