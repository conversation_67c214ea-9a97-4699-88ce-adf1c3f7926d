package com.iciyun.system.service;

import cn.hutool.core.util.StrUtil;
import com.iciyun.common.constant.CacheConstants;
import com.iciyun.common.core.domain.model.UserStyleDto;
import com.iciyun.common.utils.sign.Md5Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> on 2025-05-20 10:46.
 */
@Slf4j
@Component
public class ScenicCacheManager {

    //默认 天
    @Value("${scenise.label.cache_time}")
    private Integer labelCacheTime;

    @Autowired
    private ISysUserService userService;

    public String getCacheKey(UserStyleDto userStyleDto,String scenicId, String scenicLabel, String voiceId){
        String userStyleHash = userStyleDto.hash();

        String tempScenicLabel = StrUtil.isNotEmpty(scenicLabel)?scenicLabel:"";
        String scenicLabelHash = Md5Utils.hash(tempScenicLabel);

        String str = CacheConstants.scenicPixKey +"{scenicId}_{scenicLabelHash}:{voiceId}:{userStyleHash}";
        Map<String,String> param = new HashMap<>();
        param.put("scenicId",scenicId);
        param.put("scenicLabelHash",scenicLabelHash);
        param.put("voiceId",voiceId);
        param.put("userStyleHash",userStyleHash);

        String cacheKey = StrUtil.format(str, param);

        return cacheKey;
    }


    public Integer getCacheTime(){
        return labelCacheTime;
    }

}
