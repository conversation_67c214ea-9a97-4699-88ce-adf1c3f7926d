package com.iciyun.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.aliyun.oss.*;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.common.comm.SignVersion;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.aliyun.oss.model.GetObjectRequest;
import com.coze.openapi.client.audio.speech.CreateSpeechReq;
import com.coze.openapi.client.audio.speech.CreateSpeechResp;
import com.coze.openapi.client.audio.transcriptions.CreateTranscriptionsReq;
import com.coze.openapi.client.audio.transcriptions.CreateTranscriptionsResp;
import com.coze.openapi.client.chat.CreateChatReq;
import com.coze.openapi.client.chat.model.ChatPoll;
import com.coze.openapi.client.connversations.message.model.Message;
import com.coze.openapi.client.connversations.message.model.MessageObjectString;
import com.coze.openapi.client.files.UploadFileReq;
import com.coze.openapi.client.files.model.FileInfo;
import com.coze.openapi.service.service.CozeAPI;
import com.google.common.collect.Maps;
import com.iciyun.common.constant.CacheConstants;
import com.iciyun.common.constant.Constants;
import com.iciyun.common.core.domain.entity.*;
import com.iciyun.common.core.domain.entity.scenic.ScenicTextSpeechCmd;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.common.utils.HelpMe;
import com.iciyun.common.utils.OssUtil;
import com.iciyun.common.utils.RectangleChecker;
import com.iciyun.common.utils.file.FileUploadUtils;
import com.iciyun.common.utils.http.HttpClientUtil;
import com.iciyun.system.domain.GlobalVariablesSingleton;
import com.iciyun.system.domain.SysChatUser;
import com.iciyun.system.domain.SysTouristLabel;
import com.iciyun.system.service.AsyncService;
import com.iciyun.system.service.IGuideService;
import com.iciyun.system.service.IGuideTkService;
import com.iciyun.system.service.ISysTouristLabelService;
import com.volcengine.tos.TOSV2;
import com.volcengine.tos.TOSV2ClientBuilder;
import com.volcengine.tos.TosClientException;
import com.volcengine.tos.TosServerException;
import com.volcengine.tos.model.object.GetObjectV2Input;
import com.volcengine.tos.model.object.GetObjectV2Output;
import com.volcengine.tos.model.object.PutObjectInput;
import com.volcengine.tos.model.object.PutObjectOutput;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Consts;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.content.ContentBody;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.env.Environment;
import org.springframework.http.MediaType;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.*;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GuideServiceImpl implements IGuideService, CommandLineRunner {

    @Autowired
    private IGuideTkService guideTkService;


    private static final String noStreamChatStatueFinsh = "completed";

    @Value(value = "${oss.accessKeyId}")
    private String accessKeyId;

    @Value(value = "${oss.accessKeySecret}")
    private String accessKeySecret;

    // 填写Bucket所在地域。以华东1（杭州）为例，Region填写为cn-hangzhou。
    @Value(value = "${oss.region}")
    private String region;

    // Endpoint以华东1（杭州）为例，其它Region请按实际情况填写。

    @Value(value = "${oss.endpoint}")
    private String endpoint;

    @Value(value = "${oss.endpoint_bf}")
    private String endpointBf;

    // 填写Bucket名称，例如examplebucket。

    @Value(value = "${oss.bucketName}")
    private String bucketName;

    @Autowired
    ISysTouristLabelService sysTouristLabelService;

    @Autowired
    private Environment evn;

    @Autowired
    private TOSV2 tos;


    @Override
    public String createSession(String botId, SysChatUser sysChatUser, String cozeToken) {

        String url = "https://api.coze.cn/v1/conversation/create";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        if (StringUtils.isNotBlank(cozeToken)) {
            headers.put("Authorization", "Bearer " + cozeToken);
        } else {
            headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());
        }
        JSONObject dataJson = new JSONObject();
        dataJson.put("userId", sysChatUser.getSysChatUserId());

        JSONObject mateJson = new JSONObject();
        mateJson.put("bot_id", botId);
        mateJson.put("meta_data", dataJson);

        String data = JSONObject.toJSONString(mateJson);

        String response = HttpClientUtil.postJson(url, null, headers, data, false);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer code = jsonObject.getInteger("code");
            if (code == 0) {
                JSONObject resDataJson = jsonObject.getJSONObject("data");
                //会话ID
                String conversationId = resDataJson.getString("id");
                String meta_data = resDataJson.getString("meta_data");
                return conversationId;
            }
        }
        return null;
    }

    @Override
    public void clearContext(String conversationId, String cozeToken) {
        String url = String.format("https://api.coze.cn/v1/conversations/%s/clear", conversationId);
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        if (StringUtils.isNotBlank(cozeToken)) {
            headers.put("Authorization", "Bearer " + cozeToken);
        } else {
            headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());
        }
        HttpClientUtil.postJson(url, null, headers, null, false);
    }

    @Override
    public Flux<String> streamChat(StreamChatCmd cmd) {

        String url = "https://api.coze.cn/v3/chat?conversation_id=" + cmd.getConversationId();

        String coziToken = GlobalVariablesSingleton.getInstance().getCozeToken();

        JSONObject dataJson = new JSONObject();
        dataJson.put("bot_id", cmd.getBotId());
        dataJson.put("user_id", cmd.getUserId());
        dataJson.put("stream", true);

        //根据景区及当前用户，动态修改下面的值
        JSONObject variables = new JSONObject();
        variables.put("name", "");//景区名称
        variables.put("xingqu", "");//用户兴趣标签
        variables.put("focus", "");//用户关注点
        dataJson.put("custom_variables", variables);


        WebClient webClient = WebClient.create();
        Flux<String> resp = webClient
                .post()
                .uri(url)
                .acceptCharset(Consts.UTF_8)
                .accept(MediaType.TEXT_EVENT_STREAM) // 用于服务器发送事件 (SSE)
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", "Bearer " + coziToken)
                .bodyValue(dataJson)
                .retrieve()
                .bodyToFlux(String.class)
                .onErrorResume(e -> {
                    // 错误处理：打印错误信息并返回错误提示
                    System.err.println("Error calling service2: " + e.getMessage());
                    return Mono.just("Error calling service2");
                });

        return resp;
    }

    @Override
    public String streamChatJson(StreamChatCmd cmd, String cozeToken) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + cozeToken);

        JSONObject dataJson = new JSONObject();
        dataJson.put("bot_id", cmd.getBotId());
        dataJson.put("user_id", cmd.getUserId());
        dataJson.put("stream", true);

        JSONArray messages = new JSONArray();
        JSONObject message = new JSONObject();
        message.put("content", cmd.getQuestion());
        message.put("content_type", "text");
        message.put("role", "user");
        message.put("type", "question");

        messages.add(message);

        dataJson.put("additional_messages", messages);

        //根据景区及当前用户，动态修改下面的值
        JSONObject variables = new JSONObject();
        variables.put("name", "");//景区名称
        variables.put("xingqu", "");//用户兴趣标签
        variables.put("focus", "");//用户关注点
        dataJson.put("custom_variables", variables);

        String urlPost = "https://api.coze.cn/v3/chat?conversation_id=" + cmd.getConversationId();

        /**
         WebClient webClient = WebClient.builder()
         .codecs(item -> item.defaultCodecs().maxInMemorySize(10 * 1024 * 1024))
         .build();
         Flux<String> resp = webClient
         .post()
         .uri(urlPost)
         .acceptCharset(Consts.UTF_8)
         .accept(MediaType.TEXT_EVENT_STREAM) // 用于服务器发送事件 (SSE)
         .contentType(MediaType.APPLICATION_JSON)
         .header("Authorization", "Bearer " + cozeToken)
         .bodyValue(dataJson)
         .retrieve()
         .bodyToFlux(String.class)
         .onErrorResume(e -> {
         // 错误处理：打印错误信息并返回错误提示
         System.err.println("Error calling service2: " + e.getMessage());
         return Mono.just("Error calling service2");
         });

         //json一次性返回
         String ret = resp.collectList().block().toString();
         log.info(resp.collectList().block().toString());*/


        //json 流式返回
        List<JSONObject> bu = HttpClientUtil.postEventStream(urlPost, dataJson.toJSONString(), cozeToken);

        List<JSONObject> contentJson = bu.stream()
                .filter(e -> "assistant".equals(e.getString("role"))
                        && "answer".equals(e.getString("type"))
                        && e.containsKey("created_at")
                ).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(contentJson)) {
            String content = contentJson.get(0).getString("content");
            log.info(content);
            return content;
        }

        return null;
    }

    @Override
    public String streamChatJsonByHttp(StreamChatCmd cmd, String cozeToken) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + cozeToken);

        JSONObject dataJson = new JSONObject();
        dataJson.put("bot_id", cmd.getBotId());
        dataJson.put("user_id", cmd.getUserId());
        dataJson.put("stream", true);

        JSONArray messages = new JSONArray();
        JSONObject message = new JSONObject();
        message.put("content", cmd.getQuestion());
        message.put("content_type", "text");
        message.put("role", "user");
        message.put("type", "question");

        messages.add(message);

        dataJson.put("additional_messages", messages);

        String urlPost = "https://api.coze.cn/v3/chat?conversation_id=" + cmd.getConversationId();
        List<JSONObject> bu = HttpClientUtil.postTextJsonRtBrowse(urlPost, null, headers, dataJson.toJSONString(), false);

        List<JSONObject> contentJson = bu.stream()
                .filter(e -> "assistant".equals(e.getString("role"))
                        && "answer".equals(e.getString("type"))
                        && e.containsKey("created_at")
                ).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(contentJson)) {
            String content = contentJson.get(0).getString("content");
            String newContent = content.replaceAll("[\\s\\t\\n\\r]", "");
            log.info(newContent);
            return newContent;
        }
        return null;
    }

    @Autowired
    private RedisCache redisCache;

    //cozeToken
    private static String cozeTokenKey = "chat:cozeToken";

    //14 分钟
    private static int cozeTokenTime = 14;

    @Override
    public List<String> streamChatByCoze(StreamChatCmd chatCmd, ScenicTextSpeechCmd cmd) {
        List<String> voicesPath = new ArrayList<>();
        String cozeToken = redisCache.getCacheObject(cozeTokenKey);
        if (StringUtils.isBlank(cozeToken)) {
            cozeToken = guideTkService.getToken();
            redisCache.setCacheObject(cozeTokenKey, cozeToken, cozeTokenTime, TimeUnit.MINUTES);
        }
        if (cozeToken != null) {
            String answer = streamChatJsonByHttp(chatCmd, cozeToken);
            if (StringUtils.isNotBlank(answer)) {
                voicesPath.add(textToVoice(answer, cmd, 0));
                /*String[] lineArr = answer.split("[。；？！]");
                if (lineArr.length > 0) {
                    for (int i = 0; i < lineArr.length; i++) {
                        voicesPath.add(textToVoice(lineArr[i], cmd, i));
                    }
                    voicesPath = threadUpOSS(voicesPath, lineArr, cmd, cozeToken);
                }*/
            }
            return voicesPath;
        }
        return new ArrayList<>();
    }

    @Autowired
    private AsyncService asyncService;

    public String textToVoice(String text, ScenicTextSpeechCmd cmd, int i) {
        String cozeToken = redisCache.getCacheObject(cozeTokenKey);
        if (StringUtils.isBlank(cozeToken)) {
            cozeToken = guideTkService.getToken();
            redisCache.setCacheObject(cozeTokenKey, cozeToken, cozeTokenTime, TimeUnit.MINUTES);
        }
        if (cozeToken != null) {
            //3 合成语音，上传oss
            TextSpeechCmd textSpeechCmd = TextSpeechCmd.builder()
                    .input(text)
                    .voiceId(cmd.getVoiceId())
                    .build();
            byte[] bytes = textSpeechCoze(textSpeechCmd);
            if (bytes != null) {
                //MP3 名称
                String mp3Name = cmd.getScenicId() + "/" + i + ".mp3";
                if (StringUtils.isNotBlank(cmd.getScenicLabel())) {
                    int label = cmd.getScenicLabel().hashCode();
                    mp3Name = cmd.getScenicId() + "/" + label + "/" + i + ".mp3";
                }

                String filePath = upOSSFiles(mp3Name, bytes);
                return filePath;
            }
        }
        return "";
    }

    @Override
    public String noStreamChat(UserChatCmd cmd, String cozeToken) {

        String url = "https://api.coze.cn/v3/chat";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        if (StringUtils.isNotBlank(cozeToken)) {
            headers.put("Authorization", "Bearer " + cozeToken);
        } else {
            headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());
        }
        headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());

        Map<String, Object> urlParams = new HashMap<>();
        urlParams.put("conversation_id", cmd.getConversationId());

        JSONObject dataJson = new JSONObject();
        dataJson.put("bot_id", cmd.getBotId());
        dataJson.put("user_id", cmd.getUserId());
        dataJson.put("stream", false);

        JSONArray messages = new JSONArray();
        JSONObject message = new JSONObject();
        message.put("content", cmd.getQuestion());
        message.put("content_type", "text");
        message.put("role", "user");
        message.put("type", "question");

        messages.add(message);

        dataJson.put("additional_messages", messages);

        String response = HttpClientUtil.postJson(url, urlParams, headers, dataJson.toJSONString(), false);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer code = jsonObject.getInteger("code");
            if (code == 0) {
                log.info("发送成功");
                return jsonObject.getString("data");
            }
        }
        return null;
    }

    @Override
    public boolean noStreamChatStatus(UserChatStatueCmd cmd) {
        String url = "https://api.coze.cn/v3/chat/retrieve";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());

        Map<String, Object> urlParams = new HashMap<>();
        urlParams.put("conversation_id", cmd.getConversationId());
        urlParams.put("chat_id", cmd.getChatId());

        String response = HttpClientUtil.get(url, urlParams, headers, false);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer code = jsonObject.getInteger("code");
            if (code == 0) {
                JSONObject resDataJson = jsonObject.getJSONObject("data");
                String status = resDataJson.getString("status");
                if (noStreamChatStatueFinsh.equals(status)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public String noStreamChatAnswer(UserChatStatueCmd cmd) {
        String url = "https://api.coze.cn/v3/chat/message/list";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());

        Map<String, Object> urlParams = new HashMap<>();
        urlParams.put("conversation_id", cmd.getConversationId());
        urlParams.put("chat_id", cmd.getChatId());

        String response = HttpClientUtil.get(url, urlParams, headers, false);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer code = jsonObject.getInteger("code");
            if (code == 0) {
                String resDataJson = jsonObject.getString("data");
                return resDataJson;
            }
        }
        return null;
    }

    @Override
    public String userToolOutputs(UserToolOutputdCmd cmd) {

        String url = "https://api.coze.cn/v3/chat/submit_tool_outputs";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());

        Map<String, Object> urlParams = new HashMap<>();
        urlParams.put("conversation_id", cmd.getConversationId());
        urlParams.put("chat_id", cmd.getChatId());

        JSONObject dataJson = new JSONObject();
        dataJson.put("stream", false);
        dataJson.put("tool_outputs", JSONObject.toJSONString(cmd.getToolOutputs()));

        String response = HttpClientUtil.postJson(url, urlParams, headers, dataJson.toJSONString(), false);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer code = jsonObject.getInteger("code");
            if (code == 0) {
                return jsonObject.getString("data");
            }
        }

        return null;
    }

    @Override
    public String userCancel(UserChatStatueCmd cmd) {

        String url = "https://api.coze.cn/v3/chat/cancel";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());

        JSONObject dataJson = new JSONObject();
        dataJson.put("conversation_id", cmd.getConversationId());
        dataJson.put("chat_id", cmd.getChatId());

        String response = HttpClientUtil.postJson(url, null, headers, dataJson.toJSONString(), false);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer code = jsonObject.getInteger("code");
            if (code == 0) {
                JSONObject resDataJson = jsonObject.getJSONObject("data");
                return resDataJson.getString("status");
            }
        }
        return null;
    }

    @Override
    public String userCreateMessage(UserCreateMessageCmd cmd) {

        String url = "https://api.coze.cn/v1/conversation/message/create";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());


        Map<String, Object> urlParams = new HashMap<>();
        urlParams.put("conversation_id", cmd.getConversationId());

        JSONObject dataJson = new JSONObject();
        dataJson.put("role", cmd.getRole());
        dataJson.put("content", cmd.getContent());
        dataJson.put("contentType", cmd.getContentType());

        String response = HttpClientUtil.postJson(url, urlParams, headers, dataJson.toJSONString(), false);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer code = jsonObject.getInteger("code");
            if (code == 0) {
                return jsonObject.getString("data");
            }
        }

        return null;
    }

    @Override
    public String userGetMessages(UserGetMessagesCmd cmd) {

        String url = "https://api.coze.cn/v1/conversation/message/list";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());

        Map<String, Object> urlParams = new HashMap<>();
        urlParams.put("conversation_id", cmd.getConversationId());

        JSONObject dataJson = new JSONObject();
        dataJson.put("order", cmd.getOrder());
        dataJson.put("chat_id", cmd.getChatId());
        dataJson.put("before_id", cmd.getBeforeId());
        dataJson.put("after_id", cmd.getAfterId());
        dataJson.put("limit", cmd.getLimit());

        String response = HttpClientUtil.postJson(url, urlParams, headers, dataJson.toJSONString(), false);
        if (StringUtils.isNotBlank(response)) {
            return response;
        }
        return null;
    }

    @Override
    public String userGetMsg(GetMessageCmd cmd) {

        String url = "https://api.coze.cn/v1/conversation/message/retrieve";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());

        Map<String, Object> urlParams = new HashMap<>();
        urlParams.put("conversation_id", cmd.getConversationId());
        urlParams.put("message_id", cmd.getMessageId());

        String response = HttpClientUtil.get(url, urlParams, headers, false);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer code = jsonObject.getInteger("code");
            if (code == 0) {
                return jsonObject.getString("data");
            }
        }
        return null;
    }

    @Override
    public String userModifyMsg(ModifyMessageCmd cmd) {

        String url = "https://api.coze.cn/v1/conversation/message/modify";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());

        Map<String, Object> urlParams = new HashMap<>();
        urlParams.put("conversation_id", cmd.getConversationId());
        urlParams.put("message_id", cmd.getMessageId());

        JSONObject dataJson = new JSONObject();
        dataJson.put("content", cmd.getContent());
        dataJson.put("content_type", cmd.getContentType());
        dataJson.put("meta_data", cmd.getMetaData());

        String response = HttpClientUtil.postJson(url, urlParams, headers, dataJson.toJSONString(), false);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer code = jsonObject.getInteger("code");
            if (code == 0) {
                return jsonObject.getString("message");
            }
        }
        return null;
    }

    @Override
    public String userDelMsg(GetMessageCmd cmd) {

        String url = "https://api.coze.cn/v1/conversation/message/delete";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());

        Map<String, Object> urlParams = new HashMap<>();
        urlParams.put("conversation_id", cmd.getConversationId());
        urlParams.put("message_id", cmd.getMessageId());

        String response = HttpClientUtil.postJson(url, urlParams, headers, null, false);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer code = jsonObject.getInteger("code");
            if (code == 0) {
                return jsonObject.getString("data");
            }
        }
        return null;
    }

    @Override
    public String userUploadFile(MultipartFile file) {

        String url = "https://api.coze.cn/v1/files/upload";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());

        Map<String, ContentBody> reqParam = new HashMap<>();
        reqParam.put("file", new FileBody(FileUploadUtils.transferToFile(file)));

        String response = HttpClientUtil.postFileMultiPart(url, reqParam, headers, false);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer code = jsonObject.getInteger("code");
            if (code == 0) {
                //TODO 图片存储
                return jsonObject.getString("data");
            }
        }
        return null;
    }

    @Override
    public String getFileById(String fileId) {
        String url = "https://api.coze.cn/v1/files/retrieve";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());

        Map<String, Object> urlParams = new HashMap<>();
        urlParams.put("file_id", fileId);

        String response = HttpClientUtil.get(url, urlParams, headers, false);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer code = jsonObject.getInteger("code");
            if (code == 0) {
                return jsonObject.getString("data");
            }
        }

        return null;
    }

    @Override
    public String getVoices(GetVoicesCmd cmd) {

        String url = "https://api.coze.cn/v1/audio/voices";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());

        Map<String, Object> urlParams = new HashMap<>();
        urlParams.put("filter_system_voice", cmd.getFilterSystemVoice());
        urlParams.put("page_num", cmd.getPageNum());
        urlParams.put("page_size", cmd.getPageSize());

        String response = HttpClientUtil.get(url, urlParams, headers, false);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer code = jsonObject.getInteger("code");
            if (code == 0) {
                JSONObject dataJson = jsonObject.getJSONObject("data");
                return dataJson.getString("voice_list");
            }
        }
        return null;
    }

    @Override
    public String cloneVoice(CloneVoiceCmd cmd) {

        String url = "https://api.coze.cn/v1/audio/voices/clone";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());

        Map<String, ContentBody> reqParam = new HashMap<>();

        //普通类型
        reqParam.put("audio_format", new StringBody(cmd.getAudioFormat(), ContentType.MULTIPART_FORM_DATA));
        reqParam.put("language", new StringBody(cmd.getLanguage(), ContentType.MULTIPART_FORM_DATA));
        reqParam.put("preview_text", new StringBody(cmd.getPreviewText(), ContentType.MULTIPART_FORM_DATA));
        reqParam.put("text", new StringBody(cmd.getText(), ContentType.MULTIPART_FORM_DATA));
        reqParam.put("voice_id", new StringBody(cmd.getVoiceId(), ContentType.MULTIPART_FORM_DATA));
        reqParam.put("voice_name", new StringBody(cmd.getVoiceName(), ContentType.MULTIPART_FORM_DATA));

        //文件
        reqParam.put("file", new FileBody(FileUploadUtils.transferToFile(cmd.getFile())));

        String response = HttpClientUtil.postFileMultiPart(url, reqParam, headers, false);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer code = jsonObject.getInteger("code");
            if (code == 0) {
                return jsonObject.getString("data");
            }
        }
        return null;
    }


    @Override
    public byte[] textSpeech(TextSpeechCmd cmd, String cozeToken) {

        String url = "https://api.coze.cn/v1/audio/speech";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        if (StringUtils.isNotBlank(cozeToken)) {
            headers.put("Authorization", "Bearer " + cozeToken);
        } else {
            headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());
        }
        JSONObject dataJson = new JSONObject();
        dataJson.put("input", cmd.getInput());
        dataJson.put("voice_id", cmd.getVoiceId());
        dataJson.put("response_format", cmd.getResponseFormat());
        dataJson.put("speed", cmd.getSpeed());
        dataJson.put("sample_rate", cmd.getSampleRate());
        log.info("开始 textSpeech 请求接口url:{},请求参数 {}", url, dataJson);
        return HttpClientUtil.postJsonRtIo(url, null, headers, dataJson.toJSONString(), false);
    }


    @Override
    @Retryable(value = {Exception.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2))
    public String justTestRetry(String name) {
        log.info("name:{}", name);

        int number = RandomUtil.randomInt(0, 6);
        if (number % 3 == 0) {
        } else {
            throw new RuntimeException("错误！");
        }

        return "hello " + name;
    }


    /**
     * Retryable，spring 重试机制
     * @param cmd
     * @return
     */
    @Override
    @Retryable(value = {Exception.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2))
    public byte[] textSpeechCoze(TextSpeechCmd cmd) {
        long time1 = System.currentTimeMillis();
        String cozeToken = redisCache.getCacheObject(CacheConstants.cozeTokenKey);
        if (org.apache.commons.lang3.StringUtils.isBlank(cozeToken)) {
            cozeToken = guideTkService.getToken();
            redisCache.setCacheObject(CacheConstants.cozeTokenKey, cozeToken, CacheConstants.cozeTokenTime, TimeUnit.MINUTES);
        }

        CozeAPI coze = guideTkService.getCozeAPI(cozeToken);

        String voiceID = cmd.getVoiceId();
        String content = cmd.getInput();
        CreateSpeechReq req = CreateSpeechReq.builder()
                .input(content)
                .voiceID(voiceID)
                .sampleRate(24000)
                .speed(cmd.getSpeed().floatValue())
                .readTimeout(20000)
                .connectTimeout(20000)
                .writeTimeout(20000)
                .build();
        CreateSpeechResp resp = coze.audio().speech().create(req);

        long time2 = System.currentTimeMillis();

        log.info("调用 coze speech 接口, 文本：{}，用时：{}  返回结果：{}",content, (time2-time1),resp);

        try {
            byte[] bytes = resp.getResponse().bytes();
            return bytes;
        } catch (Exception e) {
            log.error("调用 coze speech 接口失败！", e);
            throw new RuntimeException(e);
        }
    }



    @Override
    public void textSpeechBrowse(HttpServletResponse response, TextSpeechCmd cmd, String cozeToken) {
        String url = "https://api.coze.cn/v1/audio/speech";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        if (StringUtils.isNotBlank(cozeToken)) {
            headers.put("Authorization", "Bearer " + cozeToken);
        } else {
            headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());
        }
        JSONObject dataJson = new JSONObject();
        dataJson.put("input", cmd.getInput());
        dataJson.put("voice_id", cmd.getVoiceId());
        dataJson.put("response_format", cmd.getResponseFormat());
        dataJson.put("speed", cmd.getSpeed());
        dataJson.put("sample_rate", cmd.getSampleRate());
        HttpClientUtil.postJsonRtBrowse(response, url, null, headers, dataJson.toJSONString(), false);
    }

    @Override
    public String textToSpeechLocal(TextSpeechCmd cmd, String cozeToken, String path) {
        String url = "https://api.coze.cn/v1/audio/speech";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        if (StringUtils.isNotBlank(cozeToken)) {
            headers.put("Authorization", "Bearer " + cozeToken);
        } else {
            headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());
        }
        JSONObject dataJson = new JSONObject();
        dataJson.put("input", cmd.getInput());
        dataJson.put("voice_id", cmd.getVoiceId());
        dataJson.put("response_format", cmd.getResponseFormat());
        dataJson.put("speed", cmd.getSpeed());
        dataJson.put("sample_rate", cmd.getSampleRate());
        return HttpClientUtil.postJsonRtLocal(url, path, null, headers, dataJson.toJSONString(), false);
    }

    @Override
    public String transcriptions(MultipartFile file, String cozeToken) {

        String url = "https://api.coze.cn/v1/audio/transcriptions";
        Map<String, String> headers = new HashMap<>();
        if (StringUtils.isNotBlank(cozeToken)) {
            headers.put("Authorization", "Bearer " + cozeToken);
        } else {
            headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());
        }

        Map<String, ContentBody> reqParam = new HashMap<>();
        reqParam.put("file", new FileBody(FileUploadUtils.transferToFile(file)));

        String response = HttpClientUtil.postFileMultiPart(url, reqParam, headers, false);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer code = jsonObject.getInteger("code");
            if (code == 0) {
                return jsonObject.getJSONObject("data").getString("text");
            }
        }
        return null;
    }


    @Override
    public String transcriptions(MultipartFile file) {
        CozeAPI api = guideTkService.getCozeAPI();

        CreateTranscriptionsReq req = null;
        try {
            req = CreateTranscriptionsReq.of("abc.aac", file.getBytes());
        } catch (IOException e) {
            log.error("转换失败！", e);
        }
        CreateTranscriptionsResp resp = api.audio().transcription().create(req);

        return resp.getText();
    }


    @Override
    public String rooms(RoomsCmd cmd) {

        String url = "https://api.coze.cn/v1/audio/rooms";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + GlobalVariablesSingleton.getInstance().getCozeToken());

        JSONObject dataJson = new JSONObject();
        dataJson.put("bot_id", cmd.getBotId());
        dataJson.put("conversation_id", cmd.getConversationId());
        dataJson.put("voice_id", cmd.getVoiceId());
        JSONObject configJson = new JSONObject();

        JSONObject audioConfigJson = new JSONObject();
        audioConfigJson.put("codec", cmd.getConfig().getAudioConfig().getCodec());
        configJson.put("audio_config", audioConfigJson);

        JSONObject videoConfigJson = new JSONObject();
        videoConfigJson.put("codec", cmd.getConfig().getVideoConfig().getCodec());
        videoConfigJson.put("stream_video_type", cmd.getConfig().getVideoConfig().getStreamVideoType());
        configJson.put("video_config", videoConfigJson);


        dataJson.put("config", configJson);


        dataJson.put("uid", cmd.getUid());
        dataJson.put("workflow_id", cmd.getWorkflowId());

        String response = HttpClientUtil.postJson(url, null, headers, dataJson.toJSONString(), false);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSONObject.parseObject(response);
            Integer code = jsonObject.getInteger("code");
            if (code == 0) {
                return jsonObject.getString("data");
            }
        }
        return null;
    }

    @Override
    public String upOSSFiles(MultipartFile file) {
        String fileName = file.getOriginalFilename();

        try {
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, fileName, new ByteArrayInputStream(file.getBytes()));
            // 创建PutObject请求。
            PutObjectResult result = OssUtil.ossClient.putObject(putObjectRequest);
            log.info("上传oss：{}, 结果： {}", fileName, JSONObject.toJSONString(result));
            //拼装返回路径
            if (result != null) {
                return "https://" + bucketName + "." + endpoint + "/" + fileName;
            }
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    @Override
    public String upOSSFiles(String fileName, byte[] fileData) {
        String[] activeProfiles = evn.getActiveProfiles();
        if(activeProfiles != null && activeProfiles[0].equals(Constants.PRO)){
           return upHsOSSFiles(fileName, fileData);
        }

        try {
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, fileName, new ByteArrayInputStream(fileData));
            // 创建PutObject请求。
            PutObjectResult result = OssUtil.ossClient.putObject(putObjectRequest);
            log.info("上传oss：{}, 结果： {}", fileName, JSONObject.toJSONString(result));
            //拼装返回路径
            if (result != null) {
                return "https://" + bucketName + "." + endpoint + "/" + fileName;
            }
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        }
        return null;
    }

    @Override
    public String upHsOSSFiles(String fileName, byte[] fileData) {
        try{
            ByteArrayInputStream stream = new ByteArrayInputStream(fileData);
            PutObjectInput putObjectInput = new PutObjectInput().setBucket(bucketName).setKey(fileName).setContent(stream);
            PutObjectOutput output = tos.putObject(putObjectInput);
            log.info("上传oss：{}, 结果： {}", fileName, JSONObject.toJSONString(output));
            //拼装返回路径
            if (output != null) {
                return "https://" + bucketName + "." + endpointBf + "/" + fileName;
            }
        } catch (TosClientException e) {
            // 操作失败，捕获客户端异常，一般情况是请求参数错误，此时请求并未发送
            System.out.println("putObject failed");
            System.out.println("Message: " + e.getMessage());
            if (e.getCause() != null) {
                e.getCause().printStackTrace();
            }
        } catch (TosServerException e) {
            // 操作失败，捕获服务端异常，可以获取到从服务端返回的详细错误信息
            System.out.println("putObject failed");
            System.out.println("StatusCode: " + e.getStatusCode());
            System.out.println("Code: " + e.getCode());
            System.out.println("Message: " + e.getMessage());
            System.out.println("RequestID: " + e.getRequestID());
        } catch (Throwable t) {
            // 作为兜底捕获其他异常，一般不会执行到这里
            System.out.println("putObject failed");
            System.out.println("unexpected exception, message: " + t.getMessage());
        }
        return null;
    }

    @Override
    public String upHsOSSFiles(String fileName, File file) {
        try{
            InputStream inputStream = new FileInputStream(file);
            PutObjectInput putObjectInput = new PutObjectInput().setBucket(bucketName).setKey(fileName).setContent(inputStream);
            PutObjectOutput output = tos.putObject(putObjectInput);
            log.info("上传oss：{}, 结果： {}", fileName, JSONObject.toJSONString(output));
            //拼装返回路径
            if (output != null) {
                return "https://" + bucketName + "." + endpointBf + "/" + fileName;
            }
        } catch (TosClientException e) {
            // 操作失败，捕获客户端异常，一般情况是请求参数错误，此时请求并未发送
            System.out.println("putObject failed");
            System.out.println("Message: " + e.getMessage());
            if (e.getCause() != null) {
                e.getCause().printStackTrace();
            }
        } catch (TosServerException e) {
            // 操作失败，捕获服务端异常，可以获取到从服务端返回的详细错误信息
            System.out.println("putObject failed");
            System.out.println("StatusCode: " + e.getStatusCode());
            System.out.println("Code: " + e.getCode());
            System.out.println("Message: " + e.getMessage());
            System.out.println("RequestID: " + e.getRequestID());
        } catch (Throwable t) {
            // 作为兜底捕获其他异常，一般不会执行到这里
            System.out.println("putObject failed");
            System.out.println("unexpected exception, message: " + t.getMessage());
        }
        return null;
    }

    @Override
    public void getOSSFiles(String fileName, String localFilePath) {
        String[] activeProfiles = evn.getActiveProfiles();
        if(activeProfiles != null && activeProfiles[0].equals(Constants.PRO)){
            getHsOSSFiles(fileName, localFilePath);
        } else {
            try {
                OssUtil.ossClient.getObject(new GetObjectRequest(bucketName, fileName), new File(localFilePath));
            } catch (ClientException ce) {
                System.out.println("Caught an ClientException, which means the client encountered "
                        + "a serious internal problem while trying to communicate with OSS, "
                        + "such as not being able to access the network.");
                System.out.println("Error Message:" + ce.getMessage());
            }
        }
    }

    public void getHsOSSFiles(String fileName, String localPath){
        TOSV2 tos = new TOSV2ClientBuilder().build(region, endpoint, accessKeyId, accessKeySecret);
        GetObjectV2Input input = new GetObjectV2Input().setBucket(bucketName).setKey(fileName);
        // 以下代码展示如何将数据下载到本地文件
        try(FileOutputStream fileOutputStream = new FileOutputStream(localPath);
            GetObjectV2Output output = tos.getObject(input)) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = output.getContent().read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, length);
            }
        } catch (IOException e) {
            System.out.println("write data to file failed");
            e.printStackTrace();
        }
    }

    @Override
    public String upOSSFiles(File file, String fileName) {
        String[] activeProfiles = evn.getActiveProfiles();
        if(activeProfiles != null && activeProfiles[0].equals(Constants.PRO)){
            return upHsOSSFiles(fileName, file);
        }

        try {
            InputStream inputStream = new FileInputStream(file);
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, fileName, inputStream);
            // 创建PutObject请求。
            PutObjectResult result = OssUtil.ossClient.putObject(putObjectRequest);
            log.info("上传oss：{}, 结果： {}", fileName, JSONObject.toJSONString(result));
            //拼装返回路径
            if (result != null) {
                return "https://" + bucketName + "." + endpoint + "/" + fileName;
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (OSSException oe) {
            log.error("上传oss：{} 异常，msg: {}, Request ID: {}", fileName, oe.getErrorMessage(), oe.getRequestId());
        }
        return null;
    }


    @Value(value = "${coze.img2label.bot_id}")
    String botID;


    @Override
    public String img2label(Long touristId, String xy) {
        Long id = touristId == null ? 1L : touristId;
        List<SysTouristLabel> list = touristMap.get(id);

        if (CollUtil.isEmpty(list)) return "";

        String x = xy.split("x")[0];
        String y = xy.split("x")[1];

        for (SysTouristLabel item : list) {
            if (StrUtil.isEmpty(item.getX1y1()))continue;
            String str1[] = item.getX1y1().split(",");
            String x1 = str1[0];
            String y1 = str1[1];

            if (StrUtil.isEmpty(item.getX2y2()))continue;
            String str2[] = item.getX2y2().split(",");
            String x2 = str2[0];
            String y2 = str2[1];

            double[] topLeft = {Double.parseDouble(x1), Double.parseDouble(y1)};    // 矩形左上角坐标
            double[] bottomRight = {Double.parseDouble(x2), Double.parseDouble(y2)};    // 矩形右下角坐标
            double[] point = {Double.parseDouble(x), Double.parseDouble(y)};

            boolean flag = RectangleChecker.isPointInRectangle(topLeft, bottomRight, point);
            if (flag) {
                log.info("查询到的标签：{}，输入坐标：{}", item.getLabelName(), xy);
                return item.getLabelName();
            }
        }

        log.info("输入坐标：{}，未查询到标签！", xy);

        return "";
    }

    /**
     * 调用 图片识别 大模型，大约 3到5秒返回 label 文本
     *
     * @param imgUrl 可访问的公网图片地址
     * @param xy     中心点坐标，示例：400x300
     * @return label 文本
     */
    @Override
    public String img2label(String imgUrl, String xy) {

        int code = exeCommand(imgUrl, xy);

        if (code != 0) return "";

        //抠图生成到了服务器上的指定目录中
        String imagePath = "/home/<USER>/py-script/cropped_image.png";
        FileInfo imageInfo = guideTkService.getCozeAPI().files().upload(UploadFileReq.of(imagePath)).getFileInfo();

        CreateChatReq req =
                CreateChatReq.builder()
                        .botID(botID)
                        .userID(IdUtil.fastUUID())
                        .messages(
                                Collections.singletonList(
                                        Message.buildUserQuestionObjects(
                                                Arrays.asList(
//                                                        MessageObjectString.buildImageByURL(imgUrl)))))
                                                        MessageObjectString.buildImageByID(imageInfo.getID())))))

                        .build();
        ChatPoll chat = null;
        try {
            chat = guideTkService.getCozeAPI().chat().createAndPoll(req);
            if (CollUtil.isNotEmpty(chat.getMessages())) {
                String str = chat.getMessages().get(0).getContent();
                str = StrUtil.replace(str, "json", "");
                str = StrUtil.replace(str, "```", "");

                String label = JSONUtil.parseObj(str).getStr("label");

                log.info("查询到的 label:{}", label);

                return label;
            }
        } catch (Exception e) {
        }
        return "";
    }


    public int exeCommand(String url, String xy) {
        // 定义脚本文件路径和参数
        String scriptPath = "/home/<USER>/py-script/split_img.py";
        String urlParam = "--url " + url;
        String outputParam = "--output /home/<USER>/py-script";
        String xyParam = "--xy " + xy;
        String areaParam = "--area 200x200";
        String condaEnv = "img-deal";

        // 构建命令字符串
        String[] command = {
                "/bin/bash", "-c",
                "source ~/miniconda3/etc/profile.d/conda.sh && " + // 根据实际conda安装路径修改此行
                        "conda activate " + condaEnv + " && " +
                        "python " + scriptPath + " " +
                        urlParam + " " +
                        outputParam + " " +
                        xyParam + " " +
                        areaParam
        };

        log.info("截图命令:{}", ListUtil.toList(command));

        try {
            // 执行命令
            Process process = Runtime.getRuntime().exec(command);

            // 获取命令执行结果的输入流
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println(line);
            }

            // 获取错误流的输入（如果有）
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            while ((line = errorReader.readLine()) != null) {
                System.err.println(line);
                log.info("截图命令执行结果：{}", line);
            }

            // 等待进程结束并获取退出值
            int exitCode = process.waitFor();
            log.info("截图命令执行结果 exitCode：{}", exitCode);

            return exitCode;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return -999;
    }

    @Override
    @Retryable(value = {Exception.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2))
    public CloseableHttpResponse streamChat(StreamChatCmd cmd, String cozeToken) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + cozeToken);

        JSONObject dataJson = new JSONObject();
        dataJson.put("bot_id", "7496303824152477723");
        dataJson.put("user_id", cmd.getUserId());
        dataJson.put("stream", true);
        dataJson.put("auto_save_history", true);


        JSONArray messages = new JSONArray();
        JSONObject message = new JSONObject();
        message.put("content", cmd.getQuestion());
        message.put("content_type", "text");
        message.put("role", "user");
        message.put("type", "question");

        messages.add(message);

        dataJson.put("additional_messages", messages);

        if (MapUtil.isNotEmpty(cmd.getCustom_variables())){
            dataJson.put("custom_variables", cmd.getCustom_variables());
        }

        String url = "https://api.coze.cn/v3/chat?";
        if (StrUtil.isNotEmpty(cmd.getConversationId())) {
            url = "https://api.coze.cn/v3/chat?conversation_id=" + cmd.getConversationId();
        }

        return HttpClientUtil.post4Response(url, null, headers, dataJson.toJSONString(), false);

    }

    private Map<Long, List<SysTouristLabel>> touristMap = Maps.newHashMap();


    public void resetTouristMap() {
        touristMap.clear();
        List<SysTouristLabel> list = sysTouristLabelService.selectSysTouristLabelList(new SysTouristLabel());
        touristMap = HelpMe.groupList(list, SysTouristLabel::getTouristId);

        log.info("重置景区 label 信息...");
    }

    @Override
    public void run(String... args) throws Exception {
        resetTouristMap();
    }


}
