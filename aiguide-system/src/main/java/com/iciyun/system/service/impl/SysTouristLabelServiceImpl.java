package com.iciyun.system.service.impl;

import com.iciyun.common.utils.DateUtils;
import com.iciyun.system.domain.SysTouristLabel;
import com.iciyun.system.domain.bo.TouristAttractionsQry;
import com.iciyun.system.domain.qo.LabelsQuery;
import com.iciyun.system.mapper.SysTouristLabelMapper;
import com.iciyun.system.service.ISysTouristLabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 景区标签Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
@Service
public class SysTouristLabelServiceImpl implements ISysTouristLabelService 
{
    @Autowired
    private SysTouristLabelMapper sysTouristLabelMapper;

    /**
     * 查询景区标签
     * 
     * @param id 景区标签主键
     * @return 景区标签
     */
    @Override
    public SysTouristLabel selectSysTouristLabelById(Long id)
    {
        return sysTouristLabelMapper.selectSysTouristLabelById(id);
    }

    /**
     * 查询景区标签列表
     * 
     * @param sysTouristLabel 景区标签
     * @return 景区标签
     */
    @Override
    public List<SysTouristLabel> selectSysTouristLabelList(SysTouristLabel sysTouristLabel)
    {
        return sysTouristLabelMapper.selectSysTouristLabelList(sysTouristLabel);
    }

    /**
     * 新增景区标签
     * 
     * @param sysTouristLabel 景区标签
     * @return 结果
     */
    @Override
    public int insertSysTouristLabel(SysTouristLabel sysTouristLabel)
    {
        sysTouristLabel.setCreateTime(DateUtils.getNowDate());
        return sysTouristLabelMapper.insertSysTouristLabel(sysTouristLabel);
    }

    /**
     * 修改景区标签
     * 
     * @param sysTouristLabel 景区标签
     * @return 结果
     */
    @Override
    public int updateSysTouristLabel(SysTouristLabel sysTouristLabel)
    {
        return sysTouristLabelMapper.updateSysTouristLabel(sysTouristLabel);
    }

    /**
     * 批量删除景区标签
     * 
     * @param ids 需要删除的景区标签主键
     * @return 结果
     */
    @Override
    public int deleteSysTouristLabelByIds(Long[] ids)
    {
        return sysTouristLabelMapper.deleteSysTouristLabelByIds(ids);
    }

    /**
     * 删除景区标签信息
     * 
     * @param id 景区标签主键
     * @return 结果
     */
    @Override
    public int deleteSysTouristLabelById(Long id)
    {
        return sysTouristLabelMapper.deleteSysTouristLabelById(id);
    }

    @Override
    public List<SysTouristLabel> selectSyncTouris(SysTouristLabel condition) {
        return sysTouristLabelMapper.selectSyncTouris(condition);
    }
    /**
     * 查询随身讲景点
     * @param qry
     * @return
     */
    @Override
    public SysTouristLabel queryTouristAttractions(TouristAttractionsQry qry) {
        return sysTouristLabelMapper.queryTouristAttractions(qry);
    }

    @Override
    public SysTouristLabel selectOne(SysTouristLabel condition) {
        return sysTouristLabelMapper.selectOne(condition);
    }

    @Override
    public SysTouristLabel queryNearestNonRepeatLabel(TouristAttractionsQry qry) {
        return sysTouristLabelMapper.queryNearestNonRepeatLabel(qry);
    }

    @Override
    public List<SysTouristLabel> queryNearestRepeatLabel(TouristAttractionsQry qry) {
        return sysTouristLabelMapper.queryNearestRepeatLabel(qry);
    }

    @Override
    public SysTouristLabel selectByLabel(String labelName) {
        return sysTouristLabelMapper.selectByLabel(labelName);
    }

    @Override
    public List<SysTouristLabel> listQuery(LabelsQuery labelsQuery) {
        return sysTouristLabelMapper.listQuery(labelsQuery);
    }
}
