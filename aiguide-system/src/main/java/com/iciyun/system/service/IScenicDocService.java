package com.iciyun.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.iciyun.system.domain.ScenicDoc;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.bo.ScenicSpotMiniAppQuery;
import com.iciyun.system.domain.dto.ScenicDocPageParam;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02 14:44:00
 */
public interface IScenicDocService extends IService<ScenicDoc> {

    List<ScenicDoc> pageList(ScenicDocPageParam scenicDocPageParam);

}
