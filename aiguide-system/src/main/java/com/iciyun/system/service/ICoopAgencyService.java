package com.iciyun.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.iciyun.system.domain.BingAgencyCmd;
import com.iciyun.system.domain.CoopAgency;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>
 * 景区合作机构表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12 15:08:00
 */
public interface ICoopAgencyService extends IService<CoopAgency> {

    void edit(CoopAgency coopAgency);

    void delete(Integer id);

    BingAgencyCmd getBingAgency(Integer scenicId);

    void bingAgency(BingAgencyCmd bingAgencyCmd);

    List<CoopAgency> selectByUser(String userName);

}
