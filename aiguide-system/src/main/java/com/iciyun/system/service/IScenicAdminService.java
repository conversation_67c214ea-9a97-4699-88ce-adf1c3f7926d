package com.iciyun.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.iciyun.common.core.domain.R;
import com.iciyun.system.domain.ScenicAdmin;
import com.iciyun.system.domain.ScenicOtherVO;

import java.util.List;

/**
 * <p>
 * 管理员信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07 15:14:49
 */
public interface IScenicAdminService extends IService<ScenicAdmin> {

    List<ScenicAdmin> queryList(ScenicAdmin qo);

    ScenicAdmin queryByPhone(ScenicAdmin scenicAdmin);

    R edit(ScenicAdmin scenicAdmin);

    R del(ScenicAdmin scenicAdmin);

    ScenicAdmin queryByScenicId(String userPhone, Integer scenicId);

    ScenicOtherVO getScenicOhter(Integer scenicId);

    void saveScenicOhter(ScenicOtherVO cmd);
}
