package com.iciyun.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iciyun.common.core.domain.R;
import com.iciyun.common.enums.PartnerBusinessType;
import com.iciyun.common.enums.UserTypeEnum;
import com.iciyun.system.domain.ScenicAdmin;
import com.iciyun.system.domain.ScenicOtherVO;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.SysIbeacon;
import com.iciyun.system.mapper.ScenicAdminMapper;
import com.iciyun.system.service.IScenicAdminService;
import com.iciyun.system.service.IScenicSpotCustomizeService;
import com.iciyun.system.service.ISysIbeaconService;
import com.iciyun.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 管理员信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07 15:14:49
 */
@Service
public class ScenicAdminServiceImpl extends ServiceImpl<ScenicAdminMapper, ScenicAdmin> implements IScenicAdminService {

    @Autowired
    private ScenicAdminMapper scenicAdminMapper;
    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IScenicSpotCustomizeService scenicSpotCustomizeService;

    @Autowired
    private ISysIbeaconService sysIbeaconService;

    @Override
    public List<ScenicAdmin> queryList(ScenicAdmin qo) {
        return scenicAdminMapper.queryList(qo);
    }

    @Override
    public ScenicAdmin queryByPhone(ScenicAdmin scenicAdmin) {
        return scenicAdminMapper.queryByPhone(scenicAdmin);
    }

    @Override
    @Transactional
    public R edit(ScenicAdmin scenicAdmin) {
        ScenicAdmin queryAdmin = scenicAdminMapper.queryByPhone(scenicAdmin);
        if (scenicAdmin.getId() != null) {
            if (queryAdmin != null && queryAdmin.getId() != scenicAdmin.getId()) {
                return R.fail("手机号已存在。");
            }
            scenicAdminMapper.updateById(scenicAdmin);
        } else {
            if (queryAdmin != null) {
                return R.fail("手机号已存在。");
            }
            scenicAdminMapper.insert(scenicAdmin);
            String userType;
            if (scenicAdmin.getBusinessType() == PartnerBusinessType.COOPERATION.getCode()) {
                userType = UserTypeEnum.WXXCXJG.getCode();
            } else {
                return R.fail("合作方类型错误。");
            }
            sysUserService.updateUserTypeByPhone(userType, scenicAdmin.getUserPhone());
        }

        return R.ok(scenicAdmin.getId());
    }

    @Override
    @Transactional
    public R del(ScenicAdmin scenicAdmin) {
        ScenicAdmin queryAdmin = scenicAdminMapper.selectById(scenicAdmin.getId());
        scenicAdminMapper.deleteById(scenicAdmin.getId());
        sysUserService.updateUserTypeByPhone(UserTypeEnum.WXXCX.getCode(), queryAdmin.getUserPhone());
        return R.ok();
    }

    @Override
    public ScenicAdmin queryByScenicId(String userPhone, Integer scenicId) {
        return scenicAdminMapper.queryByScenicId(userPhone, scenicId);
    }

    @Override
    public ScenicOtherVO getScenicOhter(Integer scenicId) {
        ScenicOtherVO vo = new ScenicOtherVO();
        //根据景区id 查询
        ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery()
                .eq(ScenicSpot::getId, scenicId)
                .last("limit 1")
                .one();
        if (scenicSpot != null) {
            vo.setNeedToken(scenicSpot.getNeedToken());
            vo.setGridSize(scenicSpot.getGridSize());
            vo.setOpenEyeStatus(scenicSpot.isOpenEyeStatus());
            vo.setIdentifyType(scenicSpot.getIdentifyType());
            vo.setScenicId(scenicId);
        }
        //查询采集员信息
        List<ScenicAdmin> adminsCj = this.lambdaQuery()
                .eq(ScenicAdmin::getBusinessType, PartnerBusinessType.SCENIC_INFO.getCode())
                .eq(ScenicAdmin::getBusinessCode, scenicSpot.getScenicSpotId())
                .list();
        vo.setAdminsCj(adminsCj);

        //查询信标信息
        List<SysIbeacon> ibeacons = sysIbeaconService.lambdaQuery()
                .eq(SysIbeacon::getScenicId, scenicId)
                .eq(SysIbeacon::getStatue, 0)
                .list();
        vo.setIbeacons(ibeacons);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveScenicOhter(ScenicOtherVO cmd) {
        //根据景区id 查询
        ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery()
                .eq(ScenicSpot::getId, cmd.getScenicId())
                .last("limit 1")
                .one();
        if (scenicSpot != null) {
            scenicSpotCustomizeService.lambdaUpdate()
                    .set(ScenicSpot::getNeedToken, cmd.getNeedToken())
                    .set(ScenicSpot::getGridSize, cmd.getGridSize())
                    .set(ScenicSpot::isOpenEyeStatus, cmd.isOpenEyeStatus())
                    .set(ScenicSpot::getIdentifyType, cmd.getIdentifyType())
                    .eq(ScenicSpot::getId, cmd.getScenicId())
                    .update();
            //添加采集员信息
            List<ScenicAdmin> adminsCj = cmd.getAdminsCj();
            if (CollectionUtil.isNotEmpty(adminsCj)) {
                List<ScenicAdmin> dbList = this.lambdaQuery()
                        .eq(ScenicAdmin::getBusinessType, PartnerBusinessType.SCENIC_INFO.getCode())
                        .eq(ScenicAdmin::getBusinessCode, scenicSpot.getScenicSpotId())
                        .list();
                //需要修改的记录
                List<ScenicAdmin> updateList = adminsCj.stream()
                        .filter(cj -> dbList.stream().anyMatch(db -> cj.getId() != null &&
                                cj.getId().equals(db.getId()))).toList();
                //修改
                updateList.forEach(update -> {
                    this.lambdaUpdate()
                            .set(ScenicAdmin::getUserPhone, update.getUserPhone())
                            .set(ScenicAdmin::getUserName, update.getUserName())
                            .eq(ScenicAdmin::getId, update.getId())
                            .update();
                });

                //需要删除的记录
                List<ScenicAdmin> delList = dbList.stream()
                        .filter(db -> adminsCj.stream().noneMatch(cj -> cj.getId() != null &&
                                cj.getId().equals(db.getId()))).toList();
                //删除
                this.removeByIds(delList.stream().map(ScenicAdmin::getId).toList());

                //添加的记录
                List<ScenicAdmin> addList = adminsCj.stream()
                        .filter(cj -> cj.getId() == null).toList();

                //添加
                addList.forEach(admin -> {
                    admin.setBusinessType(PartnerBusinessType.SCENIC_INFO.getCode());
                    admin.setBusinessCode(scenicSpot.getScenicSpotId());
                    admin.setCreateTime(LocalDateTime.now());
                });
                this.saveBatch(addList);
            } else {
                this.remove(new QueryWrapper<ScenicAdmin>()
                        .lambda()
                        .eq(ScenicAdmin::getBusinessType, PartnerBusinessType.SCENIC_INFO.getCode())
                        .eq(ScenicAdmin::getBusinessCode, scenicSpot.getScenicSpotId()));
            }

            //添加信标信息
            List<SysIbeacon> ibeacons = cmd.getIbeacons();
            if (CollectionUtil.isNotEmpty(ibeacons)) {
                List<SysIbeacon> dbList = sysIbeaconService.lambdaQuery()
                        .eq(SysIbeacon::getScenicId, cmd.getScenicId())
                        .eq(SysIbeacon::getStatue, 0)
                        .list();
                //需要修改的记录
                List<SysIbeacon> updateList = ibeacons.stream()
                        .filter(cj -> dbList.stream().anyMatch(db -> cj.getId() != null &&
                                cj.getId().equals(db.getId()))).toList();
                //修改
                updateList.forEach(update -> {
                    sysIbeaconService.lambdaUpdate()
                            .set(SysIbeacon::getUserId, update.getUserId())
                            .set(SysIbeacon::getLabelName, update.getLabelName())
                            .set(SysIbeacon::getIbeaconUuid, update.getIbeaconUuid())
                            .eq(SysIbeacon::getId, update.getId())
                            .update();
                });

                //需要删除的记录
                List<SysIbeacon> delList = dbList.stream()
                        .filter(db -> ibeacons.stream().noneMatch(cj -> cj.getId() != null &&
                                cj.getId().equals(db.getId()))).toList();
                //删除
                List<Long> delIdList = delList.stream().map(item -> {
                    return item.getId();
                }).collect(Collectors.toList());
                sysIbeaconService.removeByIds(delIdList);

                //添加的记录
                List<SysIbeacon> addList = ibeacons.stream()
                        .filter(cj -> cj.getId() == null).toList();
                //添加
                addList.forEach(admin -> {
                    admin.setIbeaconUuid(admin.getIbeaconUuid().toUpperCase());
                    admin.setUserId(String.valueOf(cmd.getUserId()));
                    admin.setStatue(0);
                    admin.setCreateTime(LocalDateTime.now());
                    admin.setScenicName(scenicSpot.getName());
                });
                sysIbeaconService.saveBatch(addList);
            } else {
                sysIbeaconService.remove(new QueryWrapper<SysIbeacon>()
                        .lambda()
                        .eq(SysIbeacon::getScenicId, scenicSpot.getId()));
            }
        }
    }
}
