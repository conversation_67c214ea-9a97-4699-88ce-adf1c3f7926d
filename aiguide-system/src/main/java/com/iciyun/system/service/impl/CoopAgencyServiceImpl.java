package com.iciyun.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iciyun.common.constant.HttpStatus;
import com.iciyun.common.core.domain.entity.wx.WxGetCodeCmd;
import com.iciyun.common.enums.PartnerBusinessType;
import com.iciyun.common.exception.GlobalException;
import com.iciyun.common.utils.uuid.IdHutool;
import com.iciyun.system.domain.*;
import com.iciyun.system.mapper.CoopAgencyMapper;
import com.iciyun.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 景区合作机构表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12 15:08:00
 */
@Service
public class CoopAgencyServiceImpl extends ServiceImpl<CoopAgencyMapper, CoopAgency> implements ICoopAgencyService {

    @Autowired
    private IdHutool idHutool;

    @Autowired
    private IScenicAdminService scenicAdminService;

    @Autowired
    private IScenicSpotCustomizeService scenicSpotCustomizeService;

    @Autowired
    private IScenicHeadphoneService scenicHeadphoneService;

    @Autowired
    private IScenicRatioService scenicRatioService;

    @Autowired
    private IScenicLocationService scenicLocationService;

    @Autowired
    private IWxUserService iWxUserService;

    @Autowired
    private CoopAgencyMapper coopAgencyMapper;

    @Autowired
    private IAgencyAdminScenicService agencyAdminScenicService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(CoopAgency coopAgency) {
        if (coopAgency.getId() == null) {
            String content = idHutool.genCode("JG");
            coopAgency.setAgencyId(content);
            coopAgency.setCreateTime(LocalDateTime.now());
        }
        List<CoopAgency> coopAgencies = this.lambdaQuery()
                .eq(CoopAgency::getAgencyName, coopAgency.getAgencyName())
                .list();
        if (CollectionUtil.isNotEmpty(coopAgencies) && coopAgencies.size() > 1) {
            throw new GlobalException(HttpStatus.WX_CODE_ERROR, "机构名称已存在");
        }
        try {
            this.saveOrUpdate(coopAgency);
        } catch (Exception e){
            throw new GlobalException(HttpStatus.WX_CODE_ERROR, "机构名称已存在");
        }
        if (CollectionUtil.isNotEmpty(coopAgency.getScenicAdminList())) {
            QueryWrapper<ScenicAdmin> wrapper = new QueryWrapper<>();
            wrapper.lambda()
                    .eq(ScenicAdmin::getBusinessType, PartnerBusinessType.COOPERATION.getCode())
                    .eq(ScenicAdmin::getBusinessCode, coopAgency.getAgencyId());
            scenicAdminService.remove(wrapper);

            QueryWrapper<AgencyAdminScenic> wrapperS = new QueryWrapper<>();
            wrapperS.lambda().eq(AgencyAdminScenic::getAgencyId, coopAgency.getAgencyId());
            agencyAdminScenicService.remove(wrapperS);
            //保存管理员信息
            List<ScenicAdmin> adminList = coopAgency.getScenicAdminList();
            List<AgencyAdminScenic> agencyAdminScenics = new ArrayList<>();
            adminList.forEach(admin -> {
                admin.setBusinessCode(coopAgency.getAgencyId());
                admin.setBusinessType(PartnerBusinessType.COOPERATION.getCode());
                //关联景区
                List<AgencyAdminScenic> aas = admin.getAgencyAdminScenicList();
                if(CollectionUtil.isNotEmpty(aas)){
                    aas.forEach(a -> {
                        a.setId(null);
                        a.setUserPhone(admin.getUserPhone());
                        a.setAgencyId(coopAgency.getAgencyId());
                        a.setCreateTime(LocalDateTime.now());
                        agencyAdminScenics.add(a);
                    });
                }
            });
            scenicAdminService.saveBatch(adminList);
            agencyAdminScenicService.saveBatch(agencyAdminScenics);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer id) {
        CoopAgency coopInstitution = this.getById(id);
        this.removeById(id);
        scenicAdminService.remove(new QueryWrapper<ScenicAdmin>().lambda().eq(ScenicAdmin::getBusinessCode, coopInstitution.getAgencyId()));

        QueryWrapper<AgencyAdminScenic> wrapperS = new QueryWrapper<>();
        wrapperS.lambda().eq(AgencyAdminScenic::getAgencyId, coopInstitution.getAgencyId());
        agencyAdminScenicService.remove(wrapperS);
    }

    @Override
    public BingAgencyCmd getBingAgency(Integer scenicId) {
        BingAgencyCmd ret = new BingAgencyCmd();
        //根据景区id 查询
        ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery()
                .eq(ScenicSpot::getId, scenicId)
                .last("limit 1")
                .one();
        if (scenicSpot != null) {
            ret.setScenicId(scenicSpot.getId());
            ret.setServiceCharge(scenicSpot.getServiceCharge());
            ret.setServiceActivity(scenicSpot.getServiceActivity());
            ret.setActivityBeginTime(scenicSpot.getActivityBeginTime());
            ret.setActivityEndTime(scenicSpot.getActivityEndTime());
        }
        //查询耳机
        List<ScenicHeadphone> scenicHeadphones = scenicHeadphoneService.lambdaQuery()
                .eq(ScenicHeadphone::getScenicId, scenicId)
                .list();
        ret.setScenicHeadphones(scenicHeadphones);
        //查询机构合作信息
        List<ScenicRatio> scenicRatios = scenicRatioService.lambdaQuery()
                .eq(ScenicRatio::getScenicId, scenicId)
                .eq(ScenicRatio::getStatue, 0)
                .list();
        ret.setScenicRatios(scenicRatios);
        //查询物料点位信息
        List<ScenicLocation> scenicLocations = scenicLocationService.lambdaQuery()
                .eq(ScenicLocation::getScenicId, scenicId)
                .eq(ScenicLocation::getStatue, 0)
                .list();
        boolean flag = true;
        if (CollectionUtil.isNotEmpty(scenicLocations)) {
            List<ScenicLocation> defaultLocation = scenicLocations
                    .stream().filter(sl -> sl.getLocationType() == 1).collect(Collectors.toList());
            if(CollectionUtil.isEmpty(defaultLocation)){
                flag = false;
            }
        } else {
            flag = false;
        }
        if(!flag){
            //添加默认点位
            ScenicLocation scenicLocation = new ScenicLocation();
            scenicLocation.setScenicId(scenicId);
            scenicLocation.setScenicName(scenicSpot.getName());
            scenicLocation.setLocationCode(idHutool.genCode("DW"));
            scenicLocation.setLocationName("设备");
            scenicLocation.setLocationType(1);
            scenicLocation.setStatue(0);
            String content = idHutool.genCode("QR");
            WxGetCodeCmd wxGetCodeCmd = new WxGetCodeCmd();
            wxGetCodeCmd.setScene(content);
            wxGetCodeCmd.setPage("pages/payment/payment");
            String qrUrl = iWxUserService.getUnlimitedQRCode(wxGetCodeCmd);
            scenicLocation.setLocationUrl(qrUrl);
            scenicLocation.setLocationUrlCode(content);
            scenicLocation.setCreateTime(LocalDateTime.now());
            scenicLocation.setLongitude(scenicSpot.getLatitude());
            scenicLocation.setLatitude(scenicSpot.getLongitude());
            scenicLocationService.saveLocation(scenicLocation);

            scenicLocations = scenicLocationService.lambdaQuery()
                    .eq(ScenicLocation::getScenicId, scenicId)
                    .eq(ScenicLocation::getStatue, 0)
                    .list();
        }

        ret.setScenicLocations(scenicLocations);
        return ret;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bingAgency(BingAgencyCmd bingAgencyCmd) {
        //根据景区id 查询
        ScenicSpot scenicSpot = scenicSpotCustomizeService.lambdaQuery()
                .eq(ScenicSpot::getId, bingAgencyCmd.getScenicId())
                .last("limit 1")
                .one();
        if (scenicSpot != null) {
            //更新景区信息
            scenicSpotCustomizeService.lambdaUpdate()
                    .eq(ScenicSpot::getId, bingAgencyCmd.getScenicId())
                    .set(ScenicSpot::getServiceCharge, bingAgencyCmd.getServiceCharge())
                    .set(ScenicSpot::getServiceActivity, bingAgencyCmd.getServiceActivity())
                    .set(ScenicSpot::getActivityBeginTime, bingAgencyCmd.getActivityBeginTime())
                    .set(ScenicSpot::getActivityEndTime, bingAgencyCmd.getActivityEndTime())
                    .update();
            //添加耳机
            List<ScenicHeadphone> scenicHeadphones = bingAgencyCmd.getScenicHeadphones();
            if (CollectionUtil.isNotEmpty(scenicHeadphones)) {
                //db
                List<ScenicHeadphone> dbList = scenicHeadphoneService.lambdaQuery()
                       .eq(ScenicHeadphone::getScenicId, bingAgencyCmd.getScenicId())
                       .list();
                //获取页面删除的记录
                List<ScenicHeadphone> removeList = dbList.stream()
                       .filter(db ->!scenicHeadphones.stream()
                               .anyMatch(scenicHeadphone -> scenicHeadphone.getId()!= null
                                        && scenicHeadphone.getId().equals(db.getId()))).toList();
                scenicHeadphoneService.removeByIds(removeList.stream().map(ScenicHeadphone::getId).toList());

                //页面修改记录
                List<ScenicHeadphone> updateList = scenicHeadphones.stream()
                      .filter(scenicHeadphone -> dbList.stream()
                              .anyMatch(db -> scenicHeadphone.getId()!= null
                                        && db.getId().equals(scenicHeadphone.getId()))).toList();
                //修改
                updateList.forEach(headphone -> {
                    scenicHeadphoneService.saveOrUpdate(headphone);
                });

                //添加记录
                List<ScenicHeadphone> addList = scenicHeadphones.stream()
                       .filter(scenicHeadphone -> scenicHeadphone.getId() == null)
                       .toList();

                //添加
                addList.forEach(headphone -> {
                    headphone.setScenicId(bingAgencyCmd.getScenicId());
                    headphone.setScenicName(scenicSpot.getName());
                });
                scenicHeadphoneService.saveBatch(addList);
            } else {
                //删除 原有信息
                scenicHeadphoneService.remove(new QueryWrapper<ScenicHeadphone>()
                        .lambda()
                        .eq(ScenicHeadphone::getScenicId, bingAgencyCmd.getScenicId()));
            }
            //添加分佣比例
            List<ScenicRatio> scenicRatios = bingAgencyCmd.getScenicRatios();
            if (CollectionUtil.isNotEmpty(scenicRatios)) {
                //
                List<ScenicRatio> dbList = scenicRatioService.lambdaQuery()
                        .eq(ScenicRatio::getStatue, 0)
                        .eq(ScenicRatio::getScenicId, bingAgencyCmd.getScenicId())
                        .list();

                //删除的记录
                List<ScenicRatio> removeList = dbList.stream()
                        .filter(db -> !scenicRatios.stream()
                                .anyMatch(scenicRatio -> scenicRatio.getId() != null
                                        && scenicRatio.getId().equals(db.getId()))).toList();

                //删除
                removeList.forEach(r -> {
                    scenicRatioService.lambdaUpdate()
                            .set(ScenicRatio::getStatue, 1)
                            .eq(ScenicRatio::getId, r.getId())
                            .update();
                });
                //更新的记录
                List<ScenicRatio> updateList = scenicRatios.stream()
                        .filter(scenicRatio -> dbList.stream()
                                .anyMatch(ratio -> scenicRatio.getId() != null
                                        && ratio.getId().equals(scenicRatio.getId()))).toList();
                updateList.forEach(r -> {
                    scenicRatioService.lambdaUpdate()
                            .set(ScenicRatio::getAgencyName, r.getAgencyName())
                            .set(ScenicRatio::getAgencyRatio, r.getAgencyRatio())
                            .set(ScenicRatio::getRemark, r.getRemark())
                            .eq(ScenicRatio::getId, r.getId())
                            .update();
                });

                //添加的记录
                List<ScenicRatio> addList = scenicRatios.stream()
                        .filter(scenicRatio -> scenicRatio.getId() == null)
                        .toList();
                //添加
                addList.forEach(ratio -> {
                    ratio.setId(null);
                    ratio.setScenicId(bingAgencyCmd.getScenicId());
                    ratio.setScenicName(scenicSpot.getName());
                    ratio.setStatue(0);
                    ratio.setProvinceName(scenicSpot.getProvinceName());
                    ratio.setProvinceCode(scenicSpot.getProvinceCode());
                    ratio.setCityName(scenicSpot.getCityName());
                    ratio.setCityCode(scenicSpot.getCityCode());
                    ratio.setDistrictCode(scenicSpot.getDistrictCode());
                    ratio.setDistrictName(scenicSpot.getDistrictName());
                });
                scenicRatioService.saveBatch(addList);
            } else {
                scenicRatioService.lambdaUpdate()
                        .set(ScenicRatio::getStatue, 1)
                        .eq(ScenicRatio::getScenicId, bingAgencyCmd.getScenicId())
                        .update();
            }
            //添加点位信息
            List<ScenicLocation> scenicLocations = bingAgencyCmd.getScenicLocations();
            if (CollectionUtil.isNotEmpty(scenicLocations)) {
                List<ScenicLocation> dbList = scenicLocationService.lambdaQuery()
                        .eq(ScenicLocation::getStatue, 0)
                        .eq(ScenicLocation::getScenicId, bingAgencyCmd.getScenicId())
                        .list();
                //获取页面删除的记录
                List<ScenicLocation> removeList = dbList.stream()
                        .filter(db -> !scenicLocations.stream()
                                .anyMatch(scenicLocation -> scenicLocation.getId() != null
                                        && scenicLocation.getId().equals(db.getId()))).toList();

                //删除 原有信息
                removeList.forEach(r -> {
                    scenicLocationService.lambdaUpdate()
                            .set(ScenicLocation::getStatue, 1)
                            .eq(ScenicLocation::getId, r.getId())
                            .update();
                });

                //更新的记录
                List<ScenicLocation> updateList = scenicLocations.stream()
                        .filter(location -> dbList.stream()
                                .anyMatch(db -> location.getId() != null
                                        && db.getId().equals(location.getId()))).toList();
                updateList.forEach(r -> {
                    scenicLocationService.lambdaUpdate()
                            .set(ScenicLocation::getLocationName, r.getLocationName())
                            .eq(ScenicLocation::getId, r.getId())
                            .update();
                });

                //添加的记录
                List<ScenicLocation> addList = scenicLocations.stream()
                        .filter(scenicLocation -> scenicLocation.getId() == null)
                        .collect(Collectors.toList());
                //添加
                addList.forEach(location -> {
                    location.setScenicId(bingAgencyCmd.getScenicId());
                    location.setScenicName(scenicSpot.getName());
                    String code = idHutool.genCode("DW");
                    location.setLocationCode(code);
                    location.setLocationType(2);
                    location.setStatue(0);

                    String content = idHutool.genCode("QR");
                    WxGetCodeCmd wxGetCodeCmd = new WxGetCodeCmd();
                    wxGetCodeCmd.setScene(content);
                    wxGetCodeCmd.setPage("pages/payment/payment");
                    String qrUrl = iWxUserService.getUnlimitedQRCode(wxGetCodeCmd);
                    location.setLocationUrl(qrUrl);
                    location.setLocationUrlCode(content);
                    location.setLongitude(scenicSpot.getLatitude());
                    location.setLatitude(scenicSpot.getLongitude());
                });
                scenicLocationService.saveBatch(addList);
            } else {
                scenicLocationService.lambdaUpdate()
                        .set(ScenicLocation::getStatue, 1)
                        .eq(ScenicLocation::getScenicId, bingAgencyCmd.getScenicId())
                        .update();
            }
        }
    }

    @Override
    public List<CoopAgency> selectByUser(String userName) {
        return coopAgencyMapper.selectByUser(userName);
    }
}
