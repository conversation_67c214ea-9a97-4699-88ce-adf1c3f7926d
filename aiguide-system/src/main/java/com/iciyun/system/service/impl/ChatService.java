package com.iciyun.system.service.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONArray;
import com.google.common.collect.Lists;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.iciyun.common.core.domain.ChatMsgDataDto;
import com.iciyun.common.core.domain.entity.ChatRespDto;
import com.iciyun.common.core.domain.entity.MsgDataDto;
import com.iciyun.common.utils.HttpClient;
import com.iciyun.system.domain.dto.TranslaterData;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2025-05-09 12:14.
 */
@Slf4j
public class ChatService {

    private final String token;
    private final String botId;


    public ChatService(String token, String botId) {
        this.token = token;
        this.botId = botId;
    }


    //获取系统音色
    public void voices() {
        Integer pageNum = 1;

        List<Object> list = Lists.newArrayList();

        while (true) {
            String url = "https://api.coze.cn/v1/audio/voices?filter_system_voice=false&page_num="+pageNum+"&page_size=100";
            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("Authorization", "Bearer " + token)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Call call = HttpClient.getInstance().newCall(request);
            try {
                Response response = call.execute();
                String result = response.body().string();
                if (StrUtil.isNotEmpty(result)) {
                    JSONObject jsonObject = JSONUtil.parseObj(result);
                    log.info("{}", jsonObject);
                    Integer code = jsonObject.getInt("code");
                    if (code == 0) {
                        cn.hutool.json.JSONArray arr = jsonObject.getJSONObject("data").getJSONArray("voice_list");
                        Boolean hasMore = jsonObject.getJSONObject("data").getBool("has_more");
                        list.addAll(arr);
                        if (hasMore) {
                            pageNum++;
                        }else {
                            break;
                        }
                    }
                }
            } catch (IOException e) {
                log.error("创建会话失败！", e);
            }
        }

        System.out.println(list);
    }


    //创建空的会话ID
    public String genConversationId() {
        String url = "https://api.coze.cn/v1/conversation/create";

        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Bearer " + token)
                .addHeader("Content-Type", "application/json")
                .build();
        Call call = HttpClient.getInstance().newCall(request);
        try {
            Response response = call.execute();
            String result = response.body().string();
            if (StrUtil.isNotEmpty(result)) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                Integer code = jsonObject.getInt("code");
                if (code == 0) {
                    JSONObject resDataJson = jsonObject.getJSONObject("data");
                    //会话ID
                    String conversationId = resDataJson.getStr("id");
                    return conversationId;
                }
            }
        } catch (IOException e) {
            log.error("创建会话失败！", e);
        }
        return "";
    }


    public void streamChat(String userId, String userMessage, String conversationId, Map<String, Object> custom_variables) throws IOException, InterruptedException {

        long time1 = System.currentTimeMillis();

        Response response = streamChatResponse(userId, userMessage, conversationId, custom_variables);

        BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream()));
        String line;
        String currentEventId = null;
        String eventType = null;

        List<String> punctuationMarks = Arrays.asList("。", "!", "！", "?", "？",
                ",", "，", "~", "~ ", "；", ";", "：", ":", "•", "·");
        StringBuilder sb = new StringBuilder();
        boolean sentenceSegmentation = true;

        while ((line = reader.readLine()) != null) {
            if (line.startsWith("event:")) {
                String[] parts = line.split(":", 2);
                eventType = parts[1];
                eventType = "event:" + eventType;
                log.info("Event type: {}", eventType);
            } else if (line.startsWith("id:")) {
                String[] parts = line.split(":", 2);
                currentEventId = parts[1];
//                System.out.println("Event id: " + currentEventId);
            } else if (line.startsWith("data:")) {
                String[] parts = line.split(":", 2);
                String data = parts[1];
                if ("event:conversation.message.delta".equals(eventType)) {

                    JSONObject json = JSONUtil.parseObj(data);
                    String content = json.getStr("content");
                    String chatId = json.getStr("chat_id");

                    String[] split = content.split("");
                    for (String s : split) {
                        sb.append(s);
                        String text = sb.toString().replace("*", "").replace("#", "");

                        if (!sentenceSegmentation && punctuationMarks.contains(s)) {
                            long time2 = System.currentTimeMillis();
                            long time = time2 - time1;
                            log.info("{} ms chat_id:{} content:{}", time, chatId, text);
                            sb.setLength(0);
                        }
                        if (sentenceSegmentation && punctuationMarks.contains(s) && sb.length() > 5) {
                            long time2 = System.currentTimeMillis();
                            long time = time2 - time1;
                            log.info("第一句流式文本 --> {} ms chat_id:{} content:{}", time, chatId, text);
                            sb.setLength(0);
                            sentenceSegmentation = false;
                        }
                    }
                } else if ("event:conversation.message.completed".equals(eventType)) {
                    JSONObject json = JSONUtil.parseObj(data);
                    log.info("message completed json : {}", json);
                } else if ("event:conversation.chat.completed".equals(eventType)) {
                    JSONObject json = JSONUtil.parseObj(data);
                    log.info("chat completed json : {}", json);
                }
            }
        }


    }


    public Response streamChatResponse(String userId, String userMessage, String conversationId, Map<String, Object> custom_variables) throws IOException, InterruptedException {
        String url = "https://api.coze.cn/v3/chat?";
        if (StrUtil.isNotEmpty(conversationId)) {
            url = "https://api.coze.cn/v3/chat?conversation_id=" + conversationId;
        }

        com.alibaba.fastjson2.JSONObject dataJson = new com.alibaba.fastjson2.JSONObject();
        dataJson.put("bot_id", botId);
        dataJson.put("user_id", userId);
        dataJson.put("stream", true);
        dataJson.put("auto_save_history", true);

        JSONArray messages = new JSONArray();
        com.alibaba.fastjson2.JSONObject message = new com.alibaba.fastjson2.JSONObject();
        message.put("content", userMessage);
        message.put("content_type", "text");
        message.put("role", "user");
        message.put("type", "question");

        messages.add(message);

        dataJson.put("additional_messages", messages);

        if (MapUtil.isNotEmpty(custom_variables)) {
            dataJson.put("custom_variables", custom_variables);
        }

        RequestBody body = RequestBody.create(dataJson.toJSONString(), MediaType.parse("application/json; charset=utf-8"));

        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Bearer " + token)
                .addHeader("Content-Type", "application/json")
                .post(body)
                .build();

        Call call = HttpClient.getInstance().newCall(request);
        Response response = call.execute();

        if (!response.isSuccessful()) {
            throw new IOException("Unexpected code " + response);
        }

        return response;
    }


    /**
     * en=英语、ja=日语、ko=韩语、fr=法语、de=德语、es=西班牙语、ru=俄语、ar=阿拉伯语、vi=越南语、th=泰语
     * @param msg  翻译成 [en,ja]：今天天气真好，晚上的月亮好圆呀。
     * @return
     *
     *      返回的 json 内容格式：
            {
                "status": "success",
                "source_text": "在这里说什么都会直接进行翻译。",
                "results": [
                  {
                    "lang": "en",
                    "translation": "Whatever you say here will be directly translated.",
                    "confidence": 0.99,
                    "notes": []
                  },
                  {
                    "lang": "ja",
                    "translation": "ここで何を言っても直接翻訳されます。",
                    "confidence": 0.99,
                    "notes": []
                  },
                  {
                    "lang": "ko",
                    "translation": "여기서 무엇을 하더라도 바로 번역됩니다.",
                    "confidence": 0.99,
                    "notes": []
                  }
                ],
                "timestamp": "2024-07-05T10:34:10+08:00"
            }
     *
     * @throws Exception
     */
    public ChatRespDto translaterChat(String msg) throws Exception {
        ChatRespDto chatRespDto = new ChatRespDto();

        String url = "https://api.coze.cn/v3/chat?";

        com.alibaba.fastjson2.JSONObject dataJson = new com.alibaba.fastjson2.JSONObject();
        dataJson.put("bot_id", botId);
        dataJson.put("user_id", IdUtil.fastSimpleUUID());
        dataJson.put("stream", true);
        dataJson.put("auto_save_history", false);

        JSONArray messages = new JSONArray();
        com.alibaba.fastjson2.JSONObject message = new com.alibaba.fastjson2.JSONObject();
        message.put("content", msg);
        message.put("content_type", "text");
        message.put("role", "user");
        message.put("type", "question");

        messages.add(message);

        dataJson.put("additional_messages", messages);

        RequestBody body = RequestBody.create(dataJson.toJSONString(), MediaType.parse("application/json; charset=utf-8"));

        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Bearer " + token)
                .addHeader("Content-Type", "application/json")
                .post(body)
                .build();

        Call call = HttpClient.getInstance().newCall(request);

        try (Response response = call.execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            chatRespDto.setCode(response.code());

            BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream()));
            String line;
            String currentEventId = null;
            String eventType = null;

            StringBuilder sb = new StringBuilder();
            boolean sentenceSegmentation = true;

            while ((line = reader.readLine()) != null) {
                if (line.startsWith("event:")) {
                    String[] parts = line.split(":", 2);
                    eventType = parts[1];
                    eventType = "event:" + eventType;
                } else if (line.startsWith("id:")) {
                    String[] parts = line.split(":", 2);
                    currentEventId = parts[1];
                } else if (line.startsWith("data:")) {
                    String[] parts = line.split(":", 2);
                    String data = parts[1];
                    if ("event:conversation.message.delta".equals(eventType)) {
                    } else if ("event:conversation.message.completed".equals(eventType)) {
                        JSONObject json = JSONUtil.parseObj(data);
                        String type = json.getStr("type");
                        if (type.equals("answer")) {
                            String content = json.getStr("content");
                            content = content.replace("*", "").replace("#", "");
                            content = StrUtil.removePrefix(content,"```json");
                            content = StrUtil.removeSuffix(content,"```");
                            chatRespDto.setContent(content);
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("调用 coze 失败！", e);
        }

        return chatRespDto;
    }



    public ChatRespDto streamChatResponse_Deal(List<MsgDataDto> historyContextList) throws Exception {
        ChatRespDto chatRespDto = new ChatRespDto();

        List<ChatMsgDataDto> list = historyContextList.stream().map(item -> {
            return item.trans();
        }).collect(Collectors.toList());

        String url = "https://api.coze.cn/v3/chat?";

        com.alibaba.fastjson2.JSONObject dataJson = new com.alibaba.fastjson2.JSONObject();
        dataJson.put("bot_id", botId);
        dataJson.put("user_id", IdUtil.fastSimpleUUID());
        dataJson.put("stream", true);
        dataJson.put("auto_save_history", false);

        JSONArray messages = new JSONArray();
        com.alibaba.fastjson2.JSONObject message = new com.alibaba.fastjson2.JSONObject();
        message.put("content", list.get(0).getContent());
        message.put("content_type", "text");
        message.put("role", "user");
        message.put("type", "question");

        messages.add(message);

        dataJson.put("additional_messages", messages);

        RequestBody body = RequestBody.create(dataJson.toJSONString(), MediaType.parse("application/json; charset=utf-8"));

        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Bearer " + token)
                .addHeader("Content-Type", "application/json")
                .post(body)
                .build();

        Call call = HttpClient.getInstance().newCall(request);

        try (Response response = call.execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            chatRespDto.setCode(response.code());

            BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream()));
            String line;
            String currentEventId = null;
            String eventType = null;

            StringBuilder sb = new StringBuilder();
            boolean sentenceSegmentation = true;

            while ((line = reader.readLine()) != null) {
                if (line.startsWith("event:")) {
                    String[] parts = line.split(":", 2);
                    eventType = parts[1];
                    eventType = "event:" + eventType;
                } else if (line.startsWith("id:")) {
                    String[] parts = line.split(":", 2);
                    currentEventId = parts[1];
                } else if (line.startsWith("data:")) {
                    String[] parts = line.split(":", 2);
                    String data = parts[1];
                    if ("event:conversation.message.delta".equals(eventType)) {
                    } else if ("event:conversation.message.completed".equals(eventType)) {
                        JSONObject json = JSONUtil.parseObj(data);
                        String type = json.getStr("type");
                        if (type.equals("answer")) {
                            String content = json.getStr("content");
                            content = content.replace("*", "").replace("#", "");
                            chatRespDto.setContent(content);
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("调用 coze 失败！", e);
        }

        return chatRespDto;
    }
}

class ChatEvent {
    private final String id;
    private final String eventType;
    private final String data;

    public ChatEvent(String id, String eventType, String data) {
        this.id = id;
        this.eventType = eventType;
        this.data = data;
    }

    public String getId() {
        return id;
    }

    public String getEventType() {
        return eventType;
    }

    public String getData() {
        return data;
    }

    public static void main2(String[] args) throws Exception{
        String token = "czs_lVuLylW1QxysBOAYoAdgov5CjcdGnjTjWRoJtjx6nqfEgq3zf8CA0PNgtKXT4JnjW";
        String botId = "7496303824152477723";

        String userPrompt = "请帮我介绍一下恭王府的蝠池";
        userPrompt += "，请以 现在我们来到蝠池 开头进行回复。回复风格要求：亲子教育，回复语言要求：中文";

        List<MsgDataDto> historyContextList = Lists.newArrayList();
        MsgDataDto userMsg = new MsgDataDto();
        userMsg.setRole("user");
        userMsg.setContent(userPrompt);
        historyContextList.add(userMsg);

        ChatService chatService = new ChatService(token, botId);

        long time1 = System.currentTimeMillis();

        ChatRespDto chatRespDto = chatService.streamChatResponse_Deal(historyContextList);
        long time2 = System.currentTimeMillis();

        System.out.println((time2 - time1) + "ms " + chatRespDto);

    }

    public static void main(String[] args) throws Exception{
        String token = "czs_qLSIpblrVrUetUNtxYFLjVRexlJJ6FkP1YfO21wSbH5EWuyXsA2Uw2c47gpoF8nmJ";
        String botId = "7522786915092545546";

        ChatService chatService = new ChatService(token, botId);

        long time1 = System.currentTimeMillis();

        ChatRespDto chatRespDto = chatService.translaterChat("翻译成 [en,ja]：今天天气真好，晚上的月亮好圆呀。");

        JSONObject obj = JSONUtil.parseObj(chatRespDto.getContent());
        TranslaterData translaterData = JSONUtil.toBean(obj, TranslaterData.class);

        long time2 = System.currentTimeMillis();

        System.out.println(translaterData);


        System.out.println((time2 - time1) + "ms ");
    }

}
