package com.iciyun.system.service.impl;

import com.iciyun.system.domain.UserRec;
import com.iciyun.system.domain.UserRecVo;
import com.iciyun.system.mapper.UserRecMapper;
import com.iciyun.system.service.IUserRecService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 推荐关系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07 18:20:38
 */
@Service
public class UserRecServiceImpl extends ServiceImpl<UserRecMapper, UserRec> implements IUserRecService {

    @Autowired
    private UserRecMapper userRecMapper;

    @Override
    public List<UserRecVo> selectUserRecList(String userName) {
        return userRecMapper.selectUserRecList(userName);
    }
}
