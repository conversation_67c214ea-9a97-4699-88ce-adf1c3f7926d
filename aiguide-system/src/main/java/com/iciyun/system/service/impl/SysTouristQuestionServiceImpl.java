package com.iciyun.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.common.utils.DateUtils;
import com.iciyun.common.utils.SecurityUtils;
import com.iciyun.system.domain.SysTouristQuestion;
import com.iciyun.system.mapper.SysTouristQuestionMapper;
import com.iciyun.system.service.ISysTouristQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 景区常见问题对话记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
@Service
public class SysTouristQuestionServiceImpl implements ISysTouristQuestionService 
{
    //问题缓存时间
    private static final Integer DAYS = 1;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SysTouristQuestionMapper sysTouristQuestionMapper;

    public void clearQuestionCache(){
        Collection<String> keys = redisCache.keys("*");

        for (String key : keys) {
            if (key.startsWith("questionCache")){
                redisCache.deleteObject(key);
            }
        }
    }


    /**
     * 查询景区常见问题对话记录
     * 
     * @param id 景区常见问题对话记录主键
     * @return 景区常见问题对话记录
     */
    @Override
    public SysTouristQuestion selectSysTouristQuestionById(Long id)
    {
        return sysTouristQuestionMapper.selectSysTouristQuestionById(id);
    }

    /**
     * 查询景区常见问题对话记录列表
     * 
     * @param sysTouristQuestion 景区常见问题对话记录
     * @return 景区常见问题对话记录
     */
    @Override
    public List<SysTouristQuestion> selectSysTouristQuestionList(SysTouristQuestion sysTouristQuestion)
    {
        sysTouristQuestion.setUserId(SecurityUtils.getUserId()+"");
        return sysTouristQuestionMapper.selectSysTouristQuestionList(sysTouristQuestion);
    }

    @Override
    public String questionCache(Long touristId, Long questionId) {
        String key = "questionCache:"+touristId+":"+questionId;

        Object val = redisCache.getCacheObject(key);

        return val==null?"":val.toString();
    }


    /**
     * 新增景区常见问题对话记录
     * 
     * @param sysTouristQuestion 景区常见问题对话记录
     * @return 结果
     */
    @Override
    public int insertSysTouristQuestion(SysTouristQuestion sysTouristQuestion)
    {
        Long userId = SecurityUtils.getUserId();
        sysTouristQuestion.setUserId(userId+"");

        sysTouristQuestion.setCreateTime(DateUtils.getNowDate());
        int count =  sysTouristQuestionMapper.insertSysTouristQuestion(sysTouristQuestion);

        String temp = questionCache(sysTouristQuestion.getTouristId(),sysTouristQuestion.getQuestionId());
        if (StrUtil.isEmpty(temp) && sysTouristQuestion.getType()==2){
            String key = "questionCache:"+sysTouristQuestion.getTouristId()+":"+sysTouristQuestion.getQuestionId();
            redisCache.setCacheObject(key,sysTouristQuestion.getAnswerText(),DAYS, TimeUnit.DAYS);
        }

        return count;
    }

    /**
     * 修改景区常见问题对话记录
     * 
     * @param sysTouristQuestion 景区常见问题对话记录
     * @return 结果
     */
    @Override
    public int updateSysTouristQuestion(SysTouristQuestion sysTouristQuestion)
    {
        return sysTouristQuestionMapper.updateSysTouristQuestion(sysTouristQuestion);
    }

    /**
     * 批量删除景区常见问题对话记录
     * 
     * @param ids 需要删除的景区常见问题对话记录主键
     * @return 结果
     */
    @Override
    public int deleteSysTouristQuestionByIds(Long[] ids)
    {
        return sysTouristQuestionMapper.deleteSysTouristQuestionByIds(ids);
    }

    /**
     * 删除景区常见问题对话记录信息
     * 
     * @param id 景区常见问题对话记录主键
     * @return 结果
     */
    @Override
    public int deleteSysTouristQuestionById(Long id)
    {
        return sysTouristQuestionMapper.deleteSysTouristQuestionById(id);
    }
}
