package com.iciyun.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.system.domain.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08 16:38:53
 */
public interface IGuidePayOrderService extends IService<GuidePayOrder> {

    GuidePayOrderRet guidePay(GuidePayOrderCmd cmd);

    ScenicHeadphoneVo getHeadsetphone(GuideHeadset guideHeadset);

    void collectHeadsetphone(String orderId);

    void applyHeadsetphone(String orderId);

    //更新 主订单， 明细订单 状态
    int updateGuidePayOrder(GuidePayOrderUpdate cmd);

    List<CoopAgency> getAgencies(String userName);

    List<String> getSelectId(String userName);

    List<ScenicRatio> getScenics(OperateIdCmd cmd);

    GuideOrderVo getOrders(GuideOrderQry qry);

    GuideOrderVo getOrdersLimit(GuideOrderQry qry);

    List<GuideOrderSt> getOrdersLimit1(GuideOrderQry qry);

    List<GuidePayOrderItemVo> getOrderItems(GuideOrderItemQry qry);

    void bingOrder();

    List<GuideOrderSt> exportOrders(GuideOrderQry qry);

    GuidePayOrderItemEntity getOrderItem(String orderId);
}
