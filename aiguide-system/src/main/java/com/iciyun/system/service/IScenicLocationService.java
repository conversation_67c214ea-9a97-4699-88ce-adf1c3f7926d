package com.iciyun.system.service;

import com.iciyun.system.domain.ScenicLocation;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 点位信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07 15:16:28
 */
public interface IScenicLocationService extends IService<ScenicLocation> {

    List<ScenicLocation> queryList(ScenicLocation qo);

    ScenicLocation queryByLocationName(ScenicLocation scenicLocation);

    String generateQR(ScenicLocation scenicLocation);

    void saveLocation(ScenicLocation scenicLocation);

    void removeLocation(ScenicLocation scenicLocation);
}
