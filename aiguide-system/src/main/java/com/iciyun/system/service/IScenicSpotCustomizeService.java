package com.iciyun.system.service;

import com.iciyun.system.domain.ScenicSpot;
import com.baomidou.mybatisplus.extension.service.IService;
import com.iciyun.system.domain.bo.ScenicSpotEditCmd;
import com.iciyun.system.domain.bo.ScenicSpotMiniAppQuery;
import com.iciyun.system.domain.qo.ScenicSpotListQuery;
import com.iciyun.system.domain.qo.ScenicSpotQuery;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02 14:44:00
 */
public interface IScenicSpotCustomizeService extends IService<ScenicSpot> {

    List<ScenicSpot> queryScenicSpot4MiniApp(ScenicSpotMiniAppQuery query);

    void edit(ScenicSpotEditCmd cmd);

    ScenicSpot getSimpleOne(ScenicSpotQuery query);

    List<ScenicSpot> selectByUser(ScenicSpotListQuery qry);
}
