package com.iciyun.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iciyun.common.constant.Constants;
import com.iciyun.common.constant.HttpStatus;
import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.common.enums.*;
import com.iciyun.common.exception.GlobalException;
import com.iciyun.common.utils.uuid.IdHutool;
import com.iciyun.system.domain.*;
import com.iciyun.system.mapper.CoopAgencyMapper;
import com.iciyun.system.mapper.GuidePayOrderItemMapper;
import com.iciyun.system.mapper.GuidePayOrderMapper;
import com.iciyun.system.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08 16:38:53
 */
@Service
public class GuidePayOrderServiceImpl extends ServiceImpl<GuidePayOrderMapper, GuidePayOrder> implements IGuidePayOrderService {

    @Autowired
    private IScenicLocationService iScenicLocationService;

    @Autowired
    private IScenicRatioService scenicRatioService;

    @Autowired
    private IScenicHeadphoneService scenicHeadphoneService;

    @Autowired
    private IGuidePayOrderItemService guidePayOrderItemService;

    @Autowired
    private IScenicAdminService scenicAdminService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private IRefundOrderService refundOrderService;

    @Autowired
    private ICoopAgencyService coopAgencyService;

    @Value("${order.agency_id}")
    private String agencyId;

    @Autowired
    private IGuidePayOrderItemRatioService guidePayOrderItemRatioService;

    @Autowired
    private GuidePayOrderItemMapper guidePayOrderItemMapper;

    @Autowired
    private CoopAgencyMapper coopAgencyMapper;

    @Autowired
    private IAgencyAdminScenicService agencyAdminScenicService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public GuidePayOrderRet guidePay(GuidePayOrderCmd cmd) {

        //查询 景区 点位 信息
        ScenicLocation scenicLocation = iScenicLocationService.lambdaQuery()
                .eq(ScenicLocation::getScenicId, cmd.getScenicId())
                .eq(ScenicLocation::getLocationCode, cmd.getPointId())
                .one();
        if (scenicLocation == null) {
            throw new GlobalException(HttpStatus.WX_CODE_ERROR, "景区-点位的信息不存在");
        }

        //查询合作机构
        List<ScenicRatio> scenicRatios = scenicRatioService.lambdaQuery()
                .eq(ScenicRatio::getScenicId, cmd.getScenicId())
                .list();
        if (CollectionUtil.isEmpty(scenicRatios)) {
            throw new GlobalException(HttpStatus.WX_CODE_ERROR, "景区-合作机构的信息不存在");
        }

        //查询耳机结算
        String model = "";
        List<OrderItem> orderItems = cmd.getOrderItems();
        if (CollectionUtil.isNotEmpty(orderItems)) {
            for (OrderItem orderItem : orderItems) {
                if (orderItem.getOrderItem().equals(OrderItemEnum.HEADSETPHONE.getCode())) {
                    model = orderItem.getHeadsetModel();
                }
            }
        }
        ScenicHeadphone scenicHeadphone = null;
        if (StringUtils.isNotBlank(model)) {
            scenicHeadphone = scenicHeadphoneService.lambdaQuery()
                    .eq(ScenicHeadphone::getScenicId, cmd.getScenicId())
                    .eq(ScenicHeadphone::getModel, model)
                    .eq(ScenicHeadphone::getStatus, 0)
                    .one();
            if (scenicHeadphone == null) {
                throw new GlobalException(HttpStatus.WX_CODE_ERROR, "景区的耳机结算不存在");
            }
        }

        String orderId = IdHutool.gen("DO");
        GuidePayOrder guidePayOrder = new GuidePayOrder();
        guidePayOrder.setOrderId(orderId);
        guidePayOrder.setOrderAmount(cmd.getOrderAmount());
        this.save(guidePayOrder);

        //保存明细
        List<GuidePayOrderItem> guidePayOrderItems = new ArrayList<>();
        List<GuidePayOrderItemRatio> guidePayOrderItemRatios = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(orderItems)) {
            DecimalFormat df2 = new DecimalFormat("#.00");
            for (OrderItem orderItem : orderItems) {
                GuidePayOrderItem guidePayOrderItem = new GuidePayOrderItem();
                guidePayOrderItem.setInsertTime(LocalDateTime.now());
                guidePayOrderItem.setOrderId(orderId);
                guidePayOrderItem.setOrderItem(orderItem.getOrderItem());
                guidePayOrderItem.setHeadsetModel(orderItem.getHeadsetModel());
                guidePayOrderItem.setItemAmount(orderItem.getItemAmount());
                guidePayOrderItem.setOrderAmount(cmd.getOrderAmount());
                guidePayOrderItem.setScenicId(cmd.getScenicId());
                guidePayOrderItem.setPointId(cmd.getPointId());
                guidePayOrderItem.setScenicName(scenicLocation.getScenicName());
                guidePayOrderItem.setPointName(scenicLocation.getLocationName());
                //分佣
                if (orderItem.getOrderItem().equals(OrderItemEnum.HEADSETPHONE.getCode()) &&
                        scenicHeadphone != null) {
                    guidePayOrderItem.setAgencyRaw(JSONObject.toJSONString(scenicHeadphone));
                    guidePayOrderItem.setLocations(scenicHeadphone.getLocations());
                    guidePayOrderItem.setHeadsetStatue(HeadsetPhoneStatueEnum.NO_GET.getCode());
                    GuidePayOrderItemRatio orderItemRatio = new GuidePayOrderItemRatio();
                    orderItemRatio.setAgencyId(scenicHeadphone.getAgencyId());
                    orderItemRatio.setAgencyName(scenicHeadphone.getAgencyName());
                    orderItemRatio.setAgencyAmount(scenicHeadphone.getAgencyPrice());
                    orderItemRatio.setOrderId(orderId);
                    orderItemRatio.setCreateTime(LocalDateTime.now());
                    //耳机
                    orderItemRatio.setOrderItem("1");
                    guidePayOrderItemRatios.add(orderItemRatio);
                } else {
                    guidePayOrderItem.setAgencyRaw(JSONObject.toJSONString(scenicRatios));
                    //保存订单分佣关系表：
                    scenicRatios.forEach(ratio -> {
                        GuidePayOrderItemRatio orderItemRatio = new GuidePayOrderItemRatio();
                        orderItemRatio.setOrderId(orderId);
                        orderItemRatio.setCreateTime(LocalDateTime.now());
                        orderItemRatio.setAgencyId(ratio.getAgencyId());
                        orderItemRatio.setAgencyName(ratio.getAgencyName());
                        //导游服务
                        orderItemRatio.setOrderItem("0");
                        BigDecimal agencyAmount = guidePayOrderItem.getItemAmount()
                                .multiply(new BigDecimal(ratio.getAgencyRatio())
                                        .divide(new BigDecimal("100"))).setScale(2, RoundingMode.DOWN);
                        orderItemRatio.setAgencyAmount(new BigDecimal(df2.format(agencyAmount)));
                        if (StringUtils.isNotBlank(agencyId) && agencyId.equals(ratio.getAgencyId())) {
                            BigDecimal sum = new BigDecimal("0");
                            List<ScenicRatio> x = scenicRatios.stream().filter(e -> !agencyId.equals(e.getAgencyId())).collect(Collectors.toList());
                            for (ScenicRatio r : x) {
                                BigDecimal multiply = guidePayOrderItem.getItemAmount().multiply(new BigDecimal(r.getAgencyRatio())
                                        .divide(new BigDecimal("100"))).setScale(2, RoundingMode.DOWN);
                                sum = sum.add(multiply);
                            }
                            //平台机构分佣
                            orderItemRatio.setAgencyAmount(guidePayOrderItem.getItemAmount().subtract(sum));
                        }
                        guidePayOrderItemRatios.add(orderItemRatio);
                    });

                }
                guidePayOrderItems.add(guidePayOrderItem);
            }
        }
        guidePayOrderItemService.saveBatch(guidePayOrderItems);
        guidePayOrderItemRatioService.saveBatch(guidePayOrderItemRatios);

        GuidePayOrderRet ret = new GuidePayOrderRet();
        ret.setOrderId(orderId);
        ret.setOrderAmount(cmd.getOrderAmount());

        return ret;
    }

    @Override
    public ScenicHeadphoneVo getHeadsetphone(GuideHeadset cmd) {
        GuidePayOrderItem order = guidePayOrderItemService.lambdaQuery()
                .eq(GuidePayOrderItem::getScenicId, cmd.getScenicId())
                .eq(GuidePayOrderItem::getOpenId, cmd.getOpenId())
                .apply("to_char(create_time,'yyyy-MM-dd') = {0}", DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd"))
                .eq(GuidePayOrderItem::getOrderItem, OrderItemEnum.HEADSETPHONE.getCode())
                .eq(GuidePayOrderItem::getHeadsetStatue, HeadsetPhoneStatueEnum.NO_GET.getCode())
                .orderByDesc(GuidePayOrderItem::getCreateTime)
                .one();
        if (order != null) {
            ScenicHeadphoneVo vo = new ScenicHeadphoneVo();
            vo.setModel(order.getHeadsetModel());
            vo.setLocations(order.getLocations());
            vo.setOrderId(order.getOrderId());
            return vo;
        }
        return null;
    }

    @Override
    public void collectHeadsetphone(String orderId) {
        GuidePayOrderItem order = guidePayOrderItemService.lambdaQuery()
                .eq(GuidePayOrderItem::getOrderId, orderId)
                .eq(GuidePayOrderItem::getOrderItem, OrderItemEnum.HEADSETPHONE.getCode())
                .eq(GuidePayOrderItem::getHeadsetStatue, HeadsetPhoneStatueEnum.NO_GET.getCode())
                .one();
        if (order != null) {
            order.setHeadsetStatue(HeadsetPhoneStatueEnum.GET.getCode());
            guidePayOrderItemService.updateById(order);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applyHeadsetphone(String orderId) {
        GuidePayOrderItem order = guidePayOrderItemService.lambdaQuery()
                .eq(GuidePayOrderItem::getOrderId, orderId)
                .eq(GuidePayOrderItem::getOrderItem, OrderItemEnum.HEADSETPHONE.getCode())
                .eq(GuidePayOrderItem::getHeadsetStatue, HeadsetPhoneStatueEnum.NO_GET.getCode())
                .one();
        if (order != null) {
            order.setHeadsetStatue(HeadsetPhoneStatueEnum.APPLY.getCode());
            guidePayOrderItemService.updateById(order);

            //查询机构
            GuidePayOrderItemRatio ratio = guidePayOrderItemRatioService.lambdaQuery()
                    .eq(GuidePayOrderItemRatio::getOrderId, order.getOrderId())
                    .eq(GuidePayOrderItemRatio::getOrderItem, OrderItemEnum.HEADSETPHONE.getCode())
                    .last("limit 1")
                    .one();

            //生成一条退款订单
            RefundOrder refundOrder = new RefundOrder();
            refundOrder.setOrderId(IdHutool.gen("RO"));
            refundOrder.setOrderAmount(order.getItemAmount());
            refundOrder.setOrderStatue("0");
            refundOrder.setOldOrderId(order.getOrderId());
            refundOrder.setItem(OrderItemEnum.HEADSETPHONE.getCode()); //0 AI服务， 1 耳机...
            refundOrder.setOpenId(order.getOpenId());
            refundOrder.setCreateTime(LocalDateTime.now());
            refundOrder.setScenicId(order.getScenicId());
            refundOrder.setScenicName(order.getScenicName());
            refundOrder.setAgencyId(ratio.getAgencyId());
            refundOrder.setAgencyName(ratio.getAgencyName());
            refundOrder.setUserName(order.getUserName());
            refundOrderService.save(refundOrder);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateGuidePayOrder(GuidePayOrderUpdate cmd) {
        this.lambdaUpdate()
                .eq(GuidePayOrder::getOrderId, cmd.getOrderId())
                .set(GuidePayOrder::getOrderStatue, cmd.getOrderStatus())
                .set(GuidePayOrder::getPayStatue, cmd.getPayStatue())
                .set(GuidePayOrder::getOpenId, cmd.getOpenId())
                .update();

        guidePayOrderItemService.lambdaUpdate()
                .eq(GuidePayOrderItem::getOrderId, cmd.getOrderId())
                .set(GuidePayOrderItem::getCreateTime, LocalDateTime.now())
                .set(GuidePayOrderItem::getOrderStatue, cmd.getOrderStatus())
                .set(GuidePayOrderItem::getOpenId, cmd.getOpenId())
                .update();
        return 1;
    }

    @Override
    public List<CoopAgency> getAgencies(String userName) {
        List<CoopAgency> coopAgencies = new ArrayList<>();
        Long platfrom = scenicAdminService.lambdaQuery()
                .eq(ScenicAdmin::getUserPhone, userName)
                .eq(ScenicAdmin::getBusinessCode, agencyId)
                .eq(ScenicAdmin::getBusinessType, PartnerBusinessType.COOPERATION.getCode())
                .count();
        if (platfrom != null && platfrom > 0) {
            //查询所有机构
            coopAgencies = coopAgencyService.lambdaQuery()
                    .list();
        } else {
            coopAgencies = coopAgencyService.selectByUser(userName);
        }
        return coopAgencies;
    }

    @Override
    public List<String> getSelectId(String userName) {
        //查询机构iD
        List<ScenicAdmin> sas = scenicAdminService.lambdaQuery()
                .eq(ScenicAdmin::getBusinessType, PartnerBusinessType.COOPERATION.getCode())
                .eq(ScenicAdmin::getUserPhone, userName)
                .list();
        if (CollectionUtil.isNotEmpty(sas)) {
            return sas.stream().map(ScenicAdmin::getBusinessCode).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public List<ScenicRatio> getScenics(OperateIdCmd cmd) {
        List<ScenicRatio> ratioList = new ArrayList<>();
        if (Constants.ALLSELECT.equals(cmd.getAgencyIds().get(0))) {
            Long platfrom = scenicAdminService.lambdaQuery()
                    .eq(ScenicAdmin::getUserPhone, cmd.getUserName())
                    .eq(ScenicAdmin::getBusinessCode, agencyId)
                    .eq(ScenicAdmin::getBusinessType, PartnerBusinessType.COOPERATION.getCode())
                    .count();
            if (platfrom != null && platfrom > 0) {
                //查询所有合作景区
                ratioList = scenicRatioService.lambdaQuery()
                        .list();
            } else {
                //所有机构
                List<String> agencyIds = coopAgencyService.selectByUser(cmd.getUserName())
                        .stream().map(CoopAgency::getAgencyId)
                        .collect(Collectors.toList());

                for (String agencyId : agencyIds) {
                    //获取管理员用户查看景区
                    List<AgencyAdminScenic> scenicList = agencyAdminScenicService.lambdaQuery()
                            .eq(AgencyAdminScenic::getAgencyId, agencyId)
                            .eq(AgencyAdminScenic::getUserPhone, cmd.getUserName())
                            .list();
                    if (CollectionUtil.isNotEmpty(scenicList)) {
                        for (AgencyAdminScenic scenic : scenicList) {
                            ScenicRatio sr = new ScenicRatio();
                            sr.setScenicId(scenic.getScenicId());
                            sr.setScenicName(scenic.getScenicName());
                            ratioList.add(sr);
                        }
                    } else {
                        List<ScenicRatio> adminRatioList = scenicRatioService.lambdaQuery()
                                .eq(ScenicRatio::getAgencyId, agencyId)
                                .list();
                        ratioList.addAll(adminRatioList);
                    }
                }
            }
        } else {
            for (String agencyId : cmd.getAgencyIds()) {
                //获取管理员用户查看景区
                List<AgencyAdminScenic> scenicList = agencyAdminScenicService.lambdaQuery()
                        .eq(AgencyAdminScenic::getAgencyId, agencyId)
                        .eq(AgencyAdminScenic::getUserPhone, cmd.getUserName())
                        .list();
                if (CollectionUtil.isNotEmpty(scenicList)) {
                    for (AgencyAdminScenic scenic : scenicList) {
                        ScenicRatio sr = new ScenicRatio();
                        sr.setScenicId(scenic.getScenicId());
                        sr.setScenicName(scenic.getScenicName());
                        ratioList.add(sr);
                    }
                } else {
                    List<ScenicRatio> adminRatioList = scenicRatioService.lambdaQuery()
                            .eq(ScenicRatio::getAgencyId, agencyId)
                            .list();
                    ratioList.addAll(adminRatioList);
                }
            }
        }

        //根据景区去重
        if (CollectionUtil.isNotEmpty(ratioList)) {
            List<ScenicRatio> retList = ratioList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                    () -> new TreeSet<>(Comparator.comparing(ScenicRatio::getScenicId))), ArrayList::new));
            return retList;
        }
        return null;
    }

    @Override
    public GuideOrderVo getOrders(GuideOrderQry qry) {
        //处理时间
        extractTime(qry);
        GuideOrderVo vo = new GuideOrderVo();
        if (qry.getStartTime() != null) {
            vo.setStartTime(DateUtil.format(qry.getStartTime(), "yyyy/MM/dd"));
        }
        if (qry.getEndTime() != null) {
            vo.setEndTime(DateUtil.format(qry.getEndTime(), "yyyy/MM/dd"));
        }
        List<GuideOrderSt> items = guidePayOrderItemService.getOrderItemsByLimit(qry);
        if (CollectionUtil.isNotEmpty(items)) {
            //查询景区点位数
            List<ScenicLocation> scenicLocationList = iScenicLocationService.lambdaQuery()
                    .isNotNull(ScenicLocation::getLocationCode)
                    .in(ScenicLocation::getScenicId, items.stream().map(GuideOrderSt::getScenicId).collect(Collectors.toSet()))
                    .list();
            Map<Integer, Long> scenicMap = scenicLocationList.stream()
                    .collect(Collectors.groupingBy(ScenicLocation::getScenicId, Collectors.counting()));

            List<GuideOrderSt> retItems = new ArrayList<>();
            List<GuidePayOrderItemVo> orderItemVos = new ArrayList<>();
            for (GuideOrderSt st : items) {
                //查询订单
                List<GuidePayOrderItemVo> orderList = getOrdersBySelect(st.getAgencyId(),
                        st.getScenicId(), st.getOrderItem(), qry.getStartTime(), qry.getEndTime());
                orderItemVos.addAll(orderList);
                st.setOrderIds(orderList.stream().map(p -> p.getOrderId()).collect(Collectors.toList()));
                st.setAllPointNum(scenicMap.get(st.getScenicId()));
                retItems.add(st);
            }

            //时间区间
            if (qry.getStartTime() == null) {
                LocalDateTime start = orderItemVos.stream().map(p -> p.getCreateTime()).min(LocalDateTime::compareTo).get();
                vo.setStartTime(DateUtil.format(start, "yyyy/MM/dd"));
            }
            if (qry.getEndTime() == null) {
                vo.setEndTime(DateUtil.format(LocalDateTime.now(), "yyyy/MM/dd"));
            }


            //排序
            if (qry.getOrderSortCol() != null) {
                if (OrderSortColEnum.ALLAMOUNT.getCode().equals(qry.getOrderSortCol())) {
                    if (OrderSortTypeEnum.ASC.getCode().equals(qry.getOrderSortType())) {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAllAmount));
                    } else {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAllAmount).reversed());
                    }
                } else if (OrderSortColEnum.ALLORDERNUM.getCode().equals(qry.getOrderSortCol())) {
                    if (OrderSortTypeEnum.ASC.getCode().equals(qry.getOrderSortType())) {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAllOrderNum));
                    } else {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAllOrderNum).reversed());
                    }
                } else if (OrderSortColEnum.ALLPOINTNUM.getCode().equals(qry.getOrderSortCol())) {
                    if (OrderSortTypeEnum.ASC.getCode().equals(qry.getOrderSortType())) {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAllPointNum));
                    } else {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAllPointNum).reversed());
                    }
                } else if (OrderSortColEnum.AGENCYAMOUNT.getCode().equals(qry.getOrderSortCol())) {
                    if (OrderSortTypeEnum.ASC.getCode().equals(qry.getOrderSortType())) {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAgencyAmount));
                    } else {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAgencyAmount).reversed());
                    }
                }
            }

            vo.setItems(retItems);
        }
        return vo;
    }

    private void extractTime(GuideOrderQry qry) {
        if (StringUtils.isNotBlank(qry.getTimeType())) {
            LocalDateTime startTime = LocalDateTime.now();
            LocalDateTime endTime = LocalDateTime.now();
            LocalDate now = LocalDate.now();
            LocalDate lastWeek = LocalDate.now().minus(1L, ChronoUnit.WEEKS);
            if (qry.getTimeType().equals("0")) {
                //当天
                startTime = LocalDateTime.of(now, LocalTime.MIN);
                endTime = LocalDateTime.of(now, LocalTime.MAX);
            } else if (qry.getTimeType().equals("1")) {
                //昨天
                startTime = LocalDateTime.of(now.minus(1L, ChronoUnit.DAYS), LocalTime.MIN);
                endTime = LocalDateTime.of(now.minus(1L, ChronoUnit.DAYS), LocalTime.MAX);
            } else if (qry.getTimeType().equals("2")) {
                //本周
                startTime = LocalDateTime.of(now.minusDays(now.getDayOfWeek().getValue() - 1), LocalTime.MIN);
                endTime = LocalDateTime.of(now.plusDays(7 - now.getDayOfWeek().getValue()), LocalTime.MAX);
            } else if (qry.getTimeType().equals("3")) {
                //上周
                startTime = LocalDateTime.of(lastWeek.minusDays(lastWeek.getDayOfWeek().getValue() - 1), LocalTime.MIN);
                endTime = LocalDateTime.of(lastWeek.plusDays(7 - lastWeek.getDayOfWeek().getValue()), LocalTime.MAX);
            } else if (qry.getTimeType().equals("4")) {
                //本月
                startTime = LocalDateTime.of(now.with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
                endTime = LocalDateTime.of(now.with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
            } else if (qry.getTimeType().equals("5")) {
                //上月
                startTime = LocalDateTime.of(now.minus(1L, ChronoUnit.MONTHS).with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN);
                endTime = LocalDateTime.of(now.minus(1L, ChronoUnit.MONTHS).with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
            } else if (qry.getTimeType().equals("6")) {
                //至今
                if (qry.getChannelStartTime() != null) {
                    startTime = LocalDateTime.of(qry.getChannelStartTime().getYear(), qry.getChannelStartTime().getMonth(), qry.getChannelStartTime().getDayOfMonth(), 0, 0, 0);
                } else {
                    startTime = LocalDateTime.of(2025, 5, 21, 0, 0, 0);
                }
                endTime = LocalDateTime.of(now, LocalTime.MAX);
            }
            qry.setStartTime(startTime);
            qry.setEndTime(endTime);
        }
    }


    public GuideOrderVo getOrdersLimit(GuideOrderQry qry) {
        //处理时间
        extractTime(qry);
        GuideOrderVo vo = new GuideOrderVo();
        if (qry.getStartTime() != null) {
            vo.setStartTime(DateUtil.format(qry.getStartTime(), "yyyy/MM/dd"));
        }
        if (qry.getEndTime() != null) {
            vo.setEndTime(DateUtil.format(qry.getEndTime(), "yyyy/MM/dd"));
        }
        List<GuideOrderSt> retItems = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(qry.getItems()) &&
                Constants.ALLSELECT.equals(qry.getItems().get(0))) {
            //服务类型
            qry.setItems(List.of(OrderItemEnum.AIGUIDE.getCode(), OrderItemEnum.HEADSETPHONE.getCode()));
        }


        List<String> orderItems = qry.getItems();
        LocalDateTime startTime = qry.getStartTime();
        LocalDateTime endTime = qry.getEndTime();
        List<GuideOrderSt> items = guidePayOrderItemMapper.getOrderItemsByLimit(
                qry.getAgencyIds(), qry.getScenicIds(), qry.getTuples(), orderItems, startTime, endTime);
        if (CollectionUtil.isNotEmpty(items)) {
            //查询景区点位数
            List<ScenicLocation> scenicLocationList = iScenicLocationService.lambdaQuery()
                    .isNotNull(ScenicLocation::getLocationCode)
                    .in(ScenicLocation::getScenicId, items.stream().map(GuideOrderSt::getScenicId).collect(Collectors.toSet()))
                    .list();
            Map<Integer, Long> scenicMap = scenicLocationList.stream()
                    .collect(Collectors.groupingBy(ScenicLocation::getScenicId, Collectors.counting()));
            List<GuidePayOrderItemVo> orderItemVos = new ArrayList<>();
            for (GuideOrderSt st : items) {
                //查询订单
                List<GuidePayOrderItemVo> orderList = getOrdersBySelect(st.getAgencyId(), st.getScenicId(), st.getOrderItem(),
                        startTime, endTime);
                orderItemVos.addAll(orderList);
                st.setOrderIds(orderList.stream().map(p -> p.getOrderId()).collect(Collectors.toList()));
                st.setAllPointNum(scenicMap.get(st.getScenicId()));
                retItems.add(st);
            }

            //时间区间
            if (qry.getStartTime() == null) {
                LocalDateTime start = orderItemVos.stream().map(p -> p.getCreateTime()).min(LocalDateTime::compareTo).get();
                vo.setStartTime(DateUtil.format(start, "yyyy/MM/dd"));
            }
            if (qry.getEndTime() == null) {
                vo.setEndTime(DateUtil.format(LocalDateTime.now(), "yyyy/MM/dd"));
            }

            //排序
            if (qry.getOrderSortCol() != null) {
                if (OrderSortColEnum.ALLAMOUNT.getCode().equals(qry.getOrderSortCol())) {
                    if (OrderSortTypeEnum.ASC.getCode().equals(qry.getOrderSortType())) {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAllAmount));
                    } else {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAllAmount).reversed());
                    }
                } else if (OrderSortColEnum.ALLORDERNUM.getCode().equals(qry.getOrderSortCol())) {
                    if (OrderSortTypeEnum.ASC.getCode().equals(qry.getOrderSortType())) {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAllOrderNum));
                    } else {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAllOrderNum).reversed());
                    }
                } else if (OrderSortColEnum.ALLPOINTNUM.getCode().equals(qry.getOrderSortCol())) {
                    if (OrderSortTypeEnum.ASC.getCode().equals(qry.getOrderSortType())) {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAllPointNum));
                    } else {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAllPointNum).reversed());
                    }
                } else if (OrderSortColEnum.AGENCYAMOUNT.getCode().equals(qry.getOrderSortCol())) {
                    if (OrderSortTypeEnum.ASC.getCode().equals(qry.getOrderSortType())) {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAgencyAmount));
                    } else {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAgencyAmount).reversed());
                    }
                }
            }
            vo.setItems(retItems);
        } else {
            vo.setItems(new ArrayList<>());
        }
        return vo;
    }

    @Override
    public List<GuideOrderSt> getOrdersLimit1(GuideOrderQry qry) {

        List<GuideOrderSt> retItems = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(qry.getItems()) &&
                Constants.ALLSELECT.equals(qry.getItems().get(0))) {
            //服务类型
            qry.setItems(List.of(OrderItemEnum.AIGUIDE.getCode(), OrderItemEnum.HEADSETPHONE.getCode()));
        }
        //选择全部时，处理机构，景区id
        if (CollectionUtil.isNotEmpty(qry.getAgencyIds()) &&
                Constants.ALLSELECT.equals(qry.getAgencyIds().get(0))) {
            //查询机构
            qry.setAgencyIds(null);
        }

        if (CollectionUtil.isNotEmpty(qry.getScenicIds()) &&
                Constants.SCENICSELECT == qry.getScenicIds().get(0)) {
            qry.setScenicIds(null);
        }
        List<String> orderItems = qry.getItems();
        LocalDateTime startTime = qry.getStartTime();
        LocalDateTime endTime = qry.getEndTime();
        List<GuideOrderSt> items = guidePayOrderItemMapper.getOrderItemsByLimit(
                qry.getAgencyIds(), qry.getScenicIds(), null, orderItems, startTime, endTime);
        if (CollectionUtil.isNotEmpty(items)) {
            //查询景区点位数
            List<ScenicLocation> scenicLocationList = iScenicLocationService.lambdaQuery()
                    .isNotNull(ScenicLocation::getLocationCode)
                    .in(ScenicLocation::getScenicId, items.stream().map(GuideOrderSt::getScenicId).collect(Collectors.toSet()))
                    .list();
            Map<Integer, Long> scenicMap = scenicLocationList.stream()
                    .collect(Collectors.groupingBy(ScenicLocation::getScenicId, Collectors.counting()));
            List<GuidePayOrderItemVo> orderItemVos = new ArrayList<>();
            for (GuideOrderSt st : items) {
                //查询订单
                List<GuidePayOrderItemVo> orderList = getOrdersBySelect(st.getAgencyId(), st.getScenicId(), st.getOrderItem(),
                        startTime, endTime);
                orderItemVos.addAll(orderList);
                st.setOrderIds(orderList.stream().map(p -> p.getOrderId()).collect(Collectors.toList()));
                st.setAllPointNum(scenicMap.get(st.getScenicId()));
                retItems.add(st);
            }

            //排序
            if (qry.getOrderSortCol() != null) {
                if (OrderSortColEnum.ALLAMOUNT.getCode().equals(qry.getOrderSortCol())) {
                    if (OrderSortTypeEnum.ASC.getCode().equals(qry.getOrderSortType())) {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAllAmount));
                    } else {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAllAmount).reversed());
                    }
                } else if (OrderSortColEnum.ALLORDERNUM.getCode().equals(qry.getOrderSortCol())) {
                    if (OrderSortTypeEnum.ASC.getCode().equals(qry.getOrderSortType())) {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAllOrderNum));
                    } else {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAllOrderNum).reversed());
                    }
                } else if (OrderSortColEnum.ALLPOINTNUM.getCode().equals(qry.getOrderSortCol())) {
                    if (OrderSortTypeEnum.ASC.getCode().equals(qry.getOrderSortType())) {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAllPointNum));
                    } else {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAllPointNum).reversed());
                    }
                } else if (OrderSortColEnum.AGENCYAMOUNT.getCode().equals(qry.getOrderSortCol())) {
                    if (OrderSortTypeEnum.ASC.getCode().equals(qry.getOrderSortType())) {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAgencyAmount));
                    } else {
                        retItems.sort(Comparator.comparing(GuideOrderSt::getAgencyAmount).reversed());
                    }
                }
            }
        }
        return retItems;
    }

    public List<GuidePayOrderItemVo> getOrdersBySelect(String agencyId, Integer scenicId, String orderItem,
                                                       LocalDateTime startTime, LocalDateTime endTime) {
        return guidePayOrderItemService.getOrdersBySelect(agencyId, scenicId, orderItem, startTime, endTime);
    }


    @Override
    public List<GuidePayOrderItemVo> getOrderItems(GuideOrderItemQry qry) {
        List<GuidePayOrderItemVo> items = guidePayOrderItemService.getOrderItemsBySelect(qry);
        //订单收入-> 设置为 项目金额
        for (GuidePayOrderItemVo item : items) {
            item.setOrderAmount(item.getItemAmount());
        }

        //排序
        if (qry.getOrderSortCol() != null) {
            if (OrderSortColEnum.ORDERAMOUNT.getCode().equals(qry.getOrderSortCol())) {
                if (OrderSortTypeEnum.ASC.getCode().equals(qry.getOrderSortType())) {
                    items.sort(Comparator.comparing(GuidePayOrderItemVo::getOrderAmount));
                } else {
                    items.sort(Comparator.comparing(GuidePayOrderItemVo::getOrderAmount).reversed());
                }
            } else if (OrderSortColEnum.AGENCYAMOUNT.getCode().equals(qry.getOrderSortCol())) {
                if (OrderSortTypeEnum.ASC.getCode().equals(qry.getOrderSortType())) {
                    items.sort(Comparator.comparing(GuidePayOrderItemVo::getAgencyAmount));
                } else {
                    items.sort(Comparator.comparing(GuidePayOrderItemVo::getAgencyAmount).reversed());
                }
            }
        }
        return items;
    }

    @Override
    public void bingOrder() {
        //查询订单表中没有username的订单
        List<GuidePayOrderItem> guidePayOrders = guidePayOrderItemService.lambdaQuery()
                .isNotNull(GuidePayOrderItem::getOpenId)
                .isNull(GuidePayOrderItem::getUserName)
                .list();
        if (CollectionUtil.isNotEmpty(guidePayOrders)) {
            Set<String> openIds = guidePayOrders.stream().map(GuidePayOrderItem::getOpenId).collect(Collectors.toSet());
            //查询用户表
            List<SysUser> sysUsers = userService.selectListByOpenIds(openIds);
            if (CollectionUtil.isNotEmpty(sysUsers)) {
                List<GuidePayOrderItem> updates = new ArrayList<>();
                Map<String, String> userMap = sysUsers.stream().collect(Collectors.toMap(SysUser::getOpenId, SysUser::getUserName));
                for (GuidePayOrderItem guidePayOrder : guidePayOrders) {
                    String userName = userMap.get(guidePayOrder.getOpenId());
                    guidePayOrder.setUserName(userName);
                    updates.add(guidePayOrder);
                }
                guidePayOrderItemService.updateBatchById(updates);
            }
        }
    }

    @Override
    public List<GuideOrderSt> exportOrders(GuideOrderQry qry) {
        List<GuideOrderSt> items = guidePayOrderItemService.getOrderItemsByLimit(qry);
        if (CollectionUtil.isNotEmpty(items)) {
            //查询景区点位数
            List<ScenicLocation> scenicLocationList = iScenicLocationService.lambdaQuery()
                    .in(ScenicLocation::getScenicId, items.stream().map(GuideOrderSt::getScenicId).collect(Collectors.toSet()))
                    .list();
            Map<Integer, Long> scenicMap = scenicLocationList.stream()
                    .collect(Collectors.groupingBy(ScenicLocation::getScenicId, Collectors.counting()));
            for (GuideOrderSt st : items) {
                st.setAllPointNum(scenicMap.get(st.getScenicId()));
            }
        }
        return items;
    }

    @Override
    public GuidePayOrderItemEntity getOrderItem(String orderId) {
        GuidePayOrderItemEntity vo = new GuidePayOrderItemEntity();
        GuidePayOrderItem item = guidePayOrderItemService.lambdaQuery()
                .eq(GuidePayOrderItem::getOrderId, orderId)
                .eq(GuidePayOrderItem::getOrderItem, OrderItemEnum.AIGUIDE.getCode())
                .last("limit 1")
                .one();
        if (item != null) {
            vo.setOrderId(item.getOrderId());
            vo.setScenicId(item.getScenicId());
            vo.setScenicName(item.getScenicName());
            vo.setOrderAmount(item.getOrderAmount());
            vo.setUserName(item.getUserName());
            vo.setCreateTime(item.getCreateTime());

            vo.setItemList(guidePayOrderItemService.lambdaQuery()
                    .eq(GuidePayOrderItem::getOrderId, item.getOrderId())
                    .orderByAsc(GuidePayOrderItem::getOrderItem)
                    .list());
            List<GuidePayOrderItemRatio> ratios = guidePayOrderItemRatioService.lambdaQuery()
                    .eq(GuidePayOrderItemRatio::getOrderId, item.getOrderId())
                    .orderByAsc(GuidePayOrderItemRatio::getOrderItem)
                    .list();
            Map<String, Integer> map = new HashMap<>();
            //查询分佣比例
            if (item.getOrderItem().equals("0")) {
                List<ScenicRatio> scenicRatios = JSON.parseArray(item.getAgencyRaw(), ScenicRatio.class);
                if (scenicRatios.size() == 1) {
                    if (scenicRatios.get(0) instanceof ScenicRatio) {
                        //不处理
                    } else {
                        scenicRatios = scenicRatioService.lambdaQuery()
                                .eq(ScenicRatio::getScenicId, item.getScenicId())
                                .list();
                    }
                }
                //根据机构分组
                map = scenicRatios.stream()
                        .collect(Collectors.toMap(ScenicRatio::getAgencyId, ScenicRatio::getAgencyRatio));
            }

            for (GuidePayOrderItemRatio ratio : ratios) {
                if (ratio.getOrderItem().equals("0") && CollectionUtil.isNotEmpty(map)) {
                    ratio.setRatio(map.get(ratio.getAgencyId()));
                }
            }
            vo.setRatioList(ratios);
        }
        return vo;
    }

    public static void main(String[] args) {
        String agencyId = "QD20250521000001";
        GuidePayOrderItem guidePayOrderItem = new GuidePayOrderItem();
        guidePayOrderItem.setItemAmount(new BigDecimal("0.1"));

        List<ScenicRatio> scenicRatios = new ArrayList<>();
        ScenicRatio scenicRatio = new ScenicRatio();
        scenicRatio.setAgencyId("QD20250519000001");
        scenicRatio.setAgencyRatio(30);
        scenicRatios.add(scenicRatio);

        ScenicRatio scenicRatio1 = new ScenicRatio();
        scenicRatio1.setAgencyId("YY20250519000002");
        scenicRatio1.setAgencyRatio(20);
        scenicRatios.add(scenicRatio1);

        ScenicRatio scenicRatio2 = new ScenicRatio();
        scenicRatio2.setAgencyId("QD20250521000001");
        scenicRatio2.setAgencyRatio(40);
        scenicRatios.add(scenicRatio2);

        ScenicRatio scenicRatio3 = new ScenicRatio();
        scenicRatio3.setAgencyId("1920377976337719296");
        scenicRatio3.setAgencyRatio(10);
        scenicRatios.add(scenicRatio3);


        if (StringUtils.isNotBlank(agencyId) && agencyId.equals("QD20250521000001")) {
            BigDecimal sum = new BigDecimal("0");
            List<ScenicRatio> x = scenicRatios.stream().filter(e -> !agencyId.equals(e.getAgencyId())).collect(Collectors.toList());
            for (ScenicRatio r : x) {
                BigDecimal multiply = guidePayOrderItem.getItemAmount().multiply(new BigDecimal(r.getAgencyRatio())
                        .divide(new BigDecimal("100"))).setScale(2, RoundingMode.DOWN);
                sum = sum.add(multiply);
            }
            //平台机构分佣
            System.out.println(guidePayOrderItem.getItemAmount().subtract(sum));
        }
    }
}
