package com.iciyun.system.service.impl;

import com.iciyun.common.core.domain.entity.SysWxUser;
import com.iciyun.system.mapper.SysWxUserMapper;
import com.iciyun.system.service.ISysWxUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysWxUserServiceImpl implements ISysWxUserService {

    @Autowired
    private SysWxUserMapper sysWxUserMapper;

    @Override
    public List<SysWxUser> selectWxUserList(SysWxUser user) {
        return sysWxUserMapper.selectWxUserList(user);
    }

    @Override
    public SysWxUser selectWxUserById(Long userId) {
        return sysWxUserMapper.selectWxUserById(userId);
    }
}
