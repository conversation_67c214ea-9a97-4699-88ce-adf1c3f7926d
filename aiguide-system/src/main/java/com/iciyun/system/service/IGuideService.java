package com.iciyun.system.service;

import com.iciyun.common.core.domain.entity.*;
import com.iciyun.common.core.domain.entity.scenic.ScenicTextSpeechCmd;
import com.iciyun.system.domain.SysChatUser;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.io.File;
import java.util.List;

public interface IGuideService {

    //创建会话
    String createSession(String botId, SysChatUser curChatUser, String cozeToken);

    void clearContext(String conversationId, String cozeToken);

    Flux<String> streamChat(StreamChatCmd cmd);

    String streamChatJson(StreamChatCmd cmd, String cozeToken);

    String streamChatJsonByHttp(StreamChatCmd cmd, String cozeToken);

    List<String> streamChatByCoze(StreamChatCmd chatCmd, ScenicTextSpeechCmd cmd);

    //非流式对话
    String noStreamChat(UserChatCmd cmd, String cozeToken);

    //非流式对话状态查看
    boolean noStreamChatStatus(UserChatStatueCmd cmd);

    //非流式对话回答详情
    String noStreamChatAnswer(UserChatStatueCmd cmd);

    String userToolOutputs(UserToolOutputdCmd cmd);

    String userCancel(UserChatStatueCmd cmd);

    String userCreateMessage(UserCreateMessageCmd cmd);

    String userGetMessages(UserGetMessagesCmd cmd);

    String userGetMsg(GetMessageCmd cmd);

    String userModifyMsg(ModifyMessageCmd cmd);

    String userDelMsg(GetMessageCmd cmd);

    String userUploadFile(MultipartFile file);

    String getFileById(String fileId);

    String getVoices(GetVoicesCmd cmd);

    String cloneVoice(CloneVoiceCmd cmd);

    byte[] textSpeech(TextSpeechCmd cmd, String cozeToken);

    String justTestRetry(String name);

    byte[] textSpeechCoze(TextSpeechCmd cmd);

    void textSpeechBrowse(HttpServletResponse response, TextSpeechCmd cmd, String cozeToken);

    String textToSpeechLocal(TextSpeechCmd cmd, String cozeToken, String path);

    //语音转文字
    String transcriptions(MultipartFile file, String cozeToken);

    String transcriptions(MultipartFile file);

    String rooms(RoomsCmd cmd);

    String upOSSFiles(MultipartFile file);

    String upOSSFiles(String fileName, byte[] fileData);

    String upOSSFiles(File file, String fileName);

    String img2label(String url,String xy);

    String img2label(Long touristId,String xy);

    void resetTouristMap();

    CloseableHttpResponse streamChat(StreamChatCmd cmd, String cozeToken);

    String upHsOSSFiles(String fileName, byte[] fileData);

    String upHsOSSFiles(String fileName, File file);

    void getOSSFiles(String fileName, String localPath);

}
