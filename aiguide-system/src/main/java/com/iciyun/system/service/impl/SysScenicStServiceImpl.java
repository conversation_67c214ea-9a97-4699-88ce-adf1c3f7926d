package com.iciyun.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.common.core.domain.entity.wx.WxSysUserInfo;
import com.iciyun.common.utils.StringUtils;
import com.iciyun.system.domain.ChangeType;
import com.iciyun.system.domain.SysScenicSt;
import com.iciyun.system.domain.TokenDetail;
import com.iciyun.system.mapper.SysScenicStMapper;
import com.iciyun.system.mapper.SysUserMapper;
import com.iciyun.system.service.ISysScenicStService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iciyun.system.service.ITokenDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07 10:25:15
 */
@Service
public class SysScenicStServiceImpl extends ServiceImpl<SysScenicStMapper, SysScenicSt> implements ISysScenicStService {

    @Autowired
    private ITokenDetailService tokenDetailService;

    @Autowired
    private SysUserMapper userMapper;

    @Override
    public void addScenicSt(Long userId, String scenicId, String scenicName, String text) {
        //判断是否存在
        SysScenicSt sysScenicSt = this.lambdaQuery()
                .eq(SysScenicSt::getUserId, userId)
                .eq(SysScenicSt::getScenicId, scenicId)
                .last("limit 1")
                .one();
        if(sysScenicSt == null){
            sysScenicSt = new SysScenicSt();
            sysScenicSt.setUserId(userId);
            sysScenicSt.setScenicId(scenicId);
            sysScenicSt.setScenicName(scenicName);
            sysScenicSt.setCreateTime(LocalDateTime.now());
            if(StringUtils.isNotEmpty(text)){
                sysScenicSt.setSpeakCount(1);
            } else {
                sysScenicSt.setLabelClickCount(1);
            }
        } else {
            if(StringUtils.isNotEmpty(text)){
                sysScenicSt.setSpeakCount(sysScenicSt.getSpeakCount() == null ? 1 : sysScenicSt.getSpeakCount() + 1);
            } else {
                sysScenicSt.setLabelClickCount(sysScenicSt.getLabelClickCount() == null ? 1 : sysScenicSt.getLabelClickCount() + 1);
            }
        }

        //查询使用游豆
        QueryWrapper<TokenDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TokenDetail::getChangeType, ChangeType.OUT);
        queryWrapper.lambda().eq(TokenDetail::getUserId, userId);
        queryWrapper.lambda().eq(TokenDetail::getScenicId, Integer.valueOf(scenicId));
        queryWrapper.select("SUM(amount_incurred) as total");
        Map<String, Object> countObj = tokenDetailService.getMap(queryWrapper);
        if(CollectionUtil.isNotEmpty(countObj)){
            Long total = (Long) countObj.get("total");
            sysScenicSt.setUseBeanCount(total);
        }

        //查询剩余游豆
        SysUser wxSysUserInfo = userMapper.selectUserById(userId);
        if(wxSysUserInfo != null){
            sysScenicSt.setResidueBeanCount(wxSysUserInfo.getTokenBalance().longValueExact());
        }

        this.saveOrUpdate(sysScenicSt);
    }

    @Override
    public void addScenicStByType(Long userId, String scenicId, String scenicName, Integer type) {
        //判断是否存在
        SysScenicSt sysScenicSt = this.lambdaQuery()
                .eq(SysScenicSt::getUserId, userId)
                .eq(SysScenicSt::getScenicId, scenicId)
                .last("limit 1")
                .one();
        if(sysScenicSt != null){
            if(type == 0){
                sysScenicSt.setLbsCount(sysScenicSt.getLbsCount() == null ? 1 : sysScenicSt.getLbsCount() + 1);
            } else if(type == 1){
                sysScenicSt.setEyeCount(sysScenicSt.getEyeCount() == null? 1 : sysScenicSt.getEyeCount() + 1);
            }
        } else {
            sysScenicSt = new SysScenicSt();
            sysScenicSt.setUserId(userId);
            sysScenicSt.setScenicId(scenicId);
            sysScenicSt.setScenicName(scenicName);
            sysScenicSt.setCreateTime(LocalDateTime.now());
            if(type == 0){
                sysScenicSt.setLbsCount(1);
            } else if(type == 1){
                sysScenicSt.setEyeCount(1);
            }
        }

        //查询使用游豆
        QueryWrapper<TokenDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TokenDetail::getChangeType, ChangeType.OUT);
        queryWrapper.lambda().eq(TokenDetail::getUserId, userId);
        queryWrapper.lambda().eq(TokenDetail::getScenicId, Integer.valueOf(scenicId));
        queryWrapper.select("SUM(amount_incurred) as total");
        Map<String, Object> countObj = tokenDetailService.getMap(queryWrapper);
        if(CollectionUtil.isNotEmpty(countObj)){
            Long total = (Long) countObj.get("total");
            sysScenicSt.setUseBeanCount(total);
        }

        //查询剩余游豆
        SysUser wxSysUserInfo = userMapper.selectUserById(userId);
        if(wxSysUserInfo != null){
            sysScenicSt.setResidueBeanCount(wxSysUserInfo.getTokenBalance().longValueExact());
        }

        this.saveOrUpdate(sysScenicSt);

    }


}
