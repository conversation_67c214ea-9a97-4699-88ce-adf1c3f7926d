package com.iciyun.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.iciyun.common.enums.ChangeScoreType;
import com.iciyun.system.domain.UsUserSort;
import com.iciyun.system.domain.UserSortLevel;
import lombok.Data;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-17 09:56:59
 */
public interface IUsUserSortService extends IService<UsUserSort> {

    void addUser(UsUserSort ususerSort);

    void addUserBatch(List<UsUserSort> ususerSortList);

    void updateUser(UsUserSort ususerSort);

    void deleteUser(String userId);

    /**
     * 根据当前用户ID，查询其多叉树的多级ID
     */
    List<UserSortLevel> getUserTreeList(String userId);

    /**
     * 用户的操作，执行用户的score修改
     * 记录日志
     */
    @Deprecated
    void updateUserScore(String userId, int score, ChangeScoreType changeType, String ruleId);

    /**
     * 定期更新 redis 数据
     * 记录日志
     */
    void updateUserSort();

    /**
     * 更新用户达标字段
     */
    void updateUserGoalFlag(String userId);


}
