package com.iciyun.system.service.serviceImpl;

import com.iciyun.system.domain.UserSuggestion;
import com.iciyun.system.domain.qo.UserSuggestionQo;
import com.iciyun.system.mapper.UserSuggestionMapper;
import com.iciyun.system.service.IUserSuggestionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-15 10:57:19
 */
@Service
public class UserSuggestionServiceImpl extends ServiceImpl<UserSuggestionMapper, UserSuggestion> implements IUserSuggestionService {

    @Autowired
    private UserSuggestionMapper userSuggestionMapper;

    @Override
    public List<UserSuggestion> queryList(UserSuggestionQo qo) {
        return userSuggestionMapper.queryList(qo);
    }

    @Override
    public void topSuggestion(UserSuggestion userSuggestion) {
        int num = userSuggestionMapper.getMaxNum();
        userSuggestion.setNum(num);
        userSuggestionMapper.topSuggestion(userSuggestion);
    }
}
