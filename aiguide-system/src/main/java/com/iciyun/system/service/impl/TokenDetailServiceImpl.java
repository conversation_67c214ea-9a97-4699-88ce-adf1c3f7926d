package com.iciyun.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iciyun.system.domain.TokenDetail;
import com.iciyun.system.domain.bo.AddTokenDetailCmd;
import com.iciyun.system.domain.bo.QueryTotalProfitQry;
import com.iciyun.system.mapper.TokenDetailMapper;
import com.iciyun.system.service.ITokenDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25 10:27:41
 */
@Slf4j
@Service
public class TokenDetailServiceImpl extends ServiceImpl<TokenDetailMapper, TokenDetail> implements ITokenDetailService {

    @Autowired
    private TokenDetailMapper tokenDetailMapper;

    @Override
    public void addTokenDetail(AddTokenDetailCmd cmd) {
        TokenDetail tokenDetail = new TokenDetail(cmd);
        baseMapper.insert(tokenDetail);
    }

    @Override
    public BigDecimal queryTotalProfit(QueryTotalProfitQry qry) {
        return tokenDetailMapper.queryTotalProfit(qry);
    }
}
