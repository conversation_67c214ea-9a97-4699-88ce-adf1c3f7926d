package com.iciyun.system.service.impl;

import com.iciyun.system.domain.ScenicHeadphone;
import com.iciyun.system.mapper.ScenicHeadphoneMapper;
import com.iciyun.system.service.IScenicHeadphoneService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 耳机信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07 15:16:13
 */
@Service
public class ScenicHeadphoneServiceImpl extends ServiceImpl<ScenicHeadphoneMapper, ScenicHeadphone> implements IScenicHeadphoneService {

    @Autowired
    private ScenicHeadphoneMapper scenicHeadphoneMapper;

    @Override
    public List<ScenicHeadphone> queryList(ScenicHeadphone qo) {
        return scenicHeadphoneMapper.queryList(qo);
    }

    @Override
    public ScenicHeadphone queryByModel(ScenicHeadphone scenicHeadphone) {
        return scenicHeadphoneMapper.queryByModel(scenicHeadphone);
    }

    @Override
    public void upStatus(ScenicHeadphone scenicHeadphone) {
        scenicHeadphoneMapper.upStatus(scenicHeadphone);
    }

}
