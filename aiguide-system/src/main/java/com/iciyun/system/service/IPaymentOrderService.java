package com.iciyun.system.service;

import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import com.iciyun.common.core.domain.AjaxResult;
import com.iciyun.common.core.domain.R;
import com.iciyun.system.domain.PaymentOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import com.iciyun.system.domain.PlaceOrderRechargeCmd;
import com.iciyun.system.domain.bo.PlaceOrderCmd;
import com.iciyun.system.domain.bo.RefundCmd;

/**
 * <p>
 * 支付订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24 11:16:43
 */
public interface IPaymentOrderService extends IService<PaymentOrder> {

    AjaxResult placeOrderRecharge(PlaceOrderRechargeCmd placeOrderRechargeCmd);

    R<WxPayUnifiedOrderV3Result.JsapiResult> placeOrder(PlaceOrderCmd cmd);

    R<Object> refund(RefundCmd cmd);

}
