package com.iciyun.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iciyun.system.domain.PgInfoEntity;
import com.iciyun.system.mapper.PgInfoMapper;
import com.iciyun.system.service.IPgInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PgInfoServiceImpl extends ServiceImpl<PgInfoMapper, PgInfoEntity> implements IPgInfoService {

    @Autowired
    private PgInfoMapper pgInfoMapper;

    /**
     * 由于 pg 库切换是 通过 AOP 实现的， 所有 如果 controller 里边直接调用 mybatis-plus 的 service 方法
     * 比如 lambda 表达式， 则无法切换库， 所以需要在 service 中，通过对 mapper 方法的调用实现切库
     *
     * 事务回滚
     *  master：userService
     *  slave：bookService
     *  1 slave 数据库的操作，需要在 master 之后，这样当 bookService.save 失败，会使得 userService 回滚；
     * 如果 slave 的操作先，那当 userService 失败，无法使 bookService 回滚
     * 2  @Transactional(rollbackFor = Exception.class) 需要开启新的事物
     *  @Transactional(rollbackFor = Exception.class)
     *     public void upload(ReqDto respDto){
     *         userService.save(respDto);
     *         bookService.save(respDto);
     *     }
     *
     */
    @Override
    public List<PgInfoEntity> getPgOne() {
        List<PgInfoEntity> selectList = pgInfoMapper.selectList(new QueryWrapper<PgInfoEntity>());
        return selectList;
    }
}
