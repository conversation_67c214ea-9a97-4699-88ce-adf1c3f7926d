package com.iciyun.system.service;

import java.util.List;
import com.iciyun.system.domain.SysTouristQuestion;

/**
 * 景区常见问题对话记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
public interface ISysTouristQuestionService 
{

    void clearQuestionCache();

    /**
     * 查询景区常见问题对话记录
     * 
     * @param id 景区常见问题对话记录主键
     * @return 景区常见问题对话记录
     */
    public SysTouristQuestion selectSysTouristQuestionById(Long id);

    /**
     * 查询景区常见问题对话记录列表
     * 
     * @param sysTouristQuestion 景区常见问题对话记录
     * @return 景区常见问题对话记录集合
     */
    public List<SysTouristQuestion> selectSysTouristQuestionList(SysTouristQuestion sysTouristQuestion);


    String questionCache(Long touristId,Long questionId);

    /**
     * 新增景区常见问题对话记录
     * 
     * @param sysTouristQuestion 景区常见问题对话记录
     * @return 结果
     */
    public int insertSysTouristQuestion(SysTouristQuestion sysTouristQuestion);

    /**
     * 修改景区常见问题对话记录
     * 
     * @param sysTouristQuestion 景区常见问题对话记录
     * @return 结果
     */
    public int updateSysTouristQuestion(SysTouristQuestion sysTouristQuestion);

    /**
     * 批量删除景区常见问题对话记录
     * 
     * @param ids 需要删除的景区常见问题对话记录主键集合
     * @return 结果
     */
    public int deleteSysTouristQuestionByIds(Long[] ids);

    /**
     * 删除景区常见问题对话记录信息
     * 
     * @param id 景区常见问题对话记录主键
     * @return 结果
     */
    public int deleteSysTouristQuestionById(Long id);
}
