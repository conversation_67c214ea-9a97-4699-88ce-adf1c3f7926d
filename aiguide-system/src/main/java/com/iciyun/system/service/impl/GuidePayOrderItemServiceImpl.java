package com.iciyun.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iciyun.common.constant.Constants;
import com.iciyun.common.enums.OrderItemEnum;
import com.iciyun.common.enums.PartnerBusinessType;
import com.iciyun.system.domain.*;
import com.iciyun.system.mapper.CoopAgencyMapper;
import com.iciyun.system.mapper.GuidePayOrderItemMapper;
import com.iciyun.system.service.IAgencyAdminScenicService;
import com.iciyun.system.service.IGuidePayOrderItemService;
import com.iciyun.system.service.IScenicAdminService;
import com.iciyun.system.service.IScenicRatioService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08 16:41:31
 */
@Service
public class GuidePayOrderItemServiceImpl extends ServiceImpl<GuidePayOrderItemMapper, GuidePayOrderItem> implements IGuidePayOrderItemService {

    @Autowired
    private GuidePayOrderItemMapper guidePayOrderItemMapper;

    @Autowired
    private IScenicAdminService scenicAdminService;

    @Autowired
    private CoopAgencyMapper coopAgencyMapper;

    @Autowired
    private IScenicRatioService scenicRatioService;

    @Autowired
    private IAgencyAdminScenicService agencyAdminScenicService;


    @Value("${order.agency_id}")
    private String agencyId;

    @Override
    public List<GuidePayOrderItemVo> getOrderItemsBySelect(GuideOrderItemQry qry) {
        List<GuidePayOrderItemVo> items = guidePayOrderItemMapper.getOrderItemsBySelect(
                qry.getAgencyId(), null, qry.getOrderItem(), null, null, qry.getOrderIds());
        return items;
    }

    @Override
    public List<GuideOrderSt> getOrderItemsByLimit(GuideOrderQry qry) {
        if (Constants.ALLSELECT.equals(qry.getItems().get(0))) {
            //服务类型
            qry.setItems(List.of(OrderItemEnum.AIGUIDE.getCode(), OrderItemEnum.HEADSETPHONE.getCode()));
        }
        //选择全部时，处理机构，景区id
        if (Constants.ALLSELECT.equals(qry.getAgencyIds().get(0))) {
            List<String> agencyIds = new ArrayList<>();
            //获取用户当前机构id
            Long platfrom = scenicAdminService.lambdaQuery()
                    .eq(ScenicAdmin::getUserPhone, qry.getUserName())
                    .eq(ScenicAdmin::getBusinessCode, agencyId)
                    .eq(ScenicAdmin::getBusinessType, PartnerBusinessType.COOPERATION.getCode())
                    .count();
            if (platfrom != null && platfrom > 0) {
                List<CoopAgency> coopAgencys = coopAgencyMapper.selectList(new QueryWrapper<>());
                agencyIds = coopAgencys.stream().map(CoopAgency::getAgencyId).collect(Collectors.toList());
            } else {
                List<CoopAgency> userCoopAgencys = coopAgencyMapper.selectByUser(qry.getUserName());
                agencyIds = userCoopAgencys.stream().map(CoopAgency::getAgencyId).collect(Collectors.toList());
            }
            //查询机构
            qry.setAgencyIds(new ArrayList<>(new HashSet<>(agencyIds)));
        }

        if (Constants.SCENICSELECT == qry.getScenicIds().get(0)) {
            List<Pair> tuples = new ArrayList<>();
            List<Integer> scenicIds = new ArrayList<>();
            List<String> agencyIds = qry.getAgencyIds();
            for (String agencyId : agencyIds) {
                //获取管理员用户查看景区
                List<AgencyAdminScenic> scenicList = agencyAdminScenicService.lambdaQuery()
                        .eq(AgencyAdminScenic::getAgencyId, agencyId)
                        .eq(AgencyAdminScenic::getUserPhone, qry.getUserName())
                        .list();
                if (CollectionUtil.isNotEmpty(scenicList)) {
                    for (AgencyAdminScenic scenic : scenicList) {
                        Pair tuple = new Pair();
                        scenicIds.add(scenic.getScenicId());
                        tuple.setAgencyId(agencyId);
                        tuple.setScenicId(scenic.getScenicId());
                        tuples.add(tuple);
                    }
                } else {
                    List<ScenicRatio> adminRatioList = scenicRatioService.lambdaQuery()
                            .eq(ScenicRatio::getAgencyId, agencyId)
                            .list();
                    List<Integer> adminScenis = adminRatioList.stream().map(ScenicRatio::getScenicId).collect(Collectors.toList());
                    scenicIds.addAll(adminScenis);
                    for (Integer scenicId : adminScenis) {
                        Pair tuple = new Pair();
                        tuple.setAgencyId(agencyId);
                        tuple.setScenicId(scenicId);
                        tuples.add(tuple);
                    }
                }
            }
            qry.setAgencyIds(null);
            qry.setScenicIds(null);
            qry.setTuples(tuples);
        } else {
            List<String> delAgencyIds = new ArrayList<>();
            List<String> simpleAgencyIds = new ArrayList<>();
            //获取当前用户，在选择的机构下的景区
            List<String> agencyIds = qry.getAgencyIds();
            for(String agencyId : agencyIds){
                List<AgencyAdminScenic> scenicList = agencyAdminScenicService.lambdaQuery()
                        .eq(AgencyAdminScenic::getAgencyId, agencyId)
                        .eq(AgencyAdminScenic::getUserPhone, qry.getUserName())
                        .list();
                if(CollectionUtil.isNotEmpty(scenicList)){
                    //有关联的景区，判断当前景区是否是选择的景区， 如果不是，改机构不参数与查询
                   for(AgencyAdminScenic scenic : scenicList){
                       if(!scenic.getScenicId().equals(qry.getScenicIds().get(0))){
                           delAgencyIds.add(agencyId);
                       } else {
                           //当一个机构下有多个景区时，若和选择的机构相同时， 需要将其添加进来
                           simpleAgencyIds.add(agencyId);
                       }
                   }
                }
            }
            if(CollectionUtil.isNotEmpty(delAgencyIds)){
                agencyIds.removeAll(delAgencyIds);
            }
            if(CollectionUtil.isNotEmpty(simpleAgencyIds)){
                agencyIds.addAll(simpleAgencyIds);
            }
            qry.setAgencyIds(agencyIds);
        }

        List<String> orderItems = qry.getItems();
        LocalDateTime startTime = qry.getStartTime();
        LocalDateTime endTime = qry.getEndTime();
        List<GuideOrderSt> items = guidePayOrderItemMapper.getOrderItemsByLimit(
                qry.getAgencyIds(), qry.getScenicIds(), qry.getTuples(), orderItems, startTime, endTime);
        return items;
    }

    @Override
    public List<GuidePayOrderItemVo> getOrdersBySelect(String agencyId, Integer scenicId, String orderItem,
                                                       LocalDateTime startTime, LocalDateTime endTime) {
        return guidePayOrderItemMapper.getOrdersBySelect(agencyId, scenicId, orderItem, startTime, endTime);
    }
}
