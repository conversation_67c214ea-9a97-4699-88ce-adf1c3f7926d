package com.iciyun.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iciyun.common.enums.HeadsetPhoneStatueEnum;
import com.iciyun.common.enums.OrderItemEnum;
import com.iciyun.system.domain.GuidePayOrderItem;
import com.iciyun.system.domain.RefundOrder;
import com.iciyun.system.domain.bo.RefundCmd;
import com.iciyun.system.mapper.RefundOrderMapper;
import com.iciyun.system.service.IGuidePayOrderItemService;
import com.iciyun.system.service.IPaymentOrderService;
import com.iciyun.system.service.IRefundOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 退款订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14 17:10:09
 */
@Service
@Slf4j
public class RefundOrderServiceImpl extends ServiceImpl<RefundOrderMapper, RefundOrder> implements IRefundOrderService {

    @Autowired
    private IGuidePayOrderItemService guidePayOrderItemService;
    @Autowired
    private IPaymentOrderService paymentOrderService;

    @Override
    public void audit(Integer id) {

        RefundOrder refundOrder = this.lambdaQuery()
                .eq(RefundOrder::getId, id)
                .one();
        if (refundOrder != null) {
            RefundCmd cmd = RefundCmd.builder()
                    .orderId(refundOrder.getOldOrderId())
                    .refundOrderId(refundOrder.getOrderId())
                    .amount(refundOrder.getOrderAmount())
                    .build();
            //审核通过
            refundOrder.setOrderStatue("2");
            this.updateById(refundOrder);

            paymentOrderService.refund(cmd);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRefundOrder(String refundOrderId) {
        RefundOrder refundOrder = this.lambdaQuery()
                .eq(RefundOrder::getOrderId, refundOrderId)
                .one();
        //退款状态
        if (refundOrder != null) {
            refundOrder.setOrderStatue("1");
            this.updateById(refundOrder);

            //订单状态
            guidePayOrderItemService.lambdaUpdate()
                    .eq(GuidePayOrderItem::getOrderId, refundOrder.getOldOrderId())
                    .eq(GuidePayOrderItem::getOrderItem, OrderItemEnum.HEADSETPHONE.getCode())
                    .set(GuidePayOrderItem::getHeadsetStatue, HeadsetPhoneStatueEnum.REFUND.getCode())
                    .update();
        }
    }
}
