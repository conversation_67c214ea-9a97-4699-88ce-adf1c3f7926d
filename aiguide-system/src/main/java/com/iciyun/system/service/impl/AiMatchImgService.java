package com.iciyun.system.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iciyun.common.utils.StringUtils;
import com.iciyun.common.utils.file.FileUploadUtils;
import com.iciyun.common.utils.http.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.content.ContentBody;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class AiMatchImgService {

    public String matchImg(String aiMatchImgUrl, File imgFile, String scenicId) {
        Map<String, String> headers = new HashMap<>();

        Map<String, ContentBody> reqParam = new HashMap<>();
        reqParam.put("file", new FileBody(imgFile));
        if(StringUtils.isNotEmpty(scenicId)){
            reqParam.put("scenic_code", new StringBody(scenicId, ContentType.MULTIPART_FORM_DATA));
        }
        reqParam.put("top_n", new StringBody("1", ContentType.MULTIPART_FORM_DATA));
        log.info("reqParam: {}", reqParam);
        try {
            String response = HttpClientUtil.postFileMultiPart(aiMatchImgUrl, reqParam, headers, false);
            JSONArray jsonArray = JSONArray.parseArray(response);
            if (jsonArray != null && jsonArray.size() > 0) {
                JSONObject matchJson = jsonArray.getJSONObject(0);
                float distance = matchJson.getFloatValue("distance");
                log.info("distance: {}", distance);
                //先不判定距离
                /*if (distance <= MATCH_THRESHOLD) {
                    return matchJson.getString("label");
                }*/
               return matchJson.getString("label");
            }
        } catch (Exception e) {
            log.error("matchImg error: {}", e.getMessage());
            return "";
        }
        return "";
    }
}
