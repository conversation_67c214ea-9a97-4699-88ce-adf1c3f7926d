package com.iciyun.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iciyun.common.enums.PartnerBusinessType;
import com.iciyun.common.utils.StringUtils;
import com.iciyun.system.domain.*;
import com.iciyun.system.domain.vo.ScenicChannelVO;
import com.iciyun.system.mapper.ScenicChannelMapper;
import com.iciyun.system.service.IScenicChannelService;
import com.iciyun.system.service.IScenicLocationService;
import com.iciyun.system.service.IScenicOperateService;
import com.iciyun.system.service.ISecenicChannelOperateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 渠道信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06 10:44:43
 */
@Service
public class ScenicChannelServiceImpl extends ServiceImpl<ScenicChannelMapper, ScenicChannel> implements IScenicChannelService {

    @Autowired
    private IScenicLocationService scenicLocationService;

    @Autowired
    private IScenicOperateService scenicOperateService;

    @Autowired
    private ISecenicChannelOperateService secenicChannelOperateService;

    @Override
    public List<ScenicChannelVO> getlist(ScenicChannelCmd cmd) {
        List<ScenicChannelVO> returnList = null;
        QueryWrapper<ScenicChannel> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotEmpty(cmd.getChannelStatue())) {
            queryWrapper.lambda().eq(ScenicChannel::getChannelStatue, cmd.getChannelStatue());
        }
        if (StringUtils.isNotEmpty(cmd.getChannelName())) {
            queryWrapper.lambda().like(ScenicChannel::getChannelName, cmd.getChannelName());
        }
        if (StringUtils.isNotEmpty(cmd.getChannelPerson())) {
            queryWrapper.lambda().like(ScenicChannel::getChannelPerson, cmd.getChannelPerson());
        }
        if (StringUtils.isNotEmpty(cmd.getDetailAddress())) {
            queryWrapper.lambda().like(ScenicChannel::getDetailAddress, cmd.getDetailAddress());
        }
        if (StringUtils.isNotEmpty(cmd.getScenicName())) {
            //查询景区
            List<SecenicChannelOperate> scenicList = secenicChannelOperateService.lambdaQuery()
                    .like(SecenicChannelOperate::getScenicName, cmd.getScenicName())
                    .list();
            queryWrapper.lambda().in(ScenicChannel::getChannelId, scenicList.stream().map(SecenicChannelOperate::getChannelCode).collect(Collectors.toList()));
        }

        List<ScenicChannel> scenicChannelList = this.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(scenicChannelList)) {
            List<SecenicChannelOperate> scenicList = secenicChannelOperateService.lambdaQuery()
                    .in(SecenicChannelOperate::getChannelCode, scenicChannelList.stream().map(ScenicChannel::getChannelId).collect(Collectors.toList()))
                    .list();
            Map<String, String> scenicNameMap = scenicList.stream()
                    .collect(Collectors
                            .groupingBy(SecenicChannelOperate::getChannelCode,
                                    Collectors.mapping(SecenicChannelOperate::getScenicName, Collectors.joining(","))));
            Map<String, String> operateNameMap = scenicList.stream()
                    .collect(Collectors
                            .groupingBy(SecenicChannelOperate::getChannelCode,
                                    Collectors.mapping(SecenicChannelOperate::getOperateName, Collectors.joining(","))));
            scenicChannelList.forEach(scenicChannel -> {
                ScenicChannelVO vo = ScenicChannelVO.builder()
                       .id(scenicChannel.getId())
                       .channelId(scenicChannel.getChannelId())
                       .channelName(scenicChannel.getChannelName())
                       .channelPerson(scenicChannel.getChannelPerson())
                       .channelStatue(scenicChannel.getChannelStatue())
                       .detailAddress(scenicChannel.getDetailAddress())
                       .scenicNames(scenicNameMap.get(scenicChannel.getChannelId()))
                      .operateNames(operateNameMap.get(scenicChannel.getChannelId()))
                      .build();
                returnList.add(vo);
            });
        }
        return returnList;
    }

    @Override
    public List<ScenicLocation> getScenicByOperateId(ScenicLocation scenicLocation) {
        //查询运营下的景区
        List<ScenicLocation> scenicLocationList = scenicLocationService.lambdaQuery()
                .list();
        return scenicLocationList;
    }

    @Override
    public List<ScenicOperate> getOperatelist() {
        //查询上线的运营
        List<ScenicOperate> scenicOperates = scenicOperateService.lambdaQuery()
                .eq(ScenicOperate::getOperateStatue, 0)
                .list();
        return scenicOperates;
    }

}
