package com.iciyun.system.service.impl;

import com.coze.openapi.client.auth.OAuthToken;
import com.coze.openapi.service.auth.JWTOAuth;
import com.coze.openapi.service.auth.JWTOAuthClient;
import com.coze.openapi.service.auth.TokenAuth;
import com.coze.openapi.service.service.CozeAPI;
import com.iciyun.system.service.IGuideTkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class GuideTkServiceImpl implements IGuideTkService {

    @Value("${coze.jwt.oauth.base_url}")
    private String cozeAPIBase;

    @Value("${coze.jwt.oauth.client_id}")
    private String jwtOauthClientID;

    @Value("${coze.jwt.oauth.private_key}")
    private String jwtOauthPrivateKey;

    @Value("${coze.jwt.oauth.public_key_id}")
    private String jwtOauthPublicKeyID;

    @Override
    public String getToken() {

        JWTOAuthClient oauth = null;
        try {
            oauth = new JWTOAuthClient
                    .JWTOAuthBuilder()
                    .clientID(jwtOauthClientID)
                    .privateKey(jwtOauthPrivateKey)
                    .publicKey(jwtOauthPublicKeyID)
                    .baseURL(cozeAPIBase)
                    .build();
        } catch (Exception e) {
            log.error("{}", e);
            e.printStackTrace();
        }

        String accessToken = "";
        try {
            OAuthToken resp = oauth.getAccessToken();
            accessToken = resp.getAccessToken();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return accessToken;
    }


    public CozeAPI getCozeAPI(){
        String token = getToken();

        TokenAuth authCli = new TokenAuth(token);

        // Init the Coze client through the access_token.
        CozeAPI coze =
                new CozeAPI.Builder()
                        .baseURL("https://api.coze.cn")
                        .auth(authCli)
                        .connectTimeout(9000)
                        .readTimeout(10000)
                        .build();
        ;

        return coze;
    }


    public CozeAPI getCozeAPI(String token){
        TokenAuth authCli = new TokenAuth(token);

        // Init the Coze client through the access_token.
        CozeAPI coze =
                new CozeAPI.Builder()
                        .baseURL("https://api.coze.cn")
                        .auth(authCli)
                        .connectTimeout(9000)
                        .readTimeout(10000)
                        .build();
        ;

        return coze;
    }

}
