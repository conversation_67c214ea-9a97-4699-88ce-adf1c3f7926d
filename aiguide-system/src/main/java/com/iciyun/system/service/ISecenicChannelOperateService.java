package com.iciyun.system.service;

import com.iciyun.system.domain.ScenicLocation;
import com.iciyun.system.domain.ScenicOperate;
import com.iciyun.system.domain.SecenicChannelOperate;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 渠道下的运营对应的景区 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12 11:05:25
 */
public interface ISecenicChannelOperateService extends IService<SecenicChannelOperate> {

    void edit(SecenicChannelOperate secenicChannelOperate);

    void del(Integer id);

}
