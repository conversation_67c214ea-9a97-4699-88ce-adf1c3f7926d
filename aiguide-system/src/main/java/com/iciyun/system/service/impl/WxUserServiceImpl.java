package com.iciyun.system.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.hutool.core.collection.CollectionUtil;
import com.iciyun.common.constant.CacheConstants;
import com.iciyun.common.constant.Constants;
import com.iciyun.common.constant.HttpStatus;
import com.iciyun.common.core.domain.entity.SysDictData;
import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.common.core.domain.entity.wx.*;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.common.enums.UserTypeEnum;
import com.iciyun.common.exception.ServiceException;
import com.iciyun.common.utils.SecurityUtils;
import com.iciyun.common.utils.StringUtils;
import com.iciyun.system.service.*;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class WxUserServiceImpl implements IWxUserService {

    @Autowired
    private WxMaService wxMaService;

    @Autowired
    private ISysUserService userService;

    private final static String defaultPassword = "123456.";

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysDictTypeService dictTypeService;

    @Autowired
    private IGuideService iGuideService;

    /**
     * 1 获取微信 openID
     * 2 添加用户
     * 3 生成token
     * 4 返回token
     */
    @Override
    public SysUser getUserToken(WxMiniAppCodeCmd cmd) {
        try {
            WxMaJscode2SessionResult result = wxMaService.jsCode2SessionInfo(cmd.getCode());
            String openId = result.getOpenid();

            SysUser miniUser = userService.selectUserByOpenId(openId);
            if (miniUser != null) {

                String userName = miniUser.getUserName();
                if (StringUtils.isEmpty(userName)) {
                    String number = cmd.getNumber();
                    miniUser.setUserName(number);
                    miniUser.setPhonenumber(number);

                    userService.updateUser(miniUser);

                    //增加注册奖励
                    asyncService.addRewardBySign(miniUser.getUserName(), miniUser.getUserId());
                }

                return miniUser;
            }

            SysUser sysUser = new SysUser();
            sysUser.setUserName(cmd.getNumber());
            sysUser.setPassword(SecurityUtils.encryptPassword(defaultPassword));
            if (userService.checkUserNameUnique(sysUser)) {
                if (StringUtils.isEmpty(cmd.getUserName()) ||
                        cmd.getUserName().equals("undefined")
                ) {
                    throw new ServiceException("二维码已失效，请重新生成！", HttpStatus.WX_CODE_ERROR);
                } else {
                    log.info("邀请人：{}, 被邀请人：{}", cmd.getUserName(), cmd.getNumber());
                }
                sysUser.setOpenId(openId);
                sysUser.setPhonenumber(cmd.getNumber());
                sysUser.setStatus("0");
                sysUser.setDelFlag("0");
                sysUser.setNickName("微信用户");
                sysUser.setCreateBy("admin");
                sysUser.setCreateTime(new Date());
                sysUser.setUserType(UserTypeEnum.WXXCX.getCode());
                sysUser.init();
                int count = userService.insertUser(sysUser);
                if (count > 0) {
                    WxSysUserInfo regiestUser = userService.selectWxUserInfo(cmd.getNumber());
                    //绑定推荐人
                    if(!regiestUser.getUserName().equals(cmd.getUserName())){
                        asyncService.bindCode(cmd.getUserName(), regiestUser.getUserName(), regiestUser.getUserId());
                    }
                    //增加注册奖励
                    asyncService.addRewardBySign(regiestUser.getUserName(), regiestUser.getUserId());
                }
            }
            return sysUser;
        } catch (WxErrorException e) {
            log.error("获取openid异常", e);
            throw new ServiceException("获取openid异常", HttpStatus.WX_MINI_APP_REQUEST_ERROR);
        }
    }

    @Override
    public Map<String, String> getPhoneNumber(String code) {
        Map<String, String> map = new HashMap<>();
        try {
            WxMaPhoneNumberInfo info = wxMaService.getUserService().getPhoneNoInfo(code);
            map.put("phoneNumber", info.getPurePhoneNumber());
            //放开邀请登录
            map.put("loginFlag", "Login");
            //获取检查开关
            /*List<SysDictData> data = dictTypeService.selectDictDataByType(Constants.SYS_LOGIN_SWITCH);
            if(CollectionUtil.isNotEmpty(data)){
                for(SysDictData sysDictData : data){
                    if(Constants.LOGIN_SWITCH.equals(sysDictData.getDictLabel())){
                        String val = sysDictData.getDictValue();
                        if(Constants.LOGIN_SWITCH_VAL.equals(val)){
                            log.info("checkLogin is true");
                            map.put("loginFlag", "Login");
                            return map;
                        }
                    }
                }
            }
            WxUserCmd cmd = new WxUserCmd();
            cmd.setUserName(info.getPurePhoneNumber());
            WxSysUserInfo wxSysUserInfo = getWxUserInfo(cmd);
            if (wxSysUserInfo == null) {
                map.put("loginFlag", "noLogin");
            } else {
                map.put("loginFlag", "Login");
            }
            */
        } catch (WxErrorException e) {
            log.error("获取手机号异常", e);
            return null;
        }
        return map;
    }

    @Override
    public WxSysUserInfo getWxUserInfo(WxUserCmd cmd) {
        return userService.selectWxUserInfo(cmd.getUserName());
    }

    @Override
    public String getUserCode(String userName) {
        String key = CacheConstants.userCodeKey + userName;
        return redisCache.getCacheObject(key);
    }

    @Override
    public void updateWxUser(UpdateWxUserCmd cmd) {
        userService.updateWxUser(cmd);
    }

    @Override
    public String getUnlimitedQRCode(WxGetCodeCmd cmd) {
        try {
            File file = wxMaService.getQrcodeService().createWxaCodeUnlimit(cmd.getScene(), cmd.getPage(), true,
                    null, 430, false, null, false);
            if (file != null) {
                //上传
                long time = System.currentTimeMillis();
                String name = "wxcode/" + time + ".jpg";
                String path = iGuideService.upOSSFiles(file, name);
                if(StringUtils.isNotEmpty(path)){
                    return path;
                }
            }
        } catch (Exception e) {
            log.error("生成二维码异常", e);
        }
        return null;
    }
}
