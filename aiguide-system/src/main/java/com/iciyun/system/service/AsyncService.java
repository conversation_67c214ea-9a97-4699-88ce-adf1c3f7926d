package com.iciyun.system.service;

import com.aliyun.oss.OSS;
import com.coze.openapi.client.chat.model.ChatEvent;
import com.iciyun.common.core.domain.entity.TextSpeechCmd;
import com.iciyun.common.core.domain.entity.scenic.ScenicTextSpeechCmd;
import com.iciyun.common.cqe.SaveScenicSpotBrowseHistoryCmd;
import com.iciyun.system.domain.SysChatUser;
import io.reactivex.Flowable;

import java.util.concurrent.CountDownLatch;

public interface AsyncService {

    void insertScenicBrowseHistory(SaveScenicSpotBrowseHistoryCmd cmd);
    void updateUserRecCode(String code, Long userId, String userName);

    void bindCode(String userRecCode, String userName, Long userId);

    void addRewardBySign(String userName, Long userId);

    void doCache(TextSpeechCmd.ScenicCache scenicCache, String filePath);

    void delBizByUserName(String userName, Integer scenicId);

    void delBizByUserId(Long userId, Integer scenicId);
}
