package com.iciyun.system.service;

import com.iciyun.system.domain.*;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08 16:41:31
 */
public interface IGuidePayOrderItemService extends IService<GuidePayOrderItem> {

    List<GuidePayOrderItemVo> getOrderItemsBySelect(GuideOrderItemQry qry);

    List<GuideOrderSt> getOrderItemsByLimit(GuideOrderQry qry);

    List<GuidePayOrderItemVo> getOrdersBySelect(String agencyId, Integer scenicId, String orderItem,
                                                LocalDateTime startTime, LocalDateTime endTime);
}
