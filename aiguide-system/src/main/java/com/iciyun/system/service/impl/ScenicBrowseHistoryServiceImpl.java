package com.iciyun.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iciyun.common.cqe.SaveScenicSpotBrowseHistoryCmd;
import com.iciyun.system.domain.ScenicBrowseHistory;
import com.iciyun.system.mapper.ScenicBrowseHistoryMapper;
import com.iciyun.system.service.IScenicBrowseHistoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20 14:53:14
 */
@Service
public class ScenicBrowseHistoryServiceImpl extends ServiceImpl<ScenicBrowseHistoryMapper, ScenicBrowseHistory> implements IScenicBrowseHistoryService {

    @Autowired
    private ScenicBrowseHistoryMapper scenicBrowseHistoryMapper;

    @Override
    public void saveScenicSpotBrowseHistory(SaveScenicSpotBrowseHistoryCmd cmd) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = LocalDateTime.now().format(formatter);
        LambdaQueryWrapper<ScenicBrowseHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ScenicBrowseHistory::getUserId, cmd.getUserId())
                .eq(ScenicBrowseHistory::getScenicName, cmd.getScenicName())
                .apply("to_char(browse_time, 'YYYY-MM-DD') = {0}", formattedDate);

        List<ScenicBrowseHistory> scenicBrowseHistories = scenicBrowseHistoryMapper.selectList(queryWrapper);

        if (scenicBrowseHistories == null || scenicBrowseHistories.isEmpty()) {
            ScenicBrowseHistory scenicBrowseHistory = new ScenicBrowseHistory(cmd);
            scenicBrowseHistoryMapper.insert(scenicBrowseHistory);
        }

    }
}
