package com.iciyun.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iciyun.common.enums.*;
import com.iciyun.common.exception.ServiceException;
import com.iciyun.common.utils.LambadaTools;
import com.iciyun.common.utils.uuid.IdHutool;
import com.iciyun.system.domain.UsScoreChange;
import com.iciyun.system.domain.UsSortChange;
import com.iciyun.system.domain.UsUserSort;
import com.iciyun.system.domain.UserSortLevel;
import com.iciyun.system.mapper.UsUserSortMapper;
import com.iciyun.system.service.IUsScoreChangeService;
import com.iciyun.system.service.IUsSortChangeService;
import com.iciyun.system.service.IUsUserSortService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-17 09:56:59
 */
@Service
public class UsUserSortServiceImpl extends ServiceImpl<UsUserSortMapper, UsUserSort> implements IUsUserSortService {

    private static Integer SORT_SCORE = 1000;

    @Lazy
    @Autowired
    UsUserSortServiceImpl self;

    @Autowired
    private UsUserSortMapper usUserSortMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    static String userSortRedisKey = "user_sort";

    @Autowired
    private IUsSortChangeService usSortChangeService;

    private Lock lock = new ReentrantLock(true);


    @Autowired
    private IUsScoreChangeService usScoreChangeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addUser(UsUserSort ususerSort) {
        //设置当前用户排序
        ususerSort.setUserSort(getUserSort());
        ususerSort.setStatue(UserIntStatus.OK);
        ususerSort.setCreateTime(LocalDateTime.now());
        ususerSort.setUpdateTime(LocalDateTime.now());
        ususerSort.setUserScore(SORT_SCORE);
        self.save(ususerSort);
        //更新redis排名数据
        updateUserSortRedis();
    }

    /**
     * 更新redis排名数据
     */
    public void updateUserSortRedis() {
        Boolean flag = redisTemplate.delete(userSortRedisKey);
        if (flag) {
            writeCurUserSortRedis();
        }
    }

    @Override
    public void addUserBatch(List<UsUserSort> ususerSortList) {
        long lastUserSort = getUserSort();
        ususerSortList.stream().forEach(LambadaTools.forEachWithIndex((ususerSort, index) -> {
            ususerSort.setUserSort(index + lastUserSort);
            ususerSort.setStatue(UserIntStatus.OK);
            ususerSort.setCreateTime(LocalDateTime.now());
            ususerSort.setUpdateTime(LocalDateTime.now());
            ususerSort.setUserScore(SORT_SCORE);
        }));
        self.saveBatch(ususerSortList);
        //更新redis排名数据
        updateUserSortRedis();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(UsUserSort ususerSort) {
        UsUserSort userSort = getUserByStatue(ususerSort.getUserId(), null);
        if (userSort == null) {
            throw new ServiceException(SysExceptionType.USER_SORT_NOT_EXIST.getMessage(),
                    SysExceptionType.USER_SORT_NOT_EXIST.getCode());
        }
        userSort.setStatue(ususerSort.getStatue());
        usUserSortMapper.updateById(userSort);
        //更新redis排名数据
        updateUserSortRedis();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUser(String userId) {
        UsUserSort userSort = getUserByStatue(userId, null);
        if (userSort == null) {
            throw new ServiceException(SysExceptionType.USER_SORT_NOT_EXIST.getMessage(),
                    SysExceptionType.USER_SORT_NOT_EXIST.getCode());
        }
        userSort.setStatue(UserIntStatus.DELETED);
        usUserSortMapper.updateById(userSort);
        //更新redis排名数据
        updateUserSortRedis();
    }

    /**
     * 查询用户的排名
     */
    public Long getUserSort() {
        Long userSort = 0L;
        //设置当前用户排序
        UsUserSort lastUser = self.lambdaQuery()
                .eq(UsUserSort::getStatue, UserIntStatus.OK)
                .orderByDesc(UsUserSort::getUserSort)
                .orderByDesc(UsUserSort::getUserId)
                .last("limit 1")
                .one();
        if (lastUser == null) {
            userSort = 1L;
        } else {
            userSort = lastUser.getUserSort() + 1;
        }
        return userSort;
    }

    /**
     * 查询用户
     *
     * @param userId
     * @return
     */
    public UsUserSort getUserByStatue(String userId, UserIntStatus statue) {
        //查询当前用户的排名
        LambdaQueryWrapper<UsUserSort> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UsUserSort::getUserId, userId);
        wrapper.eq(ObjectUtil.isNotNull(statue), UsUserSort::getStatue, statue);
        UsUserSort userSort = usUserSortMapper.selectOne(wrapper);
        return userSort;
    }

    @Override
    public List<UserSortLevel> getUserTreeList(String userId) {
        List<UserSortLevel> retUserSortList = new ArrayList<>();
        //查询当前用户的排名
        UsUserSort userSort = getUserByStatue(userId, UserIntStatus.OK);
        if (userSort == null) {
            throw new ServiceException(SysExceptionType.USER_SORT_NOT_EXIST.getMessage(),
                    SysExceptionType.USER_SORT_NOT_EXIST.getCode());
        }

        if (userSort.getUserSort() <= 1) {
            throw new ServiceException(SysExceptionType.USER_SORT_FIRST.getMessage(),
                    SysExceptionType.USER_SORT_FIRST.getCode());
        }

        //TODO 查询当前层级树 -- 默认 5人，3层
        //每个树杈 5人
        int treeNodeData = 5;
        long beforeSort_l = 0l;
        long beforeSort_ll = 0l;
        long beforeSort_lll = 0l;

        //获取前3层的排名
        beforeSort_l = getBeforeSortBySort(userSort.getUserSort(), treeNodeData);
        if (beforeSort_l > 1) {
            beforeSort_ll = getBeforeSortBySort(userSort.getUserSort(), treeNodeData);
        }
        if (beforeSort_ll > 1) {
            beforeSort_lll = getBeforeSortBySort(userSort.getUserSort(), treeNodeData);
        }

        //从redis获取树 list， 使用redis 存储，
        //解决一下场景： 假设某用户的排名是15，此时如果进行的定期的排名修改，排名由15改编成 18，
        // 但是只完成一部分的用户排名，此时如果获取用户排名则不是修改完成的排名。
        // 此时从 redis 获取 用户未修改之前的排名，等用户排名修改完成之后，跟新redis排名，保证数据的一致性<lock + 乐观锁>分布式锁
        if (!redisTemplate.hasKey(userSortRedisKey)) {
            //查询当前用户的排名
            writeCurUserSortRedis();
        }
        //根据排名，从redis获取 userid
        if (beforeSort_l > 1) {
            String levelFiruserId = (String) redisTemplate.opsForList().index(userSortRedisKey, beforeSort_l - 1);
            retUserSortList.add(UserSortLevel.builder()
                    .userId(levelFiruserId)
                    .userSort(beforeSort_l)
                    .userLevel(UserSortLevelType.lEVEL_1)
                    .build());
        }
        if (beforeSort_ll > 1) {
            String levelSenuserId = (String) redisTemplate.opsForList().index(userSortRedisKey, beforeSort_ll - 1);
            retUserSortList.add(UserSortLevel.builder()
                    .userId(levelSenuserId)
                    .userSort(beforeSort_ll)
                    .userLevel(UserSortLevelType.lEVEL_2)
                    .build());
        }
        if (beforeSort_lll > 1) {
            String levelThiuserId = (String) redisTemplate.opsForList().index(userSortRedisKey, beforeSort_lll - 1);
            retUserSortList.add(UserSortLevel.builder()
                    .userId(levelThiuserId)
                    .userSort(beforeSort_lll)
                    .userLevel(UserSortLevelType.lEVEL_3)
                    .build());
        }
        return retUserSortList;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateUserScore(String userId, int score, ChangeScoreType changeType, String ruleId) {
        try {
            lock.lock();
            self.excuteUserScore(userId, score, changeType, ruleId);
        } finally {
            lock.unlock();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void excuteUserScore(String userId, int score, ChangeScoreType changeType, String ruleId) {
        //查询当前用户的排名
        UsUserSort userSort = getUserByStatue(userId, UserIntStatus.OK);
        if (userSort == null) {
            throw new ServiceException(SysExceptionType.USER_SORT_NOT_EXIST.getMessage(),
                    SysExceptionType.USER_SORT_NOT_EXIST.getCode());
        }

        int curScore = 0;
        if (changeType.equals(ChangeScoreType.ADD)) {
            curScore = userSort.getUserScore() + score;
        } else {
            curScore = userSort.getUserScore() - score;
        }

        //记录日志
        UsScoreChange usScoreChange = UsScoreChange.builder()
                .ruleId(ruleId)
                .changeScore(score)
                .beforeScore(userSort.getUserScore())
                .curScore(curScore)
                .changeType(changeType.getInfo())
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .userId(userId)
                .scoreChangeId(IdHutool.gen())
                .build();
        usScoreChangeService.save(usScoreChange);

        //修改用户的 score
        userSort.setUserScore(curScore);
        usUserSortMapper.updateById(userSort);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserSort() {
        if (!redisTemplate.hasKey(userSortRedisKey)) {
            writeCurUserSortRedis();
        } else {
            List<UsSortChange> usSortChangeList = new ArrayList<>();
            //写入redis
            List<UsUserSort> usrSortChangeList = changeUserScore();
            usrSortChangeList.stream().forEach(usUserSort -> {
                //记录日志
                long beforeSort = usUserSort.getBeforeUserSort();
                long curSort = usUserSort.getUserSort();
                UsSortChange usSortChange = UsSortChange.builder()
                        .beforeSort(beforeSort)
                        .sortChangeId(IdHutool.gen())
                        .curSort(curSort)
                        .userId(usUserSort.getUserId())
                        .createTime(LocalDateTime.now())
                        .updateTime(LocalDateTime.now())
                        .build();
                if (beforeSort > curSort) {
                    usSortChange.setChangeStatue(SortUpDownType.DOWN);
                } else {
                    usSortChange.setChangeStatue(SortUpDownType.UP);
                }
                usSortChangeList.add(usSortChange);
            });

            //写入日志
            usSortChangeService.saveBatch(usSortChangeList);
        }
    }

    @Override
    public void updateUserGoalFlag(String userId) {
        UsUserSort userSort = getUserByStatue(userId, null);
        if (userSort == null) {
            throw new ServiceException(SysExceptionType.USER_SORT_NOT_EXIST.getMessage(),
                    SysExceptionType.USER_SORT_NOT_EXIST.getCode());
        }
        userSort.setGoalFlag(UserGoalType.YES);
        usUserSortMapper.updateById(userSort);
    }

    /**
     * 多叉树公式  [(n - 2) / K] + 1
     */
    public Long getBeforeSortBySort(Long userSort, int treeNodeData) {
        return ((userSort - 2) / treeNodeData) + 1;
    }

    /**
     * 根据元素当前位置, 树杈 获取其层级
     */
    public static int getLevel(long sort, int treeNodeData) {

        int n = 0;
        int sum = 0;
        while ((sum += Math.pow(treeNodeData, n)) < sort) {
            n++;
        }
        return n + 1;
    }

    /**
     * 排序规则
     * ① 将所有用户根据上次的排序，构建 X组 Y层 的树形结构
     * ② 从第一层开始，在每一层中找到，本次需要调换位置的 userid， 即未达标的用户 ，例如一层的用户 1 未达标，获取 userid_1
     * ③ 然后根据找到的 userid_1的层级 l，与 l+1 成中达标的用户，进行调换位置，如果在 l+1 中，没有找到，则继续从  l+2 中查找，
     * 直到找到 达标的用户，调换位置，如果没有找到，则不调换。
     * ④ 如果在遍历时，该位置的用户已经发生过位置调换，则跳过该位置的用户，继续遍历。
     * ⑤ 遍历完成后，将所有用户的排序，写入数据库。
     * ⑥ 查询用户，根据sort排序，然后将 userid，写入redis
     */
    public List<UsUserSort> changeUserScore() {

        //TODO 查询当前层级树 -- 默认 5人，3层
        //每个树杈 5人
        int treeNodeData = 5;

        //查询当前用户
        LambdaQueryWrapper<UsUserSort> wList = new LambdaQueryWrapper<>();
        wList.eq(UsUserSort::getStatue, UserIntStatus.OK);
        List<UsUserSort> userSortList = usUserSortMapper.selectList(wList);
        Map<String, UsUserSort> usUserSortMap = userSortList.stream().collect(Collectors.toMap(UsUserSort::getUserId, item -> item));

        Long size = redisTemplate.opsForList().size(userSortRedisKey);
        //金字塔
        List<UsUserSort> userSortTree = new ArrayList<>();
        List<String> beforeUserIdTree = redisTemplate.opsForList().range(userSortRedisKey, 0, size - 1);
        beforeUserIdTree.stream().forEach(LambadaTools.forEachWithIndex((treeNode, index) -> {
            UsUserSort usUserSort = usUserSortMap.get(treeNode);
            //根据 index 获取 level
            usUserSort.setLevel(getLevel(index + 1, treeNodeData));
            userSortTree.add(usUserSort);
        }));
        //未完成目标分层用户
        Map<Integer, List<UsUserSort>> noGoalLevelMap = userSortTree.stream().filter(l -> l.getGoalFlag().equals(UserGoalType.NO)).collect(Collectors.groupingBy(UsUserSort::getLevel));
        Map<Integer, List<UsUserSort>> yesGoalLevelMap = userSortTree.stream().filter(l -> l.getGoalFlag().equals(UserGoalType.YES)).collect(Collectors.groupingBy(UsUserSort::getLevel));

        //排序结果，只包含交换排序的用户
        List<UsUserSort> retUserSortList = new ArrayList<>();

        noGoalLevelMap.forEach((k, v) -> {
            int curLevel = k;
            //获取当前层级的用户
            List<UsUserSort> curLevelUserList = v;

            int curLevelSize = curLevelUserList.size();

            //TODO 如果配置了往下找几层，则往下找几层
            int sysNextLevel = 0;

            //获取当前层级下n层的完成用户
            List<UsUserSort> nextLevelUserList = getNextLevelUserList(curLevel, yesGoalLevelMap, curLevelSize, sysNextLevel);

            if (CollUtil.isNotEmpty(nextLevelUserList)) {
                //TODO 调用API 给完成的用户排序
                List<UsUserSort> curSortLevelUserList = curLevelUserList;
                //交换
                curSortLevelUserList.stream().forEach(LambadaTools.forEachWithIndex((curUser, index) -> {
                    //获取当前用户的 下一个 完成用户
                    UsUserSort nextUser = nextLevelUserList.get(index);
                    if (nextUser != null) {

                        long curUserSort = curUser.getUserSort();
                        long nextUserSort = nextUser.getUserSort();

                        //交换
                        curUser.setBeforeUserSort(curUserSort);
                        curUser.setUserSort(nextUserSort);
                        //是否达标复位
                        curUser.setGoalFlag(UserGoalType.NO);
                        //分值复位
                        curUser.setUserScore(SORT_SCORE);

                        nextUser.setBeforeUserSort(nextUserSort);
                        nextUser.setUserSort(curUserSort);
                        //是否达标复位
                        nextUser.setGoalFlag(UserGoalType.NO);
                        //分值复位
                        nextUser.setUserScore(SORT_SCORE);

                        retUserSortList.add(curUser);
                        retUserSortList.add(nextUser);

                        // 排除 nxet leve 中已经完成交换的用户
                        nextLevelUserList.remove(nextUser);

                    }
                }));
            }
        });

        //更新用户排名
        if (CollUtil.isNotEmpty(retUserSortList)) {
            self.updateBatchById(retUserSortList);
        }
        //删除 redis
        redisTemplate.delete(userSortRedisKey);
        writeCurUserSortRedis();
        return retUserSortList;
    }

    /*
     * 获取所有下一层的用户
     */
    public List<UsUserSort> getNextLevelUserList(int curLevel,
                                                 Map<Integer, List<UsUserSort>> yesGoalLevelMap,
                                                 int nextUseMax,
                                                 int sysNextLevel) {
        List<UsUserSort> retUserList = new ArrayList<>();
        int keySize = yesGoalLevelMap.keySet().size();
        if (sysNextLevel > 0) {
            int maxLevel = curLevel + 1 + sysNextLevel;
            if (maxLevel > keySize) {
                maxLevel = keySize;
            }
            while (curLevel <= maxLevel) {
                if(retUserList.size() >= nextUseMax){
                    return retUserList;
                }
                retUserList.addAll(yesGoalLevelMap.get(curLevel + 1));
                curLevel = curLevel + 1;
            }
        } else {
            while (curLevel <= keySize) {
                if(retUserList.size() >= nextUseMax){
                    return retUserList;
                }
                retUserList.addAll(yesGoalLevelMap.get(curLevel + 1));
                curLevel = curLevel + 1;
            }
        }
        return retUserList;
    }

    /**
     * 将用户的userid，写入redis
     */
    public List<UsUserSort> writeCurUserSortRedis() {
        //查询当前用户的排名
        LambdaQueryWrapper<UsUserSort> cList = new LambdaQueryWrapper<>();
        cList.eq(UsUserSort::getStatue, UserIntStatus.OK);
        //排序 先使用 sort 排序，再使用 id 排序
        cList.orderByAsc(UsUserSort::getUserSort);
        cList.orderByAsc(UsUserSort::getId);
        List<UsUserSort> userSortList = usUserSortMapper.selectList(cList);
        List<String> userIdSortList = userSortList.stream().map(UsUserSort::getUserId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(userIdSortList)) {
            redisTemplate.opsForList().rightPushAll(userSortRedisKey, userIdSortList);
        }
        return userSortList;
    }
}
