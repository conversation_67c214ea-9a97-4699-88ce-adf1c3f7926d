package com.iciyun.system.service;

import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.common.core.domain.entity.wx.*;
import com.iciyun.common.cqe.SaveExplanationStyleCmd;

import java.util.Map;

public interface IWxUserService {

    SysUser getUserToken(WxMiniAppCodeCmd cmd);

    Map<String, String> getPhoneNumber(String code);

    WxSysUserInfo getWxUserInfo(WxUserCmd cmd);

    String getUserCode(String userName);

    void updateWxUser(UpdateWxUserCmd cmd);

    String getUnlimitedQRCode(WxGetCodeCmd cmd);
}
