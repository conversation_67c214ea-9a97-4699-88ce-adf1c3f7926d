package com.iciyun.system.service;

import com.iciyun.system.domain.ScenicGuide;
import com.baomidou.mybatisplus.extension.service.IService;
import com.iciyun.system.domain.qo.ScenicGuideQo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-10 14:11:00
 */
public interface IScenicGuideService extends IService<ScenicGuide> {

    void upScenic(int scenicId, String scenicName, String scenicAddress);

    List<ScenicGuide> queryList(ScenicGuideQo qo);

    void audit(ScenicGuide scenicGuide);
}
