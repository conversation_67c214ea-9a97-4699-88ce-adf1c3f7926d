package com.iciyun.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iciyun.system.domain.CoopAgency;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 景区合作机构表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12 15:08:00
 */
@Mapper
public interface CoopAgencyMapper extends BaseMapper<CoopAgency> {

    List<CoopAgency> selectByUser(@Param("userName") String userName);
    List<CoopAgency> selectByScenic(@Param("scenicId") Integer scenicId, @Param("userName") String userName);

}
