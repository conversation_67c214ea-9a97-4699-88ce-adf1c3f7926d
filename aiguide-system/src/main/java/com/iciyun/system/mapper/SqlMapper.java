package com.iciyun.system.mapper;

import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2022/3/12 10:15.
 */
public interface SqlMapper {

    /**
     * @param sql：原始sql语句
     * @return：受影响的行数
     */
    Integer insert(String sql);

    /**
     * @param sql：原始sql语句
     * @return：受影响的行数
     */
    Integer delete(String sql);

    /**
     * @param sql：原始sql语句
     * @return：受影响的行数
     */
    Integer update(String sql);

    void exec(String sql);

    /**
     * 查询一个 List 集合
     * @param sql 示例：select * from user;
     * @return
     */
    List selectList(@Param("sql") String sql);

    /**
     * 查询一个 Map
     * @param sql 示例：select * from user limit 1;
     * @return
     */
    Map<String,Object> selectMap(@Param("sql") String sql);

    /**
     * 查询一个 String 字段
     * @param sql 示例：select name from user limit 1;
     * @return
     */
    String selectStr(String sql);

    /**
     * 查询一个 Integer 字段
     * @param sql 示例：select age from user limit 1;
     * @return
     */
    Integer selectInt(String sql);

    /**
     * 查询一个 BigDecimal 字段
     * @param sql 示例：select amount from user limit 1;
     * @return
     */
    BigDecimal selectBigDecimal(String sql);

    /**
     * 查询一个 Long 字段
     * @param sql 示例：select id from user limit 1;
     * @return
     */
    Long selectLong(String sql);

    /**
     * 查询总数
     * @param sql 示例：select count(*) from user;
     * @return
     */
    Integer selectCount(String sql);

}
