package com.iciyun.system.mapper;

import com.iciyun.system.domain.TokenDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iciyun.system.domain.bo.QueryTotalProfitQry;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25 10:27:41
 */
@Mapper
public interface TokenDetailMapper extends BaseMapper<TokenDetail> {

    BigDecimal queryTotalProfit(QueryTotalProfitQry qry);
}
