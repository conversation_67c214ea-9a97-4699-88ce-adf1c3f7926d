package com.iciyun.system.mapper;

import com.iciyun.system.domain.ScenicGuide;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iciyun.system.domain.qo.ScenicGuideQo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-10 14:11:00
 */
@Mapper
public interface ScenicGuideMapper extends BaseMapper<ScenicGuide> {

    void upScenic(ScenicGuide scenicGuide);

    List<ScenicGuide> queryList(ScenicGuideQo qo);

    void audit(ScenicGuide scenicGuide);
}
