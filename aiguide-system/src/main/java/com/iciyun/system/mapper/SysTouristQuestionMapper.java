package com.iciyun.system.mapper;

import java.util.List;
import com.iciyun.system.domain.SysTouristQuestion;

/**
 * 景区常见问题对话记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
public interface SysTouristQuestionMapper 
{
    /**
     * 查询景区常见问题对话记录
     * 
     * @param id 景区常见问题对话记录主键
     * @return 景区常见问题对话记录
     */
    public SysTouristQuestion selectSysTouristQuestionById(Long id);

    /**
     * 查询景区常见问题对话记录列表
     * 
     * @param sysTouristQuestion 景区常见问题对话记录
     * @return 景区常见问题对话记录集合
     */
    public List<SysTouristQuestion> selectSysTouristQuestionList(SysTouristQuestion sysTouristQuestion);

    /**
     * 新增景区常见问题对话记录
     * 
     * @param sysTouristQuestion 景区常见问题对话记录
     * @return 结果
     */
    public int insertSysTouristQuestion(SysTouristQuestion sysTouristQuestion);

    /**
     * 修改景区常见问题对话记录
     * 
     * @param sysTouristQuestion 景区常见问题对话记录
     * @return 结果
     */
    public int updateSysTouristQuestion(SysTouristQuestion sysTouristQuestion);

    /**
     * 删除景区常见问题对话记录
     * 
     * @param id 景区常见问题对话记录主键
     * @return 结果
     */
    public int deleteSysTouristQuestionById(Long id);

    /**
     * 批量删除景区常见问题对话记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysTouristQuestionByIds(Long[] ids);
}
