package com.iciyun.system.mapper;

import com.iciyun.system.domain.UserRec;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iciyun.system.domain.UserRecVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 推荐关系 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07 18:20:38
 */
@Mapper
public interface UserRecMapper extends BaseMapper<UserRec> {

    List<UserRecVo> selectUserRecList(@Param("userName") String userName);
}
