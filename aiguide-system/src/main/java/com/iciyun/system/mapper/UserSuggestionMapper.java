package com.iciyun.system.mapper;

import com.iciyun.system.domain.UserSuggestion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iciyun.system.domain.qo.UserSuggestionQo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-15 10:57:19
 */
@Mapper
public interface UserSuggestionMapper extends BaseMapper<UserSuggestion> {

    List<UserSuggestion> queryList(UserSuggestionQo qo);

    int getMaxNum();

    void topSuggestion(UserSuggestion userSuggestion);
}
