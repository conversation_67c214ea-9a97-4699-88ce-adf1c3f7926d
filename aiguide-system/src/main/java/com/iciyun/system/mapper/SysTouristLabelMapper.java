package com.iciyun.system.mapper;

import com.iciyun.system.domain.SysTouristLabel;
import com.iciyun.system.domain.bo.TouristAttractionsQry;
import com.iciyun.system.domain.dto.LabelCacheInfoDto;
import com.iciyun.system.domain.dto.SysTouristLabelQueryParam;
import com.iciyun.system.domain.qo.LabelsQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 景区标签Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface SysTouristLabelMapper {
    /**
     * 查询景区标签
     *
     * @param id 景区标签主键
     * @return 景区标签
     */
    public SysTouristLabel selectSysTouristLabelById(Long id);

    /**
     * 查询景区标签列表
     *
     * @param sysTouristLabel 景区标签
     * @return 景区标签集合
     */
    public List<SysTouristLabel> selectSysTouristLabelList(SysTouristLabel sysTouristLabel);

    public List<SysTouristLabel> selectSysTouristLabelList_Simple(SysTouristLabel sysTouristLabel);


    List<LabelCacheInfoDto> labelCacheInfo();

    /**
     * 新增景区标签
     *
     * @param sysTouristLabel 景区标签
     * @return 结果
     */
    public int insertSysTouristLabel(SysTouristLabel sysTouristLabel);

    /**
     * 修改景区标签
     *
     * @param sysTouristLabel 景区标签
     * @return 结果
     */
    public int updateSysTouristLabel(SysTouristLabel sysTouristLabel);

    void updateNote(SysTouristLabel sysTouristLabel);

    void resetCache(SysTouristLabel sysTouristLabel);

    /**
     * 删除景区标签
     *
     * @param id 景区标签主键
     * @return 结果
     */
    public int deleteSysTouristLabelById(Long id);

    /**
     * 批量删除景区标签
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysTouristLabelByIds(Long[] ids);

    List<SysTouristLabel> selectListByParam(SysTouristLabelQueryParam param);

    List<SysTouristLabel> selectSyncTouris(SysTouristLabel condition);

    /**
     * 查询 1 条未缓存的 label -- 正序
     *
     * @return
     */
    SysTouristLabel selectOne_unCache_asc();

    /**
     * 查询 1 条音频未缓存的 label -- 正序
     *
     * @return
     */
    SysTouristLabel selectOne_unAudioCache_asc();

    /**
     * 查询 1 条音频未缓存的 label -- 倒序
     *
     * @return
     */
    SysTouristLabel selectOne_unAudioCache_desc();

    /**
     * 查询 1 条未缓存的 label -- 倒序
     *
     * @return
     */
    SysTouristLabel selectOne_unCache_desc();

    /**
     * 查询随身讲景点
     *
     * @param qry
     * @return
     */
    SysTouristLabel queryTouristAttractions(TouristAttractionsQry qry);

    SysTouristLabel selectOne(SysTouristLabel condition);

    SysTouristLabel queryNearestNonRepeatLabel(TouristAttractionsQry qry);

    List<SysTouristLabel> queryNearestRepeatLabel(TouristAttractionsQry qry);

    SysTouristLabel selectByTouristIdAndLabel(@Param("labelName") String labelName,@Param("touristId") Long touristId);

    SysTouristLabel selectByLabel(@Param("labelName") String labelName);

    List<SysTouristLabel> listQuery(LabelsQuery labelsQuery);
}
