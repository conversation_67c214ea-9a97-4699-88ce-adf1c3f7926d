package com.iciyun.system.mapper;

import com.iciyun.system.domain.ScenicHeadphone;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 耳机信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07 15:16:13
 */
@Mapper
public interface ScenicHeadphoneMapper extends BaseMapper<ScenicHeadphone> {

    List<ScenicHeadphone> queryList(ScenicHeadphone qo);

    ScenicHeadphone queryByModel(ScenicHeadphone scenicHeadphone);

    void upStatus(ScenicHeadphone scenicHeadphone);

}
