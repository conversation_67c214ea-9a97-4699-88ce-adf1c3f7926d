package com.iciyun.system.mapper;

import com.iciyun.system.domain.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08 16:41:31
 */
@Mapper
public interface GuidePayOrderItemMapper extends BaseMapper<GuidePayOrderItem> {

    List<GuidePayOrderItemVo> getOrderItemsBySelect(@Param("agencyId") String agencyId,
                                                    @Param("scenicId") Integer scenicId,
                                                    @Param("orderItem") String orderItem,
                                                    @Param("startTime") LocalDateTime startTime,
                                                    @Param("endTime") LocalDateTime endTime,
                                                    @Param("orderIds") List<String> orderIds);

    List<GuideOrderSt> getOrderItemsByLimit(@Param("agencyIds") List<String> agencyIds,
                                            @Param("scenicIds") List<Integer> scenicIds,
                                            @Param("pairs") List<Pair> tuples,
                                            @Param("orderItems") List<String> orderItems,
                                            @Param("startTime") LocalDateTime startTime,
                                            @Param("endTime") LocalDateTime endTime);

    List<GuidePayOrderItemVo> getOrdersBySelect(@Param("agencyId") String agencyId,
                                                @Param("scenicId") Integer scenicId,
                                                @Param("orderItem") String orderItem,
                                                @Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime);
}
