package com.iciyun.system.mapper;

import com.iciyun.system.domain.ScenicLocation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 点位信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07 15:16:28
 */
@Mapper
public interface ScenicLocationMapper extends BaseMapper<ScenicLocation> {

    List<ScenicLocation> queryList(ScenicLocation qo);

    ScenicLocation queryByLocationName(ScenicLocation scenicLocation);

}
