package com.iciyun.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.bo.ScenicSpotMiniAppQuery;
import com.iciyun.system.domain.dto.ScenicSpotCacheInfoDto;
import com.iciyun.system.domain.qo.ScenicSpotListQuery;
import com.iciyun.system.domain.qo.ScenicSpotQuery;
import com.iciyun.system.domain.qo.SyncTouristLabelQry;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02 14:44:00
 */
@SuppressWarnings("MybatisXMapperMethodInspection")
@Mapper
public interface ScenicSpotCustomizeMapper extends BaseMapper<ScenicSpot> {

    List<ScenicSpot> queryScenicSpot4MiniApp(ScenicSpotMiniAppQuery query);

    void upServiceActivityNull(Integer id);

    void upNeedTokenNull(Integer id);

    void updateCacheOk(Integer id,Integer cacheOk);

    void updateLabelText(Integer id,String labelText);

    void updateAudioCacheOk(Integer id,Integer audioCacheOk);

    void resetCache(ScenicSpot scenicSpot);

    ScenicSpot getOneByCacheOk(Integer cacheOk);

    ScenicSpot getOneNoCacheOk();

    List<Integer> selectIdList(ScenicSpot scenicSpot);

    ScenicSpot getOneNoAudioCacheOk();

    /**
     * 查询景区合作及缓存状态
     * @param id  景区ID
     * @return
     */
    ScenicSpotCacheInfoDto scenicSpotCacheInfo_One(Integer id);

    List<ScenicSpotCacheInfoDto> scenicSpotCacheInfo();

    Integer unCacheLabelCount();

    List<ScenicSpot> nonLabels(SyncTouristLabelQry qry);

    ScenicSpot getSimpleOne(ScenicSpotQuery query);

    List<ScenicSpot> selectByUser(ScenicSpotListQuery qry);
}
