package com.iciyun.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iciyun.system.domain.ScenicSpotBjYonghg;
import com.iciyun.system.domain.bo.ScenicSpotBo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20 11:52:47
 */
@Mapper
@DS("slave")  //切换库
public interface ScenicSpotMapper extends BaseMapper<ScenicSpotBjYonghg> {


    /**
     * 向量余弦相似度搜索
     *
     * @param targetFeatures 输入的向量
     * @param topN           返回最接近的前N个
     * @return list
     */
    List<ScenicSpotBo> queryByFeatures(@Param("targetFeatures") String targetFeatures, @Param("topN") int topN);

    /**
     * 向量余弦距离搜索
     */
    List<ScenicSpotBo> queryByCosineDistance(@Param("targetFeatures") String targetFeatures, @Param("topN") int topN);

}
