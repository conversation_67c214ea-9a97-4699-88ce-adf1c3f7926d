package com.iciyun.system.mapper;

import com.iciyun.system.domain.ScenicAdmin;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 管理员信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07 15:14:49
 */
@Mapper
public interface ScenicAdminMapper extends BaseMapper<ScenicAdmin> {

    List<ScenicAdmin> queryList(ScenicAdmin qo);

    ScenicAdmin queryByPhone(ScenicAdmin scenicAdmin);

    ScenicAdmin queryByScenicId(@Param("userPhone") String userPhone, @Param("scenicId") Integer scenicId);

}
