package com.iciyun.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iciyun.system.domain.ScenicDoc;
import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.bo.ScenicSpotMiniAppQuery;
import com.iciyun.system.domain.dto.ScenicDocPageParam;
import com.iciyun.system.domain.dto.ScenicSpotCacheInfoDto;
import com.iciyun.system.domain.qo.SyncTouristLabelQry;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02 14:44:00
 */
@SuppressWarnings("MybatisXMapperMethodInspection")
@Mapper
public interface ScenicDocMapper extends BaseMapper<ScenicDoc> {

    List<ScenicDoc> pageList(ScenicDocPageParam scenicDocPageParam);

}
