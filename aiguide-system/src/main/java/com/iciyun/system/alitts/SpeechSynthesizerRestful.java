package com.iciyun.system.alitts;

import cn.hutool.core.io.IoUtil;
import com.alibaba.fastjson2.JSONObject;
import com.iciyun.common.utils.HttpClient;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

@Slf4j
public class SpeechSynthesizerRestful {
    private String accessToken;
    private String appkey;
    public SpeechSynthesizerRestful(String appkey, String token) {
        this.appkey = appkey;
        this.accessToken = token;
    }

    /**
     * HTTPS GET 请求
     */
    public void processGETRequet(String text, String audioSaveFile, String format, int sampleRate, String voice) {
        /**
         * 设置HTTPS GET请求
         * 1.使用HTTPS协议
         * 2.语音识别服务域名：nls-gateway.cn-shanghai.aliyuncs.com
         * 3.语音识别接口请求路径：/stream/v1/tts
         * 4.设置必须请求参数：appkey、token、text、format、sample_rate
         * 5.设置可选请求参数：voice、volume、speech_rate、pitch_rate
         */
        String url = "https://nls-gateway.cn-shanghai.aliyuncs.com/stream/v1/tts";
        url = url + "?appkey=" + appkey;
        url = url + "&token=" + accessToken;
        url = url + "&text=" + text;
        url = url + "&format=" + format;
        url = url + "&voice=" + voice;
        url = url + "&sample_rate=" + String.valueOf(sampleRate);
        // voice 发音人，可选，默认是xiaoyun
        // url = url + "&voice=" + "xiaoyun";
        // volume 音量，范围是0~100，可选，默认50
        // url = url + "&volume=" + String.valueOf(50);
        // speech_rate 语速，范围是-500~500，可选，默认是0
        // url = url + "&speech_rate=" + String.valueOf(0);
        // pitch_rate 语调，范围是-500~500，可选，默认是0
        // url = url + "&pitch_rate=" + String.valueOf(0);
        log.info("URL: " + url);
        /**
         * 发送HTTPS GET请求，处理服务端的响应
         */
        Request request = new Request.Builder().url(url).get().build();
        try {
            long start = System.currentTimeMillis();
            OkHttpClient client = new OkHttpClient();
            Response response = HttpClient.getInstance().newCall(request).execute();
            log.info("total latency :" + (System.currentTimeMillis() - start) + " ms");
            log.info(response.headers().toString());
            String contentType = response.header("Content-Type");
            if ("audio/mpeg".equals(contentType)) {
                File f = new File(audioSaveFile);
                FileOutputStream fout = new FileOutputStream(f);
                fout.write(response.body().bytes());
                fout.close();
                log.info("The GET request succeed!");
            }
            else {
                // ContentType 为 null 或者为 "application/json"
                String errorMessage = response.body().string();
                log.info("The GET request failed: " + errorMessage);
            }
            response.close();
        } catch (Exception e) {
            log.error("音频转换失败！",e);
        }
    }
    /**
     * HTTPS POST 请求
     */
    public void processPOSTRequest(String text, File audioSaveFile, String format, int sampleRate, String voice) {
        /**
         * 设置HTTPS POST请求
         * 1.使用HTTPS协议
         * 2.语音合成服务域名：nls-gateway.cn-shanghai.aliyuncs.com
         * 3.语音合成接口请求路径：/stream/v1/tts
         * 4.设置必须请求参数：appkey、token、text、format、sample_rate
         * 5.设置可选请求参数：voice、volume、speech_rate、pitch_rate
         */
        String url = "https://nls-gateway.cn-shanghai.aliyuncs.com/stream/v1/tts";
        JSONObject taskObject = new JSONObject();
        taskObject.put("appkey", appkey);
        taskObject.put("token", accessToken);
        taskObject.put("text", text);
        taskObject.put("format", format);
        taskObject.put("voice", voice);
        taskObject.put("sample_rate", sampleRate);
        // voice 发音人，可选，默认是xiaoyun
        // taskObject.put("voice", "xiaoyun");
        // volume 音量，范围是0~100，可选，默认50
        // taskObject.put("volume", 50);
        // speech_rate 语速，范围是-500~500，可选，默认是0
        // taskObject.put("speech_rate", 0);
        // pitch_rate 语调，范围是-500~500，可选，默认是0
        // taskObject.put("pitch_rate", 0);
        String bodyContent = taskObject.toJSONString();
        log.info("POST Body Content: " + bodyContent);
        RequestBody reqBody = RequestBody.create(MediaType.parse("application/json"), bodyContent);
        Request request = new Request.Builder()
            .url(url)
            .header("Content-Type", "application/json")
            .post(reqBody)
            .build();

        FileOutputStream fout = null;

        Response response = null;

        try {
            fout = new FileOutputStream(audioSaveFile);
            response = HttpClient.getInstance().newCall(request).execute();
            String contentType = response.header("Content-Type");
            if ("audio/mpeg".equals(contentType)) {
                fout.write(response.body().bytes());
                fout.flush();
                log.info("The POST request succeed!");
            }
            else {
                // ContentType 为 null 或者为 "application/json"
                String errorMessage = response.body().string();
                log.info("The POST request failed: " + errorMessage);
            }
        } catch (Exception e) {
            log.error("音频转换失败！",e);
        }finally {
            IoUtil.close(fout);
            IoUtil.close(response);
        }
    }


    public void genAudio(String text,File audioSaveFile,String voice){

        String format = "wav";

        int sampleRate = 8000;

        this.processPOSTRequest(text, audioSaveFile, format, sampleRate, voice);
    }


    public static void main(String[] args) {

        String token = "e820e88167cd4822b265e8be7f16171a";
        String appkey = "hc0aG3dz7T2RlM7D";

        SpeechSynthesizerRestful speechSynthesizerRestful = new SpeechSynthesizerRestful(appkey, token);

        String text = "세상에서 가장 먼 거리는 삶과 죽음이 아니라, 내가 당신의 앞에 서있어도 당신은 내가 당신을 사랑하는지 모르는 것입니다.";

        // 采用RFC 3986规范进行urlencode编码
        String textUrlEncode = text;
        try {
            textUrlEncode = URLEncoder.encode(textUrlEncode, "UTF-8")
                .replace("+", "%20")
                .replace("*", "%2A")
                .replace("%7E", "~");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        log.info(textUrlEncode);

        File audioSaveFile = new File("/Users/<USER>/Desktop/test/测试.mp3");
        String format = "mp3";
        int sampleRate = 8000;
//        speechSynthesizerRestful.processGETRequet(textUrlEncode, audioSaveFile, format, sampleRate, "siyue");
        speechSynthesizerRestful.processPOSTRequest(text, audioSaveFile, format, sampleRate, "Kyong");

        log.info("### Game Over ###");
    }


}