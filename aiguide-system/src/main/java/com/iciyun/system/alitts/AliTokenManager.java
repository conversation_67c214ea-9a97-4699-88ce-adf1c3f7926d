package com.iciyun.system.alitts;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.iciyun.common.core.domain.entity.SysDictData;
import com.iciyun.common.core.redis.RedisCache;
import com.iciyun.system.service.ISysDictTypeService;
import kotlin.Pair;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> on 2025-05-15 14:28.
 */
@Slf4j
@Component
public class AliTokenManager implements CommandLineRunner {

    @Autowired
    RedisCache redisCache;

    @Autowired
    private ISysDictTypeService dictTypeService;

    String accessKeyId = "";

    String accessKeySecret = "";

    private static final String ali_tts_token = "ali_tts_token";

    @Override
    public void run(String... args) throws Exception {
        accessKeyId = dictTypeService.dictVal("ali_key","accessKeyId");
        accessKeySecret = dictTypeService.dictVal("ali_key","accessKeySecret");

        if (accessKeyId.isEmpty() || accessKeySecret.isEmpty()) {
            log.error("阿里云accessKeyId或accessKeySecret为空");
            return;
        }
    }


    public String getToken()  {
        String token = redisCache.getCacheObject(ali_tts_token);
        if (StrUtil.isEmpty(token)) {
            try {
                Pair<String, Long> pair = genToken(accessKeyId, accessKeySecret);
                redisCache.setCacheObject(ali_tts_token, pair.getFirst(), pair.getSecond().intValue()-600, TimeUnit.SECONDS);
            } catch (ClientException e) {
                throw new RuntimeException(e);
            }
        }
        return token;
    }


    // 您的地域ID
    private static final String REGIONID = "cn-shanghai";
    // 获取Token服务域名
    private static final String DOMAIN = "nls-meta.cn-shanghai.aliyuncs.com";
    // API 版本
    private static final String API_VERSION = "2019-02-28";
    // API名称
    private static final String REQUEST_ACTION = "CreateToken";
    // 响应参数
    private static final String KEY_TOKEN = "Token";
    private static final String KEY_ID = "Id";
    private static final String KEY_EXPIRETIME = "ExpireTime";

    private static Pair<String, Long> genToken(String accessKeyId,String accessKeySecret) throws ClientException {
        // 创建DefaultAcsClient实例并初始化
        DefaultProfile profile = DefaultProfile.getProfile(
                REGIONID,
                accessKeyId,
                accessKeySecret);
        IAcsClient client = new DefaultAcsClient(profile);
        CommonRequest request = new CommonRequest();
        request.setDomain(DOMAIN);
        request.setVersion(API_VERSION);
        request.setAction(REQUEST_ACTION);
        request.setMethod(MethodType.POST);
        request.setProtocol(ProtocolType.HTTPS);
        CommonResponse response = client.getCommonResponse(request);
        log.info(response.getData());
        if (response.getHttpStatus() == 200) {
            JSONObject result = JSON.parseObject(response.getData());
            String token = result.getJSONObject(KEY_TOKEN).getString(KEY_ID);
            long expireTime = result.getJSONObject(KEY_TOKEN).getLongValue(KEY_EXPIRETIME);

            long current = System.currentTimeMillis()/1000;

            long temp = expireTime - current;

            log.info("token 有效期时长：" + temp + " 秒");

            log.info("获取到的Token： " + token + "，有效期时间戳(单位：秒): " + expireTime);
            // 将10位数的时间戳转换为北京时间
            String expireDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(expireTime * 1000));
            log.info("Token有效期的北京时间：" + expireDate);

            Pair<String, Long> pair = new Pair<>(token, temp);
            return pair;
        }
        else {
            log.info("获取Token失败！");
        }

        return null;
    }


}
