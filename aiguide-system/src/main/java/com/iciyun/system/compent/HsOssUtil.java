package com.iciyun.system.compent;

import com.volcengine.tos.TOSV2;
import com.volcengine.tos.TOSV2ClientBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class HsOssUtil {

    // 填写Bucket所在地域。以华东1（杭州）为例，Region填写为cn-hangzhou。
    @Value(value = "${oss.region}")
    private String region;

    // Endpoint以华东1（杭州）为例，其它Region请按实际情况填写。

    @Value(value = "${oss.endpoint}")
    private String endpoint;
    // 填写Bucket名称，例如examplebucket。

    @Value(value = "${oss.accessKeyId}")
    private String accessKeyId;

    @Value(value = "${oss.accessKeySecret}")
    private String accessKeySecret;

    @Bean
    public TOSV2 tos() {
        TOSV2 tos = new TOSV2ClientBuilder().build(region, endpoint, accessKeyId, accessKeySecret);
        log.info("tos初始化完成");
        return tos;
    }
}
