package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18 10:30:21
 */
@Getter
@Setter
@TableName("us_score_change")
@Builder
public class UsScoreChange implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private Long id;

    /**
     * bizID
     */
    @TableField("score_change_id")
    private String scoreChangeId;

    @TableField("user_id")
    private String userId;

    /**
     * 增加、 减少, + -
     */
    @TableField("change_type")
    private String changeType;

    /**
     * 改变的分值
     */
    @TableField("change_score")
    private Integer changeScore;

    /**
     * 规则id
     */
    @TableField("rule_id")
    private String ruleId;

    /**
     * 变化前分值
     */
    @TableField("before_score")
    private Integer beforeScore;

    /**
     * 变化后分值
     */
    @TableField("cur_score")
    private Integer curScore;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;
}
