package com.iciyun.system.domain;


import cn.hutool.core.util.IdUtil;
import com.iciyun.system.domain.bo.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Data
@NoArgsConstructor
public class TouristPackage implements Serializable {

    /**
     * 讲解包 ID
     */
    private String touristPackageId;

    private List<TouristPackageLabel> touristPackageLabels;

    public TouristPackage(TouristPackageLabelSaveCmd cmd){
        this.touristPackageId = IdUtil.getSnowflakeNextIdStr();

        if (CollectionUtils.isEmpty(this.touristPackageLabels)){
            this.touristPackageLabels = new ArrayList<>();
        }
    }

    public TouristPackageLabel saveTouristPackageLabel(TouristPackageLabelSaveCmd cmd){
        TouristPackageLabel touristPackageLabel = new TouristPackageLabel(cmd);
        touristPackageLabel.setTouristPackageId(this.touristPackageId);
        this.touristPackageLabels.add(touristPackageLabel);
        return touristPackageLabel;
    }

    public void editTouristPackageLabel(TouristPackageLabelEditCmd cmd) {

        this.touristPackageLabels.stream()
                .filter(item -> item.getPackageLabelId().equals(cmd.getPackageLabelId()))
                .findFirst()
                .ifPresent(item -> {
                    item.edit(cmd);
                });

    }

    public void changeTryListenStatus(TouristPackageLabelTryListenChangeCmd cmd) {
        this.touristPackageLabels.stream()
                .filter(item -> item.getPackageLabelId().equals(cmd.getPackageLabelId()))
                .findFirst()
                .ifPresent(item -> {
                    item.setTryListen(cmd.isTryListen());
                });
    }

    public void delTouristPackageLabel(TouristPackageLabelDelCmd cmd) {
        this.touristPackageLabels.removeIf(item -> item.getPackageLabelId().equals(cmd.getPackageLabelId()));
    }

    public void sortTouristPackageLabel(TouristPackageLabelSortCmd cmd) {

        List<TouristPackageLabelSortCmd.TouristPackageLabelSortData> dataList = cmd.getDataList();
        Map<String, TouristPackageLabelSortCmd.TouristPackageLabelSortData> map = dataList.stream()
                .collect(Collectors.toMap(TouristPackageLabelSortCmd.TouristPackageLabelSortData::getPackageLabelId, Function.identity()));

        this.touristPackageLabels.forEach(item -> {
            TouristPackageLabelSortCmd.TouristPackageLabelSortData data = map.get(item.getPackageLabelId());
            if (data != null) {
                item.setIndex(data.getIndex());
            }
        });

        this.touristPackageLabels.sort(Comparator.comparingInt(TouristPackageLabel::getIndex));

    }

    public List<TouristPackageLabel> genTouristPackageLabels(Integer touristId, String touristPackageId) {

        TouristPackageLabel touristIntroduction = new TouristPackageLabel(touristId, touristPackageId);

        this.touristPackageLabels.add(0, touristIntroduction);

        return this.touristPackageLabels;

    }


    @Data
    @NoArgsConstructor
    public static class TouristPackageLabel {

        private String packageLabelId;

        /**
         * 索引
         */
        private Integer index;

        /** 标签Id */
        private String labelId;

        /** 标签名称 */
        private String labelName;

        /**
         * 是否允许试听
         */
        private boolean tryListen;

        /** 景区ID */
        private Long touristId;

        /**
         * 景区讲解包 ID
         */
        private String touristPackageId;


        public TouristPackageLabel(TouristPackageLabelSaveCmd cmd) {
            this.packageLabelId = IdUtil.getSnowflakeNextIdStr();
            this.index = cmd.getIndex();
            this.labelId = cmd.getLabelId();
            this.labelName = cmd.getLabelName();
            this.tryListen = cmd.isTryListen();
            this.touristId = cmd.getTouristId();
        }

        public void edit(TouristPackageLabelEditCmd cmd) {
            this.labelId = cmd.getLabelId();
            this.labelName = cmd.getLabelName();
        }

        public TouristPackageLabel(Integer touristId, String touristPackageId){
            this.touristId = Long.valueOf(touristId);
            this.touristPackageId = touristPackageId;
            this.labelName = "景区简介";
            this.tryListen = true;
        }

    }


}
