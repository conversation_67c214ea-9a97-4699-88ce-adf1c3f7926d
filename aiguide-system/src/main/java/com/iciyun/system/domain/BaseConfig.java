package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07 16:49:25
 */
@Getter
@Setter
@TableName("base_config")
public class BaseConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 注册奖励
     */
    @TableField("register_reward")
    private Integer registerReward;

    /**
     * 推荐奖励
     */
    @TableField("referral_reward")
    private Integer referralReward;

    /**
     * 推荐新用户首充奖励
     */
    @TableField("referral_first_reward")
    private Integer referralFirstReward;

    /**
     * 游豆免费讲解次数
     */
    @TableField("lectures_num")
    private Integer lecturesNum;

    /**
     * 5A景区
     */
    @TableField("five_expend")
    private Integer fiveExpend;

    /**
     * 4A景区
     */
    @TableField("four_expend")
    private Integer fourExpend;

    /**
     * 3A及以下
     */
    @TableField("three_expend")
    private Integer threeExpend;

    /**
     * 导览图奖励
     */
    @TableField("guide_reward")
    private Integer guideReward;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 合作景区免费次数
     */
    @TableField("cooper_lectures_num")
    private Integer cooperLecturesNum;
}
