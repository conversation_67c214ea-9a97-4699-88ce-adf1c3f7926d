package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 景区运营
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12 11:16:57
 */
@Getter
@Setter
@TableName("scenic_operate")
public class ScenicOperate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 运营ID
     */
    @TableField("operate_id")
    private String operateId;

    /**
     * 运营名称
     */
    @TableField("operate_name")
    private String operateName;

    /**
     * 运营方负责人
     */
    @TableField("operate_person")
    private String operatePerson;

    /**
     * 渠道上线状态 0 上线，  1 下线，2 删除
     */
    @TableField("operate_statue")
    private String operateStatue;

    /**
     * 省份编码
     */
    @TableField("province_code")
    private String provinceCode;

    /**
     * 省份名称
     */
    @TableField("province_name")
    private String provinceName;

    /**
     * 城市编码
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 城市名称
     */
    @TableField("city_name")
    private String cityName;

    /**
     * 区县编码
     */
    @TableField("district_code")
    private String districtCode;

    /**
     * 区县名称
     */
    @TableField("district_name")
    private String districtName;

    /**
     * 地址
     */
    @TableField("detail_address")
    private String detailAddress;

    /**
     * 联系电话
     */
    @TableField("operate_person_phone")
    private String operatePersonPhone;


    /**
     * 景区
     */
    @TableField(exist = false)
    private String scenicNames;
}
