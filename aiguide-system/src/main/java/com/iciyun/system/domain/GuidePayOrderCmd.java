package com.iciyun.system.domain;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08 16:41:31
 */
@Getter
@Setter
public class GuidePayOrderCmd implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 运营id
     */
    @NotNull
    private String operateId;

    /**
     * 景区id
     */
    @NotNull
    private Integer scenicId;

    @NotNull
    private String pointId;

    private List<OrderItem> orderItems;

    /**
     * 金额
     */
    @NotNull
    private BigDecimal orderAmount;
}
