package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17 11:28:05
 */
@Getter
@Setter
@TableName("guide_pay_order_item_ratio")
public class GuidePayOrderItemRatio implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单id
     */
    @TableField("order_id")
    private String orderId;

    @TableField("agency_id")
    private String agencyId;

    @TableField("agency_name")
    private String agencyName;

    @TableField("agency_amount")
    private BigDecimal agencyAmount;

    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 0 ai导游， 1 耳机
     */
    @TableField("order_item")
    private String orderItem;

    /**
     * ratio 分成比例
     */
    @TableField(exist = false)
    private Integer ratio;

    public BigDecimal getAgencyAmount() {
        if(agencyAmount == null) {
            agencyAmount = new BigDecimal(0);
        }
        return agencyAmount;
    }
}
