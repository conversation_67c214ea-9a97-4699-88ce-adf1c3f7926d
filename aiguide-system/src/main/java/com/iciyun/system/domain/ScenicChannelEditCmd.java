package com.iciyun.system.domain;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 渠道信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06 10:44:43
 */
@Getter
@Setter
public class ScenicChannelEditCmd implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 渠道id
     */
    private String channelId;

    /**
     * 渠道名称
     */
    @NotNull
    private String channelName;

    /**
     * 渠道负责人
     */
    @NotNull
    private String channelPerson;

    /**
     * 渠道上线状态 0 上线，  1 下线，2 删除
     */
    @NotNull
    private String channelStatue;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    @NotNull
    private String provinceName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区县编码
     */
    @NotNull
    private String districtCode;

    /**
     * 区县名称
     */
    private String districtName;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 联系电话
     */
    @NotNull
    private String channelPersonPhone;
}
