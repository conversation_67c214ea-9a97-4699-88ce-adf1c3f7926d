package com.iciyun.system.domain;

import com.iciyun.common.annotation.Excel;
import com.iciyun.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 景区常见问题对话记录对象 sys_tourist_question
 * 
 * <AUTHOR>
 * @date 2025-04-07
 */
public class SysTouristQuestion extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 景区ID */
    @Excel(name = "景区ID")
    private Long touristId;

    /**
     * 问题ID，来自数据字典
     */
    private Long questionId;

    /** 景区名称 */
    @Excel(name = "景区名称")
    private String touristName;

    /** 用户ID */
    @Excel(name = "用户ID")
    private String userId;

    /** 1 问  2 答 */
    @Excel(name = "1 问  2 答")
    private Long type;

    /** 问题 */
    @Excel(name = "问题")
    private String questionText;

    /** 答案 */
    @Excel(name = "答案")
    private String answerText;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTouristId(Long touristId) 
    {
        this.touristId = touristId;
    }

    public Long getTouristId() 
    {
        return touristId;
    }
    public void setTouristName(String touristName) 
    {
        this.touristName = touristName;
    }

    public String getTouristName() 
    {
        return touristName;
    }
    public void setUserId(String userId) 
    {
        this.userId = userId;
    }

    public String getUserId() 
    {
        return userId;
    }
    public void setType(Long type) 
    {
        this.type = type;
    }

    public Long getType() 
    {
        return type;
    }
    public void setQuestionText(String questionText) 
    {
        this.questionText = questionText;
    }

    public String getQuestionText() 
    {
        return questionText;
    }
    public void setAnswerText(String answerText) 
    {
        this.answerText = answerText;
    }

    public String getAnswerText() 
    {
        return answerText;
    }

    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("touristId", getTouristId())
            .append("touristName", getTouristName())
            .append("userId", getUserId())
            .append("type", getType())
            .append("questionText", getQuestionText())
            .append("answerText", getAnswerText())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .toString();
    }
}
