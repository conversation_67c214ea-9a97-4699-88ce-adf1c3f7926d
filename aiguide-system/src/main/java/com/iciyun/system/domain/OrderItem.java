package com.iciyun.system.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08 16:41:31
 */
@Getter
@Setter
public class OrderItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 0 ai导游， 1 耳机
     */
    private String orderItem;

    /**
     * 项目明细，耳机类型
     */
    private String headsetModel;

    /**
     * 项目金额
     */
    private BigDecimal itemAmount;

}
