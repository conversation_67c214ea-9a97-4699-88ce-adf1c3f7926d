package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.iciyun.common.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 推荐关系
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07 18:20:38
 */
@Getter
@Setter
@TableName("user_rec")
public class UserRec implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 邀请人
     */
    @TableField("rec_user_name")
    @Excel(name = "邀请人账号")
    private String recUserName;

    /**
     * 被推荐人
     */
    @TableField("user_name")
    @Excel(name = "被邀请人账号")
    private String userName;

    /**
     * ID
     */
    @TableField("biz_id")
    private String bizId;

    @TableField("create_time")
    @Excel(name = "邀请时间", dateFormat = "yyyy-MM-dd")
    private LocalDateTime createTime;

    /**
     * 0 奖励发放成功 1 发放失败
     */
    @TableField("rec_statue")
    @Excel(name = "奖励状态", readConverterExp = "0=已发放,1=发放失败")
    private String recStatue;


    @TableField("rec_bean_count")
    @Excel(name = "邀请奖励(游豆)")
    private Integer recBeanCount;


    @TableField("user_id")
    private Long userId;



    @TableField("rec_user_id")
    private Long recUserId;



}
