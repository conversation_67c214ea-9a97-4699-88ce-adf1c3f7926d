package com.iciyun.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BingAgencyCmd implements Serializable {

    /**
     * 景区id
     */
    private Integer scenicId;


    /**
     * Ai导游服务费
     */
    private BigDecimal serviceCharge;

    /**
     * Ai导游服务活动价
     */
    private BigDecimal serviceActivity;

    /**
     * Ai导游服务活动价 -- 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityBeginTime;

    /**
     * Ai导游服务活动价 -- 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityEndTime;

    /**
     * 耳机
     *  景区id，景区名称，不需要写入
     */
    private List<ScenicHeadphone> scenicHeadphones;

    /**
     * 机构合作信息
     * 需要写入：机构名称， 机构id， 返佣比例， 备注
     */
    private List<ScenicRatio> scenicRatios;

    /**
     * 物料点位信息
     *  需要写入：点位名称
     */
    private List<ScenicLocation> scenicLocations;
}
