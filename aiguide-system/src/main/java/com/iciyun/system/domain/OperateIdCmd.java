package com.iciyun.system.domain;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08 16:41:31
 */
@Getter
@Setter
public class OperateIdCmd implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 机构id : 如果参数为 -1 ，则查全部
     */
    @Size(min = 1)
    private List<String> agencyIds;

    private String userName;

}
