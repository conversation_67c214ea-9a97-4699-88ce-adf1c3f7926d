package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.iciyun.common.annotation.Excel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07 10:25:15
 */
@Getter
@Setter
@TableName("sys_scenic_st")
public class SysScenicSt implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户
     */
    @TableField("user_id")
    @Excel(name = "用户")
    private Long userId;

    /**
     * 景区
     */
    @TableField("scenic_name")
    @Excel(name = "景区")
    private String scenicName;

    /**
     * 景区id
     */
    @TableField("scenic_id")
    private String scenicId;

    /**
     * label交互次数
     */
    @TableField("label_click_count")
    @Excel(name = "label交互次数")
    private Integer labelClickCount;

    /**
     * 按住说话交互次数
     */
    @TableField("speak_count")
    @Excel(name = "按住说话交互次数")
    private Integer speakCount;

    /**
     * 消耗游豆
     */
    @TableField("use_bean_count")
    @Excel(name = "消耗游豆")
    private Long useBeanCount;

    /**
     * 剩余游豆
     */
    @TableField("residue_bean_count")
    @Excel(name = "剩余游豆")
    private Long residueBeanCount;

    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * lsb交互次数
     */
    @TableField("lbs_count")
    @Excel(name = "lsb交互次数")
    private Integer lbsCount;

    /**
     * 开开眼交互次数
     */
    @TableField("eye_count")
    @Excel(name = "开开眼交互次数")
    private Integer eyeCount;
}
