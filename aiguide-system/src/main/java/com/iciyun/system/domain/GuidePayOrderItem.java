package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.iciyun.common.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08 16:41:31
 */
@Getter
@Setter
@TableName("guide_pay_order_item")
public class GuidePayOrderItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单id
     */
    @TableField("order_id")
    @Excel(name = "订单id")
    private String orderId;

    /**
     * 用户账号
     */
    @TableField("user_name")
    @Excel(name = "游客账号")
    private String userName;

    /**
     * 订单金额（元）
     */
    @TableField("order_amount")
    @Excel(name = "订单金额（元）")
    private BigDecimal orderAmount;


    @TableField("scenic_name")
    @Excel(name = "景区名称")
    private String scenicName;


    /**
     * 支付时间
     */
    @TableField("create_time")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;


    /**
     * 景区id
     */
    @TableField("scenic_id")
    private Integer scenicId;



    @TableField("point_id")
    private String pointId;

    @TableField("point_name")
    private String pointName;

    /**
     * 机构快照
     */
    @TableField("agency_raw")
    private String agencyRaw;

    /**
     * 0 ai导游， 1 耳机
     */
    @TableField("order_item")
    private String orderItem;

    /**
     * 项目明细，耳机类型
     */
    @TableField("headset_model")
    private String headsetModel;

    /**
     * 耳机领取状态， 0 未领取， 1 已领取， 2申请退款， 3 已退款
     */
    @TableField("headset_statue")
    private String headsetStatue;

    /**
     * 耳机领取地点
     */
    @TableField("locations")
    private String locations;

    /**
     * 项目金额
     */
    @TableField("item_amount")
    private BigDecimal itemAmount;

    /**
     * 订单状态
     */
    @TableField("order_statue")
    private String orderStatue;

    /**
     * openid
     */
    @TableField("open_id")
    private String openId;

    /**
     * 创建时间
     */
    @TableField("insert_time")
    private LocalDateTime insertTime;


    public BigDecimal getItemAmount() {
        if (itemAmount == null) {
            itemAmount = new BigDecimal(0);
        }
        return itemAmount;
    }

    public BigDecimal getOrderAmount() {
        if (orderAmount == null) {
            orderAmount = new BigDecimal(0);
        }
        return orderAmount;
    }
}
