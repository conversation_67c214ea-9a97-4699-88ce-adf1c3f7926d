package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iciyun.common.core.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02 14:44:00
 */
@Getter
@Setter
@TableName("scenic_doc")
public class ScenicDoc implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String userId;//用户标识
    private Long scenicId;//景区ID
    private String scenicName;//景区名称
    private String url;//文档地址
    private String fileName;//文档名称
    private String extName;//文档后缀
    private LocalDateTime createTime;

}
