package com.iciyun.system.domain.qo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScenicSpotQuery implements Serializable {


    private Integer id;

    /**
     * 景区 ID
     */
    private String scenicSpotId;

    private String queryType;

    public static enum QueryTypeEnum {
        /**
         * 全部信标
         */
        beacon_all,

    }

    public static void main(String[] args) {
        System.out.println(ScenicSpotQuery.QueryTypeEnum.beacon_all.name());
    }


}
