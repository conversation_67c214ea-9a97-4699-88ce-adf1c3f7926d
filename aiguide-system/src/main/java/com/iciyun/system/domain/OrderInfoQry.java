package com.iciyun.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderInfoQry implements Serializable {

    /**
     * 游客账号
     */
    private String userName;

    /**
     * 景区名称
     */
    private String scenicName;

    /**
     * Date 类型，格式为：yyyy-MM-dd
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * Date 类型，格式为：yyyy-MM-dd
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date  endTime;


}
