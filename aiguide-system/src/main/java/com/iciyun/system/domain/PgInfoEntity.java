package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20 11:52:47
 */
@Getter
@Setter
@TableName("tb_user")
@Builder
public class PgInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 景点ID
     */
    @TableField("user_name")
    private String userName;

    /**
     * 景点名称
     */
    @TableField("password")
    private String password;

    /**
     * 景点省市县
     */
    @TableField("email")
    private String email;


    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;
}
