package com.iciyun.system.domain;

import com.iciyun.common.enums.OrderSortColEnum;
import com.iciyun.common.enums.OrderSortTypeEnum;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 */
@Getter
@Setter
public class GuideOrderItemQry implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * orderIds
     */
    @Size(min = 1)
    private List<String> orderIds;

    /**
     * 机构id
     */
    private String agencyId;

    /**
     * "0" : "AI导游服务"
     * "1" : "耳机服务"
     */
    @NotNull
    private String orderItem;


    /**
     * 排序字段
     */
    private String orderSortCol;

    /**
     * 排序类型
     */
    private String orderSortType;

}
