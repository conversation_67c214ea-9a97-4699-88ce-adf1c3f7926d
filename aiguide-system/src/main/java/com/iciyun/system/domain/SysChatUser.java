package com.iciyun.system.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SysChatUser implements Serializable {

    private Long sysChatUserId;

    private String conversationId;

    private String botId;

    // 用户设置的对话风格：我的性别是XXX，我的职业是XXX，我对XXXxxx感兴趣，我喜欢XXXX，xxx风格
    private String style;
}
