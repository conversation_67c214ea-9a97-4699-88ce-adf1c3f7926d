package com.iciyun.system.domain.bo;

import com.iciyun.system.domain.ChangeType;
import com.iciyun.system.domain.TransactionType;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class AddTokenDetailCmd implements Serializable {

    private Long userId;
    private String userPhone;
    private BigDecimal amountIncurred;
    private ChangeType changeType;
    private TransactionType transcationType;
    //景区id
    private Integer scenicId;

}
