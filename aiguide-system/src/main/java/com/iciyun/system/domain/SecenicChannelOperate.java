package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 渠道下的运营对应的景区
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12 11:05:25
 */
@Getter
@Setter
@TableName("secenic_channel_operate")
public class SecenicChannelOperate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    /**
     * 类型：1、景区；2、渠道 3 运营
     */
    @TableField("business_type")
    private Integer businessType;

    @TableField("channel_code")
    private String channelCode;

    @TableField("channel_name")
    private String channelName;

    /**
     * 景区ID
     */
    @TableField("scenic_id")
    private Integer scenicId;

    /**
     * 景区名称
     */
    @TableField("scenic_name")
    private String scenicName;

    /**
     * 运营ID
     */
    @TableField("operate_id")
    private String operateId;

    /**
     * 运营名称
     */
    @TableField("operate_name")
    private String operateName;

    @TableField("create_time")
    private LocalDateTime createTime;
}
