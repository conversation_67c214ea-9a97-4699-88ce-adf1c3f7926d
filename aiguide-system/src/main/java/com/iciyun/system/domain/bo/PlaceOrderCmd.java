package com.iciyun.system.domain.bo;

import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.system.domain.RechargeConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PlaceOrderCmd implements Serializable {

    /**
     * 必传
     * openId
     */
    private String openId;

    /**
     * 可选
     * 用户 Id
     */
    private Long userId;

    /**
     * 订单金额和订单Id必须传一个
     */
    private String orderId;

    /**
     *
     * 订单金额和订单Id必须传一个
     * 单位：元
     */
    private BigDecimal amount;

    /**
     * 必传
     * 下单类型：
     * 0:海报扫码支付
     */
    private String type;
}
