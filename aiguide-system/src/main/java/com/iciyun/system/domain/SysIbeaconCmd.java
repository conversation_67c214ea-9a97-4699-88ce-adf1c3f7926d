package com.iciyun.system.domain;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SysIbeaconCmd implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 景区ID
     */
    @NotNull
    private Long scenicId;
}
