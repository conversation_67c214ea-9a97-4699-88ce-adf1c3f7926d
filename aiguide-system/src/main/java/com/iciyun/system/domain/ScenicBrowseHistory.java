package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iciyun.common.cqe.SaveScenicSpotBrowseHistoryCmd;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20 14:53:14
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("scenic_browse_history")
public class ScenicBrowseHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("user_id")
    private String userId;

    /**
     * 景区 Id
     */
    @TableField("scenic_id")
    private String scenicId;

    /**
     * 景区名称
     */
    @TableField("scenic_name")
    private String scenicName;

    /**
     * 景区位置
     */
    @TableField("scenic_location")
    private String scenicLocation;

    /**
     * 浏览时间
     */
    @TableField("browse_time")
    private LocalDateTime browseTime;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;

    public ScenicBrowseHistory(SaveScenicSpotBrowseHistoryCmd cmd) {
        this.userId = cmd.getUserId();
        this.scenicName = cmd.getScenicName();
        this.scenicId = cmd.getScenicId();
        this.scenicLocation = cmd.getScenicLocation();
        LocalDateTime now = LocalDateTime.now();
        this.browseTime = now;
        this.createTime = now;
        this.updateTime = now;
    }

    public void init(){
        LocalDateTime now = LocalDateTime.now();
        this.browseTime = now;
        this.createTime = now;
        this.updateTime = now;
    }


}
