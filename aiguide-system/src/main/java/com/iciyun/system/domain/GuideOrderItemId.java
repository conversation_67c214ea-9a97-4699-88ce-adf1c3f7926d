package com.iciyun.system.domain;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 *
 */
@Getter
@Setter
public class GuideOrderItemId implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * orderId
     */
    private String orderId;
}
