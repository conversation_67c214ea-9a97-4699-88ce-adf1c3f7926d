package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iciyun.common.handler.VectorTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20 11:52:47
 */
@Getter
@Setter
@TableName("scenic_spot_beijing_yonghegong")
@AllArgsConstructor
@NoArgsConstructor
public class ScenicSpotBjYonghg implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 景点label
     */
    @TableField("label")
    private String label;

    /**
     * 景点url
     */
    @TableField("url")
    private String url;

    // url向量特征
    @TableField(typeHandler = VectorTypeHandler.class)
    private float[] embedding;
}
