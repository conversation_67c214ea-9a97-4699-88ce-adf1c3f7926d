package com.iciyun.system.domain.qo;


import lombok.Data;

import java.io.Serializable;

@Data
public class SyncTouristLabelQry implements Serializable {

    private Integer id;


    /**
     * 景区名称
     */
    private String name;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 景区级别
     */
    private Integer level;

    /**
     * 景区状态 ON/OFF
     */
    private String status;

    private Integer limitQuery;

}
