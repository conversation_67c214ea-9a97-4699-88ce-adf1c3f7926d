package com.iciyun.system.domain;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 渠道信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06 10:44:43
 */
@Getter
@Setter
@Builder
public class ScenicChannelEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 渠道id
     */
    private String channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 渠道负责人
     */
    private String channelPerson;

    /**
     * 渠道上线状态 0 上线，  1 下线，2 删除
     */
    private String channelStatue;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 运营信息
     */
    private String operateNames;

    /**
     * 景区合作信息
     */
    private String scenicNames;


    /**
     * 联系电话
     */
    private String channelPersonPhone;
}
