package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02 14:44:00
 */
@Getter
@Setter
@TableName("sys_label_audit")
public class SysLabelAudit implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String userId;//用户标识
    private Long scenicId;//景区ID
    private String scenicName;//景区名称
    private Long labelId;//label ID
    private String labelName;//label名称
    private String language;//语言
    private String style;//风格
    private String text1;//原内容
    private String text2;//修改后内容
    private LocalDateTime createTime;

}
