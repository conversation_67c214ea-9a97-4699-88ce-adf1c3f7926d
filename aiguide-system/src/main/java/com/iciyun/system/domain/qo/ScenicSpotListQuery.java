package com.iciyun.system.domain.qo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.iciyun.system.domain.ScenicSpotBjYonghg;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ScenicSpotListQuery implements Serializable {

    /**
     * 景区名称
     */
    private String name;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 负责人
     */
    private String kahuna;

    /**
     * 上线状态
     */
    private String status;

    /**
     * 景区级别
     */
    private Integer level;

    private Long userId;

}
