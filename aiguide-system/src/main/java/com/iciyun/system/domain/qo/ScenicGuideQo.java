package com.iciyun.system.domain.qo;

import com.iciyun.system.domain.ScenicGuide;
import com.iciyun.system.domain.UserSuggestion;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ScenicGuideQo extends ScenicGuide {

    // 开始时间
    private Date beginTime;
    // 结束时间
    private Date endTime;

}
