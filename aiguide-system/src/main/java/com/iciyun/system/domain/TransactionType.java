package com.iciyun.system.domain;

import java.util.List;

public enum TransactionType {

    RECHARGE("RECHARGE", "充值"),
    USE("USE", "消费"),
    REGISTER("REGISTER", "注册"),
    RECOMMEND("RECOMMEND", "推荐"),
    GUIDEMAP("GUIDEMAP", "导览图"),
    ;

    /**
     * 枚举值
     */
    private final String code;

    /**
     * 枚举描述
     */
    private final String message;

    TransactionType(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static List<String> getTotalProfitQueryTypes() {
        return List.of(RECHARGE.code, RECOMMEND.code);
    }

}
