package com.iciyun.system.domain.bo;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class TouristAttractionsQry implements Serializable {

    /**
     * 经度
     */
    private String lng;
    /**
     * 纬度
     */
    private String lat;

    /**
     * 景区 id
     */
    private Integer scenicSpotId;

    /**
     * 围栏半径
     */
    private Integer radius;

    private String requestId;

    private List<Long> explainedIds;

    private String explainedIdsString;

    /**
     * 是否重复讲解（0：否；1：是）
     */
    private Integer repetition;

    /**
     * 必传
     * openId
     */
    private String openId;

    /**
     * 信标编码
     */
    private String beaconCode;

    public void setExplainedIds(List<Long> explainedIds) {
        this.explainedIds = explainedIds;
        if (CollectionUtils.isNotEmpty(this.explainedIds)) {
            this.explainedIdsString = this.explainedIds.stream().map(String::valueOf).collect(Collectors.joining(","));
        }
    }
}
