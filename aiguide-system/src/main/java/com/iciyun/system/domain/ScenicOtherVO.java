package com.iciyun.system.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScenicOtherVO implements Serializable {

    /**
     * 景区id
     */
    private Integer scenicId;


    /**
     * 游豆消耗数量
     */
    private BigDecimal needToken;


    /**
     * 腾讯地图点聚合距离
     * 单位像素
     * 默认50
     */
    private Integer gridSize;

    /**
     * 开开眼状态：true：启动，false：关闭
     */
    private boolean openEyeStatus;

    /**
     * 开开眼优先识别（1：文化符号；2：动植物）
     */
    private Integer identifyType;

    /**
     * 耳数据采集管理员信息
     */
    private List<ScenicAdmin> adminsCj;

    /**
     * 景区信标
     */
    private List<SysIbeacon>  ibeacons;

    private Long userId;

    /**
     * 景点名称
     */
    private String labelName;
}
