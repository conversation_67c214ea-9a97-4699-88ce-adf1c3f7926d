package com.iciyun.system.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08 16:41:31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GuidePayOrderItemEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 景区id
     */
    private Integer scenicId;


    private String scenicName;


    /**
     * 订单金额
     */

    private BigDecimal orderAmount;

    /**
     * 支付时间
     */

    private LocalDateTime createTime;


    /**
     * 用户账号
     */

    private String userName;

    //商品
    List<GuidePayOrderItem> itemList;

    //分佣
    private List<GuidePayOrderItemRatio> ratioList;


}
