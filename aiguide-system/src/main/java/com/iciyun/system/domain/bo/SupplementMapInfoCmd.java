package com.iciyun.system.domain.bo;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class SupplementMapInfoCmd implements Serializable {

    private String name;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 区县名称
     */
    private String districtName;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 景区纬度
     */
    private String latitude;

    /**
     * 景区经度
     */
    private String longitude;

    /**
     * 腾讯地图景区经纬度点串结果
     */
    private String polygon;


    /**
     * POI 分类
     */
    private String category;

    /**
     * POI 分类编码
     */
    private Integer categoryCode;


    /**
     * 腾讯地图查询关键词
     */
    private String keyword;

    /**
     * 腾讯地图查询筛选条件
     */
    private String filter;

    /**
     * 腾讯地图POIID
     */
    private String poiId;

    /**
     * 腾讯地图景区名称
     */
    private String tmapScenicName;

}
