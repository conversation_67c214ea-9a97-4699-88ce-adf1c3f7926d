package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 耳机信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07 15:16:13
 */
@Getter
@Setter
@TableName("scenic_headphone")
public class ScenicHeadphone implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 景区id
     */
    @TableField("scenic_id")
    private Integer scenicId;

    /**
     * 景区名称
     */
    @TableField("scenic_name")
    private String scenicName;

    /**
     * 耳机型号
     */
    @TableField("model")
    private String model;

    /**
     * 耳机售价
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 耳机领取地点
     */
    @TableField("locations")
    private String locations;

    /**
     * 上下架：0、下架；1、上架
     */
    @TableField("status")
    private Integer status;

    /**
     * 机构结算价
     */
    @TableField("agency_price")
    private BigDecimal agencyPrice;

    /**
     * 机构iD
     */
    @TableField("agency_id")
    private String agencyId;

    /**
     * 机构名称
     */

    @TableField("agency_name")
    private String agencyName;


    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
}
