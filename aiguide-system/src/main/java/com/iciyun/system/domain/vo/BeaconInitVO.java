package com.iciyun.system.domain.vo;

import com.iciyun.system.domain.SysTouristLabel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 景区标签对象 sys_tourist_label
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
@Data
@NoArgsConstructor
public class BeaconInitVO implements Serializable
{
    private Long id;

    /** 景区ID */
    private Long touristId;

    /** 景区名称 */
    private String touristName;

    /** 标签名称 */
    private String labelName;

    /**
     * ibeacon 标识
     */
    private String ibeaconUuid;



    public BeaconInitVO(SysTouristLabel label) {
        this.id = label.getId();
        this.touristId = label.getTouristId();
        this.touristName = label.getTouristName();
        this.labelName = label.getLabelName();
        this.ibeaconUuid = label.getBeaconCode();
    }



}
