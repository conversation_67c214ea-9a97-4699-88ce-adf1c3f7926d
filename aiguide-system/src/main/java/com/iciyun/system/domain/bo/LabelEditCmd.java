package com.iciyun.system.domain.bo;

import lombok.Data;

import java.io.Serializable;

/**
 * 景区标签对象 sys_tourist_label
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
@Data
public class LabelEditCmd implements Serializable {

    /** $column.columnComment */
    private Long id;

    /** 景区ID */
    private Long touristId;


    /** 标签名称 */
    private String labelName;

    /**
     * 是否加入点聚合，默认true:加入点聚合，false:不加入点聚合
     *
     */
    private boolean joinCluster;


    /**
     * 触发半径，默认20米
     */
    private Integer radius = 20;

    /**
     * 是否重复讲解，true:重复讲解，false：不重复讲解
     */
    private Boolean repetition = true;

    /**
     * 复讲间隔时间，默认30，单位：分钟
     */
    private Integer intervalTime = 30;

    /**
     * 是否显示，true：显示，false：不显示
     */
    private Boolean showStatus = true;

    /**
     * 是否讲解，true:讲解 false：不讲解
     */
    private Boolean explainStatus = true;

    /**
     * 自定义 POI 分类
     */
    private Integer customizeCategoryCode;

    /**
     * 景区经度
     */
    private String longitude;

    /**
     * 景区纬度
     */
    private String latitude;

    /**
     * 讲解点范围
     */
    private String polygon;

    /**
     * 腾讯地图 POI 分类编码
     */
    private Integer categoryCode;


    /**
     * 信标编码
     */
    private String beaconCode;

    /**
     * 标签类型，枚举值：poi, beacon
     */
    private String labelType;

}
