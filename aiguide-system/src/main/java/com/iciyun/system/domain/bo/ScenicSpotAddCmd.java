package com.iciyun.system.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScenicSpotAddCmd implements Serializable {

    /**
     * 景区名称
     */
    private String name;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 区县名称
     */
    private String districtName;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 负责人
     */
    private String kahuna;

    /**
     * 联系电话
     */
    private String contactNumber;

    /**
     * 上线状态
     */
    private String status;

    /**
     * 景区纬度
     */
    private String latitude;

    /**
     * 景区经度
     */
    private String longitude;

    /**
     * 景区级别
     */
    private Integer level;


    /**
     * 缩略图
     */
    private String thumbnail;

    /**
     * 腾讯地图景区经纬度点串结果
     */
    private String polygon;

    /**
     * 导览图
     */
    private String guideMap;

    /**
     * 风格和语言，json 格式：
     *
     * {
     *   "style": [
     *     "CULTURE"
     *   ],
     *   "language": [
     *     "Chinese",
     *     "English"
     *   ]
     * }
     *
     */
    private String styleAndLanguage;


    /**
     * 风格和语言开关  0 关闭  1 开启
     */
    private Integer styleAndLanguageSwitch;


    /**
     * 0 不允许AI 生成内容   1 允许AI 生成内容
     */
    private Integer aigcSwitch;

    /**
     * 图层
     */
    private Integer zoom;
}
