package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08 16:38:53
 */
@Getter
@Setter
@TableName("guide_pay_order")
public class GuidePayOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("order_id")
    private String orderId;

    @TableField("order_amount")
    private BigDecimal orderAmount;

    @TableField("pay_time")
    private LocalDateTime payTime;

    @TableField("pay_statue")
    private String payStatue;

    @TableField("order_statue")
    private String orderStatue;

    @TableField("open_id")
    private String openId;

    @TableField("user_name")
    private String userName;
}
