package com.iciyun.system.domain.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TouristRouteLabelSortCmd implements Serializable {


    /**
     * 游玩路线Id
     */
    private String touristRouteId;

    /** 景区ID */
    private Long touristId;

    private List<TouristRouteLabelSortData> dataList;



    @Data
    public static class TouristRouteLabelSortData implements Serializable{
        /**
         * 景区游玩路线讲解点ID
         */
        private String touristRouteLabelId;

        /**
         * 索引
         */
        private Integer index;
    }

}
