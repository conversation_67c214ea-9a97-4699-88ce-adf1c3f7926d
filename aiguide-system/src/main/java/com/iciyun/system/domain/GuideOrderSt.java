package com.iciyun.system.domain;

import com.iciyun.common.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 */
@Getter
@Setter
public class GuideOrderSt implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    private List<String> orderIds;

    private List<GuidePayOrderItemVo> orderItems;

    private String agencyId;

    /**
     * 机构名称
     */
    @Excel(name = "机构名称")
    private String agencyName;

    /**
     * 景区id
     */
    private Integer scenicId;

    @Excel(name = "景点名称")
    private String scenicName;


    private String pointId;

    private String pointName;

    /**
     * 0 ai导游， 1 耳机
     */
    @Excel(name = "景点名称", readConverterExp = "0=ai导游,1=耳机")
    private String orderItem;

    /**
     * 总收入 金额
     */
    @Excel(name = "总收入（元）")
    private BigDecimal allAmount;

    /**
     * 订单量
     */
    @Excel(name = "订单量")
    private Integer allOrderNum;

    /**
     * 点位数
     */
    @Excel(name = "点位数量")
    private Long allPointNum;

    /**
     * 机构结算金额
     */
    @Excel(name = "机构收入（元）")
    private BigDecimal agencyAmount;

}
