package com.iciyun.system.domain;

public enum PaymentOrderStatus {

    WAITING_PAYMENT("WAITING_PAYMENT", "待支付"),
    IN_PAYMENT("IN_PAYMENT", "支付中"),
    SUCCESS("SUCCESS", "交易成功"),
    FAIL("FAIL", "交易失败"),
    CLOSE("CLOSE", "交易关闭"),
    WAITING_REFUND("WAITING_REFUND", "待退款"),
    REFUND_PROCESSING("REFUND_PROCESSING", "退款处理中"),

    ;

    /**
     * 枚举值
     */
    private final String code;

    /**
     * 枚举描述
     */
    private final String message;

    PaymentOrderStatus(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static PaymentOrderStatus ofByWxPayRefundStatus(String refundStatus) {
        switch (refundStatus) {
            case "SUCCESS":
                return SUCCESS;
            case "CLOSED":
                return CLOSE;
            case "PROCESSING":
                return REFUND_PROCESSING;
            case "ABNORMAL":
                return FAIL;
            default:
                throw new IllegalArgumentException("Invalid WxPayRefundStatus value");
        }
    }


}
