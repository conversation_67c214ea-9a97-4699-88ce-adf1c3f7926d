package com.iciyun.system.domain.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> on 2025-05-30 12:04.
 */
@Data
public class ScenicSpotCacheInfoDto implements Serializable {

    //景区id
    private Integer id;

    //景区名称
    private String name;

    //上架状态  ON   OFF
    private String status;

    //缓存状态 0 未缓存完成  1 缓存完成
    private Integer cacheOk;

    //合作状态 0未合作 1已合作
    private Integer cooperationStatus;

}
