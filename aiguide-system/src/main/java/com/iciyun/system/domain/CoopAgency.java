package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.iciyun.common.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 景区合作机构表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12 15:55:12
 */
@Getter
@Setter
@TableName("coop_agency")
public class CoopAgency implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 机构iD
     */
    @TableField("agency_id")
    @Excel(name = "机构ID")
    private String agencyId;

    /**
     * 机构名称
     */
    @TableField("agency_name")
    @Excel(name = "机构名称")
    private String agencyName;

    /**
     * 地址
     */
    @TableField("detail_address")
    @Excel(name = "地址")
    private String detailAddress;


    /**
     * 机构方负责人
     */
    @TableField("agency_person")
    @Excel(name = "机构方负责人")
    private String agencyPerson;


    /**
     * 联系电话
     */
    @TableField("agency_person_phone")
    @Excel(name = "联系电话")
    private String agencyPersonPhone;

    /**
     * 上线状态  0 上线，  1 下线，2 删除
     */
    @TableField("agency_statue")
    @Excel(name = "上线状态", readConverterExp = "0=上线,1=下线,2=删除")
    private String agencyStatue;

    /**
     * 省份编码
     */
    @TableField("province_code")
    private String provinceCode;

    /**
     * 省份名称
     */
    @TableField("province_name")
    private String provinceName;

    /**
     * 城市编码
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 城市名称
     */
    @TableField("city_name")
    private String cityName;

    /**
     * 区县编码
     */
    @TableField("district_code")
    private String districtCode;

    /**
     * 区县名称
     */
    @TableField("district_name")
    private String districtName;



    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField(exist = false)
    List<ScenicAdmin> scenicAdminList;
}
