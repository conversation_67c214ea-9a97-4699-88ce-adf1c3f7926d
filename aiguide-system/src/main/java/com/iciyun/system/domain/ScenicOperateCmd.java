package com.iciyun.system.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 渠道信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06 10:44:43
 */
@Getter
@Setter
public class ScenicOperateCmd implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 运营id
     */
    private String operateId;

    /**
     * 运营i名称
     */
    private String operateName;

    /**
     * 运营i负责人
     */
    private String operatePerson;

    /**
     * 运营i上线状态 0 上线，  1 下线，2 删除
     */
    private String operateStatue;

    /**
     * 景区合作信息
     */
    private String scenicName;

    private String detailAddress;

}
