package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21 09:52:20
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("recharge_config")
public class RechargeConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 充值金额
     */
    @TableField("rechage_amount")
    private BigDecimal rechageAmount;

    /**
     * 等价游豆
     */
    @TableField("token")
    private Integer token;

    /**
     * 赠送游豆
     */
    @TableField("donate_token")
    private Integer donateToken;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    public void init(){
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    public void edit() {
        this.updateTime = LocalDateTime.now();
    }
}
