package com.iciyun.system.domain.dto;

import com.google.common.collect.Maps;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2025-06-08 07:03.
 */
@Data
public class TranslaterData implements Serializable {
    private String status;
    private String source_text;
    private List<TranslaterItemData> results;
    private String timestamp;

//         * en=英语、ja=日语、ko=韩语、fr=法语、de=德语、es=西班牙语、ru=俄语、ar=阿拉伯语、vi=越南语、th=泰语
    public static final Map<String,String> transLangMap = Map.of("English","en","Japanese","ja","Spanish","es","Russian","ru","Korean","ko","Vietnamese","vi","Thai","th");


    public TranslaterItemData filter(String language){
        for (TranslaterItemData item : results) {
            if (item.getLang().equalsIgnoreCase(transLangMap.get(language))) {
                return item;
            }
        }
        return null;
    }

    /*
    {
            "status": "success",
            "source_text": "在这里说什么都会直接进行翻译。",
            "results": [
                        {
                                "lang": "en",
                                "translation": "Whatever you say here will be directly translated.",
                                "confidence": 0.99,
                                "notes": []
                        },
                        {
                            "lang": "ja",
                                "translation": "ここで何を言っても直接翻訳されます。",
                                "confidence": 0.99,
                                "notes": []
                        },
                        {
                            "lang": "ko",
                                "translation": "여기서 무엇을 하더라도 바로 번역됩니다.",
                                "confidence": 0.99,
                                "notes": []
                        }
                ],
        "timestamp": "2024-07-05T10:34:10+08:00"
    }
    **/
}
