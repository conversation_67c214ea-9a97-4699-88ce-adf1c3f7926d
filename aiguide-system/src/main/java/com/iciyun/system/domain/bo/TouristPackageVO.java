package com.iciyun.system.domain.bo;

import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.TouristPackage;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class TouristPackageVO implements Serializable {

    /**
     * 总数
     */
    private Integer totalCount;

    /**
     * 可试听数
     */
    private Long listenCount;

    /**
     * 讲解点列表
     */
    private List<TouristPackage.TouristPackageLabel> labels;

    public TouristPackageVO(ScenicSpot scenicSpot) {
        scenicSpot.getTouristPackages().stream().findFirst().ifPresent(touristPackage -> {

            this.labels = touristPackage.genTouristPackageLabels(scenicSpot.getId(), touristPackage.getTouristPackageId());

            this.totalCount = touristPackage.getTouristPackageLabels().size();
            this.listenCount = touristPackage.getTouristPackageLabels().stream()
                    .filter(TouristPackage.TouristPackageLabel::isTryListen).count();

        });

    }

}
