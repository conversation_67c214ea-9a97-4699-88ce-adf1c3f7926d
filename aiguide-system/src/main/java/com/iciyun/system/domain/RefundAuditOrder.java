package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iciyun.common.annotation.Excel;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 退款订单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14 17:10:09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RefundAuditOrder implements Serializable {


    //订单
    private RefundOrder refundOrder;

    //商品
    private GuidePayOrderItem guidePayOrderItem;


}
