package com.iciyun.system.domain.vo;

import com.iciyun.system.domain.ScenicSpot;
import com.iciyun.system.domain.SysTouristLabel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 景区标签对象 sys_tourist_label
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
@Data
@NoArgsConstructor
public class LabelsListVO implements Serializable
{
    private Long id;

    /** 景区ID */
    private Long touristId;

    /** 景区名称 */
    private String touristName;

    /** 标签名称 */
    private String labelName;

    /**
     * 触发半径，默认20米
     */
    private Integer radius;

    /**
     * 是否重复讲解，true:重复讲解，false：不重复讲解
     */
    private Boolean repetition;

    /**
     * 是否显示，true：显示，false：不显示
     */
    private Boolean showStatus;

    /**
     * 是否讲解，true:讲解 false：不讲解
     */
    private Boolean explainStatus;

    /**
     * 是否加入点聚合，默认true:加入点聚合，false:不加入点聚合
     *
     */
    private boolean joinCluster;

    /**
     * 审核状态
     */
    private String status;

    /**
     * 景区经度
     */
    private String longitude;

    /**
     * 景区纬度
     */
    private String latitude;

    /**
     * 讲解点范围
     */
    private String polygon;

    /**
     *  腾讯地图 POI 分类
     */
    private String category;

    /**
     * 腾讯地图 POI 分类编码
     */
    private Integer categoryCode;

    /**
     * 自定义 POI 分类
     */
    private Integer customizeCategoryCode;

    /**
     * 复讲间隔时间，默认30，单位：分钟
     */
    private Integer intervalTime = 30;

    /**
     * 景区经度
     */
    private String touristLongitude;

    /**
     * 景区纬度
     */
    private String touristLatitude;

    /**
     * 讲解点数据来源：map：地图，mini：小程序采集，pc：PC 标记，beacon：信标
     */
    private String labelSource;

    /**
     * 标签类型，枚举值：poi, beacon
     */
    private String labelType;

//    public List<String> getPolygonList() {
//        return StringUtils.isBlank(this.polygon) ? List.of() : Arrays.asList(this.polygon.split(";"));
//    }


    public LabelsListVO(SysTouristLabel label, ScenicSpot scenicSpot) {
        this.id = label.getId();
        this.touristId = label.getTouristId();
        this.labelName = label.getLabelName();
        this.joinCluster = label.getJoinCluster();
        this.status = label.getStatus();
        this.repetition = label.getRepetition();
        this.showStatus = label.getShowStatus();
        this.explainStatus = label.getExplainStatus();
        this.radius = label.getRadius();
        this.longitude = label.getLongitude();
        this.latitude = label.getLatitude();
        this.polygon = label.getPolygon();
        this.category = label.getCategory();
        this.categoryCode = label.getCategoryCode();
        this.customizeCategoryCode = label.getCustomizeCategoryCode();
        this.intervalTime = label.getIntervalTime();
        this.touristName = scenicSpot.getName();
        this.touristLongitude = scenicSpot.getLatitude();
        this.touristLatitude = scenicSpot.getLongitude();
        this.labelSource = label.getLabelSource();
        this.labelType = label.getLabelType();
    }



}
