package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;

/**
 * <p>
 * 渠道信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06 10:44:43
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("scenic_channel")
@Builder
public class ScenicChannel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 渠道id
     */
    @TableField("channel_id")
    private String channelId;

    /**
     * 渠道名称
     */
    @TableField("channel_name")
    private String channelName;

    /**
     * 渠道负责人
     */
    @TableField("channel_person")
    private String channelPerson;

    /**
     * 渠道上线状态 0 上线，  1 下线，2 删除
     */
    @TableField("channel_statue")
    private String channelStatue;

    /**
     * 省份编码
     */
    @TableField("province_code")
    private String provinceCode;

    /**
     * 省份名称
     */
    @TableField("province_name")
    private String provinceName;

    /**
     * 城市编码
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 城市名称
     */
    @TableField("city_name")
    private String cityName;

    /**
     * 区县编码
     */
    @TableField("district_code")
    private String districtCode;

    /**
     * 区县名称
     */
    @TableField("district_name")
    private String districtName;

    /**
     * 详细地址
     */
    @TableField("detail_address")
    private String detailAddress;

    /**
     * 联系电话
     */
    @TableField("channel_person_phone")
    private String channelPersonPhone;

}
