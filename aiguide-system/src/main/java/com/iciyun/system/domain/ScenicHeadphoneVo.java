package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 耳机信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07 15:16:13
 */
@Getter
@Setter
public class ScenicHeadphoneVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private String orderId;

    /**
     * 耳机型号
     */
    private String model;

    /**
     * 耳机领取地点
     */
    private String locations;
}
