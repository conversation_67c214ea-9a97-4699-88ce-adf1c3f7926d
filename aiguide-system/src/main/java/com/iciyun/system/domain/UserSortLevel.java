package com.iciyun.system.domain;

import com.iciyun.common.enums.UserSortLevelType;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-17 09:56:59
 */
@Getter
@Setter
@Builder
public class UserSortLevel implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String userId;

    private Long userSort;

    private UserSortLevelType userLevel;
}
