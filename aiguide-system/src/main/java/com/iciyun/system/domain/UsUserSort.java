package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.iciyun.common.enums.UserGoalType;
import com.iciyun.common.enums.UserIntStatus;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-20 14:24:17
 */
@Getter
@Setter
@TableName("us_user_sort")
public class UsUserSort implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private String userId;

    /**
     * 分值
     */
    @TableField("user_score")
    private Integer userScore;

    /**
     * 是否达标 1 达标， 0 未达标
     */
    @TableField("goal_flag")
    private UserGoalType goalFlag;

    /**
     * 排序
     */
    @TableField("user_sort")
    private Long userSort;

    /**
     * 上次排序
     */
    @TableField("before_user_sort")
    private Long beforeUserSort;

    /**
     * 直接推荐人id
     */
    @TableField("user_referrer_id")
    private String userReferrerId;

    /**
     * 地方代理
     */
    @TableField("user_agency")
    private String userAgency;

    /**
     * 渠道
     */
    @TableField("user_operate")
    private String userOperate;

    /**
     * 技术团队
     */
    @TableField("user_team")
    private String userTeam;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 用户状态，0 可用， 1 不可用
     */
    @TableField("statue")
    private UserIntStatus statue;

    @TableField("task_version")
    private Integer taskVersion;

    //金字塔层级
    private Integer level;
}
