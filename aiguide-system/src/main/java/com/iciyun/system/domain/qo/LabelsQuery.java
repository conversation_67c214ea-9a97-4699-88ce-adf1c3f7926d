package com.iciyun.system.domain.qo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LabelsQuery implements Serializable {


    private Integer id;

    /** 景区ID */
    private Long touristId;

    /** 景区名称 */
    private String touristName;

    /** 标签名称 */
    private String labelName;

    /** 标签名称 */
    private String labelType;

    /**
     * 腾讯地图分类
     */
    private Integer categoryCode;

    /**
     * 自定义 POI 分类
     */
    private Integer customizeCategoryCode;

    /**
     * 信标编码
     */
    private String beaconCode;

    private List<Long> ids;

    private List<Long> notInIds;

    private String queryType;

    public static enum QueryTypeEnum {
        /**
         * 全部信标
         */
        beacon_all,

    }

    public static void main(String[] args) {
        System.out.println(LabelsQuery.QueryTypeEnum.beacon_all.name());
    }


}
