package com.iciyun.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GuideOrderGetQry implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 机构id 如果参数为 -1 ，则查全部
     */
    @Size(min = 1)
    private List<String> agencyIds;

    /**
     * 景区id 如果参数为 -1 ，则查全部
     */
    @Size(min = 1)
    private List<Integer> scenicIds;

    /**
     * "0" : "AI导游服务"
     * "1" : "耳机服务"
     * 如果参数为 -1 ，则查全部
     */
    @Size(min = 1)
    private List<String> items;

    /**
     * 时间类型
     * "0" :  今天
     * "1" :  昨天
     * "2" :  本周
     * "3" :  上周
     * "4" :  本月
     * "5" :  上月
     * "6" :  至今
     */
    private String timeType;

    //渠道开始时间 - 至今
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime channelStartTime;

    /**
     * 时间范围
     * Date 类型，格式为：yyyy-MM-dd
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     * Date 类型，格式为：yyyy-MM-dd
     */

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 排序字段
     */
    private String orderSortCol;

    /**
     * 排序类型
     */
    private String orderSortType;

    private List<String> orderIds;

    private String userName;

    private Integer pageNum;

    private Integer pageSize;

}
