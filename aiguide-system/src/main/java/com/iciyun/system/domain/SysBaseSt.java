package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.iciyun.common.annotation.Excel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 基础统计
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06 16:41:01
 */
@Getter
@Setter
@TableName("sys_base_st")
@Builder
public class SysBaseSt implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 统计日期
     */
    @TableField("st_date")
    @Excel(name = "统计日期")
    private LocalDate stDate;

    /**
     * 当天新增用户
     */
    @TableField("new_user_count")
    @Excel(name = "当天新增用户")
    private Integer newUserCount;

    /**
     * 总用户数
     */
    @TableField("user_count")
    @Excel(name = "总用户数")
    private Long userCount;

    /**
     * 游豆总发行
     */
    @TableField("issue_bean_count")
    @Excel(name = "游豆总发行数")
    private Long issueBeanCount;

    /**
     * 游豆总使用
     */
    @TableField("use_bean_count")
    @Excel(name = "游豆总使用数")
    private Long useBeanCount;

    /**
     * 游豆总剩余
     */
    @TableField("residue_bean_count")
    @Excel(name = "游豆总剩余数")
    private Long residueBeanCount;

    @TableField("create_time")
    private LocalDateTime createTime;
}
