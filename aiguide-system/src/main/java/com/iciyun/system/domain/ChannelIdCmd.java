package com.iciyun.system.domain;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08 16:41:31
 */
@Getter
@Setter
public class ChannelIdCmd implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 渠道id : 如果参数为 -1 ，则查全部
     */
    @NotNull
    private String channelId;

}
