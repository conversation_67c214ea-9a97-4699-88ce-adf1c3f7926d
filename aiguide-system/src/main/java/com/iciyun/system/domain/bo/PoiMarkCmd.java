package com.iciyun.system.domain.bo;

import com.iciyun.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PoiMarkCmd implements Serializable {

    /** 景区ID */
    private Long touristId;

    /** 标签名称 */
    private String labelName;

    /**
     * 景区经度
     */
    private String longitude;

    /**
     * 景区纬度
     */
    private String latitude;


    /**
     * 多边形坐标点集合，逗号分隔的经纬度字符串
     * 例如: 39.905605,116.504199;39.904132,116.504220;39.904230,116.508791;
     */
    private String polygon;

}
