package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.iciyun.common.annotation.Excel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 退款订单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-14 17:10:09
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("refund_order")
public class RefundOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private Integer id;

    /**
     * 订单id
     */
    @TableField("order_id")
    @Excel(name = "售后ID")
    private String orderId;

    /**
     * 主订单id
     */
    @TableField("old_order_id")
    @Excel(name = "订单ID")
    private String oldOrderId;

    /**
     * 退款金额
     */
    @TableField("order_amount")
    @Excel(name = "退款金额")
    private BigDecimal orderAmount;

    /**
     * 用户账号
     */
    @TableField("user_name")
    @Excel(name = "游客账号")
    private String userName;

    /**
     * 机构id
     */
    @TableField("agency_id")
    private String agencyId;

    /**
     * 机构名称
     */
    @TableField("agency_name")
    @Excel(name = "机构名称")
    private String agencyName;


    /**
     * 景区名称
     */
    @TableField("scenic_name")
    @Excel(name = "景区名称")
    private String scenicName;

    @TableField("create_time")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 0 申请退款， 1 退款成功 2 审核通过
     */
    @Excel(name = "状态", readConverterExp = "0=待审核,1=退款成功,2=审核通过")
    @TableField("order_statue")
    private String orderStatue;

    /**
     * 景区id
     */
    private Integer scenicId;


    /**
     * 服务类型， OrderItemEnum
     */
    @TableField("item")
    private String item;


    @TableField("open_id")
    private String openId;

    /**
     * Date 类型，格式为：yyyy-MM-dd
     */
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * Date 类型，格式为：yyyy-MM-dd
     */
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date  endTime;



}
