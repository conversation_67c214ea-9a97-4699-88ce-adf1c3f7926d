package com.iciyun.system.domain.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TouristPackageLabelSortCmd implements Serializable {

    /** 景区ID */
    private Long touristId;

    private List<TouristPackageLabelSortCmd.TouristPackageLabelSortData> dataList;


    @Data
    public static class TouristPackageLabelSortData implements Serializable{
        /**
         * 讲解包讲解点ID
         */
        private String packageLabelId;

        /**
         * 索引
         */
        private Integer index;
    }


}
