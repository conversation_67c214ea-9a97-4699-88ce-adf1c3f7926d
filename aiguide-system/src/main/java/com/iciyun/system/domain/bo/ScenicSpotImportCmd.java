package com.iciyun.system.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.iciyun.system.domain.ScenicSpot;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02 14:44:00
 */
@Getter
@Setter
public class ScenicSpotImportCmd implements Serializable {

    /**
     * 景区名称
     */
    @ExcelProperty("景区名称")
    private String name;

    /**
     * 省份名称
     */
    @ExcelProperty("省份")
    private String provinceName;

    /**
     * 城市名称
     */
    @ExcelProperty("城市")
    private String cityName;

    /**
     * 区县名称
     */
    @ExcelProperty("区县")
    private String districtName;

    /**
     * 详细地址
     */
    @ExcelProperty("详细地址")
    private String detailAddress;

    /**
     * 景区级别
     */
    @ExcelProperty("景区级别")
    private Integer level;

    /**
     * 关键词
     */
    @ExcelProperty("关键词")
    private String keyword;

    /**
     * 筛选条件
     */
    @ExcelProperty("筛选条件")
    private String filter;


    public ScenicSpot to(){
        ScenicSpot scenicSpot = new ScenicSpot();
        scenicSpot.setName(this.name);
        scenicSpot.setProvinceName(this.provinceName);
        scenicSpot.setCityName(this.cityName);
        scenicSpot.setDistrictName(this.districtName);
        scenicSpot.setDetailAddress(this.detailAddress);
        scenicSpot.setLevel(this.level);
        scenicSpot.setKeyword(this.keyword);
        scenicSpot.setFilter(this.filter);
        return scenicSpot;
    }

}
