package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 景区label解说词
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-29 17:10:34
 */
@Getter
@Setter
@TableName("scenic_commentary")
public class ScenicCommentary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 景区id
     */
    @TableField("scenic_id")
    private Integer scenicId;

    /**
     * 景区名称
     */
    @TableField("scenic_name")
    private String scenicName;

    /**
     * label名称
     */
    @TableField("label_name")
    private String labelName;

    /**
     * label解说词
     */
    @TableField("label_commentary")
    private String labelCommentary;

    @TableField("create_time")
    private LocalDateTime createTime;
}
