package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 点位信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07 15:16:28
 */
@Getter
@Setter
@TableName("scenic_location")
public class ScenicLocation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 景区id
     */
    @TableField("scenic_id")
    private Integer scenicId;

    /**
     * 景区名称
     */
    @TableField("scenic_name")
    private String scenicName;

    /**
     * 0、有效；1、无效
     */
    @TableField("statue")
    private Integer statue;
    /**
     * 点位编码
     */
    @TableField("location_code")
    private String locationCode;

    /**
     * 点位名称
     */
    @TableField("location_name")
    private String locationName;

    /**
     * 点位类型：1：默认；2：可编辑
     */
    @TableField("location_type")
    private Integer locationType;

    /**
     * 点位海报
     */
    @TableField("location_url")
    private String locationUrl;

    /**
     * 海报唯一码
     */
    @TableField("location_url_code")
    private String locationUrlCode;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 景区经度
     */
    private String longitude;

    /**
     * 景区纬度
     */
    private String latitude;

}
