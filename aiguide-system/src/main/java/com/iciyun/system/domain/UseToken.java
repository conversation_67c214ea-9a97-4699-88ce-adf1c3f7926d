package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * token 使用日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07 15:36:38
 */
@Getter
@Setter
@TableName("use_token")
public class UseToken implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * ID
     */
    @TableField("biz_id")
    private String bizId;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("use_token")
    private Integer useToken;

    @TableField("input_token")
    private Integer inputCount;

    @TableField("output_token")
    private Integer outputCount;

    @TableField("user_input")
    private String userInput;


    @TableField("user_name")
    private String userName;

    @TableField("user_id")
    private Long userId;

    /**
     * bot 平台
     */
    @TableField("bot_platform")
    private String botPlatform;

    @TableField("bot_id")
    private String botId;

    @TableField("conversation_id")
    private String conversationId;
}
