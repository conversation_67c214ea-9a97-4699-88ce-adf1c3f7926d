package com.iciyun.system.domain;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.system.domain.bo.AddTokenDetailCmd;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25 10:27:41
 */
@Getter
@Setter
@TableName("token_detail")
@NoArgsConstructor
public class TokenDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 明细 ID
     */
    @TableField("detail_id")
    private String detailId;

    /**
     * 订单 id
     */
    @TableField("payment_order_id")
    private String paymentOrderId;

    /**
     * 订单金额
     */
    @TableField("payment_amount")
    private BigDecimal paymentAmount;

    /**
     * 手机号
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 手机号
     */
    @TableField("user_phone")
    private String userPhone;

    /**
     * 发生额
     */
    @TableField("amount_incurred")
    private Integer amountIncurred;

    /**
     * 改变类型，收、支
     * IN:获得，OUT:消费
     */
    @TableField("change_type")
    private String changeType;

    /**
     * 交易类型
     *     RECHARGE : 充值
     *     USE ：消费
     *     REGISTER ：注册
     *     RECOMMEND ；推荐
     *     GUIDEMAP : 导览图
     */
    @TableField("transcation_type")
    private String transcationType;

    @TableField("transaction_time")
    private LocalDateTime transactionTime;

    @TableField("scenic_id")
    private Integer scenicId;

    public void initByRecharge(PaymentOrder paymentOrder, RechargeConfig rechargeConfig, SysUser sysUser) {
        LocalDateTime now = LocalDateTime.now();
        this.createTime = now;
        this.updateTime = now;
        this.paymentOrderId = paymentOrder.getPaymentOrderId();
        this.paymentAmount = rechargeConfig.getRechageAmount();
        this.userId = sysUser.getUserId();
        this.userPhone = sysUser.getPhonenumber();
        this.amountIncurred = rechargeConfig.getToken() + rechargeConfig.getDonateToken();
        this.changeType = ChangeType.IN.name();
        this.transcationType = TransactionType.RECHARGE.name();
        this.transactionTime = now;
        this.detailId = IdUtil.getSnowflakeNextIdStr();
    }

    public TokenDetail(AddTokenDetailCmd cmd) {
        LocalDateTime now = LocalDateTime.now();
        this.createTime = now;
        this.updateTime = now;
        this.userId = cmd.getUserId();
        this.userPhone = cmd.getUserPhone();
        this.amountIncurred = cmd.getAmountIncurred().intValue();
        this.changeType = cmd.getChangeType().name();
        this.transcationType = cmd.getTranscationType().name();
        this.transactionTime = now;
        this.detailId = IdUtil.getSnowflakeNextIdStr();
        this.scenicId = cmd.getScenicId();
    }

}
