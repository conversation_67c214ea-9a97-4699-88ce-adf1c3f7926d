package com.iciyun.system.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08 16:41:31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GuidePayOrderItemVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 景区id
     */
    private Integer scenicId;


    private String scenicName;


    private String pointId;


    private String pointName;

    /**
     * 机构快照
     */

    private String agencyRaw;

    /**
     * 0 ai导游， 1 耳机
     */

    private String orderItem;

    /**
     * 项目明细，耳机类型
     */

    private String headsetModel;

    /**
     * 耳机领取状态， 0 未领取， 1 已领取， 2申请退款， 3 已退款
     */

    private String headsetStatue;

    /**
     * 耳机领取地点
     */

    private String locations;

    /**
     * 项目金额
     */

    private BigDecimal itemAmount;

    /**
     * 订单金额
     */

    private BigDecimal orderAmount;

    /**
     * 支付时间
     */

    private LocalDateTime createTime;

    /**
     * 订单状态
     */

    private String orderStatue;

    /**
     * openid
     */

    private String openId;

    /**
     * 用户账号
     */

    private String userName;

    /**
     * 机构id
     */

    private String agencyId;

    /**
     * 机构名称
     */

    private String agencyName;

    /**
     * 机构分佣
     */
    private BigDecimal agencyAmount;


}
