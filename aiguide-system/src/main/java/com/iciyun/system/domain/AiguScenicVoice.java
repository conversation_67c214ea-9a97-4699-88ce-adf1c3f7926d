package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.iciyun.common.enums.ScenicStatus;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20 11:52:47
 */
@Getter
@Setter
@TableName("aigu_scenic_voice")
@Builder
public class AiguScenicVoice implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 景点ID
     */
    @TableField("scenic_id")
    private String scenicId;

    /**
     * 景点名称
     */
    @TableField("scenic_name")
    private String scenicName;

    /**
     * 景点省市县
     */
    @TableField("scenic_location")
    private String scenicLocation;

    /**
     * 导览图url
     */
    @TableField("img")
    private String img;

    /**
     * 景点介绍
     */
    @TableField("context")
    private String context;

    /**
     * 景点音频MP3 逗号分隔
     */
    @TableField("text_voices")
    private String textVoices;

    /**
     * 0  正常 1 无效
     */
    @TableField("statue")
    private ScenicStatus statue;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;
}
