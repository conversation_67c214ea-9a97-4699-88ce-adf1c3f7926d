package com.iciyun.system.domain;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iciyun.common.core.domain.entity.SysUser;
import com.iciyun.common.utils.uuid.IdHutool;
import com.iciyun.system.domain.bo.PlaceOrderCmd;
import com.iciyun.system.domain.bo.RefundCmd;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 支付订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24 11:16:43
 */
@Getter
@Setter
@TableName("payment_order")
@NoArgsConstructor
public class PaymentOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID, 关联会员表
     */
    @TableField("user_id")
    private String userId;

    /**
     * 会员openId
     */
    @TableField("open_id")
    private String openId;

    /**
     * 订单ID, 关联订单表
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 支付订单业务ID
     */
    @TableField("payment_order_id")
    private String paymentOrderId;

    /**
     * 支付订单状态#com.iciyun.mall.payment.biz.enums.PaymentOrderStatusEnum#
     */
    @TableField("payment_order_status")
    private String paymentOrderStatus;

    /**
     * 订单金额, #cn.hutool.core.math.Money#
     */
    @TableField("actual_transaction_amount")
    private BigDecimal actualTransactionAmount;

    /**
     * 商品描述
     */
    @TableField("description")
    private String description;

//    /**
//     * 有效期，单位：分钟
//     */
//    @TableField("effective_period")
//    private Integer effectivePeriod;
//
    /**
     * 支付超时时间
     */
    @TableField("time_expire")
    private LocalDateTime timeExpire;

    /**
     * 支付通道支付流水号
     */
    @TableField("channel_serial_number")
    private String channelSerialNumber;

    /**
     * 支付成功时间
     */
    @TableField("success_time")
    private LocalDateTime successTime;

    /**
     * 充值配置快照
     */
    @TableField("recharge_config_raw")
    private String rechargeConfigRaw;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("origin_payment_order_id")
    private String originPaymentOrderId;

    public PaymentOrder(SysUser sysUser, RechargeConfig rechargeConfig) {

        this.userId = String.valueOf(sysUser.getUserId());
        this.openId = sysUser.getOpenId();
        this.paymentOrderId = IdHutool.gen();
        this.paymentOrderStatus = PaymentOrderStatus.WAITING_PAYMENT.name();
        this.actualTransactionAmount = rechargeConfig.getRechageAmount();
        this.description = "购买AI导游商品";
        this.timeExpire = LocalDateTime.now().plusMinutes(30);
        this.rechargeConfigRaw = JSON.toJSONString(rechargeConfig);
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();

    }

    public PaymentOrder(PlaceOrderCmd cmd) {

        this.userId = String.valueOf(cmd.getUserId());
        this.openId = cmd.getOpenId();
        this.orderId = cmd.getOrderId();
        this.paymentOrderId = IdHutool.gen();
        this.paymentOrderStatus = PaymentOrderStatus.WAITING_PAYMENT.name();
        this.actualTransactionAmount = cmd.getAmount().multiply(new BigDecimal("100"));
        this.description = "购买AI导游商品";
        this.timeExpire = LocalDateTime.now().plusMinutes(30);
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();

    }

    public void placeSuccess() {
        this.updateTime = LocalDateTime.now();
        this.paymentOrderStatus = PaymentOrderStatus.IN_PAYMENT.name();
    }

    public void paySuccess(String transactionId) {
        this.channelSerialNumber = transactionId;
        this.successTime = LocalDateTime.now();
        this.paymentOrderStatus = PaymentOrderStatus.SUCCESS.name();
        this.updateTime = LocalDateTime.now();

    }

    public PaymentOrder refund(RefundCmd cmd) {
        PaymentOrder order = new PaymentOrder();
        order.setUserId(String.valueOf(this.getUserId()));
        order.setOpenId(this.getOpenId());
        order.setOrderId(cmd.getRefundOrderId());
        order.setPaymentOrderId(IdHutool.gen());
        order.setOriginPaymentOrderId(this.getPaymentOrderId());
        order.setPaymentOrderStatus(PaymentOrderStatus.WAITING_REFUND.name());
        order.setActualTransactionAmount(cmd.getAmount().multiply(new BigDecimal("100")));
        order.setDescription("购买AI导游商品-退款订单");
        order.setTimeExpire(LocalDateTime.now().plusMinutes(30));
        order.setCreateTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());
        return order;
    }

    public void syncRefund(String refundId, String refundStatus) {
        this.channelSerialNumber = refundId;
        this.paymentOrderStatus = PaymentOrderStatus.ofByWxPayRefundStatus(refundStatus).name();
        this.updateTime = LocalDateTime.now();
    }
}
