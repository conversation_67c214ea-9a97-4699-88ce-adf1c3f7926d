package com.iciyun.system.domain;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08 16:41:31
 */
@Getter
@Setter
public class GuideHeadset implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    private String openId;

    @NotNull
    private Integer scenicId;
}
