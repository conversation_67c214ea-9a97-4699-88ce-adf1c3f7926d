package com.iciyun.system.domain.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> on 2025-06-13 16:32.
 */
@Data
public class GenTextAgainParam implements Serializable {
    private Long scenicId;
    private Long labelId;//label ID，为 0 时表示景区简介
    private String style;
    private String language;
    private String content;
    private Integer type;//-1 重新生成（整个景区的全部）  0 重新生成（某个景点的全部）   1 重新生成（针对某个风格）     2 再次保存（针对某个风格）
}
