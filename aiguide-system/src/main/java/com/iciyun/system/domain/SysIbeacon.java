package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * ibeacon维护
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20 10:37:52
 */
@Getter
@Setter
@TableName("sys_ibeacon")
public class SysIbeacon implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户标识
     */
    @TableField("user_id")
    private String userId;

    /**
     * 景区ID
     */
    @TableField("scenic_id")
    private Long scenicId;

    /**
     * 景区名称
     */
    @TableField("scenic_name")
    private String scenicName;

    /**
     * label ID
     */
    @TableField("label_id")
    private Long labelId;

    /**
     * label名称
     */
    @TableField("label_name")
    private String labelName;

    /**
     * ibeacon 标识
     */
    @TableField("ibeacon_uuid")
    private String ibeaconUuid;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 0有效， 1 删除
     */
    @TableField("statue")
    private Integer statue;

}
