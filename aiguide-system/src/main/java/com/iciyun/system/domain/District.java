package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14 11:08:43
 */
@Getter
@Setter
@Builder
@TableName("district")
//@NoArgsConstructor
//@RequiredArgsConstructor
public class District implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("adcode")
    private String adcode;

    @TableField("name")
    private String name;

    @TableField("center")
    private String center;

    @TableField("level")
    private String level;

    @TableField("padcode")
    private String padcode;

    public String[] getXy() {
        return center.split(",");
    }


    @Data
    public static class DistrictQuery implements Serializable {

        /**
         * 行政区划代码
         * 查询省份传：100000
         */
        private String padcode;
    }

}
