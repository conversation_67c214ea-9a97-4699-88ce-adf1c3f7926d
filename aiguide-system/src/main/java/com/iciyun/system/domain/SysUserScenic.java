package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 用户管理景区信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09 16:38:54
 */
@Getter
@Setter
@TableName("sys_user_scenic")
public class SysUserScenic implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("user_id")
    private Long userId;

    @TableField("scenic_id")
    private Integer scenicId;

    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 景区名称
     */
    @TableField("scenic_name")
    private String scenicName;
}
