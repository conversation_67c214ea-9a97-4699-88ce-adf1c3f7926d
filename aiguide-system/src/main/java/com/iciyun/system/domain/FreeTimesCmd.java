package com.iciyun.system.domain;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-08 16:41:31
 */
@Getter
@Setter
public class FreeTimesCmd implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * openId
     */
    @NotNull
    private String openId;

    /**
     * 景区id
     */
    @NotNull
    private Integer scenicId;

    /**
     * 景区免费类型
     * "0" 游豆免费次数
     * "1" 合作景区免费次数
     */
    @NotNull
    private String freeType;

}
