package com.iciyun.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.iciyun.common.enums.ScenicSpotStatusEnum;
import com.iciyun.system.domain.ScenicSpot;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ExcelIgnoreUnannotated
public class ScenicSpotExportVO implements Serializable {

    /**
     * 景区 ID
     */
    @ExcelProperty("景区ID")
    @ColumnWidth(20)
    private Integer id;

    /**
     * 景区名称
     */
    @ExcelProperty("景区名称")
    @ColumnWidth(20)
    private String name;

    /**
     * 景区地址
     */
    @ExcelProperty("景区地址")
    @ColumnWidth(20)
    private String detailAddress;

    /**
     * 负责人
     */
    @ExcelProperty("景区负责人")
    @ColumnWidth(20)
    private String kahuna;

    /**
     * 景区级别
     */
    private Integer level;

    /**
     * 景区级别
     */
    @ExcelProperty("景区等级")
    @ColumnWidth(20)
    private String levelValue;


    /**
     * 联系电话
     */
    @ExcelProperty("联系电话")
    @ColumnWidth(20)
    private String contactNumber;

    /**
     * 上线状态
     */

    private String status;

    /**
     * 上线状态
     */
    @ExcelProperty("上线状态")
    @ColumnWidth(20)
    private String statusValue;

    public String getStatusValue() {
        if (this.status.equals(ScenicSpotStatusEnum.ON.getCode())) {
            return ScenicSpotStatusEnum.ON.getDesc();
        } else if (this.status.equals(ScenicSpotStatusEnum.OFF.getCode())) {
            return ScenicSpotStatusEnum.OFF.getDesc();
        }
        return "";
    }

    public String getLevelValue() {

        if (this.level == 5) {
            return "5A";
        } else if (this.level == 4) {
            return "4A";
        } else if (this.level <= 3) {
            return "3A及以下";
        }
        return "";
    }

    public ScenicSpotExportVO(ScenicSpot scenicSpot) {

        this.id = scenicSpot.getId();
        this.name = scenicSpot.getName();
        this.detailAddress = scenicSpot.getDetailAddress();
        this.kahuna = scenicSpot.getKahuna();
        this.level = scenicSpot.getLevel();
        this.contactNumber = scenicSpot.getContactNumber();
        this.status = String.valueOf(scenicSpot.getStatus());

        if (this.status.equals(ScenicSpotStatusEnum.ON.getCode())) {
            this.statusValue = ScenicSpotStatusEnum.ON.getDesc();
        } else if (this.status.equals(ScenicSpotStatusEnum.OFF.getCode())) {
            this.statusValue = ScenicSpotStatusEnum.OFF.getDesc();
        }

        if (this.level == null) {
            this.level = 3;
        }

        if (this.level == 5) {
            this.levelValue = "5A";
        } else if (this.level == 4) {
            this.levelValue = "4A";
        } else if (this.level <= 3) {
            this.levelValue = "3A及以下";
        } else {
            this.levelValue = "3A及以下";
        }

    }


}
