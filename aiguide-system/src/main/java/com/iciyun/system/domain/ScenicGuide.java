package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.iciyun.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-10 14:11:00
 */
@Getter
@Setter
@TableName("scenic_guide")
public class ScenicGuide extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 导览图编号
     */
    @TableField("guide_code")
    private String guideCode;

    /**
     * 用户id
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 用户手机号
     */
    @TableField("user_phone")
    private String userPhone;

    /**
     * 景区id
     */
    @TableField("scenic_id")
    private Integer scenicId;

    /**
     * 景区名称
     */
    @TableField("scenic_name")
    private String scenicName;

    /**
     * 景区地址
     */
    @TableField("scenic_address")
    private String scenicAddress;

    /**
     * 导览图
     */
    @TableField("guide_url")
    private String guideUrl;

    /**
     * 审批状态（0未通过，1已通过）
     */
    @TableField("status")
    private Integer status;

}
