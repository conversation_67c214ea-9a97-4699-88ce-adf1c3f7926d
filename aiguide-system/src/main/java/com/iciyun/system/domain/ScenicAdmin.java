package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 管理员信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07 15:14:49
 */
@Getter
@Setter
@TableName("scenic_admin")
public class ScenicAdmin implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 类型：1、机构， 4、景区数据采集员
     */
    @TableField("business_type")
    private Integer businessType;

    /**
     * 类型编码
     */
    @TableField("business_code")
    private String businessCode;

    /**
     * 用户姓名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 用户手机号
     */
    @TableField("user_phone")
    private String userPhone;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 关联景区
     */
    @TableField(exist = false)
    List<AgencyAdminScenic> agencyAdminScenicList;

    /**
     * 关联景区- 回显 , 分隔
     */
    @TableField(exist = false)
    private String scenicName;
}
