package com.iciyun.system.domain;

import org.springframework.stereotype.Component;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Component
public class GlobalVariablesSingleton {
    private static GlobalVariablesSingleton instance;

    private String cozeToken;

    private static Lock lock = new ReentrantLock();
    public static GlobalVariablesSingleton getInstance() {
        lock.lock();
        try {
            if (instance == null) {
                instance = new GlobalVariablesSingleton();
            }
        } finally {
            lock.unlock();
        }
        return instance;
    }

    public String getCozeToken() {
        return cozeToken;
    }

    public void setCozeToken(String cozeToken) {
        this.cozeToken = cozeToken;
    }
}
