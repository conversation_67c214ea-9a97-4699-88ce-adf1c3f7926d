package com.iciyun.system.domain;

import cn.hutool.core.util.IdUtil;
import com.iciyun.system.domain.bo.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 景区游玩路线
 */
@Data
@NoArgsConstructor
public class TouristRoute {

    private String touristRouteId;

    private String touristRouteName;

    /** 景区ID */
    private Long touristId;

    private List<TouristRouteLabel> touristRouteLabels;

    public TouristRoute(TouristRouteAddCmd cmd) {
        this.touristRouteId = IdUtil.getSnowflakeNextIdStr();
        this.touristId = cmd.getTouristId();
        this.touristRouteName = cmd.getTouristRouteName();
        if (CollectionUtils.isEmpty(this.touristRouteLabels)) {
            this.touristRouteLabels = new ArrayList<>();
        }
    }

    public void edit(TouristRouteEditCmd cmd) {
        this.touristRouteName = cmd.getTouristRouteName();
    }

    public TouristRouteLabel saveTouristRouteLabel(TouristRouteLabelSaveCmd cmd) {

        TouristRouteLabel touristRouteLabel = new TouristRouteLabel(cmd);
        this.touristRouteLabels.add(new TouristRouteLabel(cmd));

        return touristRouteLabel;
    }

    public void editTouristRouteLabel(TouristRouteLabelEditCmd cmd) {

        this.touristRouteLabels.stream()
                .filter(item -> item.getTouristRouteLabelId().equals(cmd.getTouristRouteLabelId()))
                .findFirst()
                .ifPresent(item -> {
                    item.edit(cmd);
                });

    }

    public void delTouristRouteLabel(TouristRouteLabelDelCmd cmd) {
        this.touristRouteLabels.removeIf(item -> item.getTouristRouteLabelId().equals(cmd.getTouristRouteLabelId()));
    }

    public void sortTouristRouteLabel(TouristRouteLabelSortCmd cmd) {

        List<TouristRouteLabelSortCmd.TouristRouteLabelSortData> dataList = cmd.getDataList();
        Map<String, TouristRouteLabelSortCmd.TouristRouteLabelSortData> map = dataList.stream()
                .collect(Collectors.toMap(TouristRouteLabelSortCmd.TouristRouteLabelSortData::getTouristRouteLabelId, Function.identity()));

        this.touristRouteLabels.forEach(item -> {
            TouristRouteLabelSortCmd.TouristRouteLabelSortData data = map.get(item.getTouristRouteLabelId());
            if (data != null) {
                item.setIndex(data.getIndex());
            }
        });

        this.touristRouteLabels.sort(Comparator.comparingInt(TouristRouteLabel::getIndex));

    }

    public Integer getTotalCount() {
        return this.touristRouteLabels.size();
    }



    @Data
    @NoArgsConstructor
    public static class TouristRouteLabel {

        private String touristRouteLabelId;

        /**
         * 索引
         */
        private Integer index;

        /** 标签Id */
        private String labelId;

        /** 标签名称 */
        private String labelName;

        /**
         * 景区经度
         */
        private String longitude;

        /**
         * 景区纬度
         */
        private String latitude;

        private String touristRouteId;

        /** 景区ID */
        private Long touristId;

        public TouristRouteLabel(TouristRouteLabelSaveCmd cmd) {
            this.touristRouteLabelId = IdUtil.getSnowflakeNextIdStr();
            this.index = cmd.getIndex();
            this.labelId = cmd.getLabelId();
            this.labelName = cmd.getLabelName();
            this.touristRouteId = cmd.getTouristRouteId();
            this.touristId = cmd.getTouristId();
            this.longitude = cmd.getLongitude();
            this.latitude = cmd.getLatitude();
        }

        public void edit(TouristRouteLabelEditCmd cmd) {
            this.labelId = cmd.getLabelId();
            this.labelName = cmd.getLabelName();
            this.longitude = cmd.getLongitude();
            this.latitude = cmd.getLatitude();
        }
    }

}
