package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.iciyun.common.enums.SortUpDownType;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-17 16:19:49
 */
@Getter
@Setter
@TableName("us_sort_change")
@Builder
public class UsSortChange implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private Long id;

    /**
     * bizID
     */
    @TableField("sort_change_id")
    private String sortChangeId;

    @TableField("user_id")
    private String userId;

    /**
     * 变化前排名
     */
    @TableField("before_sort")
    private Long beforeSort;

    /**
     * 当前排名
     */
    @TableField("cur_sort")
    private Long curSort;

    /**
     * 排名变化状态  0 上升， 1 下降， 2 不变
     */
    @TableField("change_statue")
    private SortUpDownType changeStatue;

    /**
     * 变化版本
     */
    @TableField("task_version")
    private Integer taskVersion;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;
}
