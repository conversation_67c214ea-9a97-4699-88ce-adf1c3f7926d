package com.iciyun.system.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 渠道信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06 10:44:43
 */
@Getter
@Setter
@Builder
public class ScenicChannelVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 渠道id
     */
    private String channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 渠道负责人
     */
    private String channelPerson;

    /**
     * 渠道上线状态 0 上线，  1 下线，2 删除
     */
    private String channelStatue;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 区县名称
     */
    private String districtName;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 联系电话
     */
    private String channelPersonPhone;

    /**
     * 景区
     */
    private String scenicNames;

    /**
     * 运营信息
     */
    private String operateNames;
}
