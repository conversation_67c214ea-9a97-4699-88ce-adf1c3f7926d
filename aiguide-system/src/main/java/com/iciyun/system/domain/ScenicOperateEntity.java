package com.iciyun.system.domain;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 渠道信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06 10:44:43
 */
@Getter
@Setter
@Builder
public class ScenicOperateEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 运营id
     */
    private String operateId;

    /**
     * 运营名称
     */
    private String operateName;

    /**
     * 运营负责人
     */
    private String operatePerson;

    /**
     * 运营上线状态 0 上线，  1 下线，2 删除
     */
    private String operateStatue;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 景区合作信息
     */
    private String scenicNames;


    /**
     * 联系电话
     */
    private String operatePersonPhone;
}
