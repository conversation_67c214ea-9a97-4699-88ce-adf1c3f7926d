package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 分佣比例表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07 15:16:47
 */
@Getter
@Setter
@TableName("scenic_ratio")
public class ScenicRatio implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 景区id
     */
    @TableField("scenic_id")
    private Integer scenicId;

    /**
     * 景区名称
     */
    @TableField("scenic_name")
    private String scenicName;

    /**
     * 0、有效；1、无效
     */
    @TableField("statue")
    private Integer statue;

    /**
     * 机构比例
     */
    @TableField("agency_ratio")
    private Integer agencyRatio;

    /**
     * 机构id
     */
    @TableField("agency_id")
    private String agencyId;

    /**
     * 机构名称
     */
    @TableField("agency_name")
    private String agencyName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * remark
     */
    @TableField("remark")
    private String remark;

    /**
     * 省份编码
     */
    @TableField("province_code")
    private String provinceCode;

    /**
     * 省份名称
     */
    @TableField("province_name")
    private String provinceName;

    /**
     * 城市编码
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 城市名称
     */
    @TableField("city_name")
    private String cityName;

    /**
     * 区县编码
     */
    @TableField("district_code")
    private String districtCode;

    /**
     * 区县名称
     */
    @TableField("district_name")
    private String districtName;

}
