package com.iciyun.system.domain.bo;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
public class ScenicSpotMiniAppQuery implements Serializable {

    /**
     * 当前位置经度
     */
    private String currentLocationX;

    /**
     * 当前位置纬度
     */
    private String currentLocationY;

    /**
     * 当前城市经度
     */
    private String currentCityX;

    /**
     * 当前城市纬度
     */
    private String currentCityY;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 景区名称
     */
    private String name;

    /**
     * 目标位置（Y：当前定位，N：城市中心）
     */
    private Integer targetPosition;

    public void judgeIfCurrentLocation(){

        this.targetPosition = 0;

        if (StringUtils.isBlank(this.currentLocationX) && StringUtils.isBlank(this.currentLocationY)) {
            this.targetPosition = 1;
        }

    }


}
