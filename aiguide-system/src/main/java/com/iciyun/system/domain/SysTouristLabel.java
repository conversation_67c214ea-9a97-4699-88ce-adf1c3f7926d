package com.iciyun.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.iciyun.common.annotation.Excel;
import com.iciyun.common.constant.Constants;
import com.iciyun.common.core.domain.BaseEntity;
import com.iciyun.common.enums.LabelSourceEnum;
import com.iciyun.common.enums.LabelStatusEnum;
import com.iciyun.common.enums.LabelTypeEnum;
import com.iciyun.system.domain.bo.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.commons.text.similarity.LevenshteinDistance;

import java.util.Date;
import java.util.List;

/**
 * 景区标签对象 sys_tourist_label
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
public class SysTouristLabel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 景区ID */
    @Excel(name = "景区ID")
    private Long touristId;

    /** 景区名称 */
    @Excel(name = "景区名称")
    private String touristName;

    /** 标签名称 */
    @Excel(name = "标签名称")
    private String labelName;

    /** 图片尺寸-宽 */
    @Excel(name = "图片尺寸-宽")
    private Long imageWidth;

    /** 图片尺寸-高 */
    @Excel(name = "图片尺寸-高")
    private Long imageHeight;

    /** 标签左上角坐标 */
    @Excel(name = "标签左上角坐标")
    private String x1y1;

    /** 标签右下角坐标 */
    @Excel(name = "标签右下角坐标")
    private String x2y2;

    /**
     * 景区经度
     */
    private String longitude;

    /**
     * 备注
     */
    private String note;

    /**
     * 景区纬度
     */
    private String latitude;

    private String cityCode;

    private String cityName;

    private String labelText;

    private Integer tmapStatus;

    /**
     * 是否加入点聚合，默认true:加入点聚合，false:不加入点聚合
     *
     */
    private Boolean joinCluster;

    /**
     * 讲解点范围
     */
    private String polygon;

    @TableField(exist = false)
    private Integer queryCase;

//    解说词生成标识   0 未生成   1 已生成
    private Integer cacheOk;

//    音频缓存标识  0 未缓存完成   1 已缓存完成
    private Integer audioCacheOk;

    /** 是否重要：0、不重要；1、重要； */
    @TableField("is_important")
    private String isImportant;

    /**
     * 触发半径，默认20米
     */
    private Integer radius = 20;

    /**
     * 是否重复讲解，true:重复讲解，false：不重复讲解
     */
    private Boolean repetition;

    /**
     * 复讲间隔时间，默认30，单位：分钟
     */
    private Integer intervalTime;

    /**
     * 是否显示，true：显示，false：不显示
     */
    private Boolean showStatus;

    /**
     * 是否讲解，true:讲解 false：不讲解
     */
    private Boolean explainStatus;

    /**
     * 自定义 POI 分类
     */
    private Integer customizeCategoryCode;

    /**
     * 讲解点数据来源：map：地图，mini：小程序采集，pc：PC 标记，beacon：信标
     */
    private String labelSource;

    /**
     * 讲解点类型：poi：讲解点，beacon：信标讲解点
     */
    private String labelType;

    /**
     * 信标编码
     */
    private String beaconCode;

    /**
     * 默认讲解词
     */
    private String labelTextDefault;

    public SysTouristLabel(){}

    public SysTouristLabel(ScenicSpot scenicSpot, LabelCreatedByIbeaconCmd cmd) {
        this.touristId = Long.valueOf(scenicSpot.getId());
        this.touristName = scenicSpot.getName();
        this.status = LabelStatusEnum.ONE.getCode();
        this.labelName = cmd.getLabelName();

        this.radius = 20;
        this.repetition = true;
        this.intervalTime = 30;
        this.showStatus = false;
        this.explainStatus = true;
        this.labelSource = LabelSourceEnum.BEACON.getCode();

        this.setCreateBy("system");
        this.setCreateTime(new Date());
        this.setUpdateTime(new Date());

    }

    public SysTouristLabel(ScenicSpot scenicSpot, SupplementMapInfoCmd cmd) {
        this.touristId = Long.valueOf(scenicSpot.getId());
        this.touristName = scenicSpot.getName();
        this.status = LabelStatusEnum.ONE.getCode();
        this.labelSource = LabelSourceEnum.MAP.getCode();
        this.supplementMapInfo(cmd);
        this.setDefaultValue();
    }

    public SysTouristLabel(PoiMarkCmd cmd, ScenicSpot scenicSpot) {

        this.labelName = cmd.getLabelName();
        this.touristId = cmd.getTouristId();
        this.touristName = scenicSpot.getName();
        this.longitude = cmd.getLongitude();
        this.latitude = cmd.getLatitude();
        this.joinCluster = true;
        this.polygon = cmd.getPolygon();
        this.cityCode = scenicSpot.getCityCode();
        this.cityName = scenicSpot.getCityName();
        this.category = Constants.CATEGORY_DEFAULT;
        this.categoryCode = Constants.CATEGORY_CODE_DEFAULT;
        this.status = LabelStatusEnum.ONE.getCode();
        this.labelSource = LabelSourceEnum.MINI.getCode();
        this.setDefaultValue();
    }

    public SysTouristLabel(LabelCreateCmd cmd) {

        this.labelName = cmd.getLabelName();
        this.touristId = cmd.getTouristId();
        this.touristName = cmd.getTouristName();
        this.longitude = cmd.getLongitude();
        this.latitude = cmd.getLatitude();
        this.polygon = cmd.getPolygon();
        this.cityCode = cmd.getCityCode();
        this.cityName = cmd.getCityName();
        this.categoryCode = cmd.getCategoryCode();
        this.customizeCategoryCode = cmd.getCustomizeCategoryCode();
        this.status = LabelStatusEnum.ZERO.getCode();
        this.beaconCode = cmd.getBeaconCode();
        String labelType = cmd.getLabelType();
        labelType = StringUtils.isBlank(labelType) ? LabelTypeEnum.POI.getCode() : labelType;
        if (labelType.equals(LabelTypeEnum.POI.getCode()) ) {
            this.labelType = LabelTypeEnum.POI.getCode();
            this.joinCluster = cmd.getJoinCluster();
            this.radius = cmd.getRadius();
            this.repetition = cmd.getRepetition();
            this.intervalTime = cmd.getIntervalTime();
            this.showStatus = cmd.getShowStatus();
            this.explainStatus = cmd.getExplainStatus();
            this.labelSource = LabelSourceEnum.PC.getCode();
        }

        if (labelType.equals(LabelTypeEnum.BEACON.getCode())) {
            this.labelType = LabelTypeEnum.BEACON.getCode();
            this.joinCluster = true;
            this.radius = 20;
            this.repetition = cmd.getRepetition();
            this.intervalTime = cmd.getIntervalTime();
            this.showStatus = false;
            this.explainStatus = true;
            this.labelSource = LabelSourceEnum.BEACON.getCode();
        }

        this.setCreateBy("system");
        this.setCreateTime(new Date());
        this.setUpdateTime(new Date());

    }

    public String getBeaconCode() {
        return beaconCode;
    }

    public void setBeaconCode(String beaconCode) {
        this.beaconCode = beaconCode;
    }

    public String getLabelType() {
        return labelType;
    }

    public void setLabelType(String labelType) {
        this.labelType = labelType;
    }

    public String getLabelSource() {
        return labelSource;
    }

    public void setLabelSource(String labelSource) {
        this.labelSource = labelSource;
    }

    public Integer getCacheOk() {
        return cacheOk;
    }

    public void setCacheOk(Integer cacheOk) {
        this.cacheOk = cacheOk;
    }

    public String getLabelText() {
        return labelText;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public void setLabelText(String labelText) {
        this.labelText = labelText;
    }
    /**
     * 审核状态
     */
    @TableField("status")
    private String status;

    /**
     * POI 分类
     */
    @TableField("category")
    private String category;

    /**
     * POI 分类编码
     */
    @TableField("category_code")
    private Integer categoryCode;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Integer getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(Integer categoryCode) {
        this.categoryCode = categoryCode;
    }

    public String getPolygon() {
        return polygon;
    }

    public void setPolygon(String polygon) {
        this.polygon = polygon;
    }

    public Integer getQueryCase() {
        return queryCase;
    }

    public void setQueryCase(Integer queryCase) {
        this.queryCase = queryCase;
    }

    public Integer getTmapStatus() {
        return tmapStatus;
    }

    public void setTmapStatus(Integer tmapStatus) {
        this.tmapStatus = tmapStatus;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTouristId(Long touristId) 
    {
        this.touristId = touristId;
    }

    public Long getTouristId() 
    {
        return touristId;
    }
    public void setTouristName(String touristName) 
    {
        this.touristName = touristName;
    }

    public String getTouristName() 
    {
        return touristName;
    }
    public void setLabelName(String labelName) 
    {
        this.labelName = labelName;
    }

    public String getLabelName() 
    {
        return labelName;
    }
    public void setImageWidth(Long imageWidth) 
    {
        this.imageWidth = imageWidth;
    }

    public Long getImageWidth() 
    {
        return imageWidth;
    }
    public void setImageHeight(Long imageHeight) 
    {
        this.imageHeight = imageHeight;
    }

    public Long getImageHeight() 
    {
        return imageHeight;
    }
    public void setX1y1(String x1y1) 
    {
        this.x1y1 = x1y1;
    }

    public String getX1y1() 
    {
        return x1y1;
    }
    public void setX2y2(String x2y2) 
    {
        this.x2y2 = x2y2;
    }

    public String getX2y2() 
    {
        return x2y2;
    }

    public String getLongitude() {
        return StringUtils.isNotBlank(longitude) ? longitude.trim() : StringUtils.EMPTY;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return StringUtils.isNotBlank(latitude) ? latitude.trim() : StringUtils.EMPTY;
    }

    public double getLongitudeDouble() {
        return StringUtils.isNotBlank(longitude) ? Double.parseDouble(longitude) : 0;
    }

    public double getLatitudeDouble() {
        return StringUtils.isNotBlank(latitude) ? Double.parseDouble(latitude) : 0;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String genAddress(){
        return this.touristName + this.labelName;
    }

    public Boolean getJoinCluster() {
        return joinCluster;
    }

    public void setJoinCluster(Boolean joinCluster) {
        this.joinCluster = joinCluster;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("touristId", getTouristId())
            .append("touristName", getTouristName())
            .append("labelName", getLabelName())
            .append("imageWidth", getImageWidth())
            .append("imageHeight", getImageHeight())
            .append("x1y1", getX1y1())
            .append("x2y2", getX2y2())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("remark", getRemark())
            .toString();
    }

    public static String dealLabelName(String touristName, String labelName){


        final String regex = "[()（）·]";

        List<String> list = List.of("观光区", "自然风景区", "风景区", "景区", "办公区", "自然保护区", "观光体验", "景区(4A)",
                "博物馆", "风景名胜区", "自然风景区", "生态旅游景区", "旅游风景区", "旅游休闲度假区", "旅游度假区", "主峰", "原始生态旅游区",
                "旅游度假区", "旅游区", "景区站", "3期", "旅游景区", "旅游区");

        String labelNameDeal = labelName;

        if (touristName.equals(labelNameDeal)) {
            return labelNameDeal;
        }

        if (labelNameDeal.contains("-")) {
            String prefix = labelNameDeal.substring(0, labelNameDeal.indexOf("-"));
            LevenshteinDistance distance = LevenshteinDistance.getDefaultInstance();
            int distanceValue = distance.apply(touristName, prefix);
            double similarity = 1.0 - (double) distanceValue / Math.max(touristName.length(), prefix.length());
            if (similarity >= 0.2) {
                labelNameDeal = labelNameDeal.substring(labelNameDeal.indexOf("-") + 1);
                return labelNameDeal.replaceAll(regex, "");
            }
        }

        labelNameDeal = labelNameDeal.replace(touristName, "");

        for (String s : list) {

            if (labelNameDeal.equals(s)) {

                return (touristName + labelNameDeal).replaceAll(regex, "");
            }

            if (labelNameDeal.contains(s)) {
                labelNameDeal = labelNameDeal.replace(s, "");
                return labelNameDeal.replaceAll(regex, "");
            }
        }

        return labelNameDeal.replaceAll(regex, "");
    }

    public void setDefaultValue(){

        this.radius = 20;
        this.repetition = true;
        this.intervalTime = 30 * 60;
        this.showStatus = true;
        this.explainStatus = true;
        this.joinCluster = true;

        this.setCreateBy("system");
        this.setCreateTime(new Date());
        this.setUpdateTime(new Date());
    }

    public Integer getAudioCacheOk() {
        return audioCacheOk;
    }

    public void setAudioCacheOk(Integer audioCacheOk) {
        this.audioCacheOk = audioCacheOk;
    }

    public Integer getRadius() {
        return radius;
    }

    public void setRadius(Integer radius) {
        this.radius = radius;
    }

    public Boolean getRepetition() {
        return repetition;
    }

    public void setRepetition(Boolean repetition) {
        this.repetition = repetition;
    }

    public Integer getIntervalTime() {
        return intervalTime;
    }

    public void setIntervalTime(Integer intervalTime) {
        this.intervalTime = intervalTime;
    }

    public Boolean getShowStatus() {
        return showStatus;
    }

    public void setShowStatus(Boolean showStatus) {
        this.showStatus = showStatus;
    }

    public Boolean getExplainStatus() {
        return explainStatus;
    }

    public void setExplainStatus(Boolean explainStatus) {
        this.explainStatus = explainStatus;
    }

    public Integer getCustomizeCategoryCode() {
        return customizeCategoryCode;
    }

    public void setCustomizeCategoryCode(Integer customizeCategoryCode) {
        this.customizeCategoryCode = customizeCategoryCode;
    }

    public String getIsImportant() {
        return isImportant;
    }

    public void setIsImportant(String isImportant) {
        this.isImportant = isImportant;
    }

    public void editLabel(LabelEditCmd cmd) {
        this.id = cmd.getId();
        this.touristId = cmd.getTouristId();
        this.labelName = cmd.getLabelName();
        this.joinCluster = cmd.isJoinCluster();
        this.radius = cmd.getRadius();
        this.repetition = cmd.getRepetition();
        this.intervalTime = cmd.getIntervalTime();
        this.showStatus = cmd.getShowStatus();
        this.explainStatus = cmd.getExplainStatus();
        this.categoryCode = cmd.getCategoryCode();
        this.customizeCategoryCode = cmd.getCustomizeCategoryCode();
        this.status = LabelStatusEnum.ONE.getCode();
        this.polygon = cmd.getPolygon();
        this.longitude = cmd.getLongitude();
        this.latitude = cmd.getLatitude();
        this.setUpdateTime(new Date());
        this.beaconCode = cmd.getBeaconCode();
        this.labelType = cmd.getLabelType();

    }

    public void  supplementMapInfo(SupplementMapInfoCmd cmd){
        this.cityName = cmd.getCityName();
        this.cityCode = cmd.getCityCode();
        this.longitude = cmd.getLongitude();
        this.latitude = cmd.getLatitude();
        this.category = cmd.getCategory();
        this.categoryCode = cmd.getCategoryCode();
        this.polygon = cmd.getPolygon();
        this.labelName = cmd.getName();
    }

}
