--------------------------------------------------------------------------------
用户表添加一个 language_flag 新字段，依次执行下面的 sql 语句：

    -- 添加新字段基础语法
    ALTER TABLE sys_user
    ADD COLUMN language_flag VARCHAR(255);
    
    -- 添加默认值
    ALTER TABLE sys_user
    ALTER COLUMN language_flag SET DEFAULT 'Chinese';
    
    -- 添加字段注释
    COMMENT ON COLUMN sys_user.language_flag IS '语言';
    
    -- 设置默认值
    update sys_user set language_flag = 'Chinese' where language_flag is null;


    ALTER TABLE scenic_spot ADD COLUMN service_charge numeric(5,2);
    COMMENT ON COLUMN scenic_spot.service_charge IS 'Ai导游服务费';
    
    ALTER TABLE scenic_spot ADD COLUMN radius int4 DEFAULT 20;
    COMMENT ON COLUMN scenic_spot.radius IS '围栏圆形半径（米）';
    
    ALTER TABLE scenic_spot ADD COLUMN repetition int4 DEFAULT 0;
    COMMENT ON COLUMN scenic_spot.repetition IS '是否重复讲解（0：否；1：是）';
    
    ALTER TABLE scenic_spot ADD COLUMN interval_time int4 DEFAULT 30;
    COMMENT ON COLUMN scenic_spot.interval_time IS '复讲间隔时间（秒）';
    
    ALTER TABLE scenic_spot ADD COLUMN tmap_show int4 DEFAULT 1;
    COMMENT ON COLUMN scenic_spot.tmap_show IS '是否显示地图（0：否；1：是）';

    ALTER TABLE scenic_spot ADD COLUMN identify_type int4 DEFAULT 1;
    COMMENT ON COLUMN scenic_spot.identify_type IS '开开眼优先识别（1：文化符号；2：动植物）';
    
    ALTER TABLE scenic_spot ADD COLUMN service_activity numeric(5,2);
    COMMENT ON COLUMN scenic_spot.service_activity IS 'Ai导游服务活动价';


    CREATE TABLE scenic_admin (
        id SERIAL PRIMARY KEY,
        business_type int4 NOT NULL,
        business_code VARCHAR NOT NULL,
        user_name VARCHAR NOT NULL,
        user_phone VARCHAR NOT NULL,
        create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
    );
    -- 添加表注释
    COMMENT ON TABLE scenic_admin IS '管理员信息表';

    -- 添加列注释
    COMMENT ON COLUMN scenic_admin.id IS '主键ID';
    COMMENT ON COLUMN scenic_admin.business_type IS '类型：1、景区；2、渠道';
    COMMENT ON COLUMN scenic_admin.business_code IS '类型编码';
    COMMENT ON COLUMN scenic_admin.user_name IS '用户姓名';
    COMMENT ON COLUMN scenic_admin.user_phone IS '用户手机号';
    COMMENT ON COLUMN scenic_admin.create_time IS '创建时间';
    -- 索引
    CREATE INDEX idx_scenic_admin_type_code ON scenic_admin(business_type, business_code);

    CREATE TABLE scenic_headphone (
        id SERIAL PRIMARY KEY,
        business_type int4 NOT NULL,
        business_code VARCHAR NOT NULL,
        scenic_id int4,
        scenic_name VARCHAR,
        model VARCHAR NOT NULL,
        price numeric(5,2),
        locations VARCHAR,
        status int4 DEFAULT 1,
        scenic_price numeric(5,2),
        operate_price numeric(5,2),
        channel_price numeric(5,2),
        platform_price numeric(5,2),
        create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
    );
    -- 添加表注释
    COMMENT ON TABLE scenic_headphone IS '耳机信息表';

    -- 添加列注释
    COMMENT ON COLUMN scenic_headphone.id IS '主键ID';
    COMMENT ON COLUMN scenic_headphone.business_type IS '类型：1、景区；2、渠道';
    COMMENT ON COLUMN scenic_headphone.business_code IS '类型编码';
    COMMENT ON COLUMN scenic_headphone.scenic_id IS '景区id';
    COMMENT ON COLUMN scenic_headphone.scenic_name IS '景区名称';
    COMMENT ON COLUMN scenic_headphone.model IS '耳机型号';
    COMMENT ON COLUMN scenic_headphone.price IS '耳机售价';
    COMMENT ON COLUMN scenic_headphone.locations IS '耳机领取地点';
    COMMENT ON COLUMN scenic_headphone.status IS '上下架：0、下架；1、上架';
    COMMENT ON COLUMN scenic_headphone.scenic_price IS '景区结算价';
    COMMENT ON COLUMN scenic_headphone.operate_price IS '运营方结算价';
    COMMENT ON COLUMN scenic_headphone.channel_price IS '渠道结算价';
    COMMENT ON COLUMN scenic_headphone.platform_price IS '平台结算价';
    COMMENT ON COLUMN scenic_headphone.create_time IS '创建时间';
    -- 索引
    CREATE INDEX idx_scenic_headphone_type_code ON scenic_headphone(business_type, business_code);




    CREATE TABLE scenic_location (
        id SERIAL PRIMARY KEY,
        business_type int4 NOT NULL,
        business_code VARCHAR NOT NULL,
        scenic_id int4,
        scenic_name VARCHAR,
        location_code VARCHAR NOT NULL,
        location_name VARCHAR NOT NULL,
        location_type int4,
        location_url VARCHAR,
        create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
    );
    -- 添加表注释
    COMMENT ON TABLE scenic_location IS '点位信息表';

    -- 添加列注释
    COMMENT ON COLUMN scenic_location.id IS '主键ID';
    COMMENT ON COLUMN scenic_location.business_type IS '类型：1、景区；2、渠道';
    COMMENT ON COLUMN scenic_location.business_code IS '类型编码';
    COMMENT ON COLUMN scenic_location.scenic_id IS '景区id';
    COMMENT ON COLUMN scenic_location.scenic_name IS '景区名称';
    COMMENT ON COLUMN scenic_location.location_code IS '点位编码';
    COMMENT ON COLUMN scenic_location.location_name IS '点位名称';
    COMMENT ON COLUMN scenic_location.location_type IS '点位类型：1：默认；2：可编辑';
    COMMENT ON COLUMN scenic_location.location_url IS '点位海报';
    COMMENT ON COLUMN scenic_location.create_time IS '创建时间';
    -- 索引
    CREATE INDEX idx_scenic_location_type_code ON scenic_location(business_type, business_code);


    CREATE TABLE scenic_ratio (
        id SERIAL PRIMARY KEY,
        business_type int4 NOT NULL,
        business_code VARCHAR NOT NULL,
        scenic_id int4,
        scenic_name VARCHAR,
        scenic_ratio int4,
        channel_ratio int4,
        platform_ratio int4,
        create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
    );
    -- 添加表注释
    COMMENT ON TABLE scenic_ratio IS '分佣比例表';

    -- 添加列注释
    COMMENT ON COLUMN scenic_ratio.id IS '主键ID';
    COMMENT ON COLUMN scenic_ratio.business_type IS '类型：1、景区；2、渠道';
    COMMENT ON COLUMN scenic_ratio.business_code IS '类型编码';
    COMMENT ON COLUMN scenic_ratio.scenic_id IS '景区id';
    COMMENT ON COLUMN scenic_ratio.scenic_name IS '景区名称';
    COMMENT ON COLUMN scenic_ratio.scenic_ratio IS '景区比例';
    COMMENT ON COLUMN scenic_ratio.channel_ratio IS '渠道比例';
    COMMENT ON COLUMN scenic_ratio.platform_ratio IS '平台比例';
    COMMENT ON COLUMN scenic_ratio.create_time IS '创建时间';
    -- 索引
    CREATE INDEX idx_scenic_ratio_type_code ON scenic_ratio(business_type, business_code);

    -- token_detail
    ALTER TABLE token_detail ADD COLUMN scenic_id int4;

    -- scenic_channel
        CREATE TABLE "scenic_channel" (
        "id" BIGSERIAL PRIMARY KEY,
        "channel_id" "varchar" COLLATE "default",
        "channel_name" "varchar" COLLATE "default" NOT NULL,
        "channel_person" "varchar" COLLATE "default" NOT NULL,
        "channel_statue" "varchar" COLLATE "default",
        "province_code" "varchar" COLLATE "default",
        "province_name" "varchar" COLLATE "default",
        "city_code" "varchar" COLLATE "default",
        "city_name" "varchar" COLLATE "default",
        "district_code" "varchar" COLLATE "default",
        "district_name" "varchar" COLLATE "default",
        "detail_address" "varchar" COLLATE "default",
        "channel_person_phone" "varchar" COLLATE "default"
        )
        ;
        COMMENT ON COLUMN "scenic_channel"."channel_id" IS '渠道id';
        COMMENT ON COLUMN "scenic_channel"."channel_name" IS '渠道名称';
        COMMENT ON COLUMN "scenic_channel"."channel_person" IS '渠道负责人';
        COMMENT ON COLUMN "scenic_channel"."channel_statue" IS '渠道上线状态 0 上线，  1 下线，2 删除';
        COMMENT ON COLUMN "scenic_channel"."province_code" IS '省份编码';
        COMMENT ON COLUMN "scenic_channel"."province_name" IS '省份名称';
        COMMENT ON COLUMN "scenic_channel"."city_code" IS '城市编码';
        COMMENT ON COLUMN "scenic_channel"."city_name" IS '城市名称';
        COMMENT ON COLUMN "scenic_channel"."district_code" IS '区县编码';
        COMMENT ON COLUMN "scenic_channel"."district_name" IS '区县名称';
        COMMENT ON COLUMN "scenic_channel"."detail_address" IS '详细地址';
        COMMENT ON COLUMN "scenic_channel"."channel_person_phone" IS '联系电话';
        COMMENT ON TABLE "scenic_channel" IS '渠道信息';
        
        CREATE INDEX "c_idx" ON "scenic_channel" USING btree ("channel_id" COLLATE "default" "text_ops" ASC NULLS LAST);    

    -- scenic_operate
        CREATE TABLE "scenic_operate" (
        "id" BIGSERIAL PRIMARY KEY,
        "operate_id" "varchar" COLLATE "default",
        "operate_name" "varchar" COLLATE "default" NOT NULL,
        "operate_person" "varchar" COLLATE "default" NOT NULL,
        "operate_statue" "varchar" COLLATE "default",
        "province_code" "varchar" COLLATE "default",
        "province_name" "varchar" COLLATE "default",
        "city_code" "varchar" COLLATE "default",
        "city_name" "varchar" COLLATE "default",
        "district_code" "varchar" COLLATE "default",
        "district_name" "varchar" COLLATE "default",
        "detail_address" "varchar" COLLATE "default",
        "operate_person_phone" "varchar" COLLATE "default"
        )
        ;
        COMMENT ON COLUMN "scenic_operate"."operate_id" IS '运营ID';
        COMMENT ON COLUMN "scenic_operate"."operate_name" IS '运营名称';
        COMMENT ON COLUMN "scenic_operate"."operate_person" IS '运营方负责人';
        COMMENT ON COLUMN "scenic_operate"."operate_statue" IS '上线状态';
        COMMENT ON COLUMN "scenic_operate"."province_code" IS '省份编码';
        COMMENT ON COLUMN "scenic_operate"."province_name" IS '省份名称';
        COMMENT ON COLUMN "scenic_operate"."city_code" IS '城市编码';
        COMMENT ON COLUMN "scenic_operate"."city_name" IS '城市名称';
        COMMENT ON COLUMN "scenic_operate"."district_code" IS '区县编码';
        COMMENT ON COLUMN "scenic_operate"."district_name" IS '区县名称';
        COMMENT ON COLUMN "scenic_operate"."detail_address" IS '地址';
        COMMENT ON COLUMN "scenic_operate"."operate_person_phone" IS '联系电话';
        COMMENT ON TABLE "scenic_operate" IS '景区运营';

    -- secenic_channel_operate
        CREATE TABLE "secenic_channel_operate" (
        "id" SERIAL PRIMARY KEY,
        "business_type" "int4" NOT NULL,
        "channel_code" "varchar" COLLATE "default" NOT NULL,
        "scenic_id" "int4",
        "scenic_name" "varchar" COLLATE "default",
        "operate_id" "varchar" COLLATE "default",
        "operate_name" "varchar" COLLATE "default",
        "create_time" "timestamp" NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "channel_name" "varchar" COLLATE "default"
        )
        ;
        COMMENT ON COLUMN "secenic_channel_operate"."business_type" IS '类型：1、景区；2、渠道 3 运营';
        COMMENT ON COLUMN "secenic_channel_operate"."scenic_id" IS '景区ID';
        COMMENT ON COLUMN "secenic_channel_operate"."scenic_name" IS '景区名称';
        COMMENT ON COLUMN "secenic_channel_operate"."operate_id" IS '运营ID';
        COMMENT ON COLUMN "secenic_channel_operate"."operate_name" IS '运营名称';
        COMMENT ON TABLE "secenic_channel_operate" IS '渠道下的运营对应的景区';


ALTER TABLE IF EXISTS public.scenic_spot
ADD COLUMN tmap_scenic_name character varying(255);

ALTER TABLE IF EXISTS public.scenic_spot
ADD COLUMN check_time timestamp without time zone;

ALTER TABLE IF EXISTS public.payment_order
RENAME me_open_id TO open_id;

ALTER TABLE IF EXISTS public.scenic_spot
ALTER COLUMN zoom SET DEFAULT 16;

ALTER TABLE IF EXISTS public.scenic_spot
ADD COLUMN poi_id character varying(64);

ALTER TABLE IF EXISTS public.scenic_spot
ADD COLUMN polygon character varying(512);
ALTER TABLE IF EXISTS public.payment_order
ADD COLUMN order_id character varying(64);
ALTER TABLE IF EXISTS public.payment_order
ADD COLUMN origin_payment_order_id character varying(64);
ALTER TABLE public.scenic_spot
ALTER COLUMN polygon TYPE text COLLATE pg_catalog."default";


    -- guide_pay_order
        CREATE TABLE "guide_pay_order" (
        "id" BIGSERIAL PRIMARY KEY,
        "order_id" "varchar" COLLATE "default",
        "pay_time" "timestamp",
        "pay_statue" "varchar" COLLATE "default",
        "order_statue" "varchar" COLLATE "default",
        "open_id" "varchar" COLLATE "default",
        "user_name" "varchar" COLLATE "default",
        "order_amount" numeric(7,2)
        )
        ;

    -- guide_pay_order_item

        CREATE TABLE "guide_pay_order_item" (
        "id" BIGSERIAL PRIMARY KEY,
        "order_id" "varchar" COLLATE "default",
        "channel_id" "varchar" COLLATE "default",
        "channel_name" "varchar" COLLATE "default",
        "scenic_name" "varchar" COLLATE "default",
        "point_id" "varchar" COLLATE "default",
        "point_name" "varchar" COLLATE "default",
        "channel_raw" "text" COLLATE "default",
        "order_item" "varchar" COLLATE "default",
        "headset_model" "varchar" COLLATE "default",
        "create_time" "timestamp",
        "order_statue" "varchar" COLLATE "default",
        "open_id" "varchar" COLLATE "default",
        "user_name" "varchar" COLLATE "default",
        "order_amount" numeric(7,2),
        "item_amount" numeric(7,2),
        "item_platform_amount" numeric(7,2),
        "item_channel_amount" numeric(7,2),
        "item_guide_amount" numeric(7,2),
        "item_operate_amount" numeric(7,2),
        "operate_id" "varchar" COLLATE "default",
        "operate_name" "varchar" COLLATE "default",
        "scenic_id" "int4",
        "headset_statue" "varchar" COLLATE "default",
        "locations" "varchar" COLLATE "default"
        )
        ;
        COMMENT ON COLUMN "guide_pay_order_item"."order_id" IS '订单id';
        COMMENT ON COLUMN "guide_pay_order_item"."channel_id" IS '渠道id';
        COMMENT ON COLUMN "guide_pay_order_item"."channel_raw" IS '渠道快照';
        COMMENT ON COLUMN "guide_pay_order_item"."order_item" IS '0 ai导游， 1 耳机';
        COMMENT ON COLUMN "guide_pay_order_item"."headset_model" IS '项目明细，耳机类型';
        COMMENT ON COLUMN "guide_pay_order_item"."create_time" IS '支付时间';
        COMMENT ON COLUMN "guide_pay_order_item"."order_statue" IS '订单状态';
        COMMENT ON COLUMN "guide_pay_order_item"."open_id" IS 'openid';
        COMMENT ON COLUMN "guide_pay_order_item"."user_name" IS '用户账号';
        COMMENT ON COLUMN "guide_pay_order_item"."operate_id" IS '运营ID';
        COMMENT ON COLUMN "guide_pay_order_item"."operate_name" IS ' 运营name';
        COMMENT ON COLUMN "guide_pay_order_item"."scenic_id" IS '景区ID';
        COMMENT ON COLUMN "guide_pay_order_item"."headset_statue" IS '耳机领取状态， 0 未领取， 1 已领取， 2申请退款， 3 已退款';
        COMMENT ON COLUMN "guide_pay_order_item"."locations" IS '耳机领取地点';

    -- refund_order

        CREATE TABLE "refund_order" (
        "id" SERIAL PRIMARY KEY,
        "order_id" "varchar" COLLATE "default",
        "old_order_id" "varchar" COLLATE "default",
        "order_amount" numeric(7,2),
        "item" "varchar" COLLATE "default",
        "order_statue" "varchar" COLLATE "default",
        "create_time" "timestamp",
        "open_id" "varchar" COLLATE "default"
        )
        ;
        COMMENT ON COLUMN "refund_order"."order_id" IS '订单id';
        COMMENT ON COLUMN "refund_order"."old_order_id" IS '主订单id';
        COMMENT ON COLUMN "refund_order"."order_amount" IS '退款金额';
        COMMENT ON COLUMN "refund_order"."item" IS '服务类型， 耳机';
        COMMENT ON COLUMN "refund_order"."order_statue" IS '0 申请退款， 1 退款成功';
        COMMENT ON COLUMN "refund_order"."open_id" IS 'openId';
        COMMENT ON TABLE "refund_order" IS '退款订单';



--------------------------------------------------------------------------------


