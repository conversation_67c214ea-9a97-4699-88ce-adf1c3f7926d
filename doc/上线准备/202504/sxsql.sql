-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_user";
CREATE TABLE "public"."sys_user" (
                                     "user_id" bigserial  PRIMARY KEY,
                                     "dept_id" "pg_catalog"."int8",
                                     "user_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "nick_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "user_type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT '00'::character varying,
                                     "email" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "phonenumber" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "sex" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT '0'::character varying,
                                     "avatar" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "password" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "status" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
                                     "del_flag" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
                                     "login_ip" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "login_date" "pg_catalog"."timestamp",
                                     "create_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "create_time" "pg_catalog"."timestamp",
                                     "update_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "update_time" "pg_catalog"."timestamp",
                                     "remark" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "role_type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "hobby_types" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "guide_style" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "guide_persona" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "open_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "user_rec_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "token_balance" "pg_catalog"."numeric",
                                     "token_balance_total" "pg_catalog"."numeric"
)
;
COMMENT ON COLUMN "public"."sys_user"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."sys_user"."dept_id" IS '部门ID';
COMMENT ON COLUMN "public"."sys_user"."user_name" IS '用户账号';
COMMENT ON COLUMN "public"."sys_user"."nick_name" IS '用户昵称';
COMMENT ON COLUMN "public"."sys_user"."user_type" IS '用户类型（00系统用户）';
COMMENT ON COLUMN "public"."sys_user"."email" IS '用户邮箱';
COMMENT ON COLUMN "public"."sys_user"."phonenumber" IS '手机号码';
COMMENT ON COLUMN "public"."sys_user"."sex" IS '性别';
COMMENT ON COLUMN "public"."sys_user"."avatar" IS '头像地址';
COMMENT ON COLUMN "public"."sys_user"."password" IS '密码';
COMMENT ON COLUMN "public"."sys_user"."status" IS '帐号状态（0正常 1停用）';
COMMENT ON COLUMN "public"."sys_user"."del_flag" IS '删除标志（0代表存在 2代表删除）';
COMMENT ON COLUMN "public"."sys_user"."login_ip" IS '最后登录IP';
COMMENT ON COLUMN "public"."sys_user"."login_date" IS '最后登录时间';
COMMENT ON COLUMN "public"."sys_user"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_user"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_user"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_user"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."sys_user"."remark" IS '备注';
COMMENT ON COLUMN "public"."sys_user"."role_type" IS '角色类型';
COMMENT ON COLUMN "public"."sys_user"."hobby_types" IS '兴趣爱好';
COMMENT ON COLUMN "public"."sys_user"."guide_style" IS '导游风格';
COMMENT ON COLUMN "public"."sys_user"."guide_persona" IS '讲解人设';
COMMENT ON COLUMN "public"."sys_user"."open_id" IS '微信 openid';
COMMENT ON COLUMN "public"."sys_user"."user_rec_code" IS '用户推荐码';
COMMENT ON COLUMN "public"."sys_user"."token_balance" IS '游豆余额';
COMMENT ON COLUMN "public"."sys_user"."token_balance_total" IS '游豆累计';
COMMENT ON TABLE "public"."sys_user" IS '用户信息表';

-- ----------------------------
-- Indexes structure for table sys_user
-- ----------------------------
CREATE INDEX "u_idx" ON "public"."sys_user" USING btree (
    "user_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

INSERT INTO "public"."sys_user"("dept_id", "user_name", "nick_name", "user_type", "email", "phonenumber", "sex", "avatar", "password", "status", "del_flag", "login_ip", "login_date", "create_by", "create_time", "update_by", "update_time", "remark", "role_type", "hobby_types", "guide_style", "guide_persona", "open_id", "user_rec_code", "token_balance", "token_balance_total") VALUES (103, 'admin', '若依', '00', '<EMAIL>', '15888888888', '1', NULL, '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '***********', '2025-04-16 10:18:03.068', 'admin', '2025-02-12 10:50:21', NULL, '2025-04-16 10:18:03.073698', '管理员', 'ADULT', NULL, NULL, 'BOY', NULL, NULL, '0.0000', '0.0000');
INSERT INTO "public"."sys_user"("dept_id", "user_name", "nick_name", "user_type", "email", "phonenumber", "sex", "avatar", "password", "status", "del_flag", "login_ip", "login_date", "create_by", "create_time", "update_by", "update_time", "remark", "role_type", "hobby_types", "guide_style", "guide_persona", "open_id", "user_rec_code", "token_balance", "token_balance_total") VALUES (105, 'ry', '若依', '00', '<EMAIL>', '15666666666', '1', NULL, '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2025-02-12 10:50:21', 'admin', '2025-02-12 10:50:21', NULL, NULL, '测试员', 'ADULT', NULL, NULL, 'BOY', NULL, NULL, NULL, NULL);



-- ----------------------------
-- Table structure for aigu_scenic_voice
-- ----------------------------
DROP TABLE IF EXISTS "public"."aigu_scenic_voice";
CREATE TABLE "public"."aigu_scenic_voice" (
                                              "id"  bigserial  PRIMARY KEY,
                                              "scenic_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                              "scenic_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                              "scenic_location" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                              "img" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                              "context" "pg_catalog"."text" COLLATE "pg_catalog"."default",
                                              "text_voices" "pg_catalog"."text" COLLATE "pg_catalog"."default",
                                              "statue" "pg_catalog"."int4",
                                              "create_time" "pg_catalog"."timestamp",
                                              "update_time" "pg_catalog"."timestamp"
)
;
COMMENT ON COLUMN "public"."aigu_scenic_voice"."scenic_id" IS '景点ID';
COMMENT ON COLUMN "public"."aigu_scenic_voice"."scenic_name" IS '景点名称';
COMMENT ON COLUMN "public"."aigu_scenic_voice"."scenic_location" IS '景点省市县';
COMMENT ON COLUMN "public"."aigu_scenic_voice"."img" IS '导览图url';
COMMENT ON COLUMN "public"."aigu_scenic_voice"."context" IS '景点介绍';
COMMENT ON COLUMN "public"."aigu_scenic_voice"."text_voices" IS '景点音频MP3 逗号分隔';
COMMENT ON COLUMN "public"."aigu_scenic_voice"."statue" IS '0  正常 1 无效';


-- ----------------------------
-- Table structure for base_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."base_config";
CREATE TABLE "public"."base_config" (
                                        "id"  serial  PRIMARY KEY,
                                        "register_reward" "pg_catalog"."int4",
                                        "referral_reward" "pg_catalog"."int4",
                                        "referral_first_reward" "pg_catalog"."int4",
                                        "lectures_num" "pg_catalog"."int4",
                                        "five_expend" "pg_catalog"."int4",
                                        "four_expend" "pg_catalog"."int4",
                                        "three_expend" "pg_catalog"."int4",
                                        "guide_reward" "pg_catalog"."int4",
                                        "create_time" "pg_catalog"."timestamp",
                                        "update_time" "pg_catalog"."timestamp"
)
;
COMMENT ON COLUMN "public"."base_config"."register_reward" IS '注册奖励';
COMMENT ON COLUMN "public"."base_config"."referral_reward" IS '推荐奖励';
COMMENT ON COLUMN "public"."base_config"."referral_first_reward" IS '推荐新用户首充奖励';
COMMENT ON COLUMN "public"."base_config"."lectures_num" IS '免费讲解次数';
COMMENT ON COLUMN "public"."base_config"."five_expend" IS '5A景区';
COMMENT ON COLUMN "public"."base_config"."four_expend" IS '4A景区';
COMMENT ON COLUMN "public"."base_config"."three_expend" IS '3A及以下';
COMMENT ON COLUMN "public"."base_config"."guide_reward" IS '导览图奖励';
COMMENT ON COLUMN "public"."base_config"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."base_config"."update_time" IS '修改时间';

-- ----------------------------
-- Records of base_config
-- ----------------------------
INSERT INTO "public"."base_config" VALUES (1, 50, 10, 5, 8, 20, 10, 5, 10, '2025-04-07 16:56:52', '2025-04-10 17:57:48');


-- ----------------------------
-- Table structure for payment_order
-- ----------------------------
DROP TABLE IF EXISTS "public"."payment_order";
CREATE TABLE "public"."payment_order" (
                                          "id"  bigserial  PRIMARY KEY,
                                          "user_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
                                          "me_open_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
                                          "payment_order_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
                                          "payment_order_status" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
                                          "actual_transaction_amount" "pg_catalog"."numeric" NOT NULL,
                                          "description" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                          "effective_period" "pg_catalog"."int4",
                                          "time_expire" "pg_catalog"."timestamp",
                                          "channel_serial_number" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                          "success_time" "pg_catalog"."timestamp",
                                          "recharge_config_raw" "pg_catalog"."text" COLLATE "pg_catalog"."default",
                                          "create_time" "pg_catalog"."timestamp",
                                          "update_time" "pg_catalog"."timestamp"
)
;
COMMENT ON COLUMN "public"."payment_order"."id" IS '主键';
COMMENT ON COLUMN "public"."payment_order"."user_id" IS '用户ID, 关联会员表';
COMMENT ON COLUMN "public"."payment_order"."me_open_id" IS '会员openId';
COMMENT ON COLUMN "public"."payment_order"."payment_order_id" IS '支付订单业务ID';
COMMENT ON COLUMN "public"."payment_order"."payment_order_status" IS '支付订单状态#com.iciyun.mall.payment.biz.enums.PaymentOrderStatusEnum#';
COMMENT ON COLUMN "public"."payment_order"."actual_transaction_amount" IS '订单金额, #cn.hutool.core.math.Money#';
COMMENT ON COLUMN "public"."payment_order"."description" IS '商品描述';
COMMENT ON COLUMN "public"."payment_order"."effective_period" IS '有效期，单位：分钟';
COMMENT ON COLUMN "public"."payment_order"."time_expire" IS '支付超时时间';
COMMENT ON COLUMN "public"."payment_order"."channel_serial_number" IS '支付通道支付流水号';
COMMENT ON COLUMN "public"."payment_order"."success_time" IS '支付成功时间';
COMMENT ON COLUMN "public"."payment_order"."recharge_config_raw" IS '充值配置快照';
COMMENT ON COLUMN "public"."payment_order"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."payment_order"."update_time" IS '更新时间';
COMMENT ON TABLE "public"."payment_order" IS '支付订单表';

-- ----------------------------
-- Indexes structure for table payment_order
-- ----------------------------
CREATE UNIQUE INDEX "UNIQUE_INDEX_PA_PAYMENT_ORDERID" ON "public"."payment_order" USING btree (
    "payment_order_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );



-- ----------------------------
-- Table structure for recharge_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."recharge_config";
CREATE TABLE "public"."recharge_config" (
                                            "id" bigserial  PRIMARY KEY,
                                            "rechage_amount" "pg_catalog"."numeric",
                                            "token" "pg_catalog"."int4",
                                            "donate_token" "pg_catalog"."int4",
                                            "create_time" "pg_catalog"."timestamp",
                                            "update_time" "pg_catalog"."timestamp"
)
;
COMMENT ON COLUMN "public"."recharge_config"."rechage_amount" IS '充值金额';
COMMENT ON COLUMN "public"."recharge_config"."token" IS '等价游豆';
COMMENT ON COLUMN "public"."recharge_config"."donate_token" IS '赠送游豆';
COMMENT ON COLUMN "public"."recharge_config"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."recharge_config"."update_time" IS '修改时间';


-- ----------------------------
-- Table structure for scenic_browse_history
-- ----------------------------
DROP TABLE IF EXISTS "public"."scenic_browse_history";
CREATE TABLE "public"."scenic_browse_history" (
                                                  "id" bigserial  PRIMARY KEY,
                                                  "user_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                                  "scenic_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                                  "scenic_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                                  "scenic_location" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                                  "browse_time" "pg_catalog"."timestamp",
                                                  "create_time" "pg_catalog"."timestamp",
                                                  "update_time" "pg_catalog"."timestamp"
)
;
COMMENT ON COLUMN "public"."scenic_browse_history"."scenic_id" IS '景区 Id';
COMMENT ON COLUMN "public"."scenic_browse_history"."scenic_name" IS '景区名称';
COMMENT ON COLUMN "public"."scenic_browse_history"."scenic_location" IS '景区位置';
COMMENT ON COLUMN "public"."scenic_browse_history"."browse_time" IS '浏览时间';



-- ----------------------------
-- Table structure for scenic_guide
-- ----------------------------
DROP TABLE IF EXISTS "public"."scenic_guide";
CREATE TABLE "public"."scenic_guide" (
                                         "id" bigserial  PRIMARY KEY,
                                         "guide_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "user_id" "pg_catalog"."int4",
                                         "user_phone" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "scenic_id" "pg_catalog"."int4",
                                         "scenic_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "scenic_address" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "guide_url" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "status" "pg_catalog"."int4",
                                         "create_time" "pg_catalog"."timestamp",
                                         "update_time" "pg_catalog"."timestamp"
)
;
COMMENT ON COLUMN "public"."scenic_guide"."guide_code" IS '导览图编号';
COMMENT ON COLUMN "public"."scenic_guide"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."scenic_guide"."user_phone" IS '用户手机号';
COMMENT ON COLUMN "public"."scenic_guide"."scenic_id" IS '景区id';
COMMENT ON COLUMN "public"."scenic_guide"."scenic_name" IS '景区名称';
COMMENT ON COLUMN "public"."scenic_guide"."scenic_address" IS '景区地址';
COMMENT ON COLUMN "public"."scenic_guide"."guide_url" IS '导览图';
COMMENT ON COLUMN "public"."scenic_guide"."status" IS '审批状态（0未通过，1已通过）';
COMMENT ON COLUMN "public"."scenic_guide"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."scenic_guide"."update_time" IS '更新时间';


-- ----------------------------
-- Table structure for sys_basics
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_basics";
CREATE TABLE "public"."sys_basics" (
                                       "id" bigserial  PRIMARY KEY,
                                       "people_num" "pg_catalog"."int4",
                                       "group_num" "pg_catalog"."int4",
                                       "series_num" "pg_catalog"."int4",
                                       "order_num" "pg_catalog"."int4",
                                       "own_ratio" "pg_catalog"."int4",
                                       "superior_ratio" "pg_catalog"."int4",
                                       "referrer_ratio" "pg_catalog"."int4",
                                       "agent_ratio" "pg_catalog"."int4",
                                       "technology_ratio" "pg_catalog"."int4",
                                       "market_ratio" "pg_catalog"."int4",
                                       "create_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                       "create_time" "pg_catalog"."timestamp",
                                       "update_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                       "update_time" "pg_catalog"."timestamp"
)
;
COMMENT ON COLUMN "public"."sys_basics"."id" IS '主键';
COMMENT ON COLUMN "public"."sys_basics"."people_num" IS '人数';
COMMENT ON COLUMN "public"."sys_basics"."group_num" IS '组数';
COMMENT ON COLUMN "public"."sys_basics"."series_num" IS '返佣级数';
COMMENT ON COLUMN "public"."sys_basics"."order_num" IS '下单数';
COMMENT ON COLUMN "public"."sys_basics"."own_ratio" IS '自占比';
COMMENT ON COLUMN "public"."sys_basics"."superior_ratio" IS '上级占比';
COMMENT ON COLUMN "public"."sys_basics"."referrer_ratio" IS '推荐人占比';
COMMENT ON COLUMN "public"."sys_basics"."agent_ratio" IS '代理占比';
COMMENT ON COLUMN "public"."sys_basics"."technology_ratio" IS '科技公司占比';
COMMENT ON COLUMN "public"."sys_basics"."market_ratio" IS '市场公司占比';
COMMENT ON COLUMN "public"."sys_basics"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_basics"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_basics"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_basics"."update_time" IS '更新时间';


-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_config";
CREATE TABLE "public"."sys_config" (
                                       "config_id" serial PRIMARY KEY,
                                       "config_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                       "config_key" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                       "config_value" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                       "config_type" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default",
                                       "create_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                       "create_time" "pg_catalog"."timestamp",
                                       "update_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                       "update_time" "pg_catalog"."timestamp",
                                       "remark" "pg_catalog"."varchar" COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."sys_config"."config_id" IS '参数主键';
COMMENT ON COLUMN "public"."sys_config"."config_name" IS '参数名称';
COMMENT ON COLUMN "public"."sys_config"."config_key" IS '参数键名';
COMMENT ON COLUMN "public"."sys_config"."config_value" IS '参数键值';
COMMENT ON COLUMN "public"."sys_config"."config_type" IS '系统内置（Y是 N否）';
COMMENT ON COLUMN "public"."sys_config"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_config"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_config"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_config"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."sys_config"."remark" IS '备注';
COMMENT ON TABLE "public"."sys_config" IS '参数配置表';

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO "public"."sys_config" VALUES (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '2025-02-12 10:50:22', '', NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO "public"."sys_config" VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', '2025-02-12 10:50:22', '', NULL, '初始化密码 123456');
INSERT INTO "public"."sys_config" VALUES (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', '2025-02-12 10:50:22', '', NULL, '深色主题theme-dark，浅色主题theme-light');
INSERT INTO "public"."sys_config" VALUES (4, '账号自助-验证码开关', 'sys.account.captchaEnabled', 'true', 'Y', 'admin', '2025-02-12 10:50:22', '', NULL, '是否开启验证码功能（true开启，false关闭）');
INSERT INTO "public"."sys_config" VALUES (5, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', '2025-02-12 10:50:22', '', NULL, '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO "public"."sys_config" VALUES (6, '用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 'admin', '2025-02-12 10:50:22', '', NULL, '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');
INSERT INTO "public"."sys_config" VALUES (100, '通用配置(json格式)', 'common', '{
  "bot1": {
    "botId1": "7477544904571715621",
    "cozeAccessToken": "pat_33VZB2QAomItCJIB09PQleolmrLWz4NXmmHJMWS0pdEILTDJ4lopVKk7jAem0RPh"
  }
}', 'N', 'admin', '2025-03-15 09:00:10', 'admin', '2025-03-26 14:15:02', '严格配置为 json 格式');


-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_dept";
CREATE TABLE "public"."sys_dept" (
                                     "dept_id"  bigserial  PRIMARY KEY,
                                     "parent_id" "pg_catalog"."int8",
                                     "ancestors" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "dept_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "order_num" "pg_catalog"."int4",
                                     "leader" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "phone" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "email" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "status" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default",
                                     "del_flag" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default",
                                     "create_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "create_time" "pg_catalog"."timestamp",
                                     "update_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "update_time" "pg_catalog"."timestamp"
)
;
COMMENT ON COLUMN "public"."sys_dept"."dept_id" IS '部门id';
COMMENT ON COLUMN "public"."sys_dept"."parent_id" IS '父部门id';
COMMENT ON COLUMN "public"."sys_dept"."ancestors" IS '祖级列表';
COMMENT ON COLUMN "public"."sys_dept"."dept_name" IS '部门名称';
COMMENT ON COLUMN "public"."sys_dept"."order_num" IS '显示顺序';
COMMENT ON COLUMN "public"."sys_dept"."leader" IS '负责人';
COMMENT ON COLUMN "public"."sys_dept"."phone" IS '联系电话';
COMMENT ON COLUMN "public"."sys_dept"."email" IS '邮箱';
COMMENT ON COLUMN "public"."sys_dept"."status" IS '部门状态（0正常 1停用）';
COMMENT ON COLUMN "public"."sys_dept"."del_flag" IS '删除标志（0代表存在 2代表删除）';
COMMENT ON COLUMN "public"."sys_dept"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_dept"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_dept"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_dept"."update_time" IS '更新时间';
COMMENT ON TABLE "public"."sys_dept" IS '部门表';

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO "public"."sys_dept" VALUES (100, 0, '0', '若依科技', 0, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-02-12 10:50:20', '', NULL);
INSERT INTO "public"."sys_dept" VALUES (101, 100, '0,100', '深圳总公司', 1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-02-12 10:50:20', '', NULL);
INSERT INTO "public"."sys_dept" VALUES (102, 100, '0,100', '长沙分公司', 2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-02-12 10:50:20', '', NULL);
INSERT INTO "public"."sys_dept" VALUES (103, 101, '0,100,101', '研发部门', 1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-02-12 10:50:20', '', NULL);
INSERT INTO "public"."sys_dept" VALUES (104, 101, '0,100,101', '市场部门', 2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-02-12 10:50:20', '', NULL);
INSERT INTO "public"."sys_dept" VALUES (105, 101, '0,100,101', '测试部门', 3, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-02-12 10:50:20', '', NULL);
INSERT INTO "public"."sys_dept" VALUES (106, 101, '0,100,101', '财务部门', 4, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-02-12 10:50:20', '', NULL);
INSERT INTO "public"."sys_dept" VALUES (107, 101, '0,100,101', '运维部门', 5, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-02-12 10:50:20', '', NULL);
INSERT INTO "public"."sys_dept" VALUES (108, 102, '0,100,102', '市场部门', 1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-02-12 10:50:21', '', NULL);
INSERT INTO "public"."sys_dept" VALUES (109, 102, '0,100,102', '财务部门', 2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2025-02-12 10:50:21', '', NULL);


-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_dict_data";
CREATE TABLE "public"."sys_dict_data" (
                                          "dict_code" bigserial  PRIMARY KEY,
                                          "dict_sort" "pg_catalog"."int4" DEFAULT 0,
                                          "dict_label" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                          "dict_value" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                          "dict_type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                          "css_class" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                          "list_class" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                          "is_default" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default" DEFAULT 'N'::bpchar,
                                          "status" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
                                          "create_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                          "create_time" "pg_catalog"."timestamp",
                                          "update_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                          "update_time" "pg_catalog"."timestamp",
                                          "remark" "pg_catalog"."varchar" COLLATE "pg_catalog"."default"
)
;

COMMENT ON COLUMN "public"."sys_dict_data"."dict_code" IS '字典编码';
COMMENT ON COLUMN "public"."sys_dict_data"."dict_sort" IS '字典排序';
COMMENT ON COLUMN "public"."sys_dict_data"."dict_label" IS '字典标签';
COMMENT ON COLUMN "public"."sys_dict_data"."dict_value" IS '字典键值';
COMMENT ON COLUMN "public"."sys_dict_data"."dict_type" IS '字典类型';
COMMENT ON COLUMN "public"."sys_dict_data"."css_class" IS '样式属性（其他样式扩展）';
COMMENT ON COLUMN "public"."sys_dict_data"."list_class" IS '表格回显样式';
COMMENT ON COLUMN "public"."sys_dict_data"."is_default" IS '是否默认（Y是 N否）';
COMMENT ON COLUMN "public"."sys_dict_data"."status" IS '状态（0正常 1停用）';
COMMENT ON COLUMN "public"."sys_dict_data"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_dict_data"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_dict_data"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_dict_data"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."sys_dict_data"."remark" IS '备注';
COMMENT ON TABLE "public"."sys_dict_data" IS '字典数据表';

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO "public"."sys_dict_data" VALUES (1, 1, '男', '0', 'sys_user_sex', NULL, NULL, 'Y', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (2, 2, '女', '1', 'sys_user_sex', NULL, NULL, 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (3, 3, '未知', '2', 'sys_user_sex', NULL, NULL, 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (4, 1, '显示', '0', 'sys_show_hide', NULL, 'primary', 'Y', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (5, 2, '隐藏', '1', 'sys_show_hide', NULL, 'danger', 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (6, 1, '正常', '0', 'sys_normal_disable', NULL, 'primary', 'Y', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (7, 2, '停用', '1', 'sys_normal_disable', NULL, 'danger', 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (8, 1, '正常', '0', 'sys_job_status', NULL, 'primary', 'Y', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (9, 2, '暂停', '1', 'sys_job_status', NULL, 'danger', 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', NULL, NULL, 'Y', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', NULL, NULL, 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (12, 1, '是', 'Y', 'sys_yes_no', NULL, 'primary', 'Y', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (13, 2, '否', 'N', 'sys_yes_no', NULL, 'danger', 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (14, 1, '通知', '1', 'sys_notice_type', NULL, 'warning', 'Y', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (15, 2, '公告', '2', 'sys_notice_type', NULL, 'success', 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (16, 1, '正常', '0', 'sys_notice_status', NULL, 'primary', 'Y', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (17, 2, '关闭', '1', 'sys_notice_status', NULL, 'danger', 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (18, 99, '其他', '0', 'sys_oper_type', NULL, 'info', 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (19, 1, '新增', '1', 'sys_oper_type', NULL, 'info', 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (20, 2, '修改', '2', 'sys_oper_type', NULL, 'info', 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (21, 3, '删除', '3', 'sys_oper_type', NULL, 'danger', 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (22, 4, '授权', '4', 'sys_oper_type', NULL, 'primary', 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (23, 5, '导出', '5', 'sys_oper_type', NULL, 'warning', 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (24, 6, '导入', '6', 'sys_oper_type', NULL, 'warning', 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (25, 7, '强退', '7', 'sys_oper_type', NULL, 'danger', 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (26, 8, '生成代码', '8', 'sys_oper_type', NULL, 'warning', 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (27, 9, '清空数据', '9', 'sys_oper_type', NULL, 'danger', 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (28, 1, '成功', '0', 'sys_common_status', NULL, 'primary', 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (29, 2, '失败', '1', 'sys_common_status', NULL, 'danger', 'N', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '0001-01-01 00:00:00');
INSERT INTO "public"."sys_dict_data" VALUES (100, 0, 'test', '测试', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-06 10:22:12', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (101, 0, 'gongwangfu', '恭王府', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-06 10:23:02', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (102, 0, 'zhaocaijinbao', '招财进宝', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:40:42', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (103, 0, 'miehuoqi', '灭火器', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:41:34', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (104, 0, 'lajitong', '垃圾桶', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:42:29', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (105, 0, 'yiyaoxiang', '医药箱', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:43:02', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (106, 0, 'lv zhi', '绿植', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:43:26', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (107, 0, 'zhi shui ji', '制水机', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:44:17', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (108, 0, 'xin xin xiong', '心心熊', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:44:45', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (109, 0, 'yinshuiji', '饮水机', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:45:14', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (110, 0, 'chouzhihe', '抽纸盒', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:46:44', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (111, 0, 'jinyanbiaoshipai', '禁烟标识牌', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:47:22', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (112, 0, 'xiaolajitong', '小垃圾桶', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:47:42', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (113, 0, 'lingshijia', '零食架', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:48:04', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (114, 0, 'yibao', '怡宝', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:48:31', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (115, 0, 'wenxintishi', '温馨提示', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:48:50', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (116, 0, 'bitong', '笔筒', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:49:10', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (117, 0, 'tuoyingyi', '投影仪', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:50:34', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (118, 0, 'yaokongqi', '空调遥控器', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:51:15', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (119, 0, 'yanhuigang', '烟灰缸', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:51:33', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (120, 0, 'dianwenpai', '电蚊拍', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:52:13', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (121, 0, 'pingjinzha', '平津闸', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:52:50', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (122, 0, 'tenglongge', '滕隆阁', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:53:11', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (123, 0, 'longyanfang', '龙砚舫', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:53:33', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (124, 0, 'jiangjunmaio', '将军庙', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:53:59', 'admin', '2025-07-03 16:23:04', NULL);
INSERT INTO "public"."sys_dict_data" VALUES (125, 0, 'longwangmiao', '龙王庙', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:54:23', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (126, 0, 'guoshoujingxiang', '郭守敬石像', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:54:53', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (127, 0, 'qukuailianBQ', '中国区块链百强榜', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:55:22', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (128, 0, 'hulianwangTOP100', '中国产业互联网TOP100', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:55:39', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (129, 0, 'gongyinglianKJCX', '电商供应链科技服务创新企业', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:56:01', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (130, 0, 'QKL&GYLyouxiu', '区块链+供应链优秀案例', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 10:56:18', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (132, 0, 'hongyadong', '洪崖洞', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 14:03:10', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (133, 0, 'laifushi', '来福士', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 14:05:57', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (134, 0, 'chongqingdajuyuan', '重庆大剧院', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 14:06:31', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (135, 0, 'mianxixishouye', '免洗洗手液', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 14:07:08', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (136, 0, 'shuzirenzhiboji', '数字人直播机', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 14:07:44', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (137, 0, 'paochahu', '泡茶壶', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 14:08:11', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (138, 0, 'chayeguan', '茶叶罐', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 14:09:00', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (139, 0, 'xianshiqi', '显示器', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-07 14:09:17', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (140, 0, 'kongtiao', '空调', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-10 19:38:55', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (142, 0, 'shaoshuihu', '烧水壶', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-12 11:11:56', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (143, 0, 'yijia', '衣架', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-12 11:20:21', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (144, 0, 'taishiyi', '太师椅', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-12 11:21:22', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (145, 0, 'muzhibitong', '木质笔筒', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-12 11:21:59', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (146, 0, 'longfengchahu', '龙凤茶壶', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-12 11:23:27', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (147, 0, 'jinchanchachong', '金蟾茶宠', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-12 11:24:01', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (148, 0, 'dianfengshan', '电风扇', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-17 11:59:50', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (149, 0, 'chabei', '茶杯', 'laber_mapping', NULL, 'default', 'N', '0', 'admin', '2025-03-17 12:00:11', NULL, NULL, NULL);
INSERT INTO "public"."sys_dict_data" VALUES (150, 0, 'q1', '景区出发前准备和注意事项', 'tourist_question', NULL, 'default', 'N', '0', 'admin', '2025-04-07 18:09:25', 'admin', '2025-08-04 10:40:36', '请用50字以内简要说明{touristName}景区出发前准备和注意事项。');
INSERT INTO "public"."sys_dict_data" VALUES (151, 0, 'q2', '门票价格和优惠政策？', 'tourist_question', NULL, 'default', 'N', '0', 'admin', '2025-04-07 18:10:08', 'admin', '2025-07-04 18:11:48', '分点列出{touristName}门票价格：1.成人票__元；2.儿童/学生票__元（需证件）；3.免票政策（如老人、军人等）。');
INSERT INTO "public"."sys_dict_data" VALUES (152, 0, 'q3', '景区具体地址和交通方式？', 'tourist_question', NULL, 'default', 'N', '0', 'admin', '2025-04-07 18:11:03', 'admin', '2025-07-04 18:12:01', '用一句话说明{touristName}的导航地址，并推荐最便捷的公共交通路线（如：地铁X号线XX站+步行X分钟）。');
INSERT INTO "public"."sys_dict_data" VALUES (153, 0, 'q4', '推荐游玩路线（半日/一日游）？', 'tourist_question', NULL, 'default', 'N', '0', 'admin', '2025-04-07 18:13:06', NULL, NULL, '为{touristName}设计一条[半日游/一日游]路线，包含3个必打卡景点和预估步行时间。');
INSERT INTO "public"."sys_dict_data" VALUES (154, 0, 'q5', '景区内有哪些餐饮/休息区？', 'tourist_question', NULL, 'default', 'N', '0', 'admin', '2025-04-07 18:13:45', NULL, NULL, '标记{touristName}内餐饮集中区域（如：山顶餐厅、游客中心快餐），并提醒人均消费约__元。');
INSERT INTO "public"."sys_dict_data" VALUES (155, 0, 'q6', '特色体验项目或表演时间？', 'tourist_question', NULL, 'default', 'N', '0', 'admin', '2025-04-07 18:14:37', NULL, NULL, '用表格列出{touristName}今日特色活动：1.项目名称；2.时间；3.推荐指数（1-5星）。');
INSERT INTO "public"."sys_dict_data" VALUES (156, 0, 'q7', '是否有寄存行李/租借物品服务？', 'tourist_question', NULL, 'default', 'N', '0', 'admin', '2025-04-07 18:15:16', NULL, NULL, '说明{touristName}行李寄存柜位置（如：南门入口处）及收费标准（__元/小时）。');
INSERT INTO "public"."sys_dict_data" VALUES (157, 0, 'q8', '景区内卫生间和母婴室分布？', 'tourist_question', NULL, 'default', 'N', '0', 'admin', '2025-04-07 18:15:54', NULL, NULL, '指出{touristName}内母婴室和无障碍卫生间的位置（靠近XX景点或XX服务区）。');
INSERT INTO "public"."sys_dict_data" VALUES (158, 0, 'q9', '紧急情况联系方式？', 'tourist_question', NULL, 'default', 'N', '0', 'admin', '2025-04-07 18:16:33', NULL, NULL, '提供{touristName}紧急救援电话__和医疗服务站位置（如：游客中心二楼）。');
INSERT INTO "public"."sys_dict_data" VALUES (159, 0, 'q10', '是否需要提前预约？', 'tourist_question', NULL, 'default', 'N', '0', 'admin', '2025-04-07 18:22:43', NULL, NULL, '明确回答{touristName}是否需要线上预约，并附官网/公众号预约链接。');
INSERT INTO "public"."sys_dict_data" VALUES (160, 0, 'q11', '退改签规则？', 'tourist_question', NULL, 'default', 'N', '0', 'admin', '2025-04-07 18:26:55', NULL, NULL, '用条款形式说明{touristName}门票退改规则：1.未使用可退__天内退款；2.过期作废。');
INSERT INTO "public"."sys_dict_data" VALUES (162, 0, 'q13', '雨天/雾天游玩建议？', 'tourist_question', NULL, 'default', 'N', '0', 'admin', '2025-04-07 18:28:08', NULL, NULL, '给出{touristName}雨天游玩贴士：1.室内景点推荐XX；2.必备物品（防滑鞋等）。');
INSERT INTO "public"."sys_dict_data" VALUES (163, 0, 'q14', '最佳拍照打卡点？', 'tourist_question', NULL, 'default', 'N', '0', 'admin', '2025-04-07 18:28:51', NULL, NULL, '推荐{touristName}3个最佳拍照点.');
INSERT INTO "public"."sys_dict_data" VALUES (164, 0, 'q15', '附近住宿推荐？', 'tourist_question', NULL, 'default', 'N', '0', 'admin', '2025-04-07 18:29:30', NULL, NULL, '列举{touristName}周边3家高性价比酒店，标注距离（如：1.XX酒店，步行10分钟，￥200起）。');
INSERT INTO "public"."sys_dict_data" VALUES (165, 0, 'q16', '景区开放时间是？', 'tourist_question', NULL, 'default', 'N', '0', 'admin', '2025-04-08 10:39:04', 'admin', '2025-08-04 10:39:14', '请用30字以内简要说明{touristName}当前开放时间，格式为：上午X点-下午X点。');
INSERT INTO "public"."sys_dict_data" VALUES (161, 0, 'q12', '宠物/轮椅是否允许进入？', 'tourist_question', NULL, 'default', 'N', '0', 'admin', '2025-04-07 18:27:37', NULL, NULL, '帮我介绍一下 {touristName} 可以不可以携带宠物，轮椅通道是否完善，回答我时明确告诉我结果。');
INSERT INTO "public"."sys_dict_data" VALUES (166, 0, 'phone1', '18514756315', 'white_mobile', NULL, 'default', 'N', '0', 'admin', '2025-04-15 17:00:56.52933', NULL, NULL, 'meng');
INSERT INTO "public"."sys_dict_data" VALUES (167, 0, 'phone2', '17610993829', 'white_mobile', NULL, 'default', 'N', '0', 'admin', '2025-04-15 17:01:23.629188', NULL, NULL, 'xiu');


-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_dict_type";
CREATE TABLE "public"."sys_dict_type" (
                                          "dict_id" bigserial  PRIMARY KEY,
                                          "dict_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                          "dict_type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                          "status" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
                                          "create_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                          "create_time" "pg_catalog"."timestamp",
                                          "update_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                          "update_time" "pg_catalog"."timestamp",
                                          "remark" "pg_catalog"."varchar" COLLATE "pg_catalog"."default"
)
;

COMMENT ON COLUMN "public"."sys_dict_type"."dict_id" IS '字典主键';
COMMENT ON COLUMN "public"."sys_dict_type"."dict_name" IS '字典名称';
COMMENT ON COLUMN "public"."sys_dict_type"."dict_type" IS '字典类型';
COMMENT ON COLUMN "public"."sys_dict_type"."status" IS '状态（0正常 1停用）';
COMMENT ON COLUMN "public"."sys_dict_type"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_dict_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_dict_type"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_dict_type"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."sys_dict_type"."remark" IS '备注';
COMMENT ON TABLE "public"."sys_dict_type" IS '字典类型表';

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO "public"."sys_dict_type" VALUES (1, '用户性别', 'sys_user_sex', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '用户性别列表');
INSERT INTO "public"."sys_dict_type" VALUES (2, '菜单状态', 'sys_show_hide', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '菜单状态列表');
INSERT INTO "public"."sys_dict_type" VALUES (3, '系统开关', 'sys_normal_disable', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '系统开关列表');
INSERT INTO "public"."sys_dict_type" VALUES (4, '任务状态', 'sys_job_status', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '任务状态列表');
INSERT INTO "public"."sys_dict_type" VALUES (5, '任务分组', 'sys_job_group', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '任务分组列表');
INSERT INTO "public"."sys_dict_type" VALUES (6, '系统是否', 'sys_yes_no', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '系统是否列表');
INSERT INTO "public"."sys_dict_type" VALUES (7, '通知类型', 'sys_notice_type', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '通知类型列表');
INSERT INTO "public"."sys_dict_type" VALUES (8, '通知状态', 'sys_notice_status', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '通知状态列表');
INSERT INTO "public"."sys_dict_type" VALUES (9, '操作类型', 'sys_oper_type', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '操作类型列表');
INSERT INTO "public"."sys_dict_type" VALUES (10, '系统状态', 'sys_common_status', '0', 'admin', '2025-02-12 10:50:22', NULL, NULL, '登录状态列表');
INSERT INTO "public"."sys_dict_type" VALUES (100, 'laber映射', 'laber_mapping', '0', 'admin', '2025-03-06 10:21:37', NULL, NULL, 'laber映射');
INSERT INTO "public"."sys_dict_type" VALUES (101, '景区常见问题', 'tourist_question', '0', 'admin', '2025-04-07 18:07:29', NULL, NULL, '景区常见问题配置');
INSERT INTO "public"."sys_dict_type" VALUES (102, '白名单手机号', 'white_mobile', '0', 'admin', '2025-04-15 16:59:08.22347', NULL, NULL, '白名单手机号，可以直接进入小程序');


-- ----------------------------
-- Table structure for sys_index
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_index";
CREATE TABLE "public"."sys_index" (
                                      "id" bigserial  PRIMARY KEY,
                                      "index_db" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                      "label_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                      "indice" "pg_catalog"."int4",
                                      "longitude" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                      "latitude" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                      "create_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                      "create_time" "pg_catalog"."timestamp",
                                      "remark" "pg_catalog"."varchar" COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."sys_index"."index_db" IS '索引库名称';
COMMENT ON COLUMN "public"."sys_index"."label_name" IS '标签名称';
COMMENT ON COLUMN "public"."sys_index"."indice" IS '索引位置';
COMMENT ON COLUMN "public"."sys_index"."longitude" IS '经度';
COMMENT ON COLUMN "public"."sys_index"."latitude" IS '纬度';
COMMENT ON COLUMN "public"."sys_index"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_index"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_index"."remark" IS '备注';
COMMENT ON TABLE "public"."sys_index" IS '图片索引表';
-- ----------------------------
-- Records of sys_index
-- ----------------------------
INSERT INTO "public"."sys_index" VALUES (45, 'test.index', '123', 0, '0', '0', NULL, '2025-03-25 06:49:23', NULL);
INSERT INTO "public"."sys_index" VALUES (46, 'test.index', '我我', 1, '0', '0', NULL, '2025-03-25 06:52:57', NULL);
INSERT INTO "public"."sys_index" VALUES (47, 'test.index', '我我', 2, '0', '0', NULL, '2025-03-25 06:52:57', NULL);
INSERT INTO "public"."sys_index" VALUES (48, 'test.index', 'xxx', 3, '0', '0', NULL, '2025-03-25 06:58:09', NULL);
INSERT INTO "public"."sys_index" VALUES (49, 'test.index', '大的', 4, '0', '0', NULL, '2025-03-25 07:01:12', NULL);
INSERT INTO "public"."sys_index" VALUES (50, 'test.index', '大的', 5, '0', '0', NULL, '2025-03-25 07:01:12', NULL);
INSERT INTO "public"."sys_index" VALUES (51, 'test.index', '1', 6, '0', '0', NULL, '2025-03-25 09:24:23', NULL);
INSERT INTO "public"."sys_index" VALUES (52, 'test.index', '独乐峰', 7, '0', '0', NULL, '2025-03-25 09:27:39', NULL);
INSERT INTO "public"."sys_index" VALUES (53, 'test.index', '独乐峰', 8, '0', '0', NULL, '2025-03-25 09:27:39', NULL);
INSERT INTO "public"."sys_index" VALUES (208, 'test.index', '测试', 9, '0', '0', NULL, '2025-03-25 10:44:02', NULL);
INSERT INTO "public"."sys_index" VALUES (209, 'test.index', '无敌大帅哥', 10, '0', '0', NULL, '2025-03-25 11:15:05', NULL);
INSERT INTO "public"."sys_index" VALUES (217, 'gwf.index', '乐道堂', 0, '39.935514', '22', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (218, 'gwf.index', '乐道堂', 1, '39.935543', '23', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (219, 'gwf.index', '乐道堂', 2, '39.935606', '24', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (220, 'gwf.index', '乐道堂', 3, '39.935627', '25', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (221, 'gwf.index', '乐道堂', 4, '0', '26', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (222, 'gwf.index', '二宫门', 5, '39.934982', '27', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (223, 'gwf.index', '二宫门', 6, '39.934881', '28', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (224, 'gwf.index', '二宫门', 7, '0', '29', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (225, 'gwf.index', '凌倒景', 8, '0', '30', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (226, 'gwf.index', '凌倒景', 9, '0', '31', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (227, 'gwf.index', '后罩楼', 10, '39.935867', '32', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (228, 'gwf.index', '后罩楼', 11, '39.935792', '33', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (229, 'gwf.index', '后罩楼', 12, '0', '34', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (230, 'gwf.index', '听雨轩', 13, '39.936899', '35', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (231, 'gwf.index', '听雨轩', 14, '39.937085', '36', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (232, 'gwf.index', '听雨轩', 15, '0', '37', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (233, 'gwf.index', '周汝昌纪念馆', 16, '39.936679', '38', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (234, 'gwf.index', '周汝昌纪念馆', 17, '39.936617', '39', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (235, 'gwf.index', '周汝昌纪念馆', 18, '0', '40', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (236, 'gwf.index', '嘉乐堂', 19, '39.935445', '41', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (237, 'gwf.index', '嘉乐堂', 20, '39.935526', '42', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (238, 'gwf.index', '嘉乐堂', 21, '39.935593', '43', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (239, 'gwf.index', '嘉乐堂', 22, '0', '44', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (240, 'gwf.index', '垂花门', 23, '39.936345', '45', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (241, 'gwf.index', '垂花门', 24, '0', '46', NULL, '2025-03-25 14:46:35', NULL);
INSERT INTO "public"."sys_index" VALUES (242, 'gwf.index', '多福轩', 25, '39.935367', '47', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (243, 'gwf.index', '多福轩', 26, '39.935324', '48', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (244, 'gwf.index', '多福轩', 27, '39.935277', '49', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (245, 'gwf.index', '多福轩', 28, '39.935196', '50', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (246, 'gwf.index', '多福轩', 29, '0', '51', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (247, 'gwf.index', '大戏楼', 30, '39.936617', '52', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (248, 'gwf.index', '大戏楼', 31, '39.936927', '53', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (249, 'gwf.index', '大戏楼', 32, '39.936887', '54', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (250, 'gwf.index', '大戏楼', 33, '0', '55', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (251, 'gwf.index', '妙香亭', 34, '39.936290', '56', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (252, 'gwf.index', '妙香亭', 35, '39.936215', '57', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (253, 'gwf.index', '妙香亭', 36, '0', '58', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (254, 'gwf.index', '安善堂', 37, '39.936480', '59', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (255, 'gwf.index', '安善堂', 38, '39.936540', '60', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (256, 'gwf.index', '安善堂', 39, '0', '61', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (257, 'gwf.index', '山神庙', 40, '0', '62', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (258, 'gwf.index', '山神庙', 41, '0', '63', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (259, 'gwf.index', '平步青云路', 42, '39.937120', '64', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (260, 'gwf.index', '平步青云路', 43, '39.937036', '65', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (261, 'gwf.index', '平步青云路', 44, '0', '66', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (262, 'gwf.index', '抱厦', 45, '39.935830', '67', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (263, 'gwf.index', '抱厦', 46, '39.935840', '68', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (264, 'gwf.index', '抱厦', 47, '0', '69', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (265, 'gwf.index', '方塘水榭', 48, '39.936809', '70', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (266, 'gwf.index', '方塘水榭', 49, '39.936464', '71', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (267, 'gwf.index', '方塘水榭', 50, '39.936187', '72', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (268, 'gwf.index', '方塘水榭', 51, '39.936246', '73', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (269, 'gwf.index', '方塘水榭', 52, '0', '74', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (270, 'gwf.index', '明道斋', 53, '39.936482', '75', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (271, 'gwf.index', '明道斋', 54, '39.936472', '76', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (272, 'gwf.index', '明道斋', 55, '0', '77', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (273, 'gwf.index', '曲径通幽', 56, '0', '78', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (274, 'gwf.index', '曲径通幽', 57, '0', '79', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (275, 'gwf.index', '梧桐院', 58, '39.937083', '80', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (276, 'gwf.index', '梧桐院', 59, '39.937187', '81', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (277, 'gwf.index', '棣华轩', 60, '39.936371', '82', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (278, 'gwf.index', '棣华轩', 61, '39.936434', '83', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (279, 'gwf.index', '棣华轩', 62, '0', '84', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (280, 'gwf.index', '榆关', 63, '39.936422', '85', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (281, 'gwf.index', '榆关', 64, '39.936168', '86', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (282, 'gwf.index', '榆关', 65, '0', '87', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (283, 'gwf.index', '沁秋亭', 66, '39.936457', '88', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (284, 'gwf.index', '沁秋亭', 67, '39.936355', '89', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (285, 'gwf.index', '沁秋亭', 68, '0', '90', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (286, 'gwf.index', '滴翠岩', 69, '39.936831', '91', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (287, 'gwf.index', '滴翠岩', 70, '39.936828', '92', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (288, 'gwf.index', '滴翠岩', 71, '0', '93', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (289, 'gwf.index', '澄怀撷秀', 72, '0', '94', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (290, 'gwf.index', '澄怀撷秀', 73, '0', '95', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (291, 'gwf.index', '牡丹园', 74, '39.936508', '96', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (292, 'gwf.index', '牡丹园', 75, '39.936557', '97', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (293, 'gwf.index', '牡丹园', 76, '39.936557', '98', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (294, 'gwf.index', '牡丹园', 77, '39.936560', '99', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (295, 'gwf.index', '牡丹园', 78, '39.936706', '100', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (296, 'gwf.index', '牡丹园', 79, '39.936690', '101', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (297, 'gwf.index', '牡丹园', 80, '0', '102', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (298, 'gwf.index', '独乐峰', 81, '39.936139', '103', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (299, 'gwf.index', '独乐峰', 82, '39.936261', '104', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (300, 'gwf.index', '独乐峰', 83, '39.936260', '105', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (301, 'gwf.index', '独乐峰', 84, '39.936324', '106', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (302, 'gwf.index', '独乐峰', 85, '0', '107', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (303, 'gwf.index', '福字碑', 86, '39.936883', '108', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (304, 'gwf.index', '福字碑', 87, '39.936921', '109', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (305, 'gwf.index', '福字碑', 88, '0', '110', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (306, 'gwf.index', '秋水山房及绎志斋', 89, '0', '111', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (307, 'gwf.index', '秋水山房及绎志斋', 90, '0', '112', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (308, 'gwf.index', '秘云洞', 91, '39.936902', '113', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (309, 'gwf.index', '秘云洞', 92, '39.936949', '114', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (310, 'gwf.index', '秘云洞', 93, '0', '115', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (311, 'gwf.index', '竹子院', 94, '39.936386', '116', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (312, 'gwf.index', '竹子院', 95, '39.936491', '117', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (313, 'gwf.index', '竹子院', 96, '39.936492', '118', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (314, 'gwf.index', '竹子院', 97, '0', '119', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (315, 'gwf.index', '箭道', 98, '39.936011', '120', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (316, 'gwf.index', '箭道', 99, '39.936037', '121', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (317, 'gwf.index', '箭道', 100, '39.936098', '122', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (318, 'gwf.index', '箭道', 101, '0', '123', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (319, 'gwf.index', '萃锦园', 102, '0', '124', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (320, 'gwf.index', '萃锦园', 103, '39.935902', '125', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (321, 'gwf.index', '萃锦园', 104, '39.935907', '126', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (322, 'gwf.index', '萃锦园', 105, '39.935848', '127', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (323, 'gwf.index', '葆光室', 106, '39.935148', '128', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (324, 'gwf.index', '葆光室', 107, '39.935269', '129', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (325, 'gwf.index', '葆光室', 108, '0', '130', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (326, 'gwf.index', '蓺蔬圃', 109, '39.936355', '131', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (327, 'gwf.index', '蓺蔬圃', 110, '0', '132', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (328, 'gwf.index', '蝠厅', 111, '39.937232', '133', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (329, 'gwf.index', '蝠厅', 112, '39.937184', '134', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (330, 'gwf.index', '蝠厅', 113, '39.937196', '135', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (331, 'gwf.index', '蝠厅', 114, '39.937169', '136', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (332, 'gwf.index', '蝠厅', 115, '0', '137', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (333, 'gwf.index', '蝠池', 116, '0', '138', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (334, 'gwf.index', '蝠池', 117, '39.936336', '139', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (335, 'gwf.index', '蝠池', 118, '39.936379', '140', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (336, 'gwf.index', '蝠池', 119, '0', '141', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (337, 'gwf.index', '诗画舫_韵花移', 120, '0', '142', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (338, 'gwf.index', '诗画舫_韵花移', 121, '0', '143', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (339, 'gwf.index', '邀月台', 122, '39.937075', '144', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (340, 'gwf.index', '邀月台', 123, '39.937102', '145', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (341, 'gwf.index', '邀月台', 124, '39.937002', '146', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (342, 'gwf.index', '邀月台', 125, '0', '147', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (343, 'gwf.index', '锡晋斋', 126, '39.935423', '148', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (344, 'gwf.index', '锡晋斋', 127, '39.935557', '149', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (345, 'gwf.index', '锡晋斋', 128, '0', '150', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (346, 'gwf.index', '龙王庙', 129, '39.936100', '151', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (347, 'gwf.index', '龙王庙', 130, '39.935959', '152', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (348, 'gwf.index', '龙王庙', 131, '0', '153', NULL, '2025-03-25 14:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (349, 'gwf.index', '宝约楼', 136, '0', '154', NULL, '2025-03-25 15:20:38', NULL);
INSERT INTO "public"."sys_index" VALUES (350, 'gwf.index', '水法楼', 137, '0', '155', NULL, '2025-03-25 15:20:38', NULL);
INSERT INTO "public"."sys_index" VALUES (351, 'gwf.index', '瞻霁楼', 138, '0', '156', NULL, '2025-03-25 15:20:38', NULL);
INSERT INTO "public"."sys_index" VALUES (352, 'gwf.index', '云停', 132, '39.935473', '157', NULL, '2025-03-25 15:20:54', NULL);
INSERT INTO "public"."sys_index" VALUES (353, 'gwf.index', '云停', 133, '39.935506', '158', NULL, '2025-03-25 15:20:54', NULL);
INSERT INTO "public"."sys_index" VALUES (354, 'gwf.index', '云停', 134, '39.935494', '159', NULL, '2025-03-25 15:20:54', NULL);
INSERT INTO "public"."sys_index" VALUES (355, 'gwf.index', '云停', 135, '0', '160', NULL, '2025-03-25 15:20:54', NULL);
INSERT INTO "public"."sys_index" VALUES (356, 'test.index', '福字碑', 11, '0', '0', NULL, '2025-03-25 15:25:06', NULL);
INSERT INTO "public"."sys_index" VALUES (357, 'ciyun.index', '怡宝矿泉水', 0, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (358, 'ciyun.index', '怡宝矿泉水', 1, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (359, 'ciyun.index', '怡宝矿泉水', 2, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (360, 'ciyun.index', '怡宝矿泉水', 3, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (361, 'ciyun.index', '怡宝矿泉水', 4, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (362, 'ciyun.index', '怡宝矿泉水', 5, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (363, 'ciyun.index', '怡宝矿泉水', 6, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (364, 'ciyun.index', '怡宝矿泉水', 7, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (365, 'ciyun.index', '怡宝矿泉水', 8, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (366, 'ciyun.index', '怡宝矿泉水', 9, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (367, 'ciyun.index', '怡宝矿泉水', 10, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (368, 'ciyun.index', '怡宝矿泉水', 11, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (369, 'ciyun.index', '怡宝矿泉水', 12, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (370, 'ciyun.index', '怡宝矿泉水', 13, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (371, 'ciyun.index', '怡宝矿泉水', 14, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (372, 'ciyun.index', '怡宝矿泉水', 15, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (373, 'ciyun.index', '怡宝矿泉水', 16, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (374, 'ciyun.index', '怡宝矿泉水', 17, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (375, 'ciyun.index', '怡宝矿泉水', 18, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (376, 'ciyun.index', '怡宝矿泉水', 19, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (377, 'ciyun.index', '怡宝矿泉水', 20, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (378, 'ciyun.index', '怡宝矿泉水', 21, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (379, 'ciyun.index', '怡宝矿泉水', 22, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (380, 'ciyun.index', '怡宝矿泉水', 23, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (381, 'ciyun.index', '怡宝矿泉水', 24, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (382, 'ciyun.index', '怡宝矿泉水', 25, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (383, 'ciyun.index', '怡宝矿泉水', 26, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (384, 'ciyun.index', '怡宝矿泉水', 27, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (385, 'ciyun.index', '怡宝矿泉水', 28, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (386, 'ciyun.index', '怡宝矿泉水', 29, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (387, 'ciyun.index', '怡宝矿泉水', 30, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (388, 'ciyun.index', '怡宝矿泉水', 31, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (389, 'ciyun.index', '怡宝矿泉水', 32, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (390, 'ciyun.index', '怡宝矿泉水', 33, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (391, 'ciyun.index', '怡宝矿泉水', 34, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (392, 'ciyun.index', '怡宝矿泉水', 35, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (393, 'ciyun.index', '怡宝矿泉水', 36, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (394, 'ciyun.index', '怡宝矿泉水', 37, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (395, 'ciyun.index', '怡宝矿泉水', 38, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (396, 'ciyun.index', '怡宝矿泉水', 39, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (397, 'ciyun.index', '怡宝矿泉水', 40, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (398, 'ciyun.index', '怡宝矿泉水', 41, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (399, 'ciyun.index', '怡宝矿泉水', 42, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (400, 'ciyun.index', '怡宝矿泉水', 43, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (401, 'ciyun.index', '怡宝矿泉水', 44, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (402, 'ciyun.index', '怡宝矿泉水', 45, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (403, 'ciyun.index', '怡宝矿泉水', 46, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (404, 'ciyun.index', '怡宝矿泉水', 47, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (405, 'ciyun.index', '怡宝矿泉水', 48, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (406, 'ciyun.index', '怡宝矿泉水', 49, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (407, 'ciyun.index', '怡宝矿泉水', 50, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (408, 'ciyun.index', '怡宝矿泉水', 51, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (409, 'ciyun.index', '怡宝矿泉水', 52, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (410, 'ciyun.index', '怡宝矿泉水', 53, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (411, 'ciyun.index', '怡宝矿泉水', 54, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (412, 'ciyun.index', '怡宝矿泉水', 55, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (413, 'ciyun.index', '怡宝矿泉水', 56, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (414, 'ciyun.index', '怡宝矿泉水', 57, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (415, 'ciyun.index', '怡宝矿泉水', 58, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (416, 'ciyun.index', '怡宝矿泉水', 59, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (417, 'ciyun.index', '怡宝矿泉水', 60, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (418, 'ciyun.index', '怡宝矿泉水', 61, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (419, 'ciyun.index', '怡宝矿泉水', 62, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (420, 'ciyun.index', '怡宝矿泉水', 63, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (421, 'ciyun.index', '怡宝矿泉水', 64, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (422, 'ciyun.index', '怡宝矿泉水', 65, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (423, 'ciyun.index', '怡宝矿泉水', 66, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (424, 'ciyun.index', '怡宝矿泉水', 67, '0', '0', NULL, '2025-03-25 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (425, 'ciyun.index', '抽纸盒', 68, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (426, 'ciyun.index', '抽纸盒', 69, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (427, 'ciyun.index', '抽纸盒', 70, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (428, 'ciyun.index', '抽纸盒', 71, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (429, 'ciyun.index', '抽纸盒', 72, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (430, 'ciyun.index', '抽纸盒', 73, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (431, 'ciyun.index', '抽纸盒', 74, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (432, 'ciyun.index', '抽纸盒', 75, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (433, 'ciyun.index', '抽纸盒', 76, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (434, 'ciyun.index', '抽纸盒', 77, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (435, 'ciyun.index', '抽纸盒', 78, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (436, 'ciyun.index', '抽纸盒', 79, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (437, 'ciyun.index', '抽纸盒', 80, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (438, 'ciyun.index', '抽纸盒', 81, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (439, 'ciyun.index', '抽纸盒', 82, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (440, 'ciyun.index', '抽纸盒', 83, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (441, 'ciyun.index', '抽纸盒', 84, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (442, 'ciyun.index', '抽纸盒', 85, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (443, 'ciyun.index', '抽纸盒', 86, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (444, 'ciyun.index', '抽纸盒', 87, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (445, 'ciyun.index', '抽纸盒', 88, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (446, 'ciyun.index', '抽纸盒', 89, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (447, 'ciyun.index', '抽纸盒', 90, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (448, 'ciyun.index', '抽纸盒', 91, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (449, 'ciyun.index', '抽纸盒', 92, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (450, 'ciyun.index', '抽纸盒', 93, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (451, 'ciyun.index', '抽纸盒', 94, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (452, 'ciyun.index', '抽纸盒', 95, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (453, 'ciyun.index', '抽纸盒', 96, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (454, 'ciyun.index', '抽纸盒', 97, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (455, 'ciyun.index', '抽纸盒', 98, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (456, 'ciyun.index', '抽纸盒', 99, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (457, 'ciyun.index', '抽纸盒', 100, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (458, 'ciyun.index', '抽纸盒', 101, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (459, 'ciyun.index', '抽纸盒', 102, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (460, 'ciyun.index', '抽纸盒', 103, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (461, 'ciyun.index', '抽纸盒', 104, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (462, 'ciyun.index', '抽纸盒', 105, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (463, 'ciyun.index', '抽纸盒', 106, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (464, 'ciyun.index', '抽纸盒', 107, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (465, 'ciyun.index', '抽纸盒', 108, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (466, 'ciyun.index', '抽纸盒', 109, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (467, 'ciyun.index', '抽纸盒', 110, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (468, 'ciyun.index', '抽纸盒', 111, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (469, 'ciyun.index', '抽纸盒', 112, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (470, 'ciyun.index', '抽纸盒', 113, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (471, 'ciyun.index', '抽纸盒', 114, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (472, 'ciyun.index', '抽纸盒', 115, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (473, 'ciyun.index', '抽纸盒', 116, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (474, 'ciyun.index', '抽纸盒', 117, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (475, 'ciyun.index', '抽纸盒', 118, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (476, 'ciyun.index', '抽纸盒', 119, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (477, 'ciyun.index', '抽纸盒', 120, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (478, 'ciyun.index', '抽纸盒', 121, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (479, 'ciyun.index', '抽纸盒', 122, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (480, 'ciyun.index', '抽纸盒', 123, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (481, 'ciyun.index', '抽纸盒', 124, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (482, 'ciyun.index', '抽纸盒', 125, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (483, 'ciyun.index', '抽纸盒', 126, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (484, 'ciyun.index', '抽纸盒', 127, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (485, 'ciyun.index', '抽纸盒', 128, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (486, 'ciyun.index', '抽纸盒', 129, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (487, 'ciyun.index', '抽纸盒', 130, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (488, 'ciyun.index', '抽纸盒', 131, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (489, 'ciyun.index', '抽纸盒', 132, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (490, 'ciyun.index', '抽纸盒', 133, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (491, 'ciyun.index', '抽纸盒', 134, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (492, 'ciyun.index', '抽纸盒', 135, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (493, 'ciyun.index', '桌旗摆件', 136, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (494, 'ciyun.index', '桌旗摆件', 137, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (495, 'ciyun.index', '桌旗摆件', 138, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (496, 'ciyun.index', '桌旗摆件', 139, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (497, 'ciyun.index', '桌旗摆件', 140, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (498, 'ciyun.index', '桌旗摆件', 141, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (499, 'ciyun.index', '桌旗摆件', 142, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (500, 'ciyun.index', '桌旗摆件', 143, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (501, 'ciyun.index', '桌旗摆件', 144, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (502, 'ciyun.index', '桌旗摆件', 145, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (503, 'ciyun.index', '桌旗摆件', 146, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (504, 'ciyun.index', '桌旗摆件', 147, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (505, 'ciyun.index', '桌旗摆件', 148, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (506, 'ciyun.index', '桌旗摆件', 149, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (507, 'ciyun.index', '桌旗摆件', 150, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (508, 'ciyun.index', '桌旗摆件', 151, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (509, 'ciyun.index', '桌旗摆件', 152, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (510, 'ciyun.index', '桌旗摆件', 153, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (511, 'ciyun.index', '桌旗摆件', 154, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (512, 'ciyun.index', '桌旗摆件', 155, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (513, 'ciyun.index', '桌旗摆件', 156, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (514, 'ciyun.index', '桌旗摆件', 157, '0', '0', NULL, '2025-03-25 18:31:07', NULL);
INSERT INTO "public"."sys_index" VALUES (515, 'ciyun.index', '桌旗摆件', 158, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (516, 'ciyun.index', '桌旗摆件', 159, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (517, 'ciyun.index', '桌旗摆件', 160, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (518, 'ciyun.index', '桌旗摆件', 161, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (519, 'ciyun.index', '桌旗摆件', 162, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (520, 'ciyun.index', '桌旗摆件', 163, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (521, 'ciyun.index', '桌旗摆件', 164, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (522, 'ciyun.index', '桌旗摆件', 165, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (523, 'ciyun.index', '桌旗摆件', 166, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (524, 'ciyun.index', '桌旗摆件', 167, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (525, 'ciyun.index', '桌旗摆件', 168, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (526, 'ciyun.index', '桌旗摆件', 169, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (527, 'ciyun.index', '桌旗摆件', 170, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (528, 'ciyun.index', '桌旗摆件', 171, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (529, 'ciyun.index', '桌旗摆件', 172, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (530, 'ciyun.index', '桌旗摆件', 173, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (531, 'ciyun.index', '桌旗摆件', 174, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (532, 'ciyun.index', '桌旗摆件', 175, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (533, 'ciyun.index', '桌旗摆件', 176, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (534, 'ciyun.index', '桌旗摆件', 177, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (535, 'ciyun.index', '桌旗摆件', 178, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (536, 'ciyun.index', '桌旗摆件', 179, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (537, 'ciyun.index', '桌旗摆件', 180, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (538, 'ciyun.index', '桌旗摆件', 181, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (539, 'ciyun.index', '桌旗摆件', 182, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (540, 'ciyun.index', '桌旗摆件', 183, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (541, 'ciyun.index', '桌旗摆件', 184, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (542, 'ciyun.index', '桌旗摆件', 185, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (543, 'ciyun.index', '桌旗摆件', 186, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (544, 'ciyun.index', '桌旗摆件', 187, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (545, 'ciyun.index', '桌旗摆件', 188, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (546, 'ciyun.index', '桌旗摆件', 189, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (547, 'ciyun.index', '桌旗摆件', 190, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (548, 'ciyun.index', '桌旗摆件', 191, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (549, 'ciyun.index', '桌旗摆件', 192, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (550, 'ciyun.index', '桌旗摆件', 193, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (551, 'ciyun.index', '桌旗摆件', 194, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (552, 'ciyun.index', '桌旗摆件', 195, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (553, 'ciyun.index', '桌旗摆件', 196, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (554, 'ciyun.index', '桌旗摆件', 197, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (555, 'ciyun.index', '桌旗摆件', 198, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (556, 'ciyun.index', '桌旗摆件', 199, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (557, 'ciyun.index', '桌旗摆件', 200, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (558, 'ciyun.index', '桌旗摆件', 201, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (559, 'ciyun.index', '桌旗摆件', 202, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (560, 'ciyun.index', '桌旗摆件', 203, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (561, 'ciyun.index', '桌旗摆件', 204, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (562, 'ciyun.index', '禁烟标识牌', 205, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (563, 'ciyun.index', '禁烟标识牌', 206, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (564, 'ciyun.index', '禁烟标识牌', 207, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (565, 'ciyun.index', '禁烟标识牌', 208, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (566, 'ciyun.index', '禁烟标识牌', 209, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (567, 'ciyun.index', '禁烟标识牌', 210, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (568, 'ciyun.index', '禁烟标识牌', 211, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (569, 'ciyun.index', '禁烟标识牌', 212, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (570, 'ciyun.index', '禁烟标识牌', 213, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (571, 'ciyun.index', '禁烟标识牌', 214, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (572, 'ciyun.index', '禁烟标识牌', 215, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (573, 'ciyun.index', '禁烟标识牌', 216, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (574, 'ciyun.index', '禁烟标识牌', 217, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (575, 'ciyun.index', '禁烟标识牌', 218, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (576, 'ciyun.index', '禁烟标识牌', 219, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (577, 'ciyun.index', '禁烟标识牌', 220, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (578, 'ciyun.index', '禁烟标识牌', 221, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (579, 'ciyun.index', '禁烟标识牌', 222, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (580, 'ciyun.index', '禁烟标识牌', 223, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (581, 'ciyun.index', '禁烟标识牌', 224, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (582, 'ciyun.index', '禁烟标识牌', 225, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (583, 'ciyun.index', '禁烟标识牌', 226, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (584, 'ciyun.index', '禁烟标识牌', 227, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (585, 'ciyun.index', '禁烟标识牌', 228, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (586, 'ciyun.index', '禁烟标识牌', 229, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (587, 'ciyun.index', '禁烟标识牌', 230, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (588, 'ciyun.index', '禁烟标识牌', 231, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (589, 'ciyun.index', '禁烟标识牌', 232, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (590, 'ciyun.index', '禁烟标识牌', 233, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (591, 'ciyun.index', '禁烟标识牌', 234, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (592, 'ciyun.index', '禁烟标识牌', 235, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (593, 'ciyun.index', '禁烟标识牌', 236, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (594, 'ciyun.index', '禁烟标识牌', 237, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (595, 'ciyun.index', '禁烟标识牌', 238, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (596, 'ciyun.index', '禁烟标识牌', 239, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (597, 'ciyun.index', '禁烟标识牌', 240, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (598, 'ciyun.index', '禁烟标识牌', 241, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (599, 'ciyun.index', '禁烟标识牌', 242, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (600, 'ciyun.index', '禁烟标识牌', 243, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (601, 'ciyun.index', '禁烟标识牌', 244, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (602, 'ciyun.index', '禁烟标识牌', 245, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (603, 'ciyun.index', '禁烟标识牌', 246, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (604, 'ciyun.index', '禁烟标识牌', 247, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (605, 'ciyun.index', '禁烟标识牌', 248, '0', '0', NULL, '2025-03-25 18:31:08', NULL);
INSERT INTO "public"."sys_index" VALUES (606, 'ciyun.index', '禁烟标识牌', 249, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (607, 'ciyun.index', '禁烟标识牌', 250, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (608, 'ciyun.index', '禁烟标识牌', 251, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (609, 'ciyun.index', '禁烟标识牌', 252, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (610, 'ciyun.index', '禁烟标识牌', 253, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (611, 'ciyun.index', '禁烟标识牌', 254, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (612, 'ciyun.index', '禁烟标识牌', 255, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (613, 'ciyun.index', '禁烟标识牌', 256, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (614, 'ciyun.index', '禁烟标识牌', 257, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (615, 'ciyun.index', '禁烟标识牌', 258, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (616, 'ciyun.index', '禁烟标识牌', 259, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (617, 'ciyun.index', '禁烟标识牌', 260, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (618, 'ciyun.index', '禁烟标识牌', 261, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (619, 'ciyun.index', '禁烟标识牌', 262, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (620, 'ciyun.index', '禁烟标识牌', 263, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (621, 'ciyun.index', '禁烟标识牌', 264, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (622, 'ciyun.index', '禁烟标识牌', 265, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (623, 'ciyun.index', '禁烟标识牌', 266, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (624, 'ciyun.index', '禁烟标识牌', 267, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (625, 'ciyun.index', '禁烟标识牌', 268, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (626, 'ciyun.index', '禁烟标识牌', 269, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (627, 'ciyun.index', '禁烟标识牌', 270, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (628, 'ciyun.index', '禁烟标识牌', 271, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (629, 'ciyun.index', '禁烟标识牌', 272, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (630, 'ciyun.index', '禁烟标识牌', 273, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (631, 'ciyun.index', '禁烟标识牌', 274, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (632, 'ciyun.index', '禁烟标识牌', 275, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (633, 'ciyun.index', '空调遥控器', 276, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (634, 'ciyun.index', '空调遥控器', 277, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (635, 'ciyun.index', '空调遥控器', 278, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (636, 'ciyun.index', '空调遥控器', 279, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (637, 'ciyun.index', '空调遥控器', 280, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (638, 'ciyun.index', '空调遥控器', 281, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (639, 'ciyun.index', '空调遥控器', 282, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (640, 'ciyun.index', '空调遥控器', 283, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (641, 'ciyun.index', '空调遥控器', 284, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (642, 'ciyun.index', '空调遥控器', 285, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (643, 'ciyun.index', '空调遥控器', 286, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (644, 'ciyun.index', '空调遥控器', 287, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (645, 'ciyun.index', '空调遥控器', 288, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (646, 'ciyun.index', '空调遥控器', 289, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (647, 'ciyun.index', '空调遥控器', 290, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (648, 'ciyun.index', '空调遥控器', 291, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (649, 'ciyun.index', '空调遥控器', 292, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (650, 'ciyun.index', '空调遥控器', 293, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (651, 'ciyun.index', '空调遥控器', 294, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (652, 'ciyun.index', '空调遥控器', 295, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (653, 'ciyun.index', '空调遥控器', 296, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (654, 'ciyun.index', '空调遥控器', 297, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (655, 'ciyun.index', '空调遥控器', 298, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (656, 'ciyun.index', '空调遥控器', 299, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (657, 'ciyun.index', '空调遥控器', 300, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (658, 'ciyun.index', '空调遥控器', 301, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (659, 'ciyun.index', '空调遥控器', 302, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (660, 'ciyun.index', '空调遥控器', 303, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (661, 'ciyun.index', '空调遥控器', 304, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (662, 'ciyun.index', '空调遥控器', 305, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (663, 'ciyun.index', '空调遥控器', 306, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (664, 'ciyun.index', '空调遥控器', 307, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (665, 'ciyun.index', '空调遥控器', 308, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (666, 'ciyun.index', '空调遥控器', 309, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (667, 'ciyun.index', '空调遥控器', 310, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (668, 'ciyun.index', '空调遥控器', 311, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (669, 'ciyun.index', '空调遥控器', 312, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (670, 'ciyun.index', '空调遥控器', 313, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (671, 'ciyun.index', '空调遥控器', 314, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (672, 'ciyun.index', '空调遥控器', 315, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (673, 'ciyun.index', '空调遥控器', 316, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (674, 'ciyun.index', '空调遥控器', 317, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (675, 'ciyun.index', '空调遥控器', 318, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (676, 'ciyun.index', '空调遥控器', 319, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (677, 'ciyun.index', '空调遥控器', 320, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (678, 'ciyun.index', '空调遥控器', 321, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (679, 'ciyun.index', '空调遥控器', 322, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (680, 'ciyun.index', '空调遥控器', 323, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (681, 'ciyun.index', '空调遥控器', 324, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (682, 'ciyun.index', '空调遥控器', 325, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (683, 'ciyun.index', '空调遥控器', 326, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (684, 'ciyun.index', '空调遥控器', 327, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (685, 'ciyun.index', '空调遥控器', 328, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (686, 'ciyun.index', '空调遥控器', 329, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (687, 'ciyun.index', '空调遥控器', 330, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (688, 'ciyun.index', '空调遥控器', 331, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (689, 'ciyun.index', '空调遥控器', 332, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (690, 'ciyun.index', '空调遥控器', 333, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (691, 'ciyun.index', '空调遥控器', 334, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (692, 'ciyun.index', '空调遥控器', 335, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (693, 'ciyun.index', '空调遥控器', 336, '0', '0', NULL, '2025-03-25 18:31:09', NULL);
INSERT INTO "public"."sys_index" VALUES (694, 'ciyun.index', '空调遥控器', 337, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (695, 'ciyun.index', '空调遥控器', 338, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (696, 'ciyun.index', '空调遥控器', 339, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (697, 'ciyun.index', '空调遥控器', 340, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (698, 'ciyun.index', '空调遥控器', 341, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (699, 'ciyun.index', '空调遥控器', 342, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (700, 'ciyun.index', '空调遥控器', 343, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (701, 'ciyun.index', '空调遥控器', 344, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (702, 'ciyun.index', '空调遥控器', 345, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (703, 'ciyun.index', '空调遥控器', 346, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (704, 'ciyun.index', '空调遥控器', 347, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (705, 'ciyun.index', '空调遥控器', 348, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (706, 'ciyun.index', '空调遥控器', 349, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (707, 'ciyun.index', '空调遥控器', 350, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (708, 'ciyun.index', '空调遥控器', 351, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (709, 'ciyun.index', '空调遥控器', 352, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (710, 'ciyun.index', '空调遥控器', 353, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (711, 'ciyun.index', '空调遥控器', 354, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (712, 'ciyun.index', '空调遥控器', 355, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (713, 'ciyun.index', '空调遥控器', 356, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (714, 'ciyun.index', '空调遥控器', 357, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (715, 'ciyun.index', '空调遥控器', 358, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (716, 'ciyun.index', '空调遥控器', 359, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (717, 'ciyun.index', '空调遥控器', 360, '0', '0', NULL, '2025-03-25 18:31:10', NULL);
INSERT INTO "public"."sys_index" VALUES (718, 'ciyun.index', '怡宝矿泉水', 361, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (719, 'ciyun.index', '怡宝矿泉水', 362, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (720, 'ciyun.index', '怡宝矿泉水', 363, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (721, 'ciyun.index', '怡宝矿泉水', 364, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (722, 'ciyun.index', '怡宝矿泉水', 365, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (723, 'ciyun.index', '抽纸盒', 366, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (724, 'ciyun.index', '抽纸盒', 367, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (725, 'ciyun.index', '抽纸盒', 368, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (726, 'ciyun.index', '抽纸盒', 369, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (727, 'ciyun.index', '抽纸盒', 370, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (728, 'ciyun.index', '抽纸盒', 371, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (729, 'ciyun.index', '抽纸盒', 372, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (730, 'ciyun.index', '抽纸盒', 373, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (731, 'ciyun.index', '桌旗摆件', 374, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (732, 'ciyun.index', '桌旗摆件', 375, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (733, 'ciyun.index', '桌旗摆件', 376, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (734, 'ciyun.index', '桌旗摆件', 377, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (735, 'ciyun.index', '桌旗摆件', 378, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (736, 'ciyun.index', '桌旗摆件', 379, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (737, 'ciyun.index', '桌旗摆件', 380, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (738, 'ciyun.index', '禁烟标识牌', 381, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (739, 'ciyun.index', '禁烟标识牌', 382, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (740, 'ciyun.index', '禁烟标识牌', 383, '0', '0', NULL, '2025-03-26 17:30:00', NULL);
INSERT INTO "public"."sys_index" VALUES (741, 'ciyun.index', '禁烟标识牌', 384, '0', '0', NULL, '2025-03-26 17:30:01', NULL);
INSERT INTO "public"."sys_index" VALUES (742, 'ciyun.index', '禁烟标识牌', 385, '0', '0', NULL, '2025-03-26 17:30:01', NULL);
INSERT INTO "public"."sys_index" VALUES (743, 'ciyun.index', '禁烟标识牌', 386, '0', '0', NULL, '2025-03-26 17:30:01', NULL);
INSERT INTO "public"."sys_index" VALUES (744, 'ciyun.index', '禁烟标识牌', 387, '0', '0', NULL, '2025-03-26 17:30:01', NULL);
INSERT INTO "public"."sys_index" VALUES (745, 'ciyun.index', '空调遥控器', 388, '0', '0', NULL, '2025-03-26 17:30:01', NULL);
INSERT INTO "public"."sys_index" VALUES (746, 'ciyun.index', '空调遥控器', 389, '0', '0', NULL, '2025-03-26 17:30:01', NULL);
INSERT INTO "public"."sys_index" VALUES (747, 'ciyun.index', '空调遥控器', 390, '0', '0', NULL, '2025-03-26 17:30:01', NULL);
INSERT INTO "public"."sys_index" VALUES (748, 'ciyun.index', '空调遥控器', 391, '0', '0', NULL, '2025-03-26 17:30:01', NULL);
INSERT INTO "public"."sys_index" VALUES (749, 'ciyun.index', '空调遥控器', 392, '0', '0', NULL, '2025-03-26 17:30:01', NULL);
INSERT INTO "public"."sys_index" VALUES (750, 'ciyun.index', '空调遥控器', 393, '0', '0', NULL, '2025-03-26 17:30:01', NULL);
INSERT INTO "public"."sys_index" VALUES (751, 'ciyun.index', '空调遥控器', 394, '0', '0', NULL, '2025-03-26 17:30:01', NULL);
INSERT INTO "public"."sys_index" VALUES (752, 'test.index', '电脑', 14, '0', '0', NULL, '2025-04-01 14:27:08', NULL);
INSERT INTO "public"."sys_index" VALUES (753, 'test.index', '电脑', 15, '0', '0', NULL, '2025-04-01 14:28:11', NULL);
INSERT INTO "public"."sys_index" VALUES (754, 'test.index', '电脑', 16, '0', '0', NULL, '2025-04-01 14:28:11', NULL);
INSERT INTO "public"."sys_index" VALUES (755, 'test.index', '电脑', 17, '0', '0', NULL, '2025-04-01 14:29:46', NULL);
INSERT INTO "public"."sys_index" VALUES (798, 'test.index', '25', 18, '116.506875', '39.903246', NULL, '2025-04-02 19:55:26', NULL);
INSERT INTO "public"."sys_index" VALUES (799, 'test.index', '25', 19, '116.506875', '39.903246', NULL, '2025-04-02 19:55:26', NULL);
INSERT INTO "public"."sys_index" VALUES (801, 'ymy.index', '123', 0, '116.506875', '39.903246', NULL, '2025-04-03 09:33:51', NULL);
INSERT INTO "public"."sys_index" VALUES (802, 'ymy.index', '圆明园博物馆', 1, '116.303562', '39.998491', NULL, '2025-04-03 10:11:12', NULL);
INSERT INTO "public"."sys_index" VALUES (803, 'ymy.index', '圆明园博物馆', 2, '116.303562', '39.998491', NULL, '2025-04-03 10:11:12', NULL);
INSERT INTO "public"."sys_index" VALUES (804, 'ymy.index', '圆明园博物馆', 3, '116.303562', '39.998491', NULL, '2025-04-03 10:11:12', NULL);
INSERT INTO "public"."sys_index" VALUES (805, 'ymy.index', '圆明园博物馆', 4, '116.303562', '39.998491', NULL, '2025-04-03 10:11:12', NULL);
INSERT INTO "public"."sys_index" VALUES (806, 'ymy.index', '圆明园博物馆', 5, '116.303562', '39.998491', NULL, '2025-04-03 10:11:12', NULL);
INSERT INTO "public"."sys_index" VALUES (807, 'ymy.index', '圆明园博物馆', 6, '116.303562', '39.998491', NULL, '2025-04-03 10:11:12', NULL);
INSERT INTO "public"."sys_index" VALUES (808, 'ymy.index', '圆明园博物馆', 7, '116.303562', '39.998491', NULL, '2025-04-03 10:11:12', NULL);
INSERT INTO "public"."sys_index" VALUES (809, 'ymy.index', '圆明园博物馆', 8, '116.303562', '39.998491', NULL, '2025-04-03 10:11:12', NULL);
INSERT INTO "public"."sys_index" VALUES (810, 'ymy.index', '圆明园博物馆', 9, '116.303562', '39.998491', NULL, '2025-04-03 10:11:12', NULL);
INSERT INTO "public"."sys_index" VALUES (811, 'ymy.index', '圆明园博物馆', 10, '116.303562', '39.998491', NULL, '2025-04-03 10:11:12', NULL);
INSERT INTO "public"."sys_index" VALUES (812, 'ymy.index', '圆明园博物馆', 11, '116.303562', '39.998491', NULL, '2025-04-03 10:11:12', NULL);
INSERT INTO "public"."sys_index" VALUES (813, 'ymy.index', '圆明园博物馆', 12, '116.303562', '39.998491', NULL, '2025-04-03 10:11:12', NULL);
INSERT INTO "public"."sys_index" VALUES (814, 'ymy.index', '圆明园博物馆', 13, '116.303562', '39.998491', NULL, '2025-04-03 10:11:12', NULL);
INSERT INTO "public"."sys_index" VALUES (815, 'ymy.index', '圆明园博物馆', 14, '116.303562', '39.998491', NULL, '2025-04-03 10:11:12', NULL);
INSERT INTO "public"."sys_index" VALUES (816, 'ymy.index', '山门', 15, '116.303419', '39.998748', NULL, '2025-04-03 10:20:23', NULL);
INSERT INTO "public"."sys_index" VALUES (817, 'ymy.index', '山门', 16, '116.303419', '39.998748', NULL, '2025-04-03 10:20:23', NULL);
INSERT INTO "public"."sys_index" VALUES (818, 'ymy.index', '山门', 17, '116.303419', '39.998748', NULL, '2025-04-03 10:20:23', NULL);
INSERT INTO "public"."sys_index" VALUES (819, 'ymy.index', '山门', 18, '116.303419', '39.998748', NULL, '2025-04-03 10:20:23', NULL);
INSERT INTO "public"."sys_index" VALUES (820, 'ymy.index', '山门', 19, '116.303419', '39.998748', NULL, '2025-04-03 10:20:23', NULL);
INSERT INTO "public"."sys_index" VALUES (821, 'ymy.index', '山门', 20, '116.303419', '39.998748', NULL, '2025-04-03 10:20:23', NULL);
INSERT INTO "public"."sys_index" VALUES (822, 'ymy.index', '山门', 21, '116.303419', '39.998748', NULL, '2025-04-03 10:20:23', NULL);
INSERT INTO "public"."sys_index" VALUES (823, 'ymy.index', '山门', 22, '116.303419', '39.998748', NULL, '2025-04-03 10:20:23', NULL);
INSERT INTO "public"."sys_index" VALUES (824, 'ymy.index', '山门', 23, '116.303419', '39.998748', NULL, '2025-04-03 10:20:23', NULL);
INSERT INTO "public"."sys_index" VALUES (825, 'ymy.index', '山门', 24, '116.303419', '39.998748', NULL, '2025-04-03 10:20:23', NULL);
INSERT INTO "public"."sys_index" VALUES (826, 'ymy.index', '山门', 25, '116.303419', '39.998748', NULL, '2025-04-03 10:20:23', NULL);
INSERT INTO "public"."sys_index" VALUES (827, 'ymy.index', '山门', 26, '116.303419', '39.998748', NULL, '2025-04-03 10:20:23', NULL);
INSERT INTO "public"."sys_index" VALUES (828, 'ymy.index', '山门', 27, '116.303419', '39.998748', NULL, '2025-04-03 10:20:23', NULL);
INSERT INTO "public"."sys_index" VALUES (829, 'ymy.index', '山门', 28, '116.303419', '39.998748', NULL, '2025-04-03 10:20:23', NULL);
INSERT INTO "public"."sys_index" VALUES (830, 'ymy.index', '三圣殿', 29, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (831, 'ymy.index', '三圣殿', 30, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (832, 'ymy.index', '三圣殿', 31, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (833, 'ymy.index', '三圣殿', 32, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (834, 'ymy.index', '三圣殿', 33, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (835, 'ymy.index', '三圣殿', 34, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (836, 'ymy.index', '三圣殿', 35, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (837, 'ymy.index', '三圣殿', 36, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (838, 'ymy.index', '三圣殿', 37, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (839, 'ymy.index', '三圣殿', 38, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (840, 'ymy.index', '三圣殿', 39, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (841, 'ymy.index', '三圣殿', 40, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (842, 'ymy.index', '三圣殿', 41, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (843, 'ymy.index', '三圣殿', 42, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (844, 'ymy.index', '三圣殿', 43, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (845, 'ymy.index', '三圣殿', 44, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (846, 'ymy.index', '三圣殿', 45, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (847, 'ymy.index', '三圣殿', 46, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (848, 'ymy.index', '三圣殿', 47, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (849, 'ymy.index', '三圣殿', 48, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (850, 'ymy.index', '三圣殿', 49, '116.303372', '39.999152', NULL, '2025-04-03 10:30:02', NULL);
INSERT INTO "public"."sys_index" VALUES (851, 'ymy.index', '文殊亭', 50, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (852, 'ymy.index', '文殊亭', 51, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (853, 'ymy.index', '文殊亭', 52, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (854, 'ymy.index', '文殊亭', 53, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (855, 'ymy.index', '文殊亭', 54, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (856, 'ymy.index', '文殊亭', 55, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (857, 'ymy.index', '文殊亭', 56, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (858, 'ymy.index', '文殊亭', 57, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (859, 'ymy.index', '文殊亭', 58, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (860, 'ymy.index', '文殊亭', 59, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (861, 'ymy.index', '文殊亭', 60, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (862, 'ymy.index', '文殊亭', 61, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (863, 'ymy.index', '文殊亭', 62, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (864, 'ymy.index', '文殊亭', 63, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (865, 'ymy.index', '文殊亭', 64, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (866, 'ymy.index', '文殊亭', 65, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (867, 'ymy.index', '文殊亭', 66, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (868, 'ymy.index', '文殊亭', 67, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (869, 'ymy.index', '文殊亭', 68, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (870, 'ymy.index', '文殊亭', 69, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (871, 'ymy.index', '文殊亭', 70, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (872, 'ymy.index', '文殊亭', 71, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (873, 'ymy.index', '文殊亭', 72, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (874, 'ymy.index', '文殊亭', 73, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (875, 'ymy.index', '文殊亭', 74, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (876, 'ymy.index', '文殊亭', 75, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (877, 'ymy.index', '文殊亭', 76, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (878, 'ymy.index', '文殊亭', 77, '116.303308', '39.999498', NULL, '2025-04-03 10:41:12', NULL);
INSERT INTO "public"."sys_index" VALUES (879, 'ymy.index', '天王殿', 78, '116.303494', '39.998893', NULL, '2025-04-03 10:46:59', NULL);
INSERT INTO "public"."sys_index" VALUES (880, 'ymy.index', '天王殿', 79, '116.303494', '39.998893', NULL, '2025-04-03 10:46:59', NULL);
INSERT INTO "public"."sys_index" VALUES (881, 'ymy.index', '天王殿', 80, '116.303494', '39.998893', NULL, '2025-04-03 10:46:59', NULL);
INSERT INTO "public"."sys_index" VALUES (882, 'ymy.index', '天王殿', 81, '116.303494', '39.998893', NULL, '2025-04-03 10:46:59', NULL);
INSERT INTO "public"."sys_index" VALUES (883, 'ymy.index', '天王殿', 82, '116.303494', '39.998893', NULL, '2025-04-03 10:46:59', NULL);
INSERT INTO "public"."sys_index" VALUES (884, 'ymy.index', '天王殿', 83, '116.303494', '39.998893', NULL, '2025-04-03 10:46:59', NULL);
INSERT INTO "public"."sys_index" VALUES (885, 'ymy.index', '天王殿', 84, '116.303494', '39.998893', NULL, '2025-04-03 10:46:59', NULL);
INSERT INTO "public"."sys_index" VALUES (886, 'ymy.index', '天王殿', 85, '116.303494', '39.998893', NULL, '2025-04-03 10:46:59', NULL);
INSERT INTO "public"."sys_index" VALUES (887, 'ymy.index', '天王殿', 86, '116.303494', '39.998893', NULL, '2025-04-03 10:46:59', NULL);
INSERT INTO "public"."sys_index" VALUES (888, 'ymy.index', '天王殿', 87, '116.303494', '39.998893', NULL, '2025-04-03 10:46:59', NULL);
INSERT INTO "public"."sys_index" VALUES (889, 'ymy.index', '天王殿', 88, '116.303494', '39.998893', NULL, '2025-04-03 10:46:59', NULL);
INSERT INTO "public"."sys_index" VALUES (890, 'ymy.index', '天王殿', 89, '116.303494', '39.998893', NULL, '2025-04-03 10:46:59', NULL);
INSERT INTO "public"."sys_index" VALUES (891, 'ymy.index', '天王殿', 90, '116.303494', '39.998893', NULL, '2025-04-03 10:46:59', NULL);
INSERT INTO "public"."sys_index" VALUES (892, 'ymy.index', '天王殿', 91, '116.303494', '39.998893', NULL, '2025-04-03 10:46:59', NULL);
INSERT INTO "public"."sys_index" VALUES (893, 'ymy.index', '天王殿', 92, '116.303494', '39.998893', NULL, '2025-04-03 10:46:59', NULL);
INSERT INTO "public"."sys_index" VALUES (894, 'ymy.index', '天王殿', 93, '116.303494', '39.998893', NULL, '2025-04-03 10:46:59', NULL);
INSERT INTO "public"."sys_index" VALUES (895, 'ymy.index', '天王殿', 94, '116.303494', '39.998893', NULL, '2025-04-03 10:46:59', NULL);
INSERT INTO "public"."sys_index" VALUES (896, 'ymy.index', '天王殿', 95, '116.303494', '39.998893', NULL, '2025-04-03 10:46:59', NULL);
INSERT INTO "public"."sys_index" VALUES (897, 'ymy.index', '天王殿', 96, '116.303494', '39.998893', NULL, '2025-04-03 10:46:59', NULL);
INSERT INTO "public"."sys_index" VALUES (898, 'ymy.index', '最上楼', 97, '116.303267', '39.999764', NULL, '2025-04-03 10:52:49', NULL);
INSERT INTO "public"."sys_index" VALUES (899, 'ymy.index', '最上楼', 98, '116.303267', '39.999764', NULL, '2025-04-03 10:52:49', NULL);
INSERT INTO "public"."sys_index" VALUES (900, 'ymy.index', '最上楼', 99, '116.303267', '39.999764', NULL, '2025-04-03 10:52:49', NULL);
INSERT INTO "public"."sys_index" VALUES (901, 'ymy.index', '最上楼', 100, '116.303267', '39.999764', NULL, '2025-04-03 10:52:49', NULL);
INSERT INTO "public"."sys_index" VALUES (902, 'ymy.index', '最上楼', 101, '116.303267', '39.999764', NULL, '2025-04-03 10:52:49', NULL);
INSERT INTO "public"."sys_index" VALUES (903, 'ymy.index', '最上楼', 102, '116.303267', '39.999764', NULL, '2025-04-03 10:52:49', NULL);
INSERT INTO "public"."sys_index" VALUES (904, 'ymy.index', '最上楼', 103, '116.303267', '39.999764', NULL, '2025-04-03 10:52:49', NULL);
INSERT INTO "public"."sys_index" VALUES (905, 'ymy.index', '最上楼', 104, '116.303267', '39.999764', NULL, '2025-04-03 10:52:49', NULL);
INSERT INTO "public"."sys_index" VALUES (906, 'ymy.index', '最上楼', 105, '116.303267', '39.999764', NULL, '2025-04-03 10:52:49', NULL);
INSERT INTO "public"."sys_index" VALUES (907, 'ymy.index', '最上楼', 106, '116.303267', '39.999764', NULL, '2025-04-03 10:52:49', NULL);
INSERT INTO "public"."sys_index" VALUES (908, 'ymy.index', '最上楼', 107, '116.303267', '39.999764', NULL, '2025-04-03 10:52:49', NULL);
INSERT INTO "public"."sys_index" VALUES (909, 'ymy.index', '最上楼', 108, '116.303267', '39.999764', NULL, '2025-04-03 10:52:49', NULL);
INSERT INTO "public"."sys_index" VALUES (910, 'ymy.index', '最上楼', 109, '116.303267', '39.999764', NULL, '2025-04-03 10:52:49', NULL);
INSERT INTO "public"."sys_index" VALUES (911, 'ymy.index', '最上楼', 110, '116.303267', '39.999764', NULL, '2025-04-03 10:52:49', NULL);
INSERT INTO "public"."sys_index" VALUES (912, 'ymy.index', '最上楼', 111, '116.303267', '39.999764', NULL, '2025-04-03 10:52:49', NULL);
INSERT INTO "public"."sys_index" VALUES (913, 'ymy.index', '最上楼', 112, '116.303267', '39.999764', NULL, '2025-04-03 10:52:49', NULL);
INSERT INTO "public"."sys_index" VALUES (914, 'ymy.index', '蘋香榭', 113, '116.303938', '40.002090', NULL, '2025-04-03 11:10:30', NULL);
INSERT INTO "public"."sys_index" VALUES (915, 'ymy.index', '蘋香榭', 114, '116.303938', '40.002090', NULL, '2025-04-03 11:10:30', NULL);
INSERT INTO "public"."sys_index" VALUES (916, 'ymy.index', '蘋香榭', 115, '116.303938', '40.002090', NULL, '2025-04-03 11:10:30', NULL);
INSERT INTO "public"."sys_index" VALUES (917, 'ymy.index', '蘋香榭', 116, '116.303938', '40.002090', NULL, '2025-04-03 11:10:30', NULL);
INSERT INTO "public"."sys_index" VALUES (918, 'ymy.index', '蘋香榭', 117, '116.303938', '40.002090', NULL, '2025-04-03 11:10:30', NULL);
INSERT INTO "public"."sys_index" VALUES (919, 'ymy.index', '蘋香榭', 118, '116.303938', '40.002090', NULL, '2025-04-03 11:10:30', NULL);
INSERT INTO "public"."sys_index" VALUES (920, 'ymy.index', '蘋香榭', 119, '116.303938', '40.002090', NULL, '2025-04-03 11:10:30', NULL);
INSERT INTO "public"."sys_index" VALUES (921, 'ymy.index', '蘋香榭', 120, '116.303938', '40.002090', NULL, '2025-04-03 11:10:30', NULL);
INSERT INTO "public"."sys_index" VALUES (922, 'ymy.index', '蘋香榭', 121, '116.303938', '40.002090', NULL, '2025-04-03 11:10:30', NULL);
INSERT INTO "public"."sys_index" VALUES (923, 'ymy.index', '蘋香榭', 122, '116.303938', '40.002090', NULL, '2025-04-03 11:10:30', NULL);
INSERT INTO "public"."sys_index" VALUES (924, 'ymy.index', '蘋香榭', 123, '116.303938', '40.002090', NULL, '2025-04-03 11:10:30', NULL);
INSERT INTO "public"."sys_index" VALUES (925, 'ymy.index', '蘋香榭', 124, '116.303938', '40.002090', NULL, '2025-04-03 11:10:30', NULL);
INSERT INTO "public"."sys_index" VALUES (926, 'ymy.index', '蘋香榭', 125, '116.303938', '40.002090', NULL, '2025-04-03 11:10:30', NULL);
INSERT INTO "public"."sys_index" VALUES (927, 'ymy.index', '蘋香榭', 126, '116.303938', '40.002090', NULL, '2025-04-03 11:10:30', NULL);
INSERT INTO "public"."sys_index" VALUES (928, 'ymy.index', '庄严法界', 127, '116.304015', '40.002093', NULL, '2025-04-03 11:14:14', NULL);
INSERT INTO "public"."sys_index" VALUES (929, 'ymy.index', '庄严法界', 128, '116.304015', '40.002093', NULL, '2025-04-03 11:14:14', NULL);
INSERT INTO "public"."sys_index" VALUES (930, 'ymy.index', '庄严法界', 129, '116.304015', '40.002093', NULL, '2025-04-03 11:14:14', NULL);
INSERT INTO "public"."sys_index" VALUES (931, 'ymy.index', '庄严法界', 130, '116.304015', '40.002093', NULL, '2025-04-03 11:14:14', NULL);
INSERT INTO "public"."sys_index" VALUES (932, 'ymy.index', '庄严法界', 131, '116.304015', '40.002093', NULL, '2025-04-03 11:14:14', NULL);
INSERT INTO "public"."sys_index" VALUES (933, 'ymy.index', '庄严法界', 132, '116.304015', '40.002093', NULL, '2025-04-03 11:14:14', NULL);
INSERT INTO "public"."sys_index" VALUES (934, 'ymy.index', '庄严法界', 133, '116.304015', '40.002093', NULL, '2025-04-03 11:14:14', NULL);
INSERT INTO "public"."sys_index" VALUES (935, 'ymy.index', '庄严法界', 134, '116.304015', '40.002093', NULL, '2025-04-03 11:14:14', NULL);
INSERT INTO "public"."sys_index" VALUES (936, 'ymy.index', '庄严法界', 135, '116.304015', '40.002093', NULL, '2025-04-03 11:14:14', NULL);
INSERT INTO "public"."sys_index" VALUES (937, 'ymy.index', '庄严法界', 136, '116.304015', '40.002093', NULL, '2025-04-03 11:14:14', NULL);
INSERT INTO "public"."sys_index" VALUES (938, 'ymy.index', '庄严法界', 137, '116.304015', '40.002093', NULL, '2025-04-03 11:14:14', NULL);
INSERT INTO "public"."sys_index" VALUES (939, 'ymy.index', '庄严法界', 138, '116.304015', '40.002093', NULL, '2025-04-03 11:14:14', NULL);
INSERT INTO "public"."sys_index" VALUES (940, 'ymy.index', '庄严法界', 139, '116.304015', '40.002093', NULL, '2025-04-03 11:14:14', NULL);
INSERT INTO "public"."sys_index" VALUES (941, 'ymy.index', '庄严法界', 140, '116.304015', '40.002093', NULL, '2025-04-03 11:14:14', NULL);
INSERT INTO "public"."sys_index" VALUES (942, 'ymy.index', '庄严法界', 141, '116.304015', '40.002093', NULL, '2025-04-03 11:14:14', NULL);
INSERT INTO "public"."sys_index" VALUES (943, 'ymy.index', '庄严法界', 142, '116.304015', '40.002093', NULL, '2025-04-03 11:14:14', NULL);
INSERT INTO "public"."sys_index" VALUES (944, 'ymy.index', '展诗应律', 143, '116.303996', '40.003283', NULL, '2025-04-03 11:19:33', NULL);
INSERT INTO "public"."sys_index" VALUES (945, 'ymy.index', '展诗应律', 144, '116.303996', '40.003283', NULL, '2025-04-03 11:19:33', NULL);
INSERT INTO "public"."sys_index" VALUES (946, 'ymy.index', '展诗应律', 145, '116.303996', '40.003283', NULL, '2025-04-03 11:19:33', NULL);
INSERT INTO "public"."sys_index" VALUES (947, 'ymy.index', '展诗应律', 146, '116.303996', '40.003283', NULL, '2025-04-03 11:19:33', NULL);
INSERT INTO "public"."sys_index" VALUES (948, 'ymy.index', '展诗应律', 147, '116.303996', '40.003283', NULL, '2025-04-03 11:19:33', NULL);
INSERT INTO "public"."sys_index" VALUES (949, 'ymy.index', '展诗应律', 148, '116.303996', '40.003283', NULL, '2025-04-03 11:19:33', NULL);
INSERT INTO "public"."sys_index" VALUES (950, 'ymy.index', '展诗应律', 149, '116.303996', '40.003283', NULL, '2025-04-03 11:19:33', NULL);
INSERT INTO "public"."sys_index" VALUES (951, 'ymy.index', '展诗应律', 150, '116.303996', '40.003283', NULL, '2025-04-03 11:19:33', NULL);
INSERT INTO "public"."sys_index" VALUES (952, 'ymy.index', '展诗应律', 151, '116.303996', '40.003283', NULL, '2025-04-03 11:19:33', NULL);
INSERT INTO "public"."sys_index" VALUES (953, 'ymy.index', '展诗应律', 152, '116.303996', '40.003283', NULL, '2025-04-03 11:19:33', NULL);
INSERT INTO "public"."sys_index" VALUES (954, 'ymy.index', '展诗应律', 153, '116.303996', '40.003283', NULL, '2025-04-03 11:19:33', NULL);
INSERT INTO "public"."sys_index" VALUES (955, 'ymy.index', '展诗应律', 154, '116.303996', '40.003283', NULL, '2025-04-03 11:19:33', NULL);
INSERT INTO "public"."sys_index" VALUES (956, 'ymy.index', '展诗应律', 155, '116.303996', '40.003283', NULL, '2025-04-03 11:19:33', NULL);
INSERT INTO "public"."sys_index" VALUES (957, 'ymy.index', '展诗应律', 156, '116.303996', '40.003283', NULL, '2025-04-03 11:19:33', NULL);
INSERT INTO "public"."sys_index" VALUES (958, 'ymy.index', '展诗应律', 157, '116.303996', '40.003283', NULL, '2025-04-03 11:19:33', NULL);
INSERT INTO "public"."sys_index" VALUES (959, 'ymy.index', '春泽斋', 158, '116.302741', '40.003016', NULL, '2025-04-03 11:22:29', NULL);
INSERT INTO "public"."sys_index" VALUES (960, 'ymy.index', '春泽斋', 159, '116.302741', '40.003016', NULL, '2025-04-03 11:22:29', NULL);
INSERT INTO "public"."sys_index" VALUES (961, 'ymy.index', '春泽斋', 160, '116.302741', '40.003016', NULL, '2025-04-03 11:22:29', NULL);
INSERT INTO "public"."sys_index" VALUES (962, 'ymy.index', '春泽斋', 161, '116.302741', '40.003016', NULL, '2025-04-03 11:22:29', NULL);
INSERT INTO "public"."sys_index" VALUES (963, 'ymy.index', '春泽斋', 162, '116.302741', '40.003016', NULL, '2025-04-03 11:22:29', NULL);
INSERT INTO "public"."sys_index" VALUES (964, 'ymy.index', '春泽斋', 163, '116.302741', '40.003016', NULL, '2025-04-03 11:22:29', NULL);
INSERT INTO "public"."sys_index" VALUES (965, 'ymy.index', '春泽斋', 164, '116.302741', '40.003016', NULL, '2025-04-03 11:22:29', NULL);
INSERT INTO "public"."sys_index" VALUES (966, 'ymy.index', '春泽斋', 165, '116.302741', '40.003016', NULL, '2025-04-03 11:22:29', NULL);
INSERT INTO "public"."sys_index" VALUES (967, 'ymy.index', '春泽斋', 166, '116.302741', '40.003016', NULL, '2025-04-03 11:22:29', NULL);
INSERT INTO "public"."sys_index" VALUES (968, 'ymy.index', '春泽斋', 167, '116.302741', '40.003016', NULL, '2025-04-03 11:22:29', NULL);
INSERT INTO "public"."sys_index" VALUES (969, 'ymy.index', '春泽斋', 168, '116.302741', '40.003016', NULL, '2025-04-03 11:22:29', NULL);
INSERT INTO "public"."sys_index" VALUES (970, 'ymy.index', '会心桥', 169, '116.301724', '40.004380', NULL, '2025-04-03 11:29:14', NULL);
INSERT INTO "public"."sys_index" VALUES (971, 'ymy.index', '会心桥', 170, '116.301724', '40.004380', NULL, '2025-04-03 11:29:14', NULL);
INSERT INTO "public"."sys_index" VALUES (972, 'ymy.index', '会心桥', 171, '116.301724', '40.004380', NULL, '2025-04-03 11:29:14', NULL);
INSERT INTO "public"."sys_index" VALUES (973, 'ymy.index', '会心桥', 172, '116.301724', '40.004380', NULL, '2025-04-03 11:29:14', NULL);
INSERT INTO "public"."sys_index" VALUES (974, 'ymy.index', '会心桥', 173, '116.301724', '40.004380', NULL, '2025-04-03 11:29:14', NULL);
INSERT INTO "public"."sys_index" VALUES (975, 'ymy.index', '会心桥', 174, '116.301724', '40.004380', NULL, '2025-04-03 11:29:14', NULL);
INSERT INTO "public"."sys_index" VALUES (976, 'ymy.index', '会心桥', 175, '116.301724', '40.004380', NULL, '2025-04-03 11:29:14', NULL);
INSERT INTO "public"."sys_index" VALUES (977, 'ymy.index', '会心桥', 176, '116.301724', '40.004380', NULL, '2025-04-03 11:29:14', NULL);
INSERT INTO "public"."sys_index" VALUES (978, 'ymy.index', '会心桥', 177, '116.301724', '40.004380', NULL, '2025-04-03 11:29:14', NULL);
INSERT INTO "public"."sys_index" VALUES (979, 'ymy.index', '会心桥', 178, '116.301724', '40.004380', NULL, '2025-04-03 11:29:14', NULL);
INSERT INTO "public"."sys_index" VALUES (980, 'ymy.index', '会心桥', 179, '116.301724', '40.004380', NULL, '2025-04-03 11:29:14', NULL);
INSERT INTO "public"."sys_index" VALUES (981, 'ymy.index', '会心桥', 180, '116.301724', '40.004380', NULL, '2025-04-03 11:29:14', NULL);
INSERT INTO "public"."sys_index" VALUES (982, 'ymy.index', '会心桥', 181, '116.301724', '40.004380', NULL, '2025-04-03 11:29:14', NULL);
INSERT INTO "public"."sys_index" VALUES (983, 'ymy.index', '会心桥', 182, '116.301724', '40.004380', NULL, '2025-04-03 11:29:14', NULL);
INSERT INTO "public"."sys_index" VALUES (984, 'ymy.index', '会心桥', 183, '116.301724', '40.004380', NULL, '2025-04-03 11:29:14', NULL);
INSERT INTO "public"."sys_index" VALUES (985, 'ymy.index', '会心桥', 184, '116.301724', '40.004380', NULL, '2025-04-03 11:29:14', NULL);
INSERT INTO "public"."sys_index" VALUES (986, 'ymy.index', '会心桥', 185, '116.301724', '40.004380', NULL, '2025-04-03 11:29:14', NULL);
INSERT INTO "public"."sys_index" VALUES (987, 'ymy.index', '别有洞天', 186, '116.301230', '40.005336', NULL, '2025-04-03 11:31:58', NULL);
INSERT INTO "public"."sys_index" VALUES (988, 'ymy.index', '别有洞天', 187, '116.301230', '40.005336', NULL, '2025-04-03 11:31:58', NULL);
INSERT INTO "public"."sys_index" VALUES (989, 'ymy.index', '别有洞天', 188, '116.301230', '40.005336', NULL, '2025-04-03 11:31:58', NULL);
INSERT INTO "public"."sys_index" VALUES (990, 'ymy.index', '别有洞天', 189, '116.301230', '40.005336', NULL, '2025-04-03 11:31:58', NULL);
INSERT INTO "public"."sys_index" VALUES (991, 'ymy.index', '别有洞天', 190, '116.301230', '40.005336', NULL, '2025-04-03 11:31:58', NULL);
INSERT INTO "public"."sys_index" VALUES (992, 'ymy.index', '别有洞天', 191, '116.301230', '40.005336', NULL, '2025-04-03 11:31:58', NULL);
INSERT INTO "public"."sys_index" VALUES (993, 'ymy.index', '别有洞天', 192, '116.301230', '40.005336', NULL, '2025-04-03 11:31:58', NULL);
INSERT INTO "public"."sys_index" VALUES (994, 'ymy.index', '别有洞天', 193, '116.301230', '40.005336', NULL, '2025-04-03 11:31:58', NULL);
INSERT INTO "public"."sys_index" VALUES (995, 'ymy.index', '别有洞天', 194, '116.301230', '40.005336', NULL, '2025-04-03 11:31:58', NULL);
INSERT INTO "public"."sys_index" VALUES (996, 'ymy.index', '别有洞天', 195, '116.301230', '40.005336', NULL, '2025-04-03 11:31:58', NULL);
INSERT INTO "public"."sys_index" VALUES (997, 'ymy.index', '别有洞天', 196, '116.301230', '40.005336', NULL, '2025-04-03 11:31:58', NULL);
INSERT INTO "public"."sys_index" VALUES (998, 'ymy.index', '广育宫', 197, '116.299329', '40.005593', NULL, '2025-04-03 11:45:17', NULL);
INSERT INTO "public"."sys_index" VALUES (999, 'ymy.index', '广育宫', 198, '116.299329', '40.005593', NULL, '2025-04-03 11:45:17', NULL);
INSERT INTO "public"."sys_index" VALUES (1000, 'ymy.index', '广育宫', 199, '116.299329', '40.005593', NULL, '2025-04-03 11:45:17', NULL);
INSERT INTO "public"."sys_index" VALUES (1001, 'ymy.index', '广育宫', 200, '116.299329', '40.005593', NULL, '2025-04-03 11:45:17', NULL);
INSERT INTO "public"."sys_index" VALUES (1002, 'ymy.index', '广育宫', 201, '116.299329', '40.005593', NULL, '2025-04-03 11:45:17', NULL);
INSERT INTO "public"."sys_index" VALUES (1003, 'ymy.index', '广育宫', 202, '116.299329', '40.005593', NULL, '2025-04-03 11:45:17', NULL);
INSERT INTO "public"."sys_index" VALUES (1004, 'ymy.index', '广育宫', 203, '116.299329', '40.005593', NULL, '2025-04-03 11:45:17', NULL);
INSERT INTO "public"."sys_index" VALUES (1005, 'ymy.index', '广育宫', 204, '116.299329', '40.005593', NULL, '2025-04-03 11:45:17', NULL);
INSERT INTO "public"."sys_index" VALUES (1006, 'ymy.index', '广育宫', 205, '116.299329', '40.005593', NULL, '2025-04-03 11:45:17', NULL);
INSERT INTO "public"."sys_index" VALUES (1007, 'ymy.index', '广育宫', 206, '116.299329', '40.005593', NULL, '2025-04-03 11:45:17', NULL);
INSERT INTO "public"."sys_index" VALUES (1008, 'ymy.index', '广育宫', 207, '116.299329', '40.005593', NULL, '2025-04-03 11:45:17', NULL);
INSERT INTO "public"."sys_index" VALUES (1009, 'ymy.index', '广育宫', 208, '116.299329', '40.005593', NULL, '2025-04-03 11:45:17', NULL);
INSERT INTO "public"."sys_index" VALUES (1010, 'ymy.index', '广育宫', 209, '116.299329', '40.005593', NULL, '2025-04-03 11:45:17', NULL);
INSERT INTO "public"."sys_index" VALUES (1011, 'ymy.index', '夹镜鸣琴', 210, '116.298156', '40.005367', NULL, '2025-04-03 11:50:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1012, 'ymy.index', '夹镜鸣琴', 211, '116.298156', '40.005367', NULL, '2025-04-03 11:50:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1013, 'ymy.index', '夹镜鸣琴', 212, '116.298156', '40.005367', NULL, '2025-04-03 11:50:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1014, 'ymy.index', '夹镜鸣琴', 213, '116.298156', '40.005367', NULL, '2025-04-03 11:50:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1015, 'ymy.index', '夹镜鸣琴', 214, '116.298156', '40.005367', NULL, '2025-04-03 11:50:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1016, 'ymy.index', '夹镜鸣琴', 215, '116.298156', '40.005367', NULL, '2025-04-03 11:50:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1017, 'ymy.index', '夹镜鸣琴', 216, '116.298156', '40.005367', NULL, '2025-04-03 11:50:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1018, 'ymy.index', '夹镜鸣琴', 217, '116.298156', '40.005367', NULL, '2025-04-03 11:50:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1019, 'ymy.index', '夹镜鸣琴', 218, '116.298156', '40.005367', NULL, '2025-04-03 11:50:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1020, 'ymy.index', '夹镜鸣琴', 219, '116.298156', '40.005367', NULL, '2025-04-03 11:50:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1021, 'ymy.index', '夹镜鸣琴', 220, '116.298156', '40.005367', NULL, '2025-04-03 11:50:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1022, 'ymy.index', '夹镜鸣琴', 221, '116.298156', '40.005367', NULL, '2025-04-03 11:50:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1023, 'ymy.index', '夹镜鸣琴', 222, '116.298156', '40.005367', NULL, '2025-04-03 11:50:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1024, 'ymy.index', '夹镜鸣琴', 223, '116.298156', '40.005367', NULL, '2025-04-03 11:50:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1025, 'ymy.index', '夹镜鸣琴', 224, '116.298156', '40.005367', NULL, '2025-04-03 11:50:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1026, 'ymy.index', '澡身浴德', 225, '116.295114', '40.006116', NULL, '2025-04-03 11:58:26', NULL);
INSERT INTO "public"."sys_index" VALUES (1027, 'ymy.index', '澡身浴德', 226, '116.295114', '40.006116', NULL, '2025-04-03 11:58:26', NULL);
INSERT INTO "public"."sys_index" VALUES (1028, 'ymy.index', '澡身浴德', 227, '116.295114', '40.006116', NULL, '2025-04-03 11:58:26', NULL);
INSERT INTO "public"."sys_index" VALUES (1029, 'ymy.index', '澡身浴德', 228, '116.295114', '40.006116', NULL, '2025-04-03 11:58:26', NULL);
INSERT INTO "public"."sys_index" VALUES (1030, 'ymy.index', '澡身浴德', 229, '116.295114', '40.006116', NULL, '2025-04-03 11:58:26', NULL);
INSERT INTO "public"."sys_index" VALUES (1031, 'ymy.index', '澡身浴德', 230, '116.295114', '40.006116', NULL, '2025-04-03 11:58:26', NULL);
INSERT INTO "public"."sys_index" VALUES (1032, 'ymy.index', '洞天深处', 231, '116.295029', '40.003747', NULL, '2025-04-03 12:06:16', NULL);
INSERT INTO "public"."sys_index" VALUES (1033, 'ymy.index', '洞天深处', 232, '116.295029', '40.003747', NULL, '2025-04-03 12:06:16', NULL);
INSERT INTO "public"."sys_index" VALUES (1034, 'ymy.index', '洞天深处', 233, '116.295029', '40.003747', NULL, '2025-04-03 12:06:17', NULL);
INSERT INTO "public"."sys_index" VALUES (1035, 'ymy.index', '洞天深处', 234, '116.295029', '40.003747', NULL, '2025-04-03 12:06:17', NULL);
INSERT INTO "public"."sys_index" VALUES (1036, 'ymy.index', '洞天深处', 235, '116.295029', '40.003747', NULL, '2025-04-03 12:06:17', NULL);
INSERT INTO "public"."sys_index" VALUES (1037, 'ymy.index', '洞天深处', 236, '116.295029', '40.003747', NULL, '2025-04-03 12:06:17', NULL);
INSERT INTO "public"."sys_index" VALUES (1038, 'ymy.index', '曲院风荷', 237, '116.294563', '40.004011', NULL, '2025-04-03 12:09:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1039, 'ymy.index', '曲院风荷', 238, '116.294563', '40.004011', NULL, '2025-04-03 12:09:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1040, 'ymy.index', '曲院风荷', 239, '116.294563', '40.004011', NULL, '2025-04-03 12:09:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1041, 'ymy.index', '曲院风荷', 240, '116.294563', '40.004011', NULL, '2025-04-03 12:09:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1042, 'ymy.index', '曲院风荷', 241, '116.294563', '40.004011', NULL, '2025-04-03 12:09:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1043, 'ymy.index', '曲院风荷', 242, '116.294563', '40.004011', NULL, '2025-04-03 12:09:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1044, 'ymy.index', '曲院风荷', 243, '116.294563', '40.004011', NULL, '2025-04-03 12:09:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1045, 'ymy.index', '曲院风荷', 244, '116.294563', '40.004011', NULL, '2025-04-03 12:09:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1046, 'ymy.index', '曲院风荷', 245, '116.294563', '40.004011', NULL, '2025-04-03 12:09:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1047, 'ymy.index', '曲院风荷', 246, '116.294563', '40.004011', NULL, '2025-04-03 12:09:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1048, 'ymy.index', '曲院风荷', 247, '116.294563', '40.004011', NULL, '2025-04-03 12:09:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1049, 'ymy.index', '曲院风荷', 248, '116.294563', '40.004011', NULL, '2025-04-03 12:09:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1050, 'ymy.index', '洞天深处', 249, '116.294655', '40.002852', NULL, '2025-04-03 12:12:05', NULL);
INSERT INTO "public"."sys_index" VALUES (1051, 'ymy.index', '洞天深处', 250, '116.294655', '40.002852', NULL, '2025-04-03 12:12:05', NULL);
INSERT INTO "public"."sys_index" VALUES (1052, 'ymy.index', '洞天深处', 251, '116.294655', '40.002852', NULL, '2025-04-03 12:12:05', NULL);
INSERT INTO "public"."sys_index" VALUES (1053, 'ymy.index', '洞天深处', 252, '116.294655', '40.002852', NULL, '2025-04-03 12:12:05', NULL);
INSERT INTO "public"."sys_index" VALUES (1054, 'ymy.index', '洞天深处', 253, '116.294655', '40.002852', NULL, '2025-04-03 12:12:05', NULL);
INSERT INTO "public"."sys_index" VALUES (1055, 'ymy.index', '洞天深处', 254, '116.294655', '40.002852', NULL, '2025-04-03 12:12:05', NULL);
INSERT INTO "public"."sys_index" VALUES (1056, 'ymy.index', '洞天深处', 255, '116.294655', '40.002852', NULL, '2025-04-03 12:12:05', NULL);
INSERT INTO "public"."sys_index" VALUES (1057, 'ymy.index', '勤政亲贤', 256, '116.292400', '40.001842', NULL, '2025-04-03 12:18:07', NULL);
INSERT INTO "public"."sys_index" VALUES (1058, 'ymy.index', '勤政亲贤', 257, '116.292400', '40.001842', NULL, '2025-04-03 12:18:07', NULL);
INSERT INTO "public"."sys_index" VALUES (1059, 'ymy.index', '勤政亲贤', 258, '116.292400', '40.001842', NULL, '2025-04-03 12:18:07', NULL);
INSERT INTO "public"."sys_index" VALUES (1060, 'ymy.index', '勤政亲贤', 259, '116.292400', '40.001842', NULL, '2025-04-03 12:18:07', NULL);
INSERT INTO "public"."sys_index" VALUES (1061, 'ymy.index', '勤政亲贤', 260, '116.292400', '40.001842', NULL, '2025-04-03 12:18:07', NULL);
INSERT INTO "public"."sys_index" VALUES (1062, 'ymy.index', '勤政亲贤', 261, '116.292400', '40.001842', NULL, '2025-04-03 12:18:07', NULL);
INSERT INTO "public"."sys_index" VALUES (1063, 'ymy.index', '勤政亲贤', 262, '116.292400', '40.001842', NULL, '2025-04-03 12:18:07', NULL);
INSERT INTO "public"."sys_index" VALUES (1064, 'ymy.index', '勤政亲贤', 263, '116.292400', '40.001842', NULL, '2025-04-03 12:18:07', NULL);
INSERT INTO "public"."sys_index" VALUES (1065, 'ymy.index', '勤政亲贤', 264, '116.292400', '40.001842', NULL, '2025-04-03 12:18:07', NULL);
INSERT INTO "public"."sys_index" VALUES (1066, 'ymy.index', '勤政亲贤', 265, '116.292400', '40.001842', NULL, '2025-04-03 12:18:07', NULL);
INSERT INTO "public"."sys_index" VALUES (1067, 'ymy.index', '勤政亲贤', 266, '116.292400', '40.001842', NULL, '2025-04-03 12:18:07', NULL);
INSERT INTO "public"."sys_index" VALUES (1068, 'ymy.index', '正大光明', 267, '116.290487', '40.001525', NULL, '2025-04-03 12:22:14', NULL);
INSERT INTO "public"."sys_index" VALUES (1069, 'ymy.index', '正大光明', 268, '116.290487', '40.001525', NULL, '2025-04-03 12:22:14', NULL);
INSERT INTO "public"."sys_index" VALUES (1070, 'ymy.index', '正大光明', 269, '116.290487', '40.001525', NULL, '2025-04-03 12:22:14', NULL);
INSERT INTO "public"."sys_index" VALUES (1071, 'ymy.index', '正大光明', 270, '116.290487', '40.001525', NULL, '2025-04-03 12:22:14', NULL);
INSERT INTO "public"."sys_index" VALUES (1072, 'ymy.index', '正大光明', 271, '116.290487', '40.001525', NULL, '2025-04-03 12:22:14', NULL);
INSERT INTO "public"."sys_index" VALUES (1073, 'ymy.index', '正大光明', 272, '116.290487', '40.001525', NULL, '2025-04-03 12:22:14', NULL);
INSERT INTO "public"."sys_index" VALUES (1074, 'ymy.index', '正大光明', 273, '116.290487', '40.001525', NULL, '2025-04-03 12:22:14', NULL);
INSERT INTO "public"."sys_index" VALUES (1075, 'ymy.index', '长春仙馆', 274, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1076, 'ymy.index', '长春仙馆', 275, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1077, 'ymy.index', '长春仙馆', 276, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1078, 'ymy.index', '长春仙馆', 277, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1079, 'ymy.index', '长春仙馆', 278, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1080, 'ymy.index', '长春仙馆', 279, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1081, 'ymy.index', '长春仙馆', 280, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1082, 'ymy.index', '长春仙馆', 281, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1083, 'ymy.index', '长春仙馆', 282, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1084, 'ymy.index', '长春仙馆', 283, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1085, 'ymy.index', '长春仙馆', 284, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1086, 'ymy.index', '长春仙馆', 285, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1087, 'ymy.index', '长春仙馆', 286, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1088, 'ymy.index', '长春仙馆', 287, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1089, 'ymy.index', '长春仙馆', 288, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1090, 'ymy.index', '长春仙馆', 289, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1091, 'ymy.index', '长春仙馆', 290, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1092, 'ymy.index', '长春仙馆', 291, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1093, 'ymy.index', '长春仙馆', 292, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1094, 'ymy.index', '长春仙馆', 293, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1095, 'ymy.index', '长春仙馆', 294, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1096, 'ymy.index', '长春仙馆', 295, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1097, 'ymy.index', '长春仙馆', 296, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1098, 'ymy.index', '长春仙馆', 297, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1099, 'ymy.index', '长春仙馆', 298, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1100, 'ymy.index', '长春仙馆', 299, '116.288602', '40.001686', NULL, '2025-04-03 12:31:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1101, 'ymy.index', '山高水长', 300, '116.287124', '40.001273', NULL, '2025-04-03 12:36:22', NULL);
INSERT INTO "public"."sys_index" VALUES (1102, 'ymy.index', '山高水长', 301, '116.287124', '40.001273', NULL, '2025-04-03 12:36:22', NULL);
INSERT INTO "public"."sys_index" VALUES (1103, 'ymy.index', '山高水长', 302, '116.287124', '40.001273', NULL, '2025-04-03 12:36:22', NULL);
INSERT INTO "public"."sys_index" VALUES (1104, 'ymy.index', '山高水长', 303, '116.287124', '40.001273', NULL, '2025-04-03 12:36:22', NULL);
INSERT INTO "public"."sys_index" VALUES (1105, 'ymy.index', '山高水长', 304, '116.287124', '40.001273', NULL, '2025-04-03 12:36:22', NULL);
INSERT INTO "public"."sys_index" VALUES (1106, 'ymy.index', '山高水长', 305, '116.287124', '40.001273', NULL, '2025-04-03 12:36:22', NULL);
INSERT INTO "public"."sys_index" VALUES (1107, 'ymy.index', '山高水长', 306, '116.287124', '40.001273', NULL, '2025-04-03 12:36:22', NULL);
INSERT INTO "public"."sys_index" VALUES (1108, 'ymy.index', '山高水长', 307, '116.287124', '40.001273', NULL, '2025-04-03 12:36:22', NULL);
INSERT INTO "public"."sys_index" VALUES (1109, 'ymy.index', '藻园', 308, '116.283996', '40.001050', NULL, '2025-04-03 13:44:02', NULL);
INSERT INTO "public"."sys_index" VALUES (1110, 'ymy.index', '藻园', 309, '116.283996', '40.001050', NULL, '2025-04-03 13:44:02', NULL);
INSERT INTO "public"."sys_index" VALUES (1111, 'ymy.index', '藻园', 310, '116.283996', '40.001050', NULL, '2025-04-03 13:44:02', NULL);
INSERT INTO "public"."sys_index" VALUES (1112, 'ymy.index', '藻园', 311, '116.283996', '40.001050', NULL, '2025-04-03 13:44:02', NULL);
INSERT INTO "public"."sys_index" VALUES (1113, 'ymy.index', '藻园', 312, '116.283996', '40.001050', NULL, '2025-04-03 13:44:02', NULL);
INSERT INTO "public"."sys_index" VALUES (1114, 'ymy.index', '藻园', 313, '116.283996', '40.001050', NULL, '2025-04-03 13:44:02', NULL);
INSERT INTO "public"."sys_index" VALUES (1115, 'ymy.index', '藻园', 314, '116.283996', '40.001050', NULL, '2025-04-03 13:44:02', NULL);
INSERT INTO "public"."sys_index" VALUES (1116, 'ymy.index', '藻园', 315, '116.283996', '40.001050', NULL, '2025-04-03 13:44:02', NULL);
INSERT INTO "public"."sys_index" VALUES (1117, 'ymy.index', '藻园', 316, '116.283996', '40.001050', NULL, '2025-04-03 13:44:02', NULL);
INSERT INTO "public"."sys_index" VALUES (1118, 'ymy.index', '藻园', 317, '116.283996', '40.001050', NULL, '2025-04-03 13:44:02', NULL);
INSERT INTO "public"."sys_index" VALUES (1119, 'ymy.index', '藻园', 318, '116.283996', '40.001050', NULL, '2025-04-03 13:44:02', NULL);
INSERT INTO "public"."sys_index" VALUES (1120, 'ymy.index', '藻园', 319, '116.283996', '40.001050', NULL, '2025-04-03 13:44:02', NULL);
INSERT INTO "public"."sys_index" VALUES (1121, 'ymy.index', '藻园', 320, '116.283996', '40.001050', NULL, '2025-04-03 13:44:02', NULL);
INSERT INTO "public"."sys_index" VALUES (1122, 'ymy.index', '藻园', 321, '116.283996', '40.001050', NULL, '2025-04-03 13:44:02', NULL);
INSERT INTO "public"."sys_index" VALUES (1123, 'ymy.index', '鸣玉溪桥', 322, '116.289186', '40.002633', NULL, '2025-04-03 13:56:59', NULL);
INSERT INTO "public"."sys_index" VALUES (1124, 'ymy.index', '鸣玉溪桥', 323, '116.289186', '40.002633', NULL, '2025-04-03 13:56:59', NULL);
INSERT INTO "public"."sys_index" VALUES (1125, 'ymy.index', '鸣玉溪桥', 324, '116.289186', '40.002633', NULL, '2025-04-03 13:56:59', NULL);
INSERT INTO "public"."sys_index" VALUES (1126, 'ymy.index', '鸣玉溪桥', 325, '116.289186', '40.002633', NULL, '2025-04-03 13:56:59', NULL);
INSERT INTO "public"."sys_index" VALUES (1127, 'ymy.index', '鸣玉溪桥', 326, '116.289186', '40.002633', NULL, '2025-04-03 13:56:59', NULL);
INSERT INTO "public"."sys_index" VALUES (1128, 'ymy.index', '鸣玉溪桥', 327, '116.289186', '40.002633', NULL, '2025-04-03 13:56:59', NULL);
INSERT INTO "public"."sys_index" VALUES (1129, 'ymy.index', '鸣玉溪桥', 328, '116.289186', '40.002633', NULL, '2025-04-03 13:56:59', NULL);
INSERT INTO "public"."sys_index" VALUES (1130, 'ymy.index', '鸣玉溪桥', 329, '116.289186', '40.002633', NULL, '2025-04-03 13:56:59', NULL);
INSERT INTO "public"."sys_index" VALUES (1131, 'ymy.index', '鸣玉溪桥', 330, '116.289186', '40.002633', NULL, '2025-04-03 13:56:59', NULL);
INSERT INTO "public"."sys_index" VALUES (1132, 'ymy.index', '鸣玉溪桥', 331, '116.289186', '40.002633', NULL, '2025-04-03 13:56:59', NULL);
INSERT INTO "public"."sys_index" VALUES (1133, 'ymy.index', '鸣玉溪桥', 332, '116.289186', '40.002633', NULL, '2025-04-03 13:56:59', NULL);
INSERT INTO "public"."sys_index" VALUES (1134, 'ymy.index', '鸣玉溪桥', 333, '116.289186', '40.002633', NULL, '2025-04-03 13:56:59', NULL);
INSERT INTO "public"."sys_index" VALUES (1135, 'ymy.index', '鸣玉溪桥', 334, '116.289186', '40.002633', NULL, '2025-04-03 13:56:59', NULL);
INSERT INTO "public"."sys_index" VALUES (1136, 'ymy.index', '鸣玉溪桥', 335, '116.289186', '40.002633', NULL, '2025-04-03 13:56:59', NULL);
INSERT INTO "public"."sys_index" VALUES (1137, 'ymy.index', '鸣玉溪桥', 336, '116.289186', '40.002633', NULL, '2025-04-03 13:56:59', NULL);
INSERT INTO "public"."sys_index" VALUES (1138, 'ymy.index', '鸣玉溪桥', 337, '116.289186', '40.002633', NULL, '2025-04-03 13:56:59', NULL);
INSERT INTO "public"."sys_index" VALUES (1139, 'ymy.index', '茹古涵今', 338, '116.288317', '40.002984', NULL, '2025-04-03 14:00:44', NULL);
INSERT INTO "public"."sys_index" VALUES (1140, 'ymy.index', '茹古涵今', 339, '116.288317', '40.002984', NULL, '2025-04-03 14:00:44', NULL);
INSERT INTO "public"."sys_index" VALUES (1141, 'ymy.index', '茹古涵今', 340, '116.288317', '40.002984', NULL, '2025-04-03 14:00:44', NULL);
INSERT INTO "public"."sys_index" VALUES (1142, 'ymy.index', '茹古涵今', 341, '116.288317', '40.002984', NULL, '2025-04-03 14:00:44', NULL);
INSERT INTO "public"."sys_index" VALUES (1143, 'ymy.index', '茹古涵今', 342, '116.288317', '40.002984', NULL, '2025-04-03 14:00:44', NULL);
INSERT INTO "public"."sys_index" VALUES (1144, 'ymy.index', '茹古涵今', 343, '116.288317', '40.002984', NULL, '2025-04-03 14:00:44', NULL);
INSERT INTO "public"."sys_index" VALUES (1145, 'ymy.index', '茹古涵今', 344, '116.288317', '40.002984', NULL, '2025-04-03 14:00:44', NULL);
INSERT INTO "public"."sys_index" VALUES (1146, 'ymy.index', '茹古涵今', 345, '116.288317', '40.002984', NULL, '2025-04-03 14:00:44', NULL);
INSERT INTO "public"."sys_index" VALUES (1147, 'ymy.index', '茹古涵今', 346, '116.288317', '40.002984', NULL, '2025-04-03 14:00:44', NULL);
INSERT INTO "public"."sys_index" VALUES (1148, 'ymy.index', '茹古涵今', 347, '116.288317', '40.002984', NULL, '2025-04-03 14:00:44', NULL);
INSERT INTO "public"."sys_index" VALUES (1149, 'ymy.index', '茹古涵今', 348, '116.288317', '40.002984', NULL, '2025-04-03 14:00:44', NULL);
INSERT INTO "public"."sys_index" VALUES (1150, 'ymy.index', '茹古涵今', 349, '116.288317', '40.002984', NULL, '2025-04-03 14:00:44', NULL);
INSERT INTO "public"."sys_index" VALUES (1151, 'ymy.index', '南大桥', 350, '116.289601', '40.002792', NULL, '2025-04-03 14:05:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1152, 'ymy.index', '南大桥', 351, '116.289601', '40.002792', NULL, '2025-04-03 14:05:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1153, 'ymy.index', '南大桥', 352, '116.289601', '40.002792', NULL, '2025-04-03 14:05:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1154, 'ymy.index', '南大桥', 353, '116.289601', '40.002792', NULL, '2025-04-03 14:05:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1155, 'ymy.index', '南大桥', 354, '116.289601', '40.002792', NULL, '2025-04-03 14:05:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1156, 'ymy.index', '前湖', 355, '116.290483', '40.002850', NULL, '2025-04-03 14:08:37', NULL);
INSERT INTO "public"."sys_index" VALUES (1157, 'ymy.index', '前湖', 356, '116.290483', '40.002850', NULL, '2025-04-03 14:08:37', NULL);
INSERT INTO "public"."sys_index" VALUES (1158, 'ymy.index', '前湖', 357, '116.290483', '40.002850', NULL, '2025-04-03 14:08:37', NULL);
INSERT INTO "public"."sys_index" VALUES (1159, 'ymy.index', '前湖', 358, '116.290483', '40.002850', NULL, '2025-04-03 14:08:37', NULL);
INSERT INTO "public"."sys_index" VALUES (1160, 'ymy.index', '前湖', 359, '116.290483', '40.002850', NULL, '2025-04-03 14:08:37', NULL);
INSERT INTO "public"."sys_index" VALUES (1161, 'ymy.index', '前湖', 360, '116.290483', '40.002850', NULL, '2025-04-03 14:08:37', NULL);
INSERT INTO "public"."sys_index" VALUES (1162, 'ymy.index', '前湖', 361, '116.290483', '40.002850', NULL, '2025-04-03 14:08:37', NULL);
INSERT INTO "public"."sys_index" VALUES (1163, 'ymy.index', '前湖', 362, '116.290483', '40.002850', NULL, '2025-04-03 14:08:37', NULL);
INSERT INTO "public"."sys_index" VALUES (1164, 'ymy.index', '前湖', 363, '116.290483', '40.002850', NULL, '2025-04-03 14:08:37', NULL);
INSERT INTO "public"."sys_index" VALUES (1165, 'ymy.index', '前湖', 364, '116.290483', '40.002850', NULL, '2025-04-03 14:08:37', NULL);
INSERT INTO "public"."sys_index" VALUES (1166, 'ymy.index', '前湖', 365, '116.290483', '40.002850', NULL, '2025-04-03 14:08:37', NULL);
INSERT INTO "public"."sys_index" VALUES (1167, 'ymy.index', '前湖', 366, '116.290483', '40.002850', NULL, '2025-04-03 14:08:37', NULL);
INSERT INTO "public"."sys_index" VALUES (1168, 'ymy.index', '九州清晏', 367, '116.290477', '40.002856', NULL, '2025-04-03 14:10:16', NULL);
INSERT INTO "public"."sys_index" VALUES (1169, 'ymy.index', '九州清晏', 368, '116.290477', '40.002856', NULL, '2025-04-03 14:10:16', NULL);
INSERT INTO "public"."sys_index" VALUES (1170, 'ymy.index', '九州清晏', 369, '116.290477', '40.002856', NULL, '2025-04-03 14:10:16', NULL);
INSERT INTO "public"."sys_index" VALUES (1171, 'ymy.index', '九州清晏', 370, '116.290477', '40.002856', NULL, '2025-04-03 14:10:16', NULL);
INSERT INTO "public"."sys_index" VALUES (1172, 'ymy.index', '九州清晏', 371, '116.290477', '40.002856', NULL, '2025-04-03 14:10:16', NULL);
INSERT INTO "public"."sys_index" VALUES (1173, 'ymy.index', '九州清晏', 372, '116.290477', '40.002856', NULL, '2025-04-03 14:10:16', NULL);
INSERT INTO "public"."sys_index" VALUES (1174, 'ymy.index', '九州清晏', 373, '116.290477', '40.002856', NULL, '2025-04-03 14:10:16', NULL);
INSERT INTO "public"."sys_index" VALUES (1175, 'ymy.index', '三一八烈士公墓', 374, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1176, 'ymy.index', '三一八烈士公墓', 375, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1177, 'ymy.index', '三一八烈士公墓', 376, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1178, 'ymy.index', '三一八烈士公墓', 377, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1179, 'ymy.index', '三一八烈士公墓', 378, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1180, 'ymy.index', '三一八烈士公墓', 379, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1181, 'ymy.index', '三一八烈士公墓', 380, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1182, 'ymy.index', '三一八烈士公墓', 381, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1183, 'ymy.index', '三一八烈士公墓', 382, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1184, 'ymy.index', '三一八烈士公墓', 383, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1185, 'ymy.index', '三一八烈士公墓', 384, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1186, 'ymy.index', '三一八烈士公墓', 385, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1187, 'ymy.index', '三一八烈士公墓', 386, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1188, 'ymy.index', '三一八烈士公墓', 387, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1189, 'ymy.index', '三一八烈士公墓', 388, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1190, 'ymy.index', '三一八烈士公墓', 389, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1191, 'ymy.index', '三一八烈士公墓', 390, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1192, 'ymy.index', '三一八烈士公墓', 391, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1193, 'ymy.index', '三一八烈士公墓', 392, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1194, 'ymy.index', '三一八烈士公墓', 393, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1195, 'ymy.index', '三一八烈士公墓', 394, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1196, 'ymy.index', '三一八烈士公墓', 395, '116.290747', '40.003096', NULL, '2025-04-03 14:16:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1197, 'ymy.index', '如意桥', 396, '116.291392', '40.002915', NULL, '2025-04-03 14:19:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1198, 'ymy.index', '如意桥', 397, '116.291392', '40.002915', NULL, '2025-04-03 14:19:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1199, 'ymy.index', '如意桥', 398, '116.291392', '40.002915', NULL, '2025-04-03 14:19:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1200, 'ymy.index', '如意桥', 399, '116.291392', '40.002915', NULL, '2025-04-03 14:19:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1201, 'ymy.index', '如意桥', 400, '116.291392', '40.002915', NULL, '2025-04-03 14:19:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1202, 'ymy.index', '如意桥', 401, '116.291392', '40.002915', NULL, '2025-04-03 14:19:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1203, 'ymy.index', '如意桥', 402, '116.291392', '40.002915', NULL, '2025-04-03 14:19:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1204, 'ymy.index', '如意桥', 403, '116.291392', '40.002915', NULL, '2025-04-03 14:19:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1205, 'ymy.index', '如意桥', 404, '116.291392', '40.002915', NULL, '2025-04-03 14:19:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1206, 'ymy.index', '如意桥', 405, '116.291392', '40.002915', NULL, '2025-04-03 14:19:57', NULL);
INSERT INTO "public"."sys_index" VALUES (1207, 'ymy.index', '镂月开云', 406, '116.292232', '40.003451', NULL, '2025-04-03 14:25:56', NULL);
INSERT INTO "public"."sys_index" VALUES (1208, 'ymy.index', '镂月开云', 407, '116.292232', '40.003451', NULL, '2025-04-03 14:25:56', NULL);
INSERT INTO "public"."sys_index" VALUES (1209, 'ymy.index', '镂月开云', 408, '116.292232', '40.003451', NULL, '2025-04-03 14:25:56', NULL);
INSERT INTO "public"."sys_index" VALUES (1210, 'ymy.index', '镂月开云', 409, '116.292232', '40.003451', NULL, '2025-04-03 14:25:56', NULL);
INSERT INTO "public"."sys_index" VALUES (1211, 'ymy.index', '镂月开云', 410, '116.292232', '40.003451', NULL, '2025-04-03 14:25:56', NULL);
INSERT INTO "public"."sys_index" VALUES (1212, 'ymy.index', '镂月开云', 411, '116.292232', '40.003451', NULL, '2025-04-03 14:25:56', NULL);
INSERT INTO "public"."sys_index" VALUES (1213, 'ymy.index', '镂月开云', 412, '116.292232', '40.003451', NULL, '2025-04-03 14:25:56', NULL);
INSERT INTO "public"."sys_index" VALUES (1214, 'ymy.index', '镂月开云', 413, '116.292232', '40.003451', NULL, '2025-04-03 14:25:56', NULL);
INSERT INTO "public"."sys_index" VALUES (1215, 'ymy.index', '镂月开云', 414, '116.292232', '40.003451', NULL, '2025-04-03 14:25:56', NULL);
INSERT INTO "public"."sys_index" VALUES (1216, 'ymy.index', '镂月开云', 415, '116.292232', '40.003451', NULL, '2025-04-03 14:25:56', NULL);
INSERT INTO "public"."sys_index" VALUES (1217, 'ymy.index', '镂月开云', 416, '116.292232', '40.003451', NULL, '2025-04-03 14:25:56', NULL);
INSERT INTO "public"."sys_index" VALUES (1218, 'ymy.index', '镂月开云', 417, '116.292232', '40.003451', NULL, '2025-04-03 14:25:56', NULL);
INSERT INTO "public"."sys_index" VALUES (1219, 'ymy.index', '镂月开云', 418, '116.292232', '40.003451', NULL, '2025-04-03 14:25:56', NULL);
INSERT INTO "public"."sys_index" VALUES (1220, 'ymy.index', '镂月开云', 419, '116.292232', '40.003451', NULL, '2025-04-03 14:25:56', NULL);
INSERT INTO "public"."sys_index" VALUES (1221, 'ymy.index', '镂月开云', 420, '116.292232', '40.003451', NULL, '2025-04-03 14:25:56', NULL);
INSERT INTO "public"."sys_index" VALUES (1222, 'ymy.index', '镂月开云', 421, '116.292232', '40.003451', NULL, '2025-04-03 14:25:56', NULL);
INSERT INTO "public"."sys_index" VALUES (1223, 'ymy.index', '镂月开云', 422, '116.292232', '40.003451', NULL, '2025-04-03 14:25:56', NULL);
INSERT INTO "public"."sys_index" VALUES (1224, 'ymy.index', '后湖', 423, '116.291579', '40.004142', NULL, '2025-04-03 14:26:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1225, 'ymy.index', '后湖', 424, '116.291579', '40.004142', NULL, '2025-04-03 14:26:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1226, 'ymy.index', '后湖', 425, '116.291579', '40.004142', NULL, '2025-04-03 14:26:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1227, 'ymy.index', '后湖', 426, '116.291579', '40.004142', NULL, '2025-04-03 14:26:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1228, 'ymy.index', '天然图画', 427, '116.291629', '40.004277', NULL, '2025-04-03 14:29:15', NULL);
INSERT INTO "public"."sys_index" VALUES (1229, 'ymy.index', '天然图画', 428, '116.291629', '40.004277', NULL, '2025-04-03 14:29:15', NULL);
INSERT INTO "public"."sys_index" VALUES (1230, 'ymy.index', '天然图画', 429, '116.291629', '40.004277', NULL, '2025-04-03 14:29:15', NULL);
INSERT INTO "public"."sys_index" VALUES (1231, 'ymy.index', '天然图画', 430, '116.291629', '40.004277', NULL, '2025-04-03 14:29:15', NULL);
INSERT INTO "public"."sys_index" VALUES (1232, 'ymy.index', '天然图画', 431, '116.291629', '40.004277', NULL, '2025-04-03 14:29:15', NULL);
INSERT INTO "public"."sys_index" VALUES (1233, 'ymy.index', '天然图画', 432, '116.291629', '40.004277', NULL, '2025-04-03 14:29:15', NULL);
INSERT INTO "public"."sys_index" VALUES (1234, 'ymy.index', '天然图画', 433, '116.291629', '40.004277', NULL, '2025-04-03 14:29:15', NULL);
INSERT INTO "public"."sys_index" VALUES (1235, 'ymy.index', '天然图画', 434, '116.291629', '40.004277', NULL, '2025-04-03 14:29:15', NULL);
INSERT INTO "public"."sys_index" VALUES (1236, 'ymy.index', '天然图画', 435, '116.291629', '40.004277', NULL, '2025-04-03 14:29:15', NULL);
INSERT INTO "public"."sys_index" VALUES (1237, 'ymy.index', '天然图画', 436, '116.291629', '40.004277', NULL, '2025-04-03 14:29:15', NULL);
INSERT INTO "public"."sys_index" VALUES (1238, 'ymy.index', '天然图画', 437, '116.291628', '40.005000', NULL, '2025-04-03 14:30:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1239, 'ymy.index', '天然图画', 438, '116.291628', '40.005000', NULL, '2025-04-03 14:30:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1240, 'ymy.index', '天然图画', 439, '116.291628', '40.005000', NULL, '2025-04-03 14:30:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1241, 'ymy.index', '天然图画', 440, '116.291628', '40.005000', NULL, '2025-04-03 14:30:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1242, 'ymy.index', '天然图画', 441, '116.291628', '40.005000', NULL, '2025-04-03 14:30:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1243, 'ymy.index', '碧桐书院', 442, '116.291296', '40.005908', NULL, '2025-04-03 14:38:55', NULL);
INSERT INTO "public"."sys_index" VALUES (1244, 'ymy.index', '碧桐书院', 443, '116.291296', '40.005908', NULL, '2025-04-03 14:38:55', NULL);
INSERT INTO "public"."sys_index" VALUES (1245, 'ymy.index', '碧桐书院', 444, '116.291296', '40.005908', NULL, '2025-04-03 14:38:55', NULL);
INSERT INTO "public"."sys_index" VALUES (1246, 'ymy.index', '碧桐书院', 445, '116.291296', '40.005908', NULL, '2025-04-03 14:38:55', NULL);
INSERT INTO "public"."sys_index" VALUES (1247, 'ymy.index', '碧桐书院', 446, '116.291296', '40.005908', NULL, '2025-04-03 14:38:55', NULL);
INSERT INTO "public"."sys_index" VALUES (1248, 'ymy.index', '碧桐书院', 447, '116.291296', '40.005908', NULL, '2025-04-03 14:38:55', NULL);
INSERT INTO "public"."sys_index" VALUES (1249, 'ymy.index', '碧桐书院', 448, '116.291296', '40.005908', NULL, '2025-04-03 14:38:55', NULL);
INSERT INTO "public"."sys_index" VALUES (1250, 'ymy.index', '碧桐书院', 449, '116.291296', '40.005908', NULL, '2025-04-03 14:38:55', NULL);
INSERT INTO "public"."sys_index" VALUES (1251, 'ymy.index', '碧桐书院', 450, '116.291296', '40.005908', NULL, '2025-04-03 14:38:55', NULL);
INSERT INTO "public"."sys_index" VALUES (1252, 'ymy.index', '碧桐书院', 451, '116.291296', '40.005908', NULL, '2025-04-03 14:38:55', NULL);
INSERT INTO "public"."sys_index" VALUES (1253, 'ymy.index', '碧桐书院', 452, '116.291296', '40.005908', NULL, '2025-04-03 14:38:55', NULL);
INSERT INTO "public"."sys_index" VALUES (1254, 'ymy.index', '碧桐书院', 453, '116.291296', '40.005908', NULL, '2025-04-03 14:38:55', NULL);
INSERT INTO "public"."sys_index" VALUES (1255, 'ymy.index', '碧桐书院', 454, '116.291296', '40.005908', NULL, '2025-04-03 14:38:55', NULL);
INSERT INTO "public"."sys_index" VALUES (1256, 'ymy.index', '碧桐书院', 455, '116.291296', '40.005908', NULL, '2025-04-03 14:38:55', NULL);
INSERT INTO "public"."sys_index" VALUES (1257, 'ymy.index', '碧桐书院', 456, '116.291296', '40.005908', NULL, '2025-04-03 14:38:55', NULL);
INSERT INTO "public"."sys_index" VALUES (1258, 'ymy.index', '碧桐书院', 457, '116.291296', '40.005908', NULL, '2025-04-03 14:38:55', NULL);
INSERT INTO "public"."sys_index" VALUES (1259, 'ymy.index', '碧桐书院', 458, '116.291296', '40.005908', NULL, '2025-04-03 14:38:55', NULL);
INSERT INTO "public"."sys_index" VALUES (1260, 'ymy.index', '碧桐书院', 459, '116.291296', '40.005908', NULL, '2025-04-03 14:38:55', NULL);
INSERT INTO "public"."sys_index" VALUES (1261, 'ymy.index', '慈云普护', 460, '116.290366', '40.005579', NULL, '2025-04-03 14:41:18', NULL);
INSERT INTO "public"."sys_index" VALUES (1262, 'ymy.index', '慈云普护', 461, '116.290366', '40.005579', NULL, '2025-04-03 14:41:18', NULL);
INSERT INTO "public"."sys_index" VALUES (1263, 'ymy.index', '慈云普护', 462, '116.290366', '40.005579', NULL, '2025-04-03 14:41:18', NULL);
INSERT INTO "public"."sys_index" VALUES (1264, 'ymy.index', '慈云普护', 463, '116.290366', '40.005579', NULL, '2025-04-03 14:41:18', NULL);
INSERT INTO "public"."sys_index" VALUES (1265, 'ymy.index', '一孔桥', 464, '116.290823', '40.005584', NULL, '2025-04-03 14:44:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1266, 'ymy.index', '一孔桥', 465, '116.290823', '40.005584', NULL, '2025-04-03 14:44:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1267, 'ymy.index', '一孔桥', 466, '116.290823', '40.005584', NULL, '2025-04-03 14:44:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1268, 'ymy.index', '一孔桥', 467, '116.290823', '40.005584', NULL, '2025-04-03 14:44:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1269, 'ymy.index', '一孔桥', 468, '116.290823', '40.005584', NULL, '2025-04-03 14:44:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1270, 'ymy.index', '一孔桥', 469, '116.290823', '40.005584', NULL, '2025-04-03 14:44:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1271, 'ymy.index', '一孔桥', 470, '116.290823', '40.005584', NULL, '2025-04-03 14:44:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1272, 'ymy.index', '一孔桥', 471, '116.290823', '40.005584', NULL, '2025-04-03 14:44:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1273, 'ymy.index', '一孔桥', 472, '116.290823', '40.005584', NULL, '2025-04-03 14:44:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1274, 'ymy.index', '一孔桥', 473, '116.290823', '40.005584', NULL, '2025-04-03 14:44:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1275, 'ymy.index', '慈云普护', 474, '116.290421', '40.005836', NULL, '2025-04-03 14:45:05', NULL);
INSERT INTO "public"."sys_index" VALUES (1276, 'ymy.index', '慈云普护', 475, '116.290421', '40.005836', NULL, '2025-04-03 14:45:05', NULL);
INSERT INTO "public"."sys_index" VALUES (1277, 'ymy.index', '慈云普护', 476, '116.290421', '40.005836', NULL, '2025-04-03 14:45:05', NULL);
INSERT INTO "public"."sys_index" VALUES (1278, 'ymy.index', '上下天光', 477, '116.289680', '40.005538', NULL, '2025-04-03 14:47:23', NULL);
INSERT INTO "public"."sys_index" VALUES (1279, 'ymy.index', '上下天光', 478, '116.289680', '40.005538', NULL, '2025-04-03 14:47:23', NULL);
INSERT INTO "public"."sys_index" VALUES (1280, 'ymy.index', '上下天光', 479, '116.289680', '40.005538', NULL, '2025-04-03 14:47:23', NULL);
INSERT INTO "public"."sys_index" VALUES (1281, 'ymy.index', '上下天光', 480, '116.289680', '40.005538', NULL, '2025-04-03 14:47:23', NULL);
INSERT INTO "public"."sys_index" VALUES (1282, 'ymy.index', '上下天光', 481, '116.289680', '40.005538', NULL, '2025-04-03 14:47:23', NULL);
INSERT INTO "public"."sys_index" VALUES (1283, 'ymy.index', '上下天光', 482, '116.289680', '40.005538', NULL, '2025-04-03 14:47:23', NULL);
INSERT INTO "public"."sys_index" VALUES (1284, 'ymy.index', '上下天光', 483, '116.289363', '40.005417', NULL, '2025-04-03 14:48:24', NULL);
INSERT INTO "public"."sys_index" VALUES (1285, 'ymy.index', '上下天光', 484, '116.289363', '40.005417', NULL, '2025-04-03 14:48:24', NULL);
INSERT INTO "public"."sys_index" VALUES (1286, 'ymy.index', '上下天光', 485, '116.289363', '40.005417', NULL, '2025-04-03 14:48:24', NULL);
INSERT INTO "public"."sys_index" VALUES (1287, 'ymy.index', '上下天光', 486, '116.289363', '40.005417', NULL, '2025-04-03 14:48:24', NULL);
INSERT INTO "public"."sys_index" VALUES (1288, 'ymy.index', '杏花春馆', 487, '116.287880', '40.004638', NULL, '2025-04-03 14:56:08', NULL);
INSERT INTO "public"."sys_index" VALUES (1289, 'ymy.index', '杏花春馆', 488, '116.287880', '40.004638', NULL, '2025-04-03 14:56:08', NULL);
INSERT INTO "public"."sys_index" VALUES (1290, 'ymy.index', '杏花春馆', 489, '116.287880', '40.004638', NULL, '2025-04-03 14:56:08', NULL);
INSERT INTO "public"."sys_index" VALUES (1291, 'ymy.index', '坦坦荡荡', 490, '116.288362', '40.004174', NULL, '2025-04-03 15:00:13', NULL);
INSERT INTO "public"."sys_index" VALUES (1292, 'ymy.index', '坦坦荡荡', 491, '116.288362', '40.004174', NULL, '2025-04-03 15:00:13', NULL);
INSERT INTO "public"."sys_index" VALUES (1293, 'ymy.index', '碧澜桥', 490, '116.288353', '40.004210', NULL, '2025-04-03 15:00:43', NULL);
INSERT INTO "public"."sys_index" VALUES (1294, 'ymy.index', '碧澜桥', 491, '116.288353', '40.004210', NULL, '2025-04-03 15:00:43', NULL);
INSERT INTO "public"."sys_index" VALUES (1295, 'ymy.index', '碧澜桥', 492, '116.288353', '40.004210', NULL, '2025-04-03 15:00:43', NULL);
INSERT INTO "public"."sys_index" VALUES (1296, 'ymy.index', '碧澜桥', 493, '116.288353', '40.004210', NULL, '2025-04-03 15:00:43', NULL);
INSERT INTO "public"."sys_index" VALUES (1297, 'ymy.index', '碧澜桥', 494, '116.288353', '40.004210', NULL, '2025-04-03 15:00:43', NULL);
INSERT INTO "public"."sys_index" VALUES (1298, 'ymy.index', '碧澜桥', 495, '116.288353', '40.004210', NULL, '2025-04-03 15:00:43', NULL);
INSERT INTO "public"."sys_index" VALUES (1299, 'ymy.index', '碧澜桥', 496, '116.288353', '40.004210', NULL, '2025-04-03 15:00:43', NULL);
INSERT INTO "public"."sys_index" VALUES (1300, 'ymy.index', '碧澜桥', 497, '116.288353', '40.004210', NULL, '2025-04-03 15:00:43', NULL);
INSERT INTO "public"."sys_index" VALUES (1301, 'ymy.index', '碧澜桥', 498, '116.288353', '40.004210', NULL, '2025-04-03 15:00:43', NULL);
INSERT INTO "public"."sys_index" VALUES (1302, 'ymy.index', '碧澜桥', 499, '116.288353', '40.004210', NULL, '2025-04-03 15:00:43', NULL);
INSERT INTO "public"."sys_index" VALUES (1303, 'ymy.index', '碧澜桥', 500, '116.288353', '40.004210', NULL, '2025-04-03 15:00:43', NULL);
INSERT INTO "public"."sys_index" VALUES (1304, 'ymy.index', '茹古涵今', 501, '116.289015', '40.003817', NULL, '2025-04-03 15:01:55', NULL);
INSERT INTO "public"."sys_index" VALUES (1305, 'ymy.index', '坦坦荡荡', 502, '116.288836', '40.004029', NULL, '2025-04-03 15:06:11', NULL);
INSERT INTO "public"."sys_index" VALUES (1306, 'ymy.index', '棕亭桥', 502, '116.289233', '40.003682', NULL, '2025-04-03 15:06:31', NULL);
INSERT INTO "public"."sys_index" VALUES (1307, 'ymy.index', '棕亭桥', 503, '116.289233', '40.003682', NULL, '2025-04-03 15:06:31', NULL);
INSERT INTO "public"."sys_index" VALUES (1308, 'ymy.index', '棕亭桥', 504, '116.289233', '40.003682', NULL, '2025-04-03 15:06:31', NULL);
INSERT INTO "public"."sys_index" VALUES (1309, 'ymy.index', '棕亭桥', 505, '116.289233', '40.003682', NULL, '2025-04-03 15:06:31', NULL);
INSERT INTO "public"."sys_index" VALUES (1310, 'ymy.index', '棕亭桥', 506, '116.289233', '40.003682', NULL, '2025-04-03 15:06:31', NULL);
INSERT INTO "public"."sys_index" VALUES (1311, 'ymy.index', '棕亭桥', 507, '116.289233', '40.003682', NULL, '2025-04-03 15:06:31', NULL);
INSERT INTO "public"."sys_index" VALUES (1312, 'ymy.index', '棕亭桥', 508, '116.289233', '40.003682', NULL, '2025-04-03 15:06:31', NULL);
INSERT INTO "public"."sys_index" VALUES (1313, 'ymy.index', '棕亭桥', 509, '116.289233', '40.003682', NULL, '2025-04-03 15:06:31', NULL);
INSERT INTO "public"."sys_index" VALUES (1314, 'ymy.index', '棕亭桥', 510, '116.289233', '40.003682', NULL, '2025-04-03 15:06:31', NULL);
INSERT INTO "public"."sys_index" VALUES (1315, 'ymy.index', '棕亭桥', 511, '116.289233', '40.003682', NULL, '2025-04-03 15:06:31', NULL);
INSERT INTO "public"."sys_index" VALUES (1316, 'ymy.index', '棕亭桥', 512, '116.289233', '40.003682', NULL, '2025-04-03 15:06:31', NULL);
INSERT INTO "public"."sys_index" VALUES (1317, 'ymy.index', '棕亭桥', 513, '116.289233', '40.003682', NULL, '2025-04-03 15:06:31', NULL);
INSERT INTO "public"."sys_index" VALUES (1318, 'ymy.index', '棕亭桥', 514, '116.289233', '40.003682', NULL, '2025-04-03 15:06:31', NULL);
INSERT INTO "public"."sys_index" VALUES (1319, 'ymy.index', '坦坦荡荡', 515, '116.288905', '40.003977', NULL, '2025-04-03 15:08:23', NULL);
INSERT INTO "public"."sys_index" VALUES (1320, 'ymy.index', '坦坦荡荡', 516, '116.288905', '40.003977', NULL, '2025-04-03 15:08:23', NULL);
INSERT INTO "public"."sys_index" VALUES (1321, 'ymy.index', '坦坦荡荡', 517, '116.288905', '40.003977', NULL, '2025-04-03 15:08:23', NULL);
INSERT INTO "public"."sys_index" VALUES (1322, 'ymy.index', '坦坦荡荡', 518, '116.288905', '40.003977', NULL, '2025-04-03 15:08:23', NULL);
INSERT INTO "public"."sys_index" VALUES (1323, 'ymy.index', '坦坦荡荡', 519, '116.288905', '40.003977', NULL, '2025-04-03 15:08:23', NULL);
INSERT INTO "public"."sys_index" VALUES (1324, 'ymy.index', '坦坦荡荡', 520, '116.288905', '40.003977', NULL, '2025-04-03 15:08:23', NULL);
INSERT INTO "public"."sys_index" VALUES (1325, 'ymy.index', '坦坦荡荡', 521, '116.288905', '40.003977', NULL, '2025-04-03 15:08:23', NULL);
INSERT INTO "public"."sys_index" VALUES (1326, 'ymy.index', '万方安和', 522, '116.287132', '40.005330', NULL, '2025-04-03 15:19:13', NULL);
INSERT INTO "public"."sys_index" VALUES (1327, 'ymy.index', '万方安和', 523, '116.287132', '40.005330', NULL, '2025-04-03 15:19:13', NULL);
INSERT INTO "public"."sys_index" VALUES (1328, 'ymy.index', '万方安和', 524, '116.287132', '40.005330', NULL, '2025-04-03 15:19:13', NULL);
INSERT INTO "public"."sys_index" VALUES (1329, 'ymy.index', '万方安和', 525, '116.287132', '40.005330', NULL, '2025-04-03 15:19:13', NULL);
INSERT INTO "public"."sys_index" VALUES (1330, 'ymy.index', '万方安和', 526, '116.287132', '40.005330', NULL, '2025-04-03 15:19:13', NULL);
INSERT INTO "public"."sys_index" VALUES (1331, 'ymy.index', '万方安和', 527, '116.287132', '40.005330', NULL, '2025-04-03 15:19:13', NULL);
INSERT INTO "public"."sys_index" VALUES (1332, 'ymy.index', '万方安和', 528, '116.287132', '40.005330', NULL, '2025-04-03 15:19:13', NULL);
INSERT INTO "public"."sys_index" VALUES (1333, 'ymy.index', '万方安和', 529, '116.287132', '40.005330', NULL, '2025-04-03 15:19:13', NULL);
INSERT INTO "public"."sys_index" VALUES (1334, 'ymy.index', '万方安和', 530, '116.287132', '40.005330', NULL, '2025-04-03 15:19:13', NULL);
INSERT INTO "public"."sys_index" VALUES (1335, 'ymy.index', '万方安和', 531, '116.287132', '40.005330', NULL, '2025-04-03 15:19:13', NULL);
INSERT INTO "public"."sys_index" VALUES (1336, 'ymy.index', '万方安和', 532, '116.287132', '40.005330', NULL, '2025-04-03 15:19:13', NULL);
INSERT INTO "public"."sys_index" VALUES (1337, 'ymy.index', '万方安和', 533, '116.287132', '40.005330', NULL, '2025-04-03 15:19:13', NULL);
INSERT INTO "public"."sys_index" VALUES (1338, 'ymy.index', '万方安和', 534, '116.287132', '40.005330', NULL, '2025-04-03 15:19:13', NULL);
INSERT INTO "public"."sys_index" VALUES (1339, 'ymy.index', '万方安和', 535, '116.287132', '40.005330', NULL, '2025-04-03 15:19:13', NULL);
INSERT INTO "public"."sys_index" VALUES (1340, 'ymy.index', '万方安和', 536, '116.287132', '40.005330', NULL, '2025-04-03 15:19:13', NULL);
INSERT INTO "public"."sys_index" VALUES (1341, 'ymy.index', '万方安和', 537, '116.287132', '40.005330', NULL, '2025-04-03 15:19:13', NULL);
INSERT INTO "public"."sys_index" VALUES (1342, 'ymy.index', '万方安和', 538, '116.287132', '40.005330', NULL, '2025-04-03 15:19:13', NULL);
INSERT INTO "public"."sys_index" VALUES (1343, 'ymy.index', '万方安和', 539, '116.286135', '40.005033', NULL, '2025-04-03 15:33:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1344, 'ymy.index', '万方安和', 540, '116.286085', '40.005782', NULL, '2025-04-03 15:36:29', NULL);
INSERT INTO "public"."sys_index" VALUES (1345, 'ymy.index', '万方安和', 541, '116.286085', '40.005782', NULL, '2025-04-03 15:36:29', NULL);
INSERT INTO "public"."sys_index" VALUES (1346, 'ymy.index', '万方安和', 542, '116.286085', '40.005782', NULL, '2025-04-03 15:36:29', NULL);
INSERT INTO "public"."sys_index" VALUES (1347, 'ymy.index', '万方安和', 543, '116.286085', '40.005782', NULL, '2025-04-03 15:36:29', NULL);
INSERT INTO "public"."sys_index" VALUES (1348, 'ymy.index', '万方安和', 544, '116.286085', '40.005782', NULL, '2025-04-03 15:36:29', NULL);
INSERT INTO "public"."sys_index" VALUES (1349, 'ymy.index', '万方安和', 545, '116.286085', '40.005782', NULL, '2025-04-03 15:36:29', NULL);
INSERT INTO "public"."sys_index" VALUES (1350, 'ymy.index', '万方安和', 546, '116.286085', '40.005782', NULL, '2025-04-03 15:36:29', NULL);
INSERT INTO "public"."sys_index" VALUES (1351, 'ymy.index', '万方安和', 547, '116.286085', '40.005782', NULL, '2025-04-03 15:36:29', NULL);
INSERT INTO "public"."sys_index" VALUES (1352, 'ymy.index', '万方安和', 548, '116.286085', '40.005782', NULL, '2025-04-03 15:36:29', NULL);
INSERT INTO "public"."sys_index" VALUES (1353, 'ymy.index', '万方安和', 549, '116.286085', '40.005782', NULL, '2025-04-03 15:36:29', NULL);
INSERT INTO "public"."sys_index" VALUES (1354, 'ymy.index', '月地云居', 550, '116.284392', '40.006028', NULL, '2025-04-03 15:43:51', NULL);
INSERT INTO "public"."sys_index" VALUES (1355, 'ymy.index', '月地云居', 551, '116.284392', '40.006028', NULL, '2025-04-03 15:43:51', NULL);
INSERT INTO "public"."sys_index" VALUES (1356, 'ymy.index', '月地云居', 552, '116.284392', '40.006028', NULL, '2025-04-03 15:43:51', NULL);
INSERT INTO "public"."sys_index" VALUES (1357, 'ymy.index', '月地云居', 553, '116.284392', '40.006028', NULL, '2025-04-03 15:43:51', NULL);
INSERT INTO "public"."sys_index" VALUES (1358, 'ymy.index', '月地云居', 554, '116.284392', '40.006028', NULL, '2025-04-03 15:43:51', NULL);
INSERT INTO "public"."sys_index" VALUES (1359, 'ymy.index', '月地云居', 555, '116.284392', '40.006028', NULL, '2025-04-03 15:43:51', NULL);
INSERT INTO "public"."sys_index" VALUES (1360, 'ymy.index', '月地云居', 556, '116.284392', '40.006028', NULL, '2025-04-03 15:43:51', NULL);
INSERT INTO "public"."sys_index" VALUES (1361, 'ymy.index', '武陵春色', 557, '116.286803', '40.006700', NULL, '2025-04-03 15:49:27', NULL);
INSERT INTO "public"."sys_index" VALUES (1362, 'ymy.index', '武陵春色', 558, '116.286803', '40.006700', NULL, '2025-04-03 15:49:27', NULL);
INSERT INTO "public"."sys_index" VALUES (1363, 'ymy.index', '武陵春色', 559, '116.286803', '40.006700', NULL, '2025-04-03 15:49:27', NULL);
INSERT INTO "public"."sys_index" VALUES (1364, 'ymy.index', '武陵春色', 560, '116.286803', '40.006700', NULL, '2025-04-03 15:49:27', NULL);
INSERT INTO "public"."sys_index" VALUES (1365, 'ymy.index', '武陵春色', 561, '116.286803', '40.006700', NULL, '2025-04-03 15:49:27', NULL);
INSERT INTO "public"."sys_index" VALUES (1366, 'ymy.index', '武陵春色', 562, '116.286803', '40.006700', NULL, '2025-04-03 15:49:27', NULL);
INSERT INTO "public"."sys_index" VALUES (1367, 'ymy.index', '武陵春色', 563, '116.286803', '40.006700', NULL, '2025-04-03 15:49:27', NULL);
INSERT INTO "public"."sys_index" VALUES (1368, 'ymy.index', '武陵春色', 564, '116.286803', '40.006700', NULL, '2025-04-03 15:49:27', NULL);
INSERT INTO "public"."sys_index" VALUES (1369, 'ymy.index', '武陵春色', 565, '116.286803', '40.006700', NULL, '2025-04-03 15:49:27', NULL);
INSERT INTO "public"."sys_index" VALUES (1370, 'ymy.index', '武陵春色', 566, '116.286803', '40.006700', NULL, '2025-04-03 15:49:27', NULL);
INSERT INTO "public"."sys_index" VALUES (1371, 'ymy.index', '濂溪乐处', 567, '116.287645', '40.008560', NULL, '2025-04-03 15:57:24', NULL);
INSERT INTO "public"."sys_index" VALUES (1372, 'ymy.index', '濂溪乐处', 568, '116.287645', '40.008560', NULL, '2025-04-03 15:57:24', NULL);
INSERT INTO "public"."sys_index" VALUES (1373, 'ymy.index', '濂溪乐处', 569, '116.287645', '40.008560', NULL, '2025-04-03 15:57:24', NULL);
INSERT INTO "public"."sys_index" VALUES (1374, 'ymy.index', '濂溪乐处', 570, '116.287645', '40.008560', NULL, '2025-04-03 15:57:24', NULL);
INSERT INTO "public"."sys_index" VALUES (1375, 'ymy.index', '濂溪乐处', 571, '116.287645', '40.008560', NULL, '2025-04-03 15:57:24', NULL);
INSERT INTO "public"."sys_index" VALUES (1376, 'ymy.index', '濂溪乐处', 572, '116.287645', '40.008560', NULL, '2025-04-03 15:57:24', NULL);
INSERT INTO "public"."sys_index" VALUES (1377, 'ymy.index', '濂溪乐处', 573, '116.287645', '40.008560', NULL, '2025-04-03 15:57:24', NULL);
INSERT INTO "public"."sys_index" VALUES (1378, 'ymy.index', '濂溪乐处', 574, '116.287645', '40.008560', NULL, '2025-04-03 15:57:24', NULL);
INSERT INTO "public"."sys_index" VALUES (1379, 'ymy.index', '濂溪乐处', 575, '116.287645', '40.008560', NULL, '2025-04-03 15:57:24', NULL);
INSERT INTO "public"."sys_index" VALUES (1380, 'ymy.index', '濂溪乐处', 576, '116.287645', '40.008560', NULL, '2025-04-03 15:57:24', NULL);
INSERT INTO "public"."sys_index" VALUES (1381, 'ymy.index', '濂溪乐处', 577, '116.287645', '40.008560', NULL, '2025-04-03 15:57:24', NULL);
INSERT INTO "public"."sys_index" VALUES (1382, 'ymy.index', '濂溪乐处', 578, '116.287645', '40.008560', NULL, '2025-04-03 15:57:24', NULL);
INSERT INTO "public"."sys_index" VALUES (1383, 'ymy.index', '濂溪乐处', 579, '116.287645', '40.008560', NULL, '2025-04-03 15:57:24', NULL);
INSERT INTO "public"."sys_index" VALUES (1384, 'ymy.index', '濂溪乐处', 580, '116.287645', '40.008560', NULL, '2025-04-03 15:57:24', NULL);
INSERT INTO "public"."sys_index" VALUES (1385, 'ymy.index', '濂溪乐处', 581, '116.286813', '40.009817', NULL, '2025-04-03 16:01:09', NULL);
INSERT INTO "public"."sys_index" VALUES (1386, 'ymy.index', '濂溪乐处', 582, '116.286813', '40.009817', NULL, '2025-04-03 16:01:09', NULL);
INSERT INTO "public"."sys_index" VALUES (1387, 'ymy.index', '濂溪乐处', 583, '116.286813', '40.009817', NULL, '2025-04-03 16:01:09', NULL);
INSERT INTO "public"."sys_index" VALUES (1388, 'ymy.index', '濂溪乐处', 584, '116.286813', '40.009817', NULL, '2025-04-03 16:01:09', NULL);
INSERT INTO "public"."sys_index" VALUES (1389, 'ymy.index', '濂溪乐处', 585, '116.286813', '40.009817', NULL, '2025-04-03 16:01:09', NULL);
INSERT INTO "public"."sys_index" VALUES (1390, 'ymy.index', '濂溪乐处', 586, '116.286813', '40.009817', NULL, '2025-04-03 16:01:09', NULL);
INSERT INTO "public"."sys_index" VALUES (1391, 'ymy.index', '濂溪乐处', 587, '116.286813', '40.009817', NULL, '2025-04-03 16:01:09', NULL);
INSERT INTO "public"."sys_index" VALUES (1392, 'ymy.index', '濂溪乐处', 588, '116.286813', '40.009817', NULL, '2025-04-03 16:01:09', NULL);
INSERT INTO "public"."sys_index" VALUES (1393, 'ymy.index', '濂溪乐处', 589, '116.287845', '40.008347', NULL, '2025-04-03 16:05:14', NULL);
INSERT INTO "public"."sys_index" VALUES (1394, 'ymy.index', '濂溪乐处', 590, '116.287845', '40.008347', NULL, '2025-04-03 16:05:14', NULL);
INSERT INTO "public"."sys_index" VALUES (1395, 'ymy.index', '濂溪乐处', 591, '116.287845', '40.008347', NULL, '2025-04-03 16:05:14', NULL);
INSERT INTO "public"."sys_index" VALUES (1396, 'ymy.index', '濂溪乐处', 592, '116.287845', '40.008347', NULL, '2025-04-03 16:05:14', NULL);
INSERT INTO "public"."sys_index" VALUES (1397, 'ymy.index', '詹泊宁静', 593, '116.289743', '40.007685', NULL, '2025-04-03 16:09:59', NULL);
INSERT INTO "public"."sys_index" VALUES (1398, 'ymy.index', '映水阑香', 594, '116.289737', '40.007617', NULL, '2025-04-03 16:10:53', NULL);
INSERT INTO "public"."sys_index" VALUES (1399, 'ymy.index', '詹泊宁静', 595, '116.291383', '40.007710', NULL, '2025-04-03 16:13:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1400, 'ymy.index', '詹泊宁静', 596, '116.291383', '40.007710', NULL, '2025-04-03 16:13:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1401, 'ymy.index', '詹泊宁静', 597, '116.291383', '40.007710', NULL, '2025-04-03 16:13:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1402, 'ymy.index', '詹泊宁静', 598, '116.291383', '40.007710', NULL, '2025-04-03 16:13:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1403, 'ymy.index', '詹泊宁静', 599, '116.291383', '40.007710', NULL, '2025-04-03 16:13:49', NULL);
INSERT INTO "public"."sys_index" VALUES (1404, 'ymy.index', '坐石临流', 600, '116.293025', '40.007438', NULL, '2025-04-03 16:19:37', NULL);
INSERT INTO "public"."sys_index" VALUES (1405, 'ymy.index', '坐石临流', 601, '116.293025', '40.007438', NULL, '2025-04-03 16:19:37', NULL);
INSERT INTO "public"."sys_index" VALUES (1406, 'ymy.index', '坐石临流', 602, '116.293025', '40.007438', NULL, '2025-04-03 16:19:37', NULL);
INSERT INTO "public"."sys_index" VALUES (1407, 'ymy.index', '坐石临流', 603, '116.293025', '40.007438', NULL, '2025-04-03 16:19:37', NULL);
INSERT INTO "public"."sys_index" VALUES (1408, 'ymy.index', '澡身浴德', 604, '116.294915', '40.006998', NULL, '2025-04-03 16:22:33', NULL);
INSERT INTO "public"."sys_index" VALUES (1409, 'ymy.index', '澡身浴德', 605, '116.294915', '40.006998', NULL, '2025-04-03 16:22:33', NULL);
INSERT INTO "public"."sys_index" VALUES (1410, 'ymy.index', '澡身浴德', 606, '116.294915', '40.006998', NULL, '2025-04-03 16:22:33', NULL);
INSERT INTO "public"."sys_index" VALUES (1411, 'ymy.index', '廓然大公', 607, '116.295208', '40.010305', NULL, '2025-04-03 16:29:58', NULL);
INSERT INTO "public"."sys_index" VALUES (1412, 'ymy.index', '廓然大公', 608, '116.295208', '40.010305', NULL, '2025-04-03 16:29:58', NULL);
INSERT INTO "public"."sys_index" VALUES (1413, 'ymy.index', '平湖秋月', 609, '116.296102', '40.010552', NULL, '2025-04-03 16:33:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1414, 'ymy.index', '平湖秋月', 610, '116.296102', '40.010552', NULL, '2025-04-03 16:33:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1415, 'ymy.index', '平湖秋月', 611, '116.296102', '40.010552', NULL, '2025-04-03 16:33:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1416, 'ymy.index', '平湖秋月', 612, '116.296102', '40.010552', NULL, '2025-04-03 16:33:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1417, 'ymy.index', '平湖秋月', 613, '116.296102', '40.010552', NULL, '2025-04-03 16:33:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1418, 'ymy.index', '平湖秋月', 614, '116.296102', '40.010552', NULL, '2025-04-03 16:33:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1419, 'ymy.index', '平湖秋月', 615, '116.296102', '40.010552', NULL, '2025-04-03 16:33:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1420, 'ymy.index', '平湖秋月', 616, '116.296102', '40.010552', NULL, '2025-04-03 16:33:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1421, 'ymy.index', '平湖秋月', 617, '116.296102', '40.010552', NULL, '2025-04-03 16:33:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1422, 'ymy.index', '平湖秋月', 618, '116.296102', '40.010552', NULL, '2025-04-03 16:33:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1423, 'ymy.index', '藏密楼', 619, '116.299457', '40.010802', NULL, '2025-04-03 16:38:20', NULL);
INSERT INTO "public"."sys_index" VALUES (1424, 'ymy.index', '藏密楼', 620, '116.299457', '40.010802', NULL, '2025-04-03 16:38:20', NULL);
INSERT INTO "public"."sys_index" VALUES (1425, 'ymy.index', '藏密楼', 621, '116.299457', '40.010802', NULL, '2025-04-03 16:38:20', NULL);
INSERT INTO "public"."sys_index" VALUES (1426, 'ymy.index', '藏密楼', 622, '116.299457', '40.010802', NULL, '2025-04-03 16:38:20', NULL);
INSERT INTO "public"."sys_index" VALUES (1427, 'ymy.index', '藏密楼', 623, '116.299457', '40.010802', NULL, '2025-04-03 16:38:20', NULL);
INSERT INTO "public"."sys_index" VALUES (1428, 'ymy.index', '藏密楼', 624, '116.299457', '40.010802', NULL, '2025-04-03 16:38:20', NULL);
INSERT INTO "public"."sys_index" VALUES (1429, 'ymy.index', '藏密楼', 625, '116.299457', '40.010802', NULL, '2025-04-03 16:38:20', NULL);
INSERT INTO "public"."sys_index" VALUES (1430, 'ymy.index', '藏密楼', 626, '116.299457', '40.010802', NULL, '2025-04-03 16:38:20', NULL);
INSERT INTO "public"."sys_index" VALUES (1431, 'ymy.index', '藏密楼', 627, '116.299457', '40.010802', NULL, '2025-04-03 16:38:20', NULL);
INSERT INTO "public"."sys_index" VALUES (1432, 'ymy.index', '福海景区', 628, '116.299803', '40.010623', NULL, '2025-04-03 16:40:32', NULL);
INSERT INTO "public"."sys_index" VALUES (1433, 'ymy.index', '福海景区', 629, '116.299803', '40.010623', NULL, '2025-04-03 16:40:32', NULL);
INSERT INTO "public"."sys_index" VALUES (1434, 'ymy.index', '福海景区', 630, '116.299803', '40.010623', NULL, '2025-04-03 16:40:32', NULL);
INSERT INTO "public"."sys_index" VALUES (1435, 'ymy.index', '福海景区', 631, '116.299803', '40.010623', NULL, '2025-04-03 16:40:32', NULL);
INSERT INTO "public"."sys_index" VALUES (1436, 'ymy.index', '福海景区', 632, '116.299803', '40.010623', NULL, '2025-04-03 16:40:32', NULL);
INSERT INTO "public"."sys_index" VALUES (1437, 'ymy.index', '福海景区', 633, '116.299803', '40.010623', NULL, '2025-04-03 16:40:32', NULL);
INSERT INTO "public"."sys_index" VALUES (1438, 'ymy.index', '福海景区', 634, '116.299803', '40.010623', NULL, '2025-04-03 16:40:32', NULL);
INSERT INTO "public"."sys_index" VALUES (1439, 'ymy.index', '福海景区', 635, '116.299803', '40.010623', NULL, '2025-04-03 16:40:32', NULL);
INSERT INTO "public"."sys_index" VALUES (1440, 'ymy.index', '福海景区', 636, '116.299803', '40.010623', NULL, '2025-04-03 16:40:32', NULL);
INSERT INTO "public"."sys_index" VALUES (1441, 'ymy.index', '拾光买卖街', 637, '116.301953', '40.009818', NULL, '2025-04-03 16:46:50', NULL);
INSERT INTO "public"."sys_index" VALUES (1442, 'ymy.index', '拾光买卖街', 638, '116.301953', '40.009818', NULL, '2025-04-03 16:46:50', NULL);
INSERT INTO "public"."sys_index" VALUES (1443, 'ymy.index', '拾光买卖街', 639, '116.301953', '40.009818', NULL, '2025-04-03 16:46:50', NULL);
INSERT INTO "public"."sys_index" VALUES (1444, 'ymy.index', '拾光买卖街', 640, '116.301953', '40.009818', NULL, '2025-04-03 16:46:50', NULL);
INSERT INTO "public"."sys_index" VALUES (1445, 'ymy.index', '拾光买卖街', 641, '116.301953', '40.009818', NULL, '2025-04-03 16:46:50', NULL);
INSERT INTO "public"."sys_index" VALUES (1446, 'ymy.index', '拾光买卖街', 642, '116.301953', '40.009818', NULL, '2025-04-03 16:46:50', NULL);
INSERT INTO "public"."sys_index" VALUES (1447, 'ymy.index', '谐奇趣北喷水池', 643, '116.302426', '40.011565', NULL, '2025-04-03 17:16:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1448, 'ymy.index', '谐奇趣北喷水池', 644, '116.302426', '40.011565', NULL, '2025-04-03 17:16:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1449, 'ymy.index', '谐奇趣北喷水池', 645, '116.302426', '40.011565', NULL, '2025-04-03 17:16:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1450, 'ymy.index', '谐奇趣北喷水池', 646, '116.302426', '40.011565', NULL, '2025-04-03 17:16:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1451, 'ymy.index', '谐奇趣北喷水池', 647, '116.302426', '40.011565', NULL, '2025-04-03 17:16:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1452, 'ymy.index', '谐奇趣北喷水池', 648, '116.302426', '40.011565', NULL, '2025-04-03 17:16:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1453, 'ymy.index', '谐奇趣北喷水池', 649, '116.302426', '40.011565', NULL, '2025-04-03 17:16:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1454, 'ymy.index', '谐奇趣北喷水池', 650, '116.302426', '40.011565', NULL, '2025-04-03 17:16:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1455, 'ymy.index', '谐奇趣北喷水池', 651, '116.302426', '40.011565', NULL, '2025-04-03 17:16:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1456, 'ymy.index', '谐奇趣北喷水池', 652, '116.302426', '40.011565', NULL, '2025-04-03 17:16:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1457, 'ymy.index', '谐奇趣北喷水池', 653, '116.302426', '40.011565', NULL, '2025-04-03 17:16:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1458, 'ymy.index', '谐奇趣北喷水池', 654, '116.302426', '40.011565', NULL, '2025-04-03 17:16:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1459, 'ymy.index', '谐奇趣北喷水池', 655, '116.302426', '40.011565', NULL, '2025-04-03 17:16:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1460, 'ymy.index', '谐奇趣北喷水池', 656, '116.302426', '40.011565', NULL, '2025-04-03 17:16:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1461, 'ymy.index', '黄花阵', 657, '116.302343', '40.011925', NULL, '2025-04-03 17:28:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1462, 'ymy.index', '黄花阵', 658, '116.302343', '40.011925', NULL, '2025-04-03 17:28:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1463, 'ymy.index', '黄花阵', 659, '116.302343', '40.011925', NULL, '2025-04-03 17:28:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1464, 'ymy.index', '黄花阵', 660, '116.302343', '40.011925', NULL, '2025-04-03 17:28:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1465, 'ymy.index', '黄花阵', 661, '116.302343', '40.011925', NULL, '2025-04-03 17:28:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1466, 'ymy.index', '黄花阵', 662, '116.302343', '40.011925', NULL, '2025-04-03 17:28:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1467, 'ymy.index', '黄花阵', 663, '116.302343', '40.011925', NULL, '2025-04-03 17:28:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1468, 'ymy.index', '黄花阵', 664, '116.302343', '40.011925', NULL, '2025-04-03 17:28:40', NULL);
INSERT INTO "public"."sys_index" VALUES (1469, 'ymy.index', '黄花阵', 665, '116.302560', '40.011851', NULL, '2025-04-03 17:30:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1470, 'ymy.index', '黄花阵', 666, '116.302560', '40.011851', NULL, '2025-04-03 17:30:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1471, 'ymy.index', '黄花阵', 667, '116.302560', '40.011851', NULL, '2025-04-03 17:30:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1472, 'ymy.index', '黄花阵', 668, '116.302560', '40.011851', NULL, '2025-04-03 17:30:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1473, 'ymy.index', '黄花阵', 669, '116.302560', '40.011851', NULL, '2025-04-03 17:30:10', NULL);
INSERT INTO "public"."sys_index" VALUES (1474, 'ymy.index', '黄花阵', 670, '116.302841', '40.012011', NULL, '2025-04-03 17:30:43', NULL);
INSERT INTO "public"."sys_index" VALUES (1475, 'ymy.index', '方外观', 671, '116.303755', '40.012035', NULL, '2025-04-03 17:38:33', NULL);
INSERT INTO "public"."sys_index" VALUES (1476, 'ymy.index', '方外观', 672, '116.303755', '40.012035', NULL, '2025-04-03 17:38:33', NULL);
INSERT INTO "public"."sys_index" VALUES (1477, 'ymy.index', '方外观', 673, '116.303755', '40.012035', NULL, '2025-04-03 17:38:33', NULL);
INSERT INTO "public"."sys_index" VALUES (1478, 'ymy.index', '方外观', 674, '116.303755', '40.012035', NULL, '2025-04-03 17:38:33', NULL);
INSERT INTO "public"."sys_index" VALUES (1479, 'ymy.index', '方外观', 675, '116.303755', '40.012035', NULL, '2025-04-03 17:38:33', NULL);
INSERT INTO "public"."sys_index" VALUES (1480, 'ymy.index', '方外观', 676, '116.303755', '40.012035', NULL, '2025-04-03 17:38:33', NULL);
INSERT INTO "public"."sys_index" VALUES (1481, 'ymy.index', '方外观', 677, '116.303755', '40.012035', NULL, '2025-04-03 17:38:33', NULL);
INSERT INTO "public"."sys_index" VALUES (1482, 'ymy.index', '方外观', 678, '116.303755', '40.012035', NULL, '2025-04-03 17:38:33', NULL);
INSERT INTO "public"."sys_index" VALUES (1483, 'ymy.index', '方外观', 679, '116.303755', '40.012035', NULL, '2025-04-03 17:38:33', NULL);
INSERT INTO "public"."sys_index" VALUES (1484, 'ymy.index', '方外观', 680, '116.303755', '40.012035', NULL, '2025-04-03 17:38:33', NULL);
INSERT INTO "public"."sys_index" VALUES (1485, 'ymy.index', '方外观', 681, '116.303755', '40.012035', NULL, '2025-04-03 17:38:33', NULL);
INSERT INTO "public"."sys_index" VALUES (1486, 'ymy.index', '方外观', 682, '116.303755', '40.012035', NULL, '2025-04-03 17:38:33', NULL);
INSERT INTO "public"."sys_index" VALUES (1487, 'ymy.index', '方外观', 683, '116.303755', '40.012035', NULL, '2025-04-03 17:38:33', NULL);
INSERT INTO "public"."sys_index" VALUES (1488, 'ymy.index', '方外观', 684, '116.303755', '40.012035', NULL, '2025-04-03 17:38:33', NULL);
INSERT INTO "public"."sys_index" VALUES (1489, 'ymy.index', '海晏堂', 685, '116.304387', '40.011853', NULL, '2025-04-03 17:40:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1490, 'ymy.index', '海晏堂', 686, '116.304387', '40.011853', NULL, '2025-04-03 17:40:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1491, 'ymy.index', '海晏堂', 687, '116.304387', '40.011853', NULL, '2025-04-03 17:40:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1492, 'ymy.index', '海晏堂', 688, '116.304387', '40.011853', NULL, '2025-04-03 17:40:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1493, 'ymy.index', '海晏堂', 689, '116.304387', '40.011853', NULL, '2025-04-03 17:40:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1494, 'ymy.index', '海晏堂', 690, '116.305498', '40.011612', NULL, '2025-04-03 17:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1495, 'ymy.index', '海晏堂', 691, '116.305498', '40.011612', NULL, '2025-04-03 17:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1496, 'ymy.index', '海晏堂', 692, '116.305498', '40.011612', NULL, '2025-04-03 17:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1497, 'ymy.index', '海晏堂', 693, '116.305498', '40.011612', NULL, '2025-04-03 17:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1498, 'ymy.index', '海晏堂', 694, '116.305498', '40.011612', NULL, '2025-04-03 17:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1499, 'ymy.index', '海晏堂', 695, '116.305498', '40.011612', NULL, '2025-04-03 17:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1500, 'ymy.index', '海晏堂', 696, '116.305498', '40.011612', NULL, '2025-04-03 17:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1501, 'ymy.index', '海晏堂', 697, '116.305498', '40.011612', NULL, '2025-04-03 17:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1502, 'ymy.index', '海晏堂', 698, '116.305498', '40.011612', NULL, '2025-04-03 17:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1503, 'ymy.index', '海晏堂', 699, '116.305498', '40.011612', NULL, '2025-04-03 17:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1504, 'ymy.index', '海晏堂', 700, '116.305498', '40.011612', NULL, '2025-04-03 17:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1505, 'ymy.index', '海晏堂', 701, '116.305498', '40.011612', NULL, '2025-04-03 17:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1506, 'ymy.index', '海晏堂', 702, '116.305498', '40.011612', NULL, '2025-04-03 17:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1507, 'ymy.index', '海晏堂', 703, '116.305498', '40.011612', NULL, '2025-04-03 17:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1508, 'ymy.index', '海晏堂', 704, '116.305498', '40.011612', NULL, '2025-04-03 17:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1509, 'ymy.index', '海晏堂', 705, '116.305498', '40.011612', NULL, '2025-04-03 17:46:36', NULL);
INSERT INTO "public"."sys_index" VALUES (1510, 'ymy.index', '海晏堂蓄水池台基', 706, '116.305968', '40.012022', NULL, '2025-04-03 17:50:11', NULL);
INSERT INTO "public"."sys_index" VALUES (1511, 'ymy.index', '海晏堂蓄水池台基', 707, '116.305968', '40.012022', NULL, '2025-04-03 17:50:11', NULL);
INSERT INTO "public"."sys_index" VALUES (1512, 'ymy.index', '海晏堂蓄水池台基', 708, '116.305968', '40.012022', NULL, '2025-04-03 17:50:11', NULL);
INSERT INTO "public"."sys_index" VALUES (1513, 'ymy.index', '海晏堂蓄水池台基', 709, '116.305968', '40.012022', NULL, '2025-04-03 17:50:11', NULL);
INSERT INTO "public"."sys_index" VALUES (1514, 'ymy.index', '海晏堂蓄水池台基', 710, '116.305968', '40.012022', NULL, '2025-04-03 17:50:11', NULL);
INSERT INTO "public"."sys_index" VALUES (1515, 'ymy.index', '圆明园展览馆', 711, '116.306008', '40.012443', NULL, '2025-04-03 17:52:19', NULL);
INSERT INTO "public"."sys_index" VALUES (1516, 'ymy.index', '圆明园展览馆', 712, '116.306008', '40.012443', NULL, '2025-04-03 17:52:19', NULL);
INSERT INTO "public"."sys_index" VALUES (1517, 'ymy.index', '圆明园展览馆', 713, '116.306008', '40.012443', NULL, '2025-04-03 17:52:19', NULL);
INSERT INTO "public"."sys_index" VALUES (1518, 'ymy.index', '圆明园展览馆', 714, '116.306008', '40.012443', NULL, '2025-04-03 17:52:20', NULL);
INSERT INTO "public"."sys_index" VALUES (1519, 'ymy.index', '圆明园展览馆', 715, '116.306008', '40.012443', NULL, '2025-04-03 17:52:20', NULL);
INSERT INTO "public"."sys_index" VALUES (1520, 'ymy.index', '圆明园展览馆', 716, '116.306008', '40.012443', NULL, '2025-04-03 17:52:20', NULL);
INSERT INTO "public"."sys_index" VALUES (1521, 'ymy.index', '大水法', 717, '116.306917', '40.012390', NULL, '2025-04-03 17:57:12', NULL);
INSERT INTO "public"."sys_index" VALUES (1522, 'ymy.index', '大水法', 718, '116.306917', '40.012390', NULL, '2025-04-03 17:57:12', NULL);
INSERT INTO "public"."sys_index" VALUES (1523, 'ymy.index', '大水法', 719, '116.306917', '40.012390', NULL, '2025-04-03 17:57:12', NULL);
INSERT INTO "public"."sys_index" VALUES (1524, 'ymy.index', '大水法', 720, '116.306917', '40.012390', NULL, '2025-04-03 17:57:12', NULL);
INSERT INTO "public"."sys_index" VALUES (1525, 'ymy.index', '大水法', 721, '116.306917', '40.012390', NULL, '2025-04-03 17:57:12', NULL);
INSERT INTO "public"."sys_index" VALUES (1526, 'ymy.index', '大水法', 722, '116.306917', '40.012390', NULL, '2025-04-03 17:57:12', NULL);
INSERT INTO "public"."sys_index" VALUES (1527, 'ymy.index', '大水法', 723, '116.306917', '40.012390', NULL, '2025-04-03 17:57:12', NULL);
INSERT INTO "public"."sys_index" VALUES (1528, 'ymy.index', '大水法', 724, '116.306917', '40.012390', NULL, '2025-04-03 17:57:12', NULL);
INSERT INTO "public"."sys_index" VALUES (1529, 'ymy.index', '大水法', 725, '116.306917', '40.012390', NULL, '2025-04-03 17:57:12', NULL);
INSERT INTO "public"."sys_index" VALUES (1530, 'ymy.index', '大水法', 726, '116.306917', '40.012390', NULL, '2025-04-03 17:57:12', NULL);
INSERT INTO "public"."sys_index" VALUES (1531, 'ymy.index', '大水法', 727, '116.306917', '40.012390', NULL, '2025-04-03 17:57:12', NULL);
INSERT INTO "public"."sys_index" VALUES (1532, 'ymy.index', '大水法', 728, '116.306917', '40.012390', NULL, '2025-04-03 17:57:12', NULL);
INSERT INTO "public"."sys_index" VALUES (1533, 'ymy.index', '大水法', 729, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1534, 'ymy.index', '大水法', 730, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1535, 'ymy.index', '大水法', 731, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1536, 'ymy.index', '大水法', 732, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1537, 'ymy.index', '大水法', 733, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1538, 'ymy.index', '大水法', 734, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1539, 'ymy.index', '大水法', 735, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1540, 'ymy.index', '大水法', 736, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1541, 'ymy.index', '大水法', 737, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1542, 'ymy.index', '大水法', 738, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1543, 'ymy.index', '大水法', 739, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1544, 'ymy.index', '大水法', 740, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1545, 'ymy.index', '大水法', 741, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1546, 'ymy.index', '大水法', 742, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1547, 'ymy.index', '大水法', 743, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1548, 'ymy.index', '大水法', 744, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1549, 'ymy.index', '大水法', 745, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1550, 'ymy.index', '大水法', 746, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1551, 'ymy.index', '大水法', 747, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1552, 'ymy.index', '大水法', 748, '116.306665', '40.011933', NULL, '2025-04-03 18:03:34', NULL);
INSERT INTO "public"."sys_index" VALUES (1553, 'ymy.index', '线法山', 749, '116.309025', '40.011503', NULL, '2025-04-03 18:13:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1554, 'ymy.index', '线法山', 750, '116.309025', '40.011503', NULL, '2025-04-03 18:13:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1555, 'ymy.index', '线法山', 751, '116.309025', '40.011503', NULL, '2025-04-03 18:13:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1556, 'ymy.index', '线法山', 752, '116.309025', '40.011503', NULL, '2025-04-03 18:13:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1557, 'ymy.index', '线法山', 753, '116.309025', '40.011503', NULL, '2025-04-03 18:13:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1558, 'ymy.index', '线法山', 754, '116.309025', '40.011503', NULL, '2025-04-03 18:13:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1559, 'ymy.index', '线法山', 755, '116.309025', '40.011503', NULL, '2025-04-03 18:13:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1560, 'ymy.index', '线法山', 756, '116.309025', '40.011503', NULL, '2025-04-03 18:13:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1561, 'ymy.index', '线法山', 757, '116.309025', '40.011503', NULL, '2025-04-03 18:13:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1562, 'ymy.index', '线法山', 758, '116.309025', '40.011503', NULL, '2025-04-03 18:13:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1563, 'ymy.index', '线法山', 759, '116.309025', '40.011503', NULL, '2025-04-03 18:13:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1564, 'ymy.index', '线法山', 760, '116.309025', '40.011503', NULL, '2025-04-03 18:13:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1565, 'ymy.index', '线法山', 761, '116.309025', '40.011503', NULL, '2025-04-03 18:13:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1566, 'ymy.index', '线法山', 762, '116.309025', '40.011503', NULL, '2025-04-03 18:13:30', NULL);
INSERT INTO "public"."sys_index" VALUES (1567, 'ymy.index', '玉玲珑馆', 763, '116.308952', '40.007195', NULL, '2025-04-03 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (1568, 'ymy.index', '玉玲珑馆', 764, '116.308952', '40.007195', NULL, '2025-04-03 18:31:06', NULL);
INSERT INTO "public"."sys_index" VALUES (1569, 'ymy.index', '詹怀堂', 765, '116.308329', '40.005940', NULL, '2025-04-03 18:35:28', NULL);
INSERT INTO "public"."sys_index" VALUES (1570, 'ymy.index', '詹怀堂', 766, '116.308329', '40.005940', NULL, '2025-04-03 18:35:28', NULL);


-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_job";
CREATE TABLE "public"."sys_job" (
                                    "job_id" bigserial PRIMARY KEY ,
                                    "job_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                    "job_group" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                    "invoke_target" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                    "cron_expression" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                    "misfire_policy" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" DEFAULT '3'::character varying,
                                    "concurrent" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default" DEFAULT '1'::bpchar,
                                    "status" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
                                    "create_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                    "create_time" "pg_catalog"."timestamp",
                                    "update_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                    "update_time" "pg_catalog"."timestamp",
                                    "remark" "pg_catalog"."varchar" COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."sys_job"."job_id" IS '任务ID';
COMMENT ON COLUMN "public"."sys_job"."job_name" IS '任务名称';
COMMENT ON COLUMN "public"."sys_job"."job_group" IS '任务组名';
COMMENT ON COLUMN "public"."sys_job"."invoke_target" IS '调用目标字符串';
COMMENT ON COLUMN "public"."sys_job"."cron_expression" IS 'cron执行表达式';
COMMENT ON COLUMN "public"."sys_job"."misfire_policy" IS '计划执行错误策略（1立即执行 2执行一次 3放弃执行）';
COMMENT ON COLUMN "public"."sys_job"."concurrent" IS '是否并发执行（0允许 1禁止）';
COMMENT ON COLUMN "public"."sys_job"."status" IS '状态（0正常 1暂停）';
COMMENT ON COLUMN "public"."sys_job"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_job"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_job"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_job"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."sys_job"."remark" IS '备注信息';
COMMENT ON TABLE "public"."sys_job" IS '定时任务调度表';
-- ----------------------------
-- Records of sys_job
-- ----------------------------
INSERT INTO "public"."sys_job" VALUES (1, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '0/10 * * * * ?', '3', '1', '1', 'admin', '2025-02-12 10:50:22', NULL, NULL, NULL);
INSERT INTO "public"."sys_job" VALUES (2, '系统默认（有参）', 'DEFAULT', 'ryTask.ryParams(''ry'')', '0/15 * * * * ?', '3', '1', '1', 'admin', '2025-02-12 10:50:22', NULL, NULL, NULL);
INSERT INTO "public"."sys_job" VALUES (3, '系统默认（多参）', 'DEFAULT', 'ryTask.ryMultipleParams(''ry'', true, 2000L, 316.50D, 100)', '0/20 * * * * ?', '3', '1', '1', 'admin', '2025-02-12 10:50:22', NULL, NULL, NULL);


-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_job_log";
CREATE TABLE "public"."sys_job_log" (
                                        "job_log_id"  bigserial PRIMARY KEY ,
                                        "job_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
                                        "job_group" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
                                        "invoke_target" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
                                        "job_message" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                        "status" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default",
                                        "exception_info" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                        "create_time" "pg_catalog"."timestamp"
)
;
COMMENT ON COLUMN "public"."sys_job_log"."job_log_id" IS '任务日志ID';
COMMENT ON COLUMN "public"."sys_job_log"."job_name" IS '任务名称';
COMMENT ON COLUMN "public"."sys_job_log"."job_group" IS '任务组名';
COMMENT ON COLUMN "public"."sys_job_log"."invoke_target" IS '调用目标字符串';
COMMENT ON COLUMN "public"."sys_job_log"."job_message" IS '日志信息';
COMMENT ON COLUMN "public"."sys_job_log"."status" IS '执行状态（0正常 1失败）';
COMMENT ON COLUMN "public"."sys_job_log"."exception_info" IS '异常信息';
COMMENT ON COLUMN "public"."sys_job_log"."create_time" IS '创建时间';
COMMENT ON TABLE "public"."sys_job_log" IS '定时任务调度日志表';



-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_logininfor";
CREATE TABLE "public"."sys_logininfor" (
                                           "info_id"  bigserial PRIMARY KEY ,
                                           "user_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                           "ipaddr" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                           "login_location" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                           "browser" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                           "os" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                           "status" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default",
                                           "msg" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                           "login_time" "pg_catalog"."timestamp"
)
;
COMMENT ON COLUMN "public"."sys_logininfor"."info_id" IS '访问ID';
COMMENT ON COLUMN "public"."sys_logininfor"."user_name" IS '用户账号';
COMMENT ON COLUMN "public"."sys_logininfor"."ipaddr" IS '登录IP地址';
COMMENT ON COLUMN "public"."sys_logininfor"."login_location" IS '登录地点';
COMMENT ON COLUMN "public"."sys_logininfor"."browser" IS '浏览器类型';
COMMENT ON COLUMN "public"."sys_logininfor"."os" IS '操作系统';
COMMENT ON COLUMN "public"."sys_logininfor"."status" IS '登录状态（0成功 1失败）';
COMMENT ON COLUMN "public"."sys_logininfor"."msg" IS '提示消息';
COMMENT ON COLUMN "public"."sys_logininfor"."login_time" IS '访问时间';
COMMENT ON TABLE "public"."sys_logininfor" IS '系统访问记录';

-- ----------------------------
-- Indexes structure for table sys_logininfor
-- ----------------------------
CREATE INDEX "idx_sys_logininfor_lt" ON "public"."sys_logininfor" USING btree (
    "login_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_sys_logininfor_s" ON "public"."sys_logininfor" USING btree (
    "status" COLLATE "pg_catalog"."default" "pg_catalog"."bpchar_ops" ASC NULLS LAST
    );




-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_menu";
CREATE TABLE "public"."sys_menu" (
                                     "menu_id"  bigserial PRIMARY KEY ,
                                     "menu_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
                                     "parent_id" "pg_catalog"."int8",
                                     "order_num" "pg_catalog"."int4",
                                     "path" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "component" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "query" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "route_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "is_frame" "pg_catalog"."int4",
                                     "is_cache" "pg_catalog"."int4",
                                     "menu_type" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default",
                                     "visible" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default",
                                     "status" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default",
                                     "perms" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "icon" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "create_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "create_time" "pg_catalog"."timestamp",
                                     "update_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "update_time" "pg_catalog"."timestamp",
                                     "remark" "pg_catalog"."varchar" COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."sys_menu"."menu_id" IS '菜单ID';
COMMENT ON COLUMN "public"."sys_menu"."menu_name" IS '菜单名称';
COMMENT ON COLUMN "public"."sys_menu"."parent_id" IS '父菜单ID';
COMMENT ON COLUMN "public"."sys_menu"."order_num" IS '显示顺序';
COMMENT ON COLUMN "public"."sys_menu"."path" IS '路由地址';
COMMENT ON COLUMN "public"."sys_menu"."component" IS '组件路径';
COMMENT ON COLUMN "public"."sys_menu"."query" IS '路由参数';
COMMENT ON COLUMN "public"."sys_menu"."route_name" IS '路由名称';
COMMENT ON COLUMN "public"."sys_menu"."is_frame" IS '是否为外链（0是 1否）';
COMMENT ON COLUMN "public"."sys_menu"."is_cache" IS '是否缓存（0缓存 1不缓存）';
COMMENT ON COLUMN "public"."sys_menu"."menu_type" IS '菜单类型（M目录 C菜单 F按钮）';
COMMENT ON COLUMN "public"."sys_menu"."visible" IS '菜单状态（0显示 1隐藏）';
COMMENT ON COLUMN "public"."sys_menu"."status" IS '菜单状态（0正常 1停用）';
COMMENT ON COLUMN "public"."sys_menu"."perms" IS '权限标识';
COMMENT ON COLUMN "public"."sys_menu"."icon" IS '菜单图标';
COMMENT ON COLUMN "public"."sys_menu"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_menu"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_menu"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_menu"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."sys_menu"."remark" IS '备注';
COMMENT ON TABLE "public"."sys_menu" IS '菜单权限表';

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO "public"."sys_menu" VALUES (1, '系统管理', 0, 1, 'system', NULL, '', '', 1, 0, 'M', '0', '0', '', 'system', 'admin', '2025-02-12 10:50:21', '', NULL, '系统管理目录');
INSERT INTO "public"."sys_menu" VALUES (2, '系统监控', 0, 2, 'monitor', NULL, '', '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', '2025-02-12 10:50:21', '', NULL, '系统监控目录');
INSERT INTO "public"."sys_menu" VALUES (3, '系统工具', 0, 3, 'tool', NULL, '', '', 1, 0, 'M', '0', '0', '', 'tool', 'admin', '2025-02-12 10:50:21', '', NULL, '系统工具目录');
INSERT INTO "public"."sys_menu" VALUES (4, '若依官网', 0, 4, 'http://ruoyi.vip', NULL, '', '', 0, 0, 'M', '0', '0', '', 'guide', 'admin', '2025-02-12 10:50:21', '', NULL, '若依官网地址');
INSERT INTO "public"."sys_menu" VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', '', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', '2025-02-12 10:50:21', '', NULL, '用户管理菜单');
INSERT INTO "public"."sys_menu" VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', '', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', '2025-02-12 10:50:21', '', NULL, '角色管理菜单');
INSERT INTO "public"."sys_menu" VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', '', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', '2025-02-12 10:50:21', '', NULL, '菜单管理菜单');
INSERT INTO "public"."sys_menu" VALUES (103, '部门管理', 1, 4, 'dept', 'system/dept/index', '', '', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', '2025-02-12 10:50:21', '', NULL, '部门管理菜单');
INSERT INTO "public"."sys_menu" VALUES (104, '岗位管理', 1, 5, 'post', 'system/post/index', '', '', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 'admin', '2025-02-12 10:50:21', '', NULL, '岗位管理菜单');
INSERT INTO "public"."sys_menu" VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', '', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'admin', '2025-02-12 10:50:21', '', NULL, '字典管理菜单');
INSERT INTO "public"."sys_menu" VALUES (106, '参数设置', 1, 7, 'config', 'system/config/index', '', '', 1, 0, 'C', '0', '0', 'system:config:list', 'edit', 'admin', '2025-02-12 10:50:21', '', NULL, '参数设置菜单');
INSERT INTO "public"."sys_menu" VALUES (107, '通知公告', 1, 8, 'notice', 'system/notice/index', '', '', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 'admin', '2025-02-12 10:50:21', '', NULL, '通知公告菜单');
INSERT INTO "public"."sys_menu" VALUES (108, '日志管理', 1, 9, 'log', '', '', '', 1, 0, 'M', '0', '0', '', 'log', 'admin', '2025-02-12 10:50:21', '', NULL, '日志管理菜单');
INSERT INTO "public"."sys_menu" VALUES (109, '在线用户', 2, 1, 'online', 'monitor/online/index', '', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', '2025-02-12 10:50:21', '', NULL, '在线用户菜单');
INSERT INTO "public"."sys_menu" VALUES (110, '定时任务', 2, 2, 'job', 'monitor/job/index', '', '', 1, 0, 'C', '0', '0', 'monitor:job:list', 'job', 'admin', '2025-02-12 10:50:21', '', NULL, '定时任务菜单');
INSERT INTO "public"."sys_menu" VALUES (111, '数据监控', 2, 3, 'druid', 'monitor/druid/index', '', '', 1, 0, 'C', '0', '0', 'monitor:druid:list', 'druid', 'admin', '2025-02-12 10:50:21', '', NULL, '数据监控菜单');
INSERT INTO "public"."sys_menu" VALUES (112, '服务监控', 2, 4, 'server', 'monitor/server/index', '', '', 1, 0, 'C', '0', '0', 'monitor:server:list', 'server', 'admin', '2025-02-12 10:50:21', '', NULL, '服务监控菜单');
INSERT INTO "public"."sys_menu" VALUES (113, '缓存监控', 2, 5, 'cache', 'monitor/cache/index', '', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis', 'admin', '2025-02-12 10:50:21', '', NULL, '缓存监控菜单');
INSERT INTO "public"."sys_menu" VALUES (114, '缓存列表', 2, 6, 'cacheList', 'monitor/cache/list', '', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis-list', 'admin', '2025-02-12 10:50:21', '', NULL, '缓存列表菜单');
INSERT INTO "public"."sys_menu" VALUES (115, '表单构建', 3, 1, 'build', 'tool/build/index', '', '', 1, 0, 'C', '0', '0', 'tool:build:list', 'build', 'admin', '2025-02-12 10:50:21', '', NULL, '表单构建菜单');
INSERT INTO "public"."sys_menu" VALUES (116, '代码生成', 3, 2, 'gen', 'tool/gen/index', '', '', 1, 0, 'C', '0', '0', 'tool:gen:list', 'code', 'admin', '2025-02-12 10:50:21', '', NULL, '代码生成菜单');
INSERT INTO "public"."sys_menu" VALUES (117, '系统接口', 3, 3, 'swagger', 'tool/swagger/index', '', '', 1, 0, 'C', '0', '0', 'tool:swagger:list', 'swagger', 'admin', '2025-02-12 10:50:21', '', NULL, '系统接口菜单');
INSERT INTO "public"."sys_menu" VALUES (500, '操作日志', 108, 1, 'operlog', 'monitor/operlog/index', '', '', 1, 0, 'C', '0', '0', 'monitor:operlog:list', 'form', 'admin', '2025-02-12 10:50:21', '', NULL, '操作日志菜单');
INSERT INTO "public"."sys_menu" VALUES (501, '登录日志', 108, 2, 'logininfor', 'monitor/logininfor/index', '', '', 1, 0, 'C', '0', '0', 'monitor:logininfor:list', 'logininfor', 'admin', '2025-02-12 10:50:21', '', NULL, '登录日志菜单');
INSERT INTO "public"."sys_menu" VALUES (1000, '用户查询', 100, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1001, '用户新增', 100, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1002, '用户修改', 100, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1003, '用户删除', 100, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1004, '用户导出', 100, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1005, '用户导入', 100, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1006, '重置密码', 100, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1007, '角色查询', 101, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1008, '角色新增', 101, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1009, '角色修改', 101, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1010, '角色删除', 101, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1011, '角色导出', 101, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1012, '菜单查询', 102, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1013, '菜单新增', 102, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1014, '菜单修改', 102, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1015, '菜单删除', 102, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1016, '部门查询', 103, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1017, '部门新增', 103, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1018, '部门修改', 103, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1019, '部门删除', 103, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1020, '岗位查询', 104, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:query', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1021, '岗位新增', 104, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:add', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1022, '岗位修改', 104, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:edit', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1023, '岗位删除', 104, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:remove', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1024, '岗位导出', 104, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:export', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1025, '字典查询', 105, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1026, '字典新增', 105, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1027, '字典修改', 105, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1028, '字典删除', 105, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1029, '字典导出', 105, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1030, '参数查询', 106, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:query', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1031, '参数新增', 106, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:add', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1032, '参数修改', 106, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:edit', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1033, '参数删除', 106, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:remove', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1034, '参数导出', 106, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:export', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1035, '公告查询', 107, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:query', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1036, '公告新增', 107, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:add', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1037, '公告修改', 107, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:edit', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1038, '公告删除', 107, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:remove', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1039, '操作查询', 500, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:query', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1040, '操作删除', 500, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:remove', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1041, '日志导出', 500, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:export', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1042, '登录查询', 501, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:query', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1043, '登录删除', 501, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:remove', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1044, '日志导出', 501, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:export', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1045, '账户解锁', 501, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:unlock', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1046, '在线查询', 109, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1047, '批量强退', 109, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1048, '单条强退', 109, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1049, '任务查询', 110, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:query', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1050, '任务新增', 110, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:add', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1051, '任务修改', 110, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:edit', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1052, '任务删除', 110, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:remove', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1053, '状态修改', 110, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:changeStatus', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1054, '任务导出', 110, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:export', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1055, '生成查询', 116, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:query', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1056, '生成修改', 116, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:edit', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1057, '生成删除', 116, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:remove', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1058, '导入代码', 116, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:import', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1059, '预览代码', 116, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:preview', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_menu" VALUES (1060, '生成代码', 116, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:code', '#', 'admin', '2025-02-12 10:50:21', '', NULL, '');

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_notice";
CREATE TABLE "public"."sys_notice" (
                                       "notice_id" serial  PRIMARY KEY,
                                       "notice_title" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
                                       "notice_type" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default" NOT NULL,
                                       "notice_content" "pg_catalog"."bytea",
                                       "status" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default",
                                       "create_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                       "create_time" "pg_catalog"."timestamp",
                                       "update_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                       "update_time" "pg_catalog"."timestamp",
                                       "remark" "pg_catalog"."varchar" COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."sys_notice"."notice_id" IS '公告ID';
COMMENT ON COLUMN "public"."sys_notice"."notice_title" IS '公告标题';
COMMENT ON COLUMN "public"."sys_notice"."notice_type" IS '公告类型（1通知 2公告）';
COMMENT ON COLUMN "public"."sys_notice"."notice_content" IS '公告内容';
COMMENT ON COLUMN "public"."sys_notice"."status" IS '公告状态（0正常 1关闭）';
COMMENT ON COLUMN "public"."sys_notice"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_notice"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_notice"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_notice"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."sys_notice"."remark" IS '备注';
COMMENT ON TABLE "public"."sys_notice" IS '通知公告表';

-- ----------------------------
-- Records of sys_notice
-- ----------------------------
INSERT INTO "public"."sys_notice" VALUES (1, '温馨提醒：2018-07-01 若依新版本发布啦', '2', E'\\346\\226\\260\\347\\211\\210\\346\\234\\254\\345\\206\\205\\345\\256\\271', '0', 'admin', '2025-02-12 10:50:23', '', NULL, '管理员');
INSERT INTO "public"."sys_notice" VALUES (2, '维护通知：2018-07-01 若依系统凌晨维护', '1', E'\\347\\273\\264\\346\\212\\244\\345\\206\\205\\345\\256\\271', '0', 'admin', '2025-02-12 10:50:23', '', NULL, '管理员');


-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_oper_log";
CREATE TABLE "public"."sys_oper_log" (
                                         "oper_id" bigserial PRIMARY KEY ,
                                         "title" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "business_type" "pg_catalog"."int4",
                                         "method" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "request_method" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "operator_type" "pg_catalog"."int4",
                                         "oper_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "dept_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "oper_url" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "oper_ip" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "oper_location" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "oper_param" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "json_result" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "status" "pg_catalog"."int4",
                                         "error_msg" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "oper_time" "pg_catalog"."timestamp",
                                         "cost_time" "pg_catalog"."int8"
)
;
COMMENT ON COLUMN "public"."sys_oper_log"."oper_id" IS '日志主键';
COMMENT ON COLUMN "public"."sys_oper_log"."title" IS '模块标题';
COMMENT ON COLUMN "public"."sys_oper_log"."business_type" IS '业务类型（0其它 1新增 2修改 3删除）';
COMMENT ON COLUMN "public"."sys_oper_log"."method" IS '方法名称';
COMMENT ON COLUMN "public"."sys_oper_log"."request_method" IS '请求方式';
COMMENT ON COLUMN "public"."sys_oper_log"."operator_type" IS '操作类别（0其它 1后台用户 2手机端用户）';
COMMENT ON COLUMN "public"."sys_oper_log"."oper_name" IS '操作人员';
COMMENT ON COLUMN "public"."sys_oper_log"."dept_name" IS '部门名称';
COMMENT ON COLUMN "public"."sys_oper_log"."oper_url" IS '请求URL';
COMMENT ON COLUMN "public"."sys_oper_log"."oper_ip" IS '主机地址';
COMMENT ON COLUMN "public"."sys_oper_log"."oper_location" IS '操作地点';
COMMENT ON COLUMN "public"."sys_oper_log"."oper_param" IS '请求参数';
COMMENT ON COLUMN "public"."sys_oper_log"."json_result" IS '返回参数';
COMMENT ON COLUMN "public"."sys_oper_log"."status" IS '操作状态（0正常 1异常）';
COMMENT ON COLUMN "public"."sys_oper_log"."error_msg" IS '错误消息';
COMMENT ON COLUMN "public"."sys_oper_log"."oper_time" IS '操作时间';
COMMENT ON COLUMN "public"."sys_oper_log"."cost_time" IS '消耗时间';
COMMENT ON TABLE "public"."sys_oper_log" IS '操作日志记录';

-- ----------------------------
-- Indexes structure for table sys_oper_log
-- ----------------------------
CREATE INDEX "idx_sys_oper_log_bt" ON "public"."sys_oper_log" USING btree (
    "business_type" "pg_catalog"."int4_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_sys_oper_log_ot" ON "public"."sys_oper_log" USING btree (
    "oper_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_sys_oper_log_s" ON "public"."sys_oper_log" USING btree (
    "status" "pg_catalog"."int4_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_post";
CREATE TABLE "public"."sys_post" (
                                     "post_id" bigserial PRIMARY KEY ,
                                     "post_code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
                                     "post_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
                                     "post_sort" "pg_catalog"."int4" NOT NULL,
                                     "status" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default" NOT NULL,
                                     "create_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "create_time" "pg_catalog"."timestamp",
                                     "update_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "update_time" "pg_catalog"."timestamp",
                                     "remark" "pg_catalog"."varchar" COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."sys_post"."post_id" IS '岗位ID';
COMMENT ON COLUMN "public"."sys_post"."post_code" IS '岗位编码';
COMMENT ON COLUMN "public"."sys_post"."post_name" IS '岗位名称';
COMMENT ON COLUMN "public"."sys_post"."post_sort" IS '显示顺序';
COMMENT ON COLUMN "public"."sys_post"."status" IS '状态（0正常 1停用）';
COMMENT ON COLUMN "public"."sys_post"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_post"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_post"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_post"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."sys_post"."remark" IS '备注';
COMMENT ON TABLE "public"."sys_post" IS '岗位信息表';

-- ----------------------------
-- Records of sys_post
-- ----------------------------
INSERT INTO "public"."sys_post" VALUES (1, 'ceo', '董事长', 1, '0', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_post" VALUES (2, 'se', '项目经理', 2, '0', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_post" VALUES (3, 'hr', '人力资源', 3, '0', 'admin', '2025-02-12 10:50:21', '', NULL, '');
INSERT INTO "public"."sys_post" VALUES (4, 'user', '普通员工', 4, '0', 'admin', '2025-02-12 10:50:21', '', NULL, '');



-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_role";
CREATE TABLE "public"."sys_role" (
                                     "role_id" bigserial PRIMARY KEY ,
                                     "role_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
                                     "role_key" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
                                     "role_sort" "pg_catalog"."int4" NOT NULL,
                                     "data_scope" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default",
                                     "menu_check_strictly" "pg_catalog"."int2",
                                     "dept_check_strictly" "pg_catalog"."int2",
                                     "status" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default" NOT NULL,
                                     "del_flag" "pg_catalog"."bpchar" COLLATE "pg_catalog"."default",
                                     "create_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "create_time" "pg_catalog"."timestamp",
                                     "update_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "update_time" "pg_catalog"."timestamp",
                                     "remark" "pg_catalog"."varchar" COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."sys_role"."role_id" IS '角色ID';
COMMENT ON COLUMN "public"."sys_role"."role_name" IS '角色名称';
COMMENT ON COLUMN "public"."sys_role"."role_key" IS '角色权限字符串';
COMMENT ON COLUMN "public"."sys_role"."role_sort" IS '显示顺序';
COMMENT ON COLUMN "public"."sys_role"."data_scope" IS '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）';
COMMENT ON COLUMN "public"."sys_role"."menu_check_strictly" IS '菜单树选择项是否关联显示';
COMMENT ON COLUMN "public"."sys_role"."dept_check_strictly" IS '部门树选择项是否关联显示';
COMMENT ON COLUMN "public"."sys_role"."status" IS '角色状态（0正常 1停用）';
COMMENT ON COLUMN "public"."sys_role"."del_flag" IS '删除标志（0代表存在 2代表删除）';
COMMENT ON COLUMN "public"."sys_role"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_role"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_role"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_role"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."sys_role"."remark" IS '备注';
COMMENT ON TABLE "public"."sys_role" IS '角色信息表';

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO "public"."sys_role" VALUES (1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', '2025-02-12 10:50:21', '', NULL, '超级管理员');
INSERT INTO "public"."sys_role" VALUES (2, '普通角色', 'common', 2, '2', 1, 1, '0', '0', 'admin', '2025-02-12 10:50:21', '', NULL, '普通角色');


-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_role_dept";
CREATE TABLE "public"."sys_role_dept" (
                                          "role_id" "pg_catalog"."int8" NOT NULL,
                                          "dept_id" "pg_catalog"."int8" NOT NULL
)
;
COMMENT ON COLUMN "public"."sys_role_dept"."role_id" IS '角色ID';
COMMENT ON COLUMN "public"."sys_role_dept"."dept_id" IS '部门ID';
COMMENT ON TABLE "public"."sys_role_dept" IS '角色和部门关联表';

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------
INSERT INTO "public"."sys_role_dept" VALUES (2, 100);
INSERT INTO "public"."sys_role_dept" VALUES (2, 101);
INSERT INTO "public"."sys_role_dept" VALUES (2, 105);



-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_role_menu";
CREATE TABLE "public"."sys_role_menu" (
                                          "role_id" "pg_catalog"."int8" NOT NULL,
                                          "menu_id" "pg_catalog"."int8" NOT NULL
)
;
COMMENT ON COLUMN "public"."sys_role_menu"."role_id" IS '角色ID';
COMMENT ON COLUMN "public"."sys_role_menu"."menu_id" IS '菜单ID';
COMMENT ON TABLE "public"."sys_role_menu" IS '角色和菜单关联表';

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO "public"."sys_role_menu" VALUES (2, 1);
INSERT INTO "public"."sys_role_menu" VALUES (2, 2);
INSERT INTO "public"."sys_role_menu" VALUES (2, 3);
INSERT INTO "public"."sys_role_menu" VALUES (2, 4);
INSERT INTO "public"."sys_role_menu" VALUES (2, 100);
INSERT INTO "public"."sys_role_menu" VALUES (2, 101);
INSERT INTO "public"."sys_role_menu" VALUES (2, 102);
INSERT INTO "public"."sys_role_menu" VALUES (2, 103);
INSERT INTO "public"."sys_role_menu" VALUES (2, 104);
INSERT INTO "public"."sys_role_menu" VALUES (2, 105);
INSERT INTO "public"."sys_role_menu" VALUES (2, 106);
INSERT INTO "public"."sys_role_menu" VALUES (2, 107);
INSERT INTO "public"."sys_role_menu" VALUES (2, 108);
INSERT INTO "public"."sys_role_menu" VALUES (2, 109);
INSERT INTO "public"."sys_role_menu" VALUES (2, 110);
INSERT INTO "public"."sys_role_menu" VALUES (2, 111);
INSERT INTO "public"."sys_role_menu" VALUES (2, 112);
INSERT INTO "public"."sys_role_menu" VALUES (2, 113);
INSERT INTO "public"."sys_role_menu" VALUES (2, 114);
INSERT INTO "public"."sys_role_menu" VALUES (2, 115);
INSERT INTO "public"."sys_role_menu" VALUES (2, 116);
INSERT INTO "public"."sys_role_menu" VALUES (2, 117);
INSERT INTO "public"."sys_role_menu" VALUES (2, 500);
INSERT INTO "public"."sys_role_menu" VALUES (2, 501);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1000);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1001);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1002);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1003);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1004);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1005);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1006);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1007);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1008);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1009);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1010);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1011);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1012);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1013);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1014);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1015);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1016);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1017);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1018);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1019);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1020);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1021);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1022);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1023);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1024);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1025);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1026);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1027);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1028);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1029);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1030);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1031);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1032);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1033);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1034);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1035);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1036);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1037);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1038);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1039);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1040);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1041);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1042);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1043);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1044);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1045);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1046);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1047);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1048);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1049);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1050);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1051);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1052);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1053);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1054);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1055);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1056);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1057);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1058);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1059);
INSERT INTO "public"."sys_role_menu" VALUES (2, 1060);



-- ----------------------------
-- Table structure for sys_task
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_task";
CREATE TABLE "public"."sys_task" (
                                     "id" bigserial PRIMARY KEY ,
                                     "code" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "type" "pg_catalog"."int4",
                                     "description" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "status" "pg_catalog"."int4",
                                     "start_date" "pg_catalog"."date",
                                     "end_date" "pg_catalog"."date",
                                     "create_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "create_time" "pg_catalog"."timestamp",
                                     "update_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "update_time" "pg_catalog"."timestamp"
)
;
COMMENT ON COLUMN "public"."sys_task"."id" IS '主键';
COMMENT ON COLUMN "public"."sys_task"."code" IS '任务编码';
COMMENT ON COLUMN "public"."sys_task"."name" IS '任务名称';
COMMENT ON COLUMN "public"."sys_task"."type" IS '任务类型';
COMMENT ON COLUMN "public"."sys_task"."description" IS '任务描述';
COMMENT ON COLUMN "public"."sys_task"."status" IS '任务状态';
COMMENT ON COLUMN "public"."sys_task"."start_date" IS '开始时间';
COMMENT ON COLUMN "public"."sys_task"."end_date" IS '结束时间';
COMMENT ON COLUMN "public"."sys_task"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_task"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_task"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_task"."update_time" IS '更新时间';


-- ----------------------------
-- Table structure for sys_tourist_label
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_tourist_label";
CREATE TABLE "public"."sys_tourist_label" (
                                              "id" bigserial PRIMARY KEY ,
                                              "tourist_id" "pg_catalog"."int8",
                                              "tourist_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                              "label_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                              "image_width" "pg_catalog"."int4",
                                              "image_height" "pg_catalog"."int4",
                                              "x1y1" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                              "x2y2" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                              "create_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                              "create_time" "pg_catalog"."timestamp",
                                              "remark" "pg_catalog"."varchar" COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."sys_tourist_label"."tourist_id" IS '景区ID';
COMMENT ON COLUMN "public"."sys_tourist_label"."tourist_name" IS '景区名称';
COMMENT ON COLUMN "public"."sys_tourist_label"."label_name" IS '标签名称';
COMMENT ON COLUMN "public"."sys_tourist_label"."image_width" IS '图片尺寸-宽';
COMMENT ON COLUMN "public"."sys_tourist_label"."image_height" IS '图片尺寸-高';
COMMENT ON COLUMN "public"."sys_tourist_label"."x1y1" IS '标签左上角坐标';
COMMENT ON COLUMN "public"."sys_tourist_label"."x2y2" IS '标签右下角坐标';
COMMENT ON COLUMN "public"."sys_tourist_label"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_tourist_label"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_tourist_label"."remark" IS '备注';
COMMENT ON TABLE "public"."sys_tourist_label" IS '景区标签表';


 -- ----------------------------
-- Table structure for sys_tourist_question
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_tourist_question";
CREATE TABLE "public"."sys_tourist_question" (
                                                 "id" bigserial PRIMARY KEY ,
                                                 "tourist_id" "pg_catalog"."int8",
                                                 "tourist_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                                 "user_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                                 "type" "pg_catalog"."int4",
                                                 "question_id" "pg_catalog"."int8",
                                                 "question_text" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                                 "answer_text" "pg_catalog"."text" COLLATE "pg_catalog"."default",
                                                 "create_by" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                                 "create_time" "pg_catalog"."timestamp"
)
;
COMMENT ON COLUMN "public"."sys_tourist_question"."tourist_id" IS '景区ID';
COMMENT ON COLUMN "public"."sys_tourist_question"."tourist_name" IS '景区名称';
COMMENT ON COLUMN "public"."sys_tourist_question"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."sys_tourist_question"."type" IS '1 问  2 答';
COMMENT ON COLUMN "public"."sys_tourist_question"."question_id" IS '问题ID，来自数据字典';
COMMENT ON COLUMN "public"."sys_tourist_question"."question_text" IS '问题';
COMMENT ON COLUMN "public"."sys_tourist_question"."answer_text" IS '答案';
COMMENT ON COLUMN "public"."sys_tourist_question"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_tourist_question"."create_time" IS '创建时间';
COMMENT ON TABLE "public"."sys_tourist_question" IS '景区常见问题对话记录表';



-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_user_post";
CREATE TABLE "public"."sys_user_post" (
                                          "user_id" "pg_catalog"."int8" NOT NULL,
                                          "post_id" "pg_catalog"."int8" NOT NULL
)
;
COMMENT ON COLUMN "public"."sys_user_post"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."sys_user_post"."post_id" IS '岗位ID';
COMMENT ON TABLE "public"."sys_user_post" IS '用户与岗位关联表';

-- ----------------------------
-- Records of sys_user_post
-- ----------------------------
INSERT INTO "public"."sys_user_post" VALUES (1, 1);
INSERT INTO "public"."sys_user_post" VALUES (2, 2);


-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_user_role";
CREATE TABLE "public"."sys_user_role" (
                                          "user_id" "pg_catalog"."int8" NOT NULL,
                                          "role_id" "pg_catalog"."int8" NOT NULL
)
;
COMMENT ON COLUMN "public"."sys_user_role"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."sys_user_role"."role_id" IS '角色ID';
COMMENT ON TABLE "public"."sys_user_role" IS '用户和角色关联表';

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO "public"."sys_user_role" VALUES (1, 1);
INSERT INTO "public"."sys_user_role" VALUES (2, 2);


-- ----------------------------
-- Table structure for token_detail
-- ----------------------------
DROP TABLE IF EXISTS "public"."token_detail";
CREATE TABLE "public"."token_detail" (
                                         "id" bigserial PRIMARY KEY ,
                                         "create_time" "pg_catalog"."timestamp",
                                         "update_time" "pg_catalog"."timestamp",
                                         "detail_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "payment_order_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "payment_amount" "pg_catalog"."numeric",
                                         "user_id" "pg_catalog"."int4",
                                         "user_phone" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "amount_incurred" "pg_catalog"."int4",
                                         "change_type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "transcation_type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "transaction_time" "pg_catalog"."timestamp"
)
;
COMMENT ON COLUMN "public"."token_detail"."detail_id" IS '明细 ID';
COMMENT ON COLUMN "public"."token_detail"."payment_order_id" IS '订单 id';
COMMENT ON COLUMN "public"."token_detail"."payment_amount" IS '订单金额';
COMMENT ON COLUMN "public"."token_detail"."user_id" IS '用户 ID';
COMMENT ON COLUMN "public"."token_detail"."user_phone" IS '手机号';
COMMENT ON COLUMN "public"."token_detail"."amount_incurred" IS '发生额';
COMMENT ON COLUMN "public"."token_detail"."change_type" IS '改变类型，收、支';
COMMENT ON COLUMN "public"."token_detail"."transcation_type" IS '交易类型';
COMMENT ON COLUMN "public"."token_detail"."transaction_time" IS '交易时间';


-- ----------------------------
-- Table structure for us_score_change
-- ----------------------------
DROP TABLE IF EXISTS "public"."us_score_change";
CREATE TABLE "public"."us_score_change" (
                                            "id" bigserial PRIMARY KEY ,
                                            "score_change_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                            "user_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                            "change_type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                            "change_score" "pg_catalog"."int4",
                                            "rule_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                            "before_score" "pg_catalog"."int4",
                                            "cur_score" "pg_catalog"."int4",
                                            "create_time" "pg_catalog"."timestamp",
                                            "update_time" "pg_catalog"."timestamp"
)
;
COMMENT ON COLUMN "public"."us_score_change"."score_change_id" IS 'bizID';
COMMENT ON COLUMN "public"."us_score_change"."change_type" IS '增加、 减少, + -';
COMMENT ON COLUMN "public"."us_score_change"."change_score" IS '改变的分值';
COMMENT ON COLUMN "public"."us_score_change"."rule_id" IS '规则id';
COMMENT ON COLUMN "public"."us_score_change"."before_score" IS '变化前分值';
COMMENT ON COLUMN "public"."us_score_change"."cur_score" IS '变化后分值';



-- ----------------------------
-- Table structure for us_sort_change
-- ----------------------------
DROP TABLE IF EXISTS "public"."us_sort_change";
CREATE TABLE "public"."us_sort_change" (
                                           "id" bigserial PRIMARY KEY ,
                                           "sort_change_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                           "user_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                           "before_sort" "pg_catalog"."int8",
                                           "cur_sort" "pg_catalog"."int8",
                                           "change_statue" "pg_catalog"."int4",
                                           "task_version" "pg_catalog"."int4",
                                           "create_time" "pg_catalog"."timestamp",
                                           "update_time" "pg_catalog"."timestamp"
)
;
COMMENT ON COLUMN "public"."us_sort_change"."sort_change_id" IS 'bizID';
COMMENT ON COLUMN "public"."us_sort_change"."before_sort" IS '变化前排名';
COMMENT ON COLUMN "public"."us_sort_change"."cur_sort" IS '当前排名';
COMMENT ON COLUMN "public"."us_sort_change"."change_statue" IS '排名变化状态  0 上升， 1 下降， 2 不变';
COMMENT ON COLUMN "public"."us_sort_change"."task_version" IS '变化版本';


-- ----------------------------
-- Table structure for us_user_sort
-- ----------------------------
DROP TABLE IF EXISTS "public"."us_user_sort";
CREATE TABLE "public"."us_user_sort" (
                                         "id" bigserial PRIMARY KEY ,
                                         "user_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "user_score" "pg_catalog"."int4",
                                         "goal_flag" "pg_catalog"."int4",
                                         "user_sort" "pg_catalog"."int8",
                                         "before_user_sort" "pg_catalog"."int8",
                                         "user_referrer_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "user_agency" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "user_operate" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "user_team" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                         "create_time" "pg_catalog"."timestamp",
                                         "update_time" "pg_catalog"."timestamp",
                                         "statue" "pg_catalog"."int4",
                                         "task_version" "pg_catalog"."int4"
)
;
COMMENT ON COLUMN "public"."us_user_sort"."user_score" IS '分值';
COMMENT ON COLUMN "public"."us_user_sort"."goal_flag" IS '是否达标 1 达标， 0 未达标';
COMMENT ON COLUMN "public"."us_user_sort"."user_sort" IS '排序';
COMMENT ON COLUMN "public"."us_user_sort"."before_user_sort" IS '上次排序';
COMMENT ON COLUMN "public"."us_user_sort"."user_referrer_id" IS '直接推荐人id';
COMMENT ON COLUMN "public"."us_user_sort"."user_agency" IS '地方代理';
COMMENT ON COLUMN "public"."us_user_sort"."user_operate" IS '渠道';
COMMENT ON COLUMN "public"."us_user_sort"."user_team" IS '技术团队';
COMMENT ON COLUMN "public"."us_user_sort"."statue" IS '用户状态，0 可用， 1 不可用';



-- ----------------------------
-- Table structure for use_token
-- ----------------------------
DROP TABLE IF EXISTS "public"."use_token";
CREATE TABLE "public"."use_token" (
                                      "id" bigserial PRIMARY KEY ,
                                      "biz_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                      "create_time" "pg_catalog"."timestamp",
                                      "use_token" "pg_catalog"."int4",
                                      "input_token" "pg_catalog"."int4",
                                      "output_token" "pg_catalog"."int4",
                                      "user_input" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                      "user_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                      "bot_platform" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                      "bot_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                      "conversation_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."use_token"."biz_id" IS 'ID';
COMMENT ON COLUMN "public"."use_token"."use_token" IS '使用的token数';
COMMENT ON COLUMN "public"."use_token"."input_token" IS '使用的token数';
COMMENT ON COLUMN "public"."use_token"."output_token" IS '使用的token数';
COMMENT ON COLUMN "public"."use_token"."user_input" IS '用户输入';
COMMENT ON COLUMN "public"."use_token"."user_name" IS '用户';
COMMENT ON COLUMN "public"."use_token"."bot_platform" IS 'bot 平台';
COMMENT ON COLUMN "public"."use_token"."bot_id" IS 'botid ';
COMMENT ON COLUMN "public"."use_token"."conversation_id" IS '会话ID';
COMMENT ON TABLE "public"."use_token" IS 'token 使用日志';


-- ----------------------------
-- Table structure for user_rec
-- ----------------------------
DROP TABLE IF EXISTS "public"."user_rec";
CREATE TABLE "public"."user_rec" (
                                     "id" bigserial PRIMARY KEY ,
                                     "biz_id" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "create_time" "pg_catalog"."timestamp",
                                     "user_id" "pg_catalog"."int8",
                                     "user_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "rec_user_id" "pg_catalog"."int8",
                                     "rec_user_name" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "rec_statue" "pg_catalog"."varchar" COLLATE "pg_catalog"."default",
                                     "rec_bean_count" "pg_catalog"."int4"
)
;
COMMENT ON COLUMN "public"."user_rec"."biz_id" IS 'ID';
COMMENT ON COLUMN "public"."user_rec"."user_id" IS '被推荐人ID';
COMMENT ON COLUMN "public"."user_rec"."user_name" IS '被推荐人name';
COMMENT ON COLUMN "public"."user_rec"."rec_user_id" IS '推荐人ID';
COMMENT ON COLUMN "public"."user_rec"."rec_user_name" IS '推荐人name';
COMMENT ON COLUMN "public"."user_rec"."rec_statue" IS '0 奖励发放成功 1 发放失败';
COMMENT ON COLUMN "public"."user_rec"."rec_bean_count" IS '奖励数量';
COMMENT ON TABLE "public"."user_rec" IS '推荐关系';



-- ----------------------------
-- Table structure for user_suggestion
-- ----------------------------
DROP TABLE IF EXISTS "public"."user_suggestion";
CREATE TABLE "public"."user_suggestion" (
                                            "id"  serial  PRIMARY KEY,
                                            "user_id" "pg_catalog"."int8" NOT NULL,
                                            "user_phone" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
                                            "type" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
                                            "content" "pg_catalog"."varchar" COLLATE "pg_catalog"."default" NOT NULL,
                                            "url_list" "pg_catalog"."text" COLLATE "pg_catalog"."default",
                                            "num" "pg_catalog"."int4" NOT NULL DEFAULT 0,
                                            "create_time" "pg_catalog"."timestamp" NOT NULL DEFAULT now(),
                                            "update_time" "pg_catalog"."timestamp" NOT NULL DEFAULT now()
)
;











